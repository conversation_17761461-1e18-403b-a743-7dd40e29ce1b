<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>API密钥管理 - 宠物医疗AI开放平台</title>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: #f8fafc;
        color: #334155;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem 2rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .header-content {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .logo {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.5rem;
        font-weight: bold;
      }

      .nav {
        display: flex;
        gap: 2rem;
      }

      .nav a {
        color: white;
        text-decoration: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        transition: background 0.2s;
      }

      .nav a:hover,
      .nav a.active {
        background: rgba(255, 255, 255, 0.2);
      }

      .user-info {
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .logout-btn {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        cursor: pointer;
        transition: background 0.2s;
      }

      .container {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 0 2rem;
      }

      .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
      }

      .page-title {
        font-size: 2rem;
        font-weight: bold;
        color: #1e293b;
      }

      .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        border: none;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
      }

      .btn-primary {
        background: #3b82f6;
        color: white;
      }

      .btn-primary:hover {
        background: #2563eb;
      }

      .btn-secondary {
        background: #6b7280;
        color: white;
      }

      .btn-danger {
        background: #ef4444;
        color: white;
      }

      .btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
      }

      .api-keys-grid {
        display: grid;
        gap: 1.5rem;
        margin-bottom: 2rem;
      }

      .api-key-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
      }

      .api-key-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
      }

      .api-key-info {
        flex: 1;
      }

      .api-key-name {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.5rem;
      }

      .api-key-description {
        color: #6b7280;
        font-size: 0.875rem;
        margin-bottom: 1rem;
      }

      .api-key-value {
        background: #f8fafc;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 0.75rem;
        font-family: "Monaco", "Menlo", monospace;
        font-size: 0.875rem;
        color: #374151;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
      }

      .api-key-masked {
        color: #6b7280;
      }

      .copy-btn {
        background: none;
        border: none;
        color: #6b7280;
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 4px;
        transition: color 0.2s;
      }

      .copy-btn:hover {
        color: #374151;
      }

      .api-key-meta {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
      }

      .meta-item {
        display: flex;
        flex-direction: column;
      }

      .meta-label {
        font-size: 0.75rem;
        color: #6b7280;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-bottom: 0.25rem;
      }

      .meta-value {
        font-weight: 500;
        color: #374151;
      }

      .status-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
      }

      .status-active {
        background: #dcfce7;
        color: #166534;
      }

      .status-expired {
        background: #fef2f2;
        color: #991b1b;
      }

      .api-key-actions {
        display: flex;
        gap: 0.5rem;
      }

      .permissions-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-top: 0.5rem;
      }

      .permission-tag {
        background: #e0e7ff;
        color: #3730a3;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        font-size: 0.75rem;
      }

      .create-key-form {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
        display: none;
      }

      .form-group {
        margin-bottom: 1.5rem;
      }

      .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #374151;
      }

      .form-input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.2s;
      }

      .form-input:focus {
        outline: none;
        border-color: #3b82f6;
      }

      .form-textarea {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        font-size: 1rem;
        resize: vertical;
        min-height: 100px;
      }

      .permissions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
      }

      .permission-group {
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 1rem;
      }

      .permission-group-title {
        font-weight: 600;
        margin-bottom: 0.75rem;
        color: #374151;
      }

      .permission-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
      }

      .permission-item input[type="checkbox"] {
        width: 1rem;
        height: 1rem;
      }

      .alert {
        padding: 1rem 1.5rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        display: none;
      }

      .alert-success {
        background: #f0fdf4;
        color: #166534;
        border: 1px solid #bbf7d0;
      }

      .alert-error {
        background: #fef2f2;
        color: #dc2626;
        border: 1px solid #fecaca;
      }

      .loading {
        text-align: center;
        padding: 2rem;
        color: #6b7280;
      }

      .loading-spinner {
        display: inline-block;
        width: 2rem;
        height: 2rem;
        border: 3px solid #e5e7eb;
        border-top: 3px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      @media (max-width: 768px) {
        .page-header {
          flex-direction: column;
          gap: 1rem;
          align-items: stretch;
        }

        .api-key-header {
          flex-direction: column;
          gap: 1rem;
        }

        .api-key-actions {
          justify-content: flex-start;
        }

        .permissions-grid {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <div class="header">
      <div class="header-content">
        <div class="logo">
          <i class="fas fa-paw"></i>
          <span>宠物医疗AI平台</span>
        </div>
        <nav class="nav">
          <a href="index.html">首页</a>
          <a href="user-management.html">用户管理</a>
          <a href="api-key-management.html" class="active">API密钥</a>
          <a href="agent-management.html">智能体</a>
          <a href="ocr-service.html">OCR服务</a>
          <a href="system-monitoring.html">系统监控</a>
        </nav>
        <div class="user-info">
          <span id="currentUserName">管理员</span>
          <button class="logout-btn" onclick="logout()">
            <i class="fas fa-sign-out-alt"></i>
            退出
          </button>
        </div>
      </div>
    </div>

    <div class="container">
      <div class="page-header">
        <h1 class="page-title">API密钥管理</h1>
        <button class="btn btn-primary" onclick="toggleCreateForm()">
          <i class="fas fa-plus"></i>
          创建API密钥
        </button>
      </div>

      <div class="alert alert-success" id="successAlert"></div>
      <div class="alert alert-error" id="errorAlert"></div>

      <!-- 创建API密钥表单 -->
      <div class="create-key-form" id="createKeyForm">
        <h3 style="margin-bottom: 1.5rem; color: #1e293b">创建新的API密钥</h3>

        <form id="apiKeyForm">
          <div class="form-group">
            <label class="form-label">密钥名称</label>
            <input
              type="text"
              class="form-input"
              id="keyName"
              placeholder="为您的API密钥起一个名称"
              required
            />
          </div>

          <div class="form-group">
            <label class="form-label">描述</label>
            <textarea
              class="form-textarea"
              id="keyDescription"
              placeholder="描述这个API密钥的用途（可选）"
            ></textarea>
          </div>

          <div class="form-group">
            <label class="form-label">过期时间</label>
            <input type="datetime-local" class="form-input" id="expiresAt" />
            <small style="color: #6b7280; font-size: 0.875rem"
              >留空表示永不过期</small
            >
          </div>

          <div class="form-group">
            <label class="form-label">权限设置</label>
            <div class="permissions-grid">
              <div class="permission-group">
                <div class="permission-group-title">用户管理</div>
                <div class="permission-item">
                  <input
                    type="checkbox"
                    id="perm_users_read"
                    value="users:read"
                  />
                  <label for="perm_users_read">查看用户</label>
                </div>
                <div class="permission-item">
                  <input
                    type="checkbox"
                    id="perm_users_write"
                    value="users:write"
                  />
                  <label for="perm_users_write">管理用户</label>
                </div>
              </div>

              <div class="permission-group">
                <div class="permission-group-title">智能体</div>
                <div class="permission-item">
                  <input
                    type="checkbox"
                    id="perm_agents_read"
                    value="agents:read"
                  />
                  <label for="perm_agents_read">查看智能体</label>
                </div>
                <div class="permission-item">
                  <input
                    type="checkbox"
                    id="perm_agents_execute"
                    value="agents:execute"
                  />
                  <label for="perm_agents_execute">执行智能体</label>
                </div>
              </div>

              <div class="permission-group">
                <div class="permission-group-title">OCR服务</div>
                <div class="permission-item">
                  <input type="checkbox" id="perm_ocr_use" value="ocr:use" />
                  <label for="perm_ocr_use">使用OCR</label>
                </div>
              </div>
            </div>
          </div>

          <div style="display: flex; gap: 1rem; justify-content: flex-end">
            <button
              type="button"
              class="btn btn-secondary"
              onclick="toggleCreateForm()"
            >
              取消
            </button>
            <button type="submit" class="btn btn-primary">创建密钥</button>
          </div>
        </form>
      </div>

      <!-- API密钥列表 -->
      <div id="loadingIndicator" class="loading">
        <div class="loading-spinner"></div>
        <div>加载中...</div>
      </div>

      <div class="api-keys-grid" id="apiKeysContainer" style="display: none">
        <!-- API密钥卡片将在这里动态生成 -->
      </div>
    </div>

    <script>
      // API配置
      const API_BASE_URL = "http://localhost:8000";

      // 全局变量
      let apiKeys = [];

      // DOM元素
      const createKeyForm = document.getElementById("createKeyForm");
      const apiKeyForm = document.getElementById("apiKeyForm");
      const loadingIndicator = document.getElementById("loadingIndicator");
      const apiKeysContainer = document.getElementById("apiKeysContainer");
      const successAlert = document.getElementById("successAlert");
      const errorAlert = document.getElementById("errorAlert");

      // 页面加载时初始化
      document.addEventListener("DOMContentLoaded", function () {
        checkAuth();
        loadApiKeys();
      });

      // 检查认证状态
      function checkAuth() {
        const token = localStorage.getItem("access_token");
        if (!token) {
          window.location.href = "auth-login.html";
          return;
        }

        const currentUser = localStorage.getItem("current_user") || "管理员";
        document.getElementById("currentUserName").textContent = currentUser;
      }

      // 显示/隐藏创建表单
      function toggleCreateForm() {
        const isVisible = createKeyForm.style.display === "block";
        createKeyForm.style.display = isVisible ? "none" : "block";

        if (!isVisible) {
          // 重置表单
          apiKeyForm.reset();
          hideAlerts();
        }
      }

      // 显示提示信息
      function showAlert(message, type = "success") {
        hideAlerts();
        const alertElement = type === "success" ? successAlert : errorAlert;
        alertElement.textContent = message;
        alertElement.style.display = "block";

        // 自动隐藏
        setTimeout(() => {
          alertElement.style.display = "none";
        }, 5000);
      }

      function hideAlerts() {
        successAlert.style.display = "none";
        errorAlert.style.display = "none";
      }

      // 加载API密钥列表（演示模式）
      async function loadApiKeys() {
        try {
          showLoading(true);

          // 模拟网络延迟
          await new Promise((resolve) => setTimeout(resolve, 500));

          // 获取或初始化演示数据
          let demoApiKeys = JSON.parse(
            localStorage.getItem("demo_api_keys") || "[]"
          );

          if (demoApiKeys.length === 0) {
            // 初始化演示API密钥数据
            demoApiKeys = [
              {
                id: 1,
                name: "开发环境密钥",
                key: "sk-demo1234567890abcdef",
                permissions: ["chat", "embeddings"],
                status: "active",
                created_at: "2024-01-15T10:30:00Z",
                last_used: "2024-01-20T14:25:00Z",
                expires_at: "2024-12-31T23:59:59Z",
                usage_count: 1250,
                usage_limit: 10000,
              },
              {
                id: 2,
                name: "生产环境密钥",
                key: "sk-prod9876543210fedcba",
                permissions: ["chat", "embeddings", "models"],
                status: "active",
                created_at: "2024-01-16T09:15:00Z",
                last_used: "2024-01-19T16:45:00Z",
                expires_at: "2024-06-30T23:59:59Z",
                usage_count: 5680,
                usage_limit: 50000,
              },
              {
                id: 3,
                name: "测试密钥",
                key: "sk-test1111222233334444",
                permissions: ["chat"],
                status: "inactive",
                created_at: "2024-01-17T11:20:00Z",
                last_used: "2024-01-18T13:30:00Z",
                expires_at: "2024-03-31T23:59:59Z",
                usage_count: 45,
                usage_limit: 1000,
              },
            ];
            localStorage.setItem("demo_api_keys", JSON.stringify(demoApiKeys));
          }

          apiKeys = demoApiKeys;
          renderApiKeys();
        } catch (error) {
          console.error("加载API密钥列表失败:", error);
          showAlert("加载失败，请重试", "error");
          apiKeys = [];
          renderApiKeys();
        } finally {
          showLoading(false);
        }
      }

      // 显示/隐藏加载状态
      function showLoading(loading) {
        if (loading) {
          loadingIndicator.style.display = "block";
          apiKeysContainer.style.display = "none";
        } else {
          loadingIndicator.style.display = "none";
          apiKeysContainer.style.display = "grid";
        }
      }

      // 渲染API密钥列表
      function renderApiKeys() {
        if (apiKeys.length === 0) {
          apiKeysContainer.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 3rem; color: #6b7280;">
                        <i class="fas fa-key" style="font-size: 3rem; margin-bottom: 1rem; display: block;"></i>
                        <h3 style="margin-bottom: 0.5rem;">暂无API密钥</h3>
                        <p>点击"创建API密钥"按钮来创建您的第一个API密钥</p>
                    </div>
                `;
          return;
        }

        apiKeysContainer.innerHTML = apiKeys
          .map(
            (key) => `
                <div class="api-key-card">
                    <div class="api-key-header">
                        <div class="api-key-info">
                            <div class="api-key-name">${escapeHtml(
                              key.name
                            )}</div>
                            <div class="api-key-description">${escapeHtml(
                              key.description || "无描述"
                            )}</div>
                        </div>
                        <div class="api-key-actions">
                            <button class="btn btn-sm btn-secondary" onclick="editApiKey('${
                              key.id
                            }')" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteApiKey('${
                              key.id
                            }', '${escapeHtml(key.name)}')" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>

                    <div class="api-key-value">
                        <span class="api-key-masked" id="key-${key.id}">
                            ${
                              key.key
                                ? maskApiKey(key.key)
                                : "sk-••••••••••••••••••••••••••••••••"
                            }
                        </span>
                        <button class="copy-btn" onclick="copyApiKey('${
                          key.key || ""
                        }')" title="复制密钥">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>

                    <div class="api-key-meta">
                        <div class="meta-item">
                            <div class="meta-label">状态</div>
                            <div class="meta-value">
                                <span class="status-badge ${
                                  getKeyStatus(key) === "激活"
                                    ? "status-active"
                                    : "status-expired"
                                }">
                                    ${getKeyStatus(key)}
                                </span>
                            </div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">创建时间</div>
                            <div class="meta-value">${formatDate(
                              key.created_at
                            )}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">过期时间</div>
                            <div class="meta-value">${
                              key.expires_at
                                ? formatDate(key.expires_at)
                                : "永不过期"
                            }</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">最后使用</div>
                            <div class="meta-value">${
                              key.last_used
                                ? formatDate(key.last_used)
                                : "从未使用"
                            }</div>
                        </div>
                    </div>

                    ${
                      key.permissions && key.permissions.length > 0
                        ? `
                        <div class="permissions-list">
                            ${key.permissions
                              .map(
                                (perm) => `
                                <span class="permission-tag">${getPermissionDisplayName(
                                  perm
                                )}</span>
                            `
                              )
                              .join("")}
                        </div>
                    `
                        : ""
                    }
                </div>
            `
          )
          .join("");
      }

      // 掩码API密钥
      function maskApiKey(key) {
        if (!key) return "sk-••••••••••••••••••••••••••••••••";
        return key.substring(0, 7) + "••••••••••••••••••••••••••••••••";
      }

      // 获取密钥状态
      function getKeyStatus(key) {
        if (!key.is_active) return "已禁用";
        if (key.expires_at && new Date(key.expires_at) < new Date())
          return "已过期";
        return "激活";
      }

      // 获取权限显示名称
      function getPermissionDisplayName(permission) {
        const permissionMap = {
          "users:read": "查看用户",
          "users:write": "管理用户",
          "agents:read": "查看智能体",
          "agents:execute": "执行智能体",
          "ocr:use": "使用OCR",
        };
        return permissionMap[permission] || permission;
      }

      // 格式化日期
      function formatDate(dateString) {
        if (!dateString) return "-";
        const date = new Date(dateString);
        return date.toLocaleString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
        });
      }

      // HTML转义
      function escapeHtml(text) {
        const div = document.createElement("div");
        div.textContent = text;
        return div.innerHTML;
      }

      // 复制API密钥
      async function copyApiKey(key) {
        if (!key) {
          showAlert("无法复制空密钥", "error");
          return;
        }

        try {
          await navigator.clipboard.writeText(key);
          showAlert("API密钥已复制到剪贴板");
        } catch (error) {
          console.error("复制失败:", error);
          showAlert("复制失败，请手动复制", "error");
        }
      }

      // 创建API密钥表单提交
      apiKeyForm.addEventListener("submit", async function (e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const permissions = [];

        // 收集选中的权限
        document
          .querySelectorAll('input[type="checkbox"]:checked')
          .forEach((checkbox) => {
            permissions.push(checkbox.value);
          });

        const keyData = {
          name: document.getElementById("keyName").value.trim(),
          description: document.getElementById("keyDescription").value.trim(),
          expires_at: document.getElementById("expiresAt").value || null,
          permissions: permissions,
        };

        await createApiKey(keyData);
      });

      // 创建API密钥（演示模式）
      async function createApiKey(keyData) {
        try {
          // 模拟网络延迟
          await new Promise((resolve) => setTimeout(resolve, 500));

          // 获取现有API密钥数据
          let demoApiKeys = JSON.parse(
            localStorage.getItem("demo_api_keys") || "[]"
          );

          // 检查名称是否已存在
          if (demoApiKeys.some((key) => key.name === keyData.name)) {
            throw new Error("API密钥名称已存在");
          }

          // 生成新的API密钥
          const newApiKey = {
            id: Math.max(...demoApiKeys.map((k) => k.id), 0) + 1,
            name: keyData.name,
            key: `sk-demo${Date.now()}${Math.random()
              .toString(36)
              .substr(2, 9)}`,
            permissions: keyData.permissions || ["chat"],
            status: "active",
            created_at: new Date().toISOString(),
            last_used: null,
            expires_at: keyData.expires_at || null,
            usage_count: 0,
            usage_limit: keyData.usage_limit || 10000,
          };

          demoApiKeys.push(newApiKey);
          localStorage.setItem("demo_api_keys", JSON.stringify(demoApiKeys));

          showAlert("API密钥创建成功");
          toggleCreateForm();
          loadApiKeys();

          // 显示新创建的密钥
          setTimeout(() => {
            alert(
              `新的API密钥已创建：\n\n${newApiKey.key}\n\n请妥善保存，此密钥只会显示一次！`
            );
          }, 500);
        } catch (error) {
          console.error("创建API密钥失败:", error);
          showAlert("创建API密钥失败: " + error.message, "error");
        }
      }

      // 编辑API密钥
      function editApiKey(keyId) {
        showAlert("编辑API密钥功能开发中...", "error");
      }

      // 删除API密钥（演示模式）
      async function deleteApiKey(keyId, keyName) {
        if (!confirm(`确定要删除API密钥 "${keyName}" 吗？此操作不可恢复。`)) {
          return;
        }

        try {
          // 模拟网络延迟
          await new Promise((resolve) => setTimeout(resolve, 500));

          // 获取现有API密钥数据
          let demoApiKeys = JSON.parse(
            localStorage.getItem("demo_api_keys") || "[]"
          );

          // 删除API密钥
          demoApiKeys = demoApiKeys.filter((key) => key.id != keyId);
          localStorage.setItem("demo_api_keys", JSON.stringify(demoApiKeys));

          showAlert("API密钥删除成功");
          loadApiKeys();
        } catch (error) {
          console.error("删除API密钥失败:", error);
          showAlert("删除API密钥失败: " + error.message, "error");
        }
      }

      // 退出登录
      function logout() {
        if (confirm("确定要退出登录吗？")) {
          localStorage.removeItem("access_token");
          localStorage.removeItem("refresh_token");
          localStorage.removeItem("current_user");
          window.location.href = "auth-login.html";
        }
      }
    </script>
  </body>
</html>
