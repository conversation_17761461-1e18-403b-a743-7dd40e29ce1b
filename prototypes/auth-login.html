<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>用户登录 - 宠物医疗AI开放平台</title>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #334155;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem;
      }

      .login-container {
        background: white;
        border-radius: 16px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        width: 100%;
        max-width: 400px;
      }

      .login-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        text-align: center;
      }

      .logo {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
      }

      .subtitle {
        opacity: 0.9;
        font-size: 0.875rem;
      }

      .login-form {
        padding: 2rem;
      }

      .form-group {
        margin-bottom: 1.5rem;
      }

      .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #374151;
      }

      .form-input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.2s, box-shadow 0.2s;
      }

      .form-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      .form-input.error {
        border-color: #ef4444;
      }

      .error-message {
        color: #ef4444;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: none;
      }

      .password-toggle {
        position: relative;
      }

      .password-toggle-btn {
        position: absolute;
        right: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #6b7280;
        cursor: pointer;
        padding: 0.25rem;
      }

      .password-toggle-btn:hover {
        color: #374151;
      }

      .form-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        font-size: 0.875rem;
      }

      .remember-me {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .remember-me input[type="checkbox"] {
        width: 1rem;
        height: 1rem;
      }

      .forgot-password {
        color: #667eea;
        text-decoration: none;
      }

      .forgot-password:hover {
        text-decoration: underline;
      }

      .login-btn {
        width: 100%;
        padding: 0.75rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: transform 0.2s, box-shadow 0.2s;
        margin-bottom: 1rem;
      }

      .login-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      }

      .login-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }

      .loading-spinner {
        display: none;
        width: 1rem;
        height: 1rem;
        border: 2px solid transparent;
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 0.5rem;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .register-link {
        text-align: center;
        padding-top: 1rem;
        border-top: 1px solid #e5e7eb;
        color: #6b7280;
        font-size: 0.875rem;
      }

      .register-link a {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;
      }

      .register-link a:hover {
        text-decoration: underline;
      }

      .alert {
        padding: 0.75rem 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        font-size: 0.875rem;
        display: none;
      }

      .alert-error {
        background: #fef2f2;
        color: #dc2626;
        border: 1px solid #fecaca;
      }

      .alert-success {
        background: #f0fdf4;
        color: #16a34a;
        border: 1px solid #bbf7d0;
      }

      .back-home {
        position: absolute;
        top: 2rem;
        left: 2rem;
        color: white;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        opacity: 0.9;
        transition: opacity 0.2s;
      }

      .back-home:hover {
        opacity: 1;
      }

      @media (max-width: 640px) {
        .login-container {
          margin: 1rem;
        }

        .back-home {
          position: static;
          margin-bottom: 2rem;
          justify-content: center;
        }
      }
    </style>
  </head>
  <body>
    <a href="index.html" class="back-home">
      <i class="fas fa-arrow-left"></i>
      返回首页
    </a>

    <div class="login-container">
      <div class="login-header">
        <div class="logo">
          <i class="fas fa-paw"></i>
          <span>宠物医疗AI平台</span>
        </div>
        <div class="subtitle">登录您的账户</div>
      </div>

      <div class="login-form">
        <div class="alert alert-error" id="errorAlert"></div>
        <div class="alert alert-success" id="successAlert"></div>

        <form id="loginForm">
          <div class="form-group">
            <label for="email" class="form-label">邮箱地址</label>
            <input
              type="email"
              id="email"
              name="email"
              class="form-input"
              placeholder="请输入您的邮箱地址"
              required
            />
            <div class="error-message" id="emailError"></div>
          </div>

          <div class="form-group">
            <label for="password" class="form-label">密码</label>
            <div class="password-toggle">
              <input
                type="password"
                id="password"
                name="password"
                class="form-input"
                placeholder="请输入您的密码"
                required
              />
              <button
                type="button"
                class="password-toggle-btn"
                id="passwordToggle"
              >
                <i class="fas fa-eye"></i>
              </button>
            </div>
            <div class="error-message" id="passwordError"></div>
          </div>

          <div class="form-options">
            <label class="remember-me">
              <input type="checkbox" id="rememberMe" name="rememberMe" />
              <span>记住我</span>
            </label>
            <a href="auth-reset.html" class="forgot-password">忘记密码？</a>
          </div>

          <button type="submit" class="login-btn" id="loginBtn">
            <span class="loading-spinner" id="loadingSpinner"></span>
            <span id="loginBtnText">登录</span>
          </button>
        </form>

        <div class="register-link">
          还没有账户？ <a href="auth-register.html">立即注册</a>
        </div>

        <!-- 演示账户提示 -->
        <div
          style="
            margin-top: 1.5rem;
            padding: 1rem;
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            font-size: 0.875rem;
            color: #0369a1;
          "
        >
          <div style="font-weight: 600; margin-bottom: 0.5rem">💡 演示账户</div>
          <div>邮箱：<EMAIL></div>
          <div>密码：123456</div>
          <div style="margin-top: 0.5rem; font-size: 0.75rem; opacity: 0.8">
            或使用 <EMAIL> / password123
          </div>
        </div>
      </div>
    </div>

    <script>
      // API配置
      const API_BASE_URL = "http://localhost:8000";

      // DOM元素
      const loginForm = document.getElementById("loginForm");
      const emailInput = document.getElementById("email");
      const passwordInput = document.getElementById("password");
      const passwordToggle = document.getElementById("passwordToggle");
      const rememberMeCheckbox = document.getElementById("rememberMe");
      const loginBtn = document.getElementById("loginBtn");
      const loginBtnText = document.getElementById("loginBtnText");
      const loadingSpinner = document.getElementById("loadingSpinner");
      const errorAlert = document.getElementById("errorAlert");
      const successAlert = document.getElementById("successAlert");

      // 密码显示/隐藏切换
      passwordToggle.addEventListener("click", function () {
        const type =
          passwordInput.getAttribute("type") === "password"
            ? "text"
            : "password";
        passwordInput.setAttribute("type", type);

        const icon = this.querySelector("i");
        icon.className =
          type === "password" ? "fas fa-eye" : "fas fa-eye-slash";
      });

      // 表单验证
      function validateForm() {
        let isValid = true;

        // 清除之前的错误状态
        clearErrors();

        // 验证邮箱
        const email = emailInput.value.trim();
        if (!email) {
          showFieldError("email", "请输入邮箱地址");
          isValid = false;
        } else if (!isValidEmail(email)) {
          showFieldError("email", "请输入有效的邮箱地址");
          isValid = false;
        }

        // 验证密码
        const password = passwordInput.value;
        if (!password) {
          showFieldError("password", "请输入密码");
          isValid = false;
        } else if (password.length < 6) {
          showFieldError("password", "密码长度至少6位");
          isValid = false;
        }

        return isValid;
      }

      function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
      }

      function showFieldError(fieldName, message) {
        const input = document.getElementById(fieldName);
        const errorElement = document.getElementById(fieldName + "Error");

        input.classList.add("error");
        errorElement.textContent = message;
        errorElement.style.display = "block";
      }

      function clearErrors() {
        const inputs = document.querySelectorAll(".form-input");
        const errors = document.querySelectorAll(".error-message");

        inputs.forEach((input) => input.classList.remove("error"));
        errors.forEach((error) => {
          error.style.display = "none";
          error.textContent = "";
        });

        hideAlert();
      }

      function showAlert(message, type = "error") {
        const alertElement = type === "error" ? errorAlert : successAlert;
        const otherAlert = type === "error" ? successAlert : errorAlert;

        otherAlert.style.display = "none";
        alertElement.textContent = message;
        alertElement.style.display = "block";

        // 自动隐藏成功消息
        if (type === "success") {
          setTimeout(() => {
            alertElement.style.display = "none";
          }, 3000);
        }
      }

      function hideAlert() {
        errorAlert.style.display = "none";
        successAlert.style.display = "none";
      }

      function setLoading(loading) {
        loginBtn.disabled = loading;
        loadingSpinner.style.display = loading ? "inline-block" : "none";
        loginBtnText.textContent = loading ? "登录中..." : "登录";
      }

      // 登录处理（演示模式）
      async function handleLogin(formData) {
        try {
          // 模拟网络延迟
          await new Promise((resolve) => setTimeout(resolve, 1000));

          // 验证演示账户
          const validAccounts = [
            {
              email: "<EMAIL>",
              password: "123456",
              username: "管理员",
            },
            {
              email: "<EMAIL>",
              password: "password123",
              username: "测试用户",
            },
            {
              email: "<EMAIL>",
              password: "demo123",
              username: "演示用户",
            },
          ];

          const account = validAccounts.find(
            (acc) =>
              acc.email === formData.email && acc.password === formData.password
          );

          if (account) {
            // 登录成功 - 存储演示令牌
            localStorage.setItem("access_token", "demo_token_" + Date.now());
            localStorage.setItem("refresh_token", "demo_refresh_" + Date.now());
            localStorage.setItem("current_user", account.username);

            if (formData.rememberMe) {
              // 记住我功能 - 存储到localStorage
              localStorage.setItem("remember_me", "true");
              localStorage.setItem("user_email", formData.email);
            } else {
              localStorage.removeItem("remember_me");
              localStorage.removeItem("user_email");
            }

            showAlert("登录成功！正在跳转...", "success");

            // 延迟跳转到主页
            setTimeout(() => {
              window.location.href = "user-management.html";
            }, 1500);
          } else {
            // 登录失败
            showAlert(
              "邮箱或密码错误，请使用演示账户：<EMAIL> / 123456"
            );
          }
        } catch (error) {
          console.error("登录请求失败:", error);
          showAlert("登录失败，请重试");
        }
      }

      // 表单提交事件
      loginForm.addEventListener("submit", async function (e) {
        e.preventDefault();

        if (!validateForm()) {
          return;
        }

        setLoading(true);

        const formData = {
          email: emailInput.value.trim(),
          password: passwordInput.value,
          rememberMe: rememberMeCheckbox.checked,
        };

        await handleLogin(formData);
        setLoading(false);
      });

      // 页面加载时检查记住我功能
      window.addEventListener("load", function () {
        if (localStorage.getItem("remember_me") === "true") {
          const savedEmail = localStorage.getItem("user_email");
          if (savedEmail) {
            emailInput.value = savedEmail;
            rememberMeCheckbox.checked = true;
          }
        }

        // 检查是否已经登录
        const accessToken = localStorage.getItem("access_token");
        if (accessToken) {
          console.log("用户已登录，令牌存在");
        }
      });

      // 输入框失焦验证
      emailInput.addEventListener("blur", function () {
        const email = this.value.trim();
        if (email && !isValidEmail(email)) {
          showFieldError("email", "请输入有效的邮箱地址");
        }
      });

      passwordInput.addEventListener("blur", function () {
        const password = this.value;
        if (password && password.length < 6) {
          showFieldError("password", "密码长度至少6位");
        }
      });

      // 清除错误状态当用户开始输入
      emailInput.addEventListener("input", function () {
        if (this.classList.contains("error")) {
          this.classList.remove("error");
          document.getElementById("emailError").style.display = "none";
        }
      });

      passwordInput.addEventListener("input", function () {
        if (this.classList.contains("error")) {
          this.classList.remove("error");
          document.getElementById("passwordError").style.display = "none";
        }
      });
    </script>
  </body>
</html>
