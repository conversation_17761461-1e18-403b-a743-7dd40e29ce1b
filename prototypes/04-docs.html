<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API文档 - 宠物医疗AI开放平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #334155;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav {
            display: flex;
            gap: 2rem;
        }

        .nav a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: background 0.2s;
        }

        .nav a:hover, .nav a.active {
            background: rgba(255,255,255,0.2);
        }

        .docs-layout {
            display: grid;
            grid-template-columns: 300px 1fr;
            max-width: 1200px;
            margin: 0 auto;
            min-height: calc(100vh - 80px);
        }

        .sidebar {
            background: white;
            border-right: 1px solid #e2e8f0;
            padding: 2rem 0;
            position: sticky;
            top: 0;
            height: calc(100vh - 80px);
            overflow-y: auto;
        }

        .sidebar-section {
            margin-bottom: 2rem;
        }

        .sidebar-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            padding: 0 1.5rem;
            margin-bottom: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-item {
            margin-bottom: 0.25rem;
        }

        .sidebar-link {
            display: block;
            padding: 0.5rem 1.5rem;
            color: #64748b;
            text-decoration: none;
            font-size: 0.875rem;
            transition: all 0.2s;
            border-left: 3px solid transparent;
        }

        .sidebar-link:hover {
            background: #f8fafc;
            color: #3b82f6;
        }

        .sidebar-link.active {
            background: #eff6ff;
            color: #3b82f6;
            border-left-color: #3b82f6;
        }

        .content {
            padding: 2rem;
            background: white;
        }

        .content-header {
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .content-title {
            font-size: 2rem;
            font-weight: bold;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }

        .content-description {
            color: #64748b;
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .api-section {
            margin-bottom: 3rem;
        }

        .api-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .method-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .method-post {
            background: #dcfce7;
            color: #166534;
        }

        .method-get {
            background: #dbeafe;
            color: #1e40af;
        }

        .endpoint-url {
            font-family: 'Monaco', 'Menlo', monospace;
            background: #f1f5f9;
            padding: 0.75rem;
            border-radius: 6px;
            margin: 1rem 0;
            font-size: 0.875rem;
            border-left: 4px solid #3b82f6;
        }

        .params-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .params-table th {
            background: #f8fafc;
            padding: 0.75rem;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e2e8f0;
        }

        .params-table td {
            padding: 0.75rem;
            border-bottom: 1px solid #e2e8f0;
            vertical-align: top;
        }

        .params-table tr:last-child td {
            border-bottom: none;
        }

        .param-name {
            font-family: 'Monaco', 'Menlo', monospace;
            font-weight: 600;
            color: #1e293b;
        }

        .param-type {
            color: #7c3aed;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .param-required {
            background: #fef2f2;
            color: #dc2626;
            padding: 0.125rem 0.375rem;
            border-radius: 3px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 1rem 0;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .code-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .code-tab {
            padding: 0.5rem 1rem;
            background: #e2e8f0;
            border: none;
            border-radius: 6px 6px 0 0;
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
            color: #64748b;
        }

        .code-tab.active {
            background: #1e293b;
            color: #e2e8f0;
        }

        .response-example {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .response-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
        }

        .try-it-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 2rem 0;
        }

        .try-it-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
        }

        .form-input, .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
        }

        .form-textarea {
            height: 120px;
            resize: vertical;
            font-family: 'Monaco', 'Menlo', monospace;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .response-output {
            background: #1e293b;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 6px;
            margin-top: 1rem;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }

        @media (max-width: 768px) {
            .docs-layout {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                position: static;
                height: auto;
                border-right: none;
                border-bottom: 1px solid #e2e8f0;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-paw"></i>
                <span>宠物医疗AI开放平台</span>
            </div>
            <nav class="nav">
                <a href="01-dashboard.html">控制台</a>
                <a href="02-api-keys.html">API密钥</a>
                <a href="03-agents.html">智能体</a>
                <a href="04-docs.html" class="active">文档</a>
            </nav>
        </div>
    </header>

    <div class="docs-layout">
        <aside class="sidebar">
            <div class="sidebar-section">
                <div class="sidebar-title">快速开始</div>
                <ul class="sidebar-menu">
                    <li class="sidebar-item">
                        <a href="#introduction" class="sidebar-link active">介绍</a>
                    </li>
                    <li class="sidebar-item">
                        <a href="#authentication" class="sidebar-link">认证</a>
                    </li>
                    <li class="sidebar-item">
                        <a href="#rate-limits" class="sidebar-link">限流规则</a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-title">AI智能体</div>
                <ul class="sidebar-menu">
                    <li class="sidebar-item">
                        <a href="#diagnosis-agent" class="sidebar-link">AI问诊</a>
                    </li>
                    <li class="sidebar-item">
                        <a href="#report-agent" class="sidebar-link">报告生成</a>
                    </li>
                    <li class="sidebar-item">
                        <a href="#analysis-agent" class="sidebar-link">报告解读</a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-title">数据管理</div>
                <ul class="sidebar-menu">
                    <li class="sidebar-item">
                        <a href="#pets-api" class="sidebar-link">宠物信息</a>
                    </li>
                    <li class="sidebar-item">
                        <a href="#records-api" class="sidebar-link">医疗记录</a>
                    </li>
                    <li class="sidebar-item">
                        <a href="#vaccines-api" class="sidebar-link">疫苗记录</a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-title">OpenAI兼容</div>
                <ul class="sidebar-menu">
                    <li class="sidebar-item">
                        <a href="#openai-chat" class="sidebar-link">Chat Completions</a>
                    </li>
                    <li class="sidebar-item">
                        <a href="#openai-models" class="sidebar-link">Models</a>
                    </li>
                </ul>
            </div>
        </aside>

        <main class="content">
            <div class="content-header">
                <h1 class="content-title">API文档</h1>
                <p class="content-description">
                    欢迎使用宠物医疗AI开放平台API！本文档将帮助您快速集成我们的AI智能体服务，
                    包括AI问诊、报告生成、报告解读等功能。我们提供RESTful API和OpenAI兼容接口。
                </p>
            </div>

            <section id="diagnosis-agent" class="api-section">
                <h2 class="api-title">
                    <span class="method-badge method-post">POST</span>
                    AI问诊智能体
                </h2>
                
                <div class="endpoint-url">
                    POST /api/v1/system-agents/diagnosis
                </div>

                <p>基于宠物症状和信息，提供专业的AI问诊服务，支持流式和非流式输出。</p>

                <h3>请求参数</h3>
                <table class="params-table">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>必需</th>
                            <th>描述</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="param-name">pet_info</td>
                            <td class="param-type">object</td>
                            <td><span class="param-required">必需</span></td>
                            <td>宠物基本信息</td>
                        </tr>
                        <tr>
                            <td class="param-name">symptoms</td>
                            <td class="param-type">array</td>
                            <td><span class="param-required">必需</span></td>
                            <td>症状列表</td>
                        </tr>
                        <tr>
                            <td class="param-name">additional_info</td>
                            <td class="param-type">string</td>
                            <td>可选</td>
                            <td>补充信息</td>
                        </tr>
                        <tr>
                            <td class="param-name">stream</td>
                            <td class="param-type">boolean</td>
                            <td>可选</td>
                            <td>是否启用流式输出，默认false</td>
                        </tr>
                    </tbody>
                </table>

                <h3>请求示例</h3>
                <div class="code-tabs">
                    <button class="code-tab active" onclick="switchCodeTab('curl')">cURL</button>
                    <button class="code-tab" onclick="switchCodeTab('python')">Python</button>
                    <button class="code-tab" onclick="switchCodeTab('javascript')">JavaScript</button>
                </div>

                <div id="curl-code" class="code-block">curl -X POST "https://api.vetai.com/api/v1/system-agents/diagnosis" \
  -H "Authorization: Bearer sk-vet-your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "pet_info": {
      "breed": "金毛犬",
      "age": "3岁",
      "gender": "雄性",
      "weight": "25"
    },
    "symptoms": ["食欲不振", "精神萎靡"],
    "additional_info": "症状持续2天",
    "stream": false
  }'</div>

                <div id="python-code" class="code-block" style="display: none;">import requests

url = "https://api.vetai.com/api/v1/system-agents/diagnosis"
headers = {
    "Authorization": "Bearer sk-vet-your-api-key",
    "Content-Type": "application/json"
}
data = {
    "pet_info": {
        "breed": "金毛犬",
        "age": "3岁",
        "gender": "雄性",
        "weight": "25"
    },
    "symptoms": ["食欲不振", "精神萎靡"],
    "additional_info": "症状持续2天",
    "stream": False
}

response = requests.post(url, headers=headers, json=data)
print(response.json())</div>

                <div id="javascript-code" class="code-block" style="display: none;">const response = await fetch('https://api.vetai.com/api/v1/system-agents/diagnosis', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer sk-vet-your-api-key',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    pet_info: {
      breed: '金毛犬',
      age: '3岁',
      gender: '雄性',
      weight: '25'
    },
    symptoms: ['食欲不振', '精神萎靡'],
    additional_info: '症状持续2天',
    stream: false
  })
});

const result = await response.json();
console.log(result);</div>

                <h3>响应示例</h3>
                <div class="response-example">
                    <div class="response-title">成功响应 (200)</div>
                    <div class="code-block">{
  "success": true,
  "data": {
    "diagnosis_id": "diag_abc123",
    "analysis": {
      "symptom_analysis": "食欲不振和精神萎靡是常见的非特异性症状...",
      "possible_diagnoses": [
        {
          "condition": "急性胃肠炎",
          "probability": 0.4,
          "description": "最可能的诊断..."
        }
      ],
      "recommendations": [
        "立即停止喂食，保持饮水",
        "监测体温变化"
      ],
      "urgency_level": "moderate"
    },
    "created_at": "2024-01-15T10:30:00Z"
  }
}</div>
                </div>

                <div class="try-it-section">
                    <h3 class="try-it-title">
                        <i class="fas fa-play"></i>
                        在线测试
                    </h3>
                    <form onsubmit="testDiagnosisAPI(event)">
                        <div class="form-group">
                            <label class="form-label">API密钥</label>
                            <input type="text" class="form-input" id="apiKey" placeholder="sk-vet-your-api-key" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">请求数据 (JSON)</label>
                            <textarea class="form-textarea" id="requestData">{
  "pet_info": {
    "breed": "金毛犬",
    "age": "3岁",
    "gender": "雄性",
    "weight": "25"
  },
  "symptoms": ["食欲不振", "精神萎靡"],
  "additional_info": "症状持续2天",
  "stream": false
}</textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i>
                            发送请求
                        </button>
                    </form>
                    <div id="apiResponse" class="response-output" style="display: none;"></div>
                </div>
            </section>
        </main>
    </div>

    <script>
        function switchCodeTab(language) {
            // 隐藏所有代码块
            document.querySelectorAll('.code-block').forEach(block => {
                if (block.id.includes('-code')) {
                    block.style.display = 'none';
                }
            });
            
            // 移除所有标签的活跃状态
            document.querySelectorAll('.code-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的代码块
            document.getElementById(language + '-code').style.display = 'block';
            
            // 激活选中的标签
            event.target.classList.add('active');
        }

        function testDiagnosisAPI(event) {
            event.preventDefault();
            
            const apiKey = document.getElementById('apiKey').value;
            const requestData = document.getElementById('requestData').value;
            const responseDiv = document.getElementById('apiResponse');
            
            // 模拟API响应
            responseDiv.style.display = 'block';
            responseDiv.textContent = '正在发送请求...';
            
            setTimeout(() => {
                const mockResponse = {
                    success: true,
                    data: {
                        diagnosis_id: "diag_demo123",
                        analysis: {
                            symptom_analysis: "食欲不振和精神萎靡是常见的非特异性症状，可能由多种原因引起。金毛犬作为中大型犬种，需特别关注消化系统问题。",
                            possible_diagnoses: [
                                {
                                    condition: "急性胃肠炎",
                                    probability: 0.4,
                                    description: "最可能的诊断，常见于饮食不当或感染"
                                },
                                {
                                    condition: "胰腺炎",
                                    probability: 0.3,
                                    description: "金毛犬易发疾病，需要进一步检查"
                                }
                            ],
                            recommendations: [
                                "立即停止喂食，保持饮水",
                                "监测体温（正常范围38-39℃）",
                                "观察是否出现呕吐/腹泻"
                            ],
                            urgency_level: "moderate"
                        },
                        created_at: new Date().toISOString()
                    }
                };
                
                responseDiv.textContent = JSON.stringify(mockResponse, null, 2);
            }, 1500);
        }
    </script>
</body>
</html>
