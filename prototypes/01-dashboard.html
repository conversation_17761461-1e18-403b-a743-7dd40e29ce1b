<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开发者控制台 - 宠物医疗AI开放平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #334155;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        .welcome-section {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-left: 4px solid;
        }

        .stat-card.api-calls { border-left-color: #3b82f6; }
        .stat-card.quota { border-left-color: #10b981; }
        .stat-card.errors { border-left-color: #ef4444; }
        .stat-card.revenue { border-left-color: #f59e0b; }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .stat-title {
            color: #64748b;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .stat-icon {
            width: 2rem;
            height: 2rem;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #1e293b;
        }

        .stat-change {
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        .stat-change.positive { color: #10b981; }
        .stat-change.negative { color: #ef4444; }

        .charts-section {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .chart-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .chart-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #1e293b;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .service-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .service-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .service-icon {
            width: 3rem;
            height: 3rem;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            color: white;
        }

        .service-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1e293b;
        }

        .service-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-dot.online { background: #10b981; }
        .status-dot.offline { background: #ef4444; }

        .service-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-top: 1rem;
        }

        .metric {
            text-align: center;
        }

        .metric-value {
            font-size: 1.25rem;
            font-weight: bold;
            color: #1e293b;
        }

        .metric-label {
            font-size: 0.75rem;
            color: #64748b;
            margin-top: 0.25rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        @media (max-width: 768px) {
            .charts-section {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 0 1rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-paw"></i>
                <span>宠物医疗AI开放平台</span>
            </div>
            <div class="user-menu">
                <span>欢迎，张开发者</span>
                <i class="fas fa-user-circle" style="font-size: 1.5rem;"></i>
            </div>
        </div>
    </header>

    <div class="container">
        <div class="welcome-section">
            <h1>开发者控制台</h1>
            <p style="color: #64748b; margin-top: 0.5rem;">
                欢迎使用宠物医疗AI开放平台！这里是您的API使用概览和服务监控中心。
            </p>
        </div>

        <div class="stats-grid">
            <div class="stat-card api-calls">
                <div class="stat-header">
                    <span class="stat-title">今日API调用</span>
                    <div class="stat-icon" style="background: #3b82f6;">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <div class="stat-value">12,847</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i> +15.3% 较昨日
                </div>
            </div>

            <div class="stat-card quota">
                <div class="stat-header">
                    <span class="stat-title">剩余配额</span>
                    <div class="stat-icon" style="background: #10b981;">
                        <i class="fas fa-database"></i>
                    </div>
                </div>
                <div class="stat-value">87,153</div>
                <div class="stat-change positive">
                    <i class="fas fa-info-circle"></i> 本月配额充足
                </div>
            </div>

            <div class="stat-card errors">
                <div class="stat-header">
                    <span class="stat-title">错误率</span>
                    <div class="stat-icon" style="background: #ef4444;">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
                <div class="stat-value">0.12%</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-down"></i> -0.05% 较昨日
                </div>
            </div>

            <div class="stat-card revenue">
                <div class="stat-header">
                    <span class="stat-title">本月费用</span>
                    <div class="stat-icon" style="background: #f59e0b;">
                        <i class="fas fa-yen-sign"></i>
                    </div>
                </div>
                <div class="stat-value">¥2,456</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i> +8.7% 较上月
                </div>
            </div>
        </div>

        <div class="charts-section">
            <div class="chart-card">
                <h3 class="chart-title">API调用趋势（最近7天）</h3>
                <canvas id="apiChart" width="400" height="200"></canvas>
            </div>

            <div class="chart-card">
                <h3 class="chart-title">服务使用分布</h3>
                <canvas id="serviceChart" width="200" height="200"></canvas>
            </div>
        </div>

        <div class="services-grid">
            <div class="service-card">
                <div class="service-header">
                    <div class="service-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                        <i class="fas fa-stethoscope"></i>
                    </div>
                    <div>
                        <div class="service-title">AI问诊智能体</div>
                        <div class="service-status">
                            <div class="status-dot online"></div>
                            <span style="color: #10b981; font-size: 0.875rem;">运行正常</span>
                        </div>
                    </div>
                </div>
                <div class="service-metrics">
                    <div class="metric">
                        <div class="metric-value">8,234</div>
                        <div class="metric-label">今日调用</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">98.7%</div>
                        <div class="metric-label">成功率</div>
                    </div>
                </div>
            </div>

            <div class="service-card">
                <div class="service-header">
                    <div class="service-icon" style="background: linear-gradient(135deg, #f093fb, #f5576c);">
                        <i class="fas fa-file-medical"></i>
                    </div>
                    <div>
                        <div class="service-title">报告生成智能体</div>
                        <div class="service-status">
                            <div class="status-dot online"></div>
                            <span style="color: #10b981; font-size: 0.875rem;">运行正常</span>
                        </div>
                    </div>
                </div>
                <div class="service-metrics">
                    <div class="metric">
                        <div class="metric-value">3,456</div>
                        <div class="metric-label">今日调用</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">99.2%</div>
                        <div class="metric-label">成功率</div>
                    </div>
                </div>
            </div>

            <div class="service-card">
                <div class="service-header">
                    <div class="service-icon" style="background: linear-gradient(135deg, #4facfe, #00f2fe);">
                        <i class="fas fa-search"></i>
                    </div>
                    <div>
                        <div class="service-title">报告解读智能体</div>
                        <div class="service-status">
                            <div class="status-dot online"></div>
                            <span style="color: #10b981; font-size: 0.875rem;">运行正常</span>
                        </div>
                    </div>
                </div>
                <div class="service-metrics">
                    <div class="metric">
                        <div class="metric-value">1,157</div>
                        <div class="metric-label">今日调用</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">97.8%</div>
                        <div class="metric-label">成功率</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API调用趋势图
        const apiCtx = document.getElementById('apiChart').getContext('2d');
        new Chart(apiCtx, {
            type: 'line',
            data: {
                labels: ['7天前', '6天前', '5天前', '4天前', '3天前', '昨天', '今天'],
                datasets: [{
                    label: 'API调用次数',
                    data: [8500, 9200, 8800, 11200, 10500, 11200, 12847],
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // 服务使用分布图
        const serviceCtx = document.getElementById('serviceChart').getContext('2d');
        new Chart(serviceCtx, {
            type: 'doughnut',
            data: {
                labels: ['AI问诊', '报告生成', '报告解读', '其他'],
                datasets: [{
                    data: [65, 27, 9, 4],
                    backgroundColor: [
                        '#667eea',
                        '#f093fb',
                        '#4facfe',
                        '#e2e8f0'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    </script>
</body>
</html>
