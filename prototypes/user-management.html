<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>用户管理 - 宠物医疗AI开放平台</title>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: #f8fafc;
        color: #334155;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem 2rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .header-content {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .logo {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.5rem;
        font-weight: bold;
      }

      .nav {
        display: flex;
        gap: 2rem;
      }

      .nav a {
        color: white;
        text-decoration: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        transition: background 0.2s;
      }

      .nav a:hover,
      .nav a.active {
        background: rgba(255, 255, 255, 0.2);
      }

      .user-info {
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .user-avatar {
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .logout-btn {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        cursor: pointer;
        transition: background 0.2s;
      }

      .logout-btn:hover {
        background: rgba(255, 255, 255, 0.3);
      }

      .container {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 0 2rem;
      }

      .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
      }

      .page-title {
        font-size: 2rem;
        font-weight: bold;
        color: #1e293b;
      }

      .page-actions {
        display: flex;
        gap: 1rem;
      }

      .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        border: none;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
      }

      .btn-primary {
        background: #3b82f6;
        color: white;
      }

      .btn-primary:hover {
        background: #2563eb;
      }

      .btn-secondary {
        background: #6b7280;
        color: white;
      }

      .btn-secondary:hover {
        background: #4b5563;
      }

      .btn-danger {
        background: #ef4444;
        color: white;
      }

      .btn-danger:hover {
        background: #dc2626;
      }

      .btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
      }

      .search-filters {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
      }

      .filters-row {
        display: grid;
        grid-template-columns: 1fr 200px 200px 150px;
        gap: 1rem;
        align-items: end;
      }

      .form-group {
        display: flex;
        flex-direction: column;
      }

      .form-label {
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #374151;
      }

      .form-input {
        padding: 0.75rem 1rem;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.2s;
      }

      .form-input:focus {
        outline: none;
        border-color: #3b82f6;
      }

      .form-select {
        padding: 0.75rem 1rem;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        font-size: 1rem;
        background: white;
        cursor: pointer;
      }

      .users-table {
        background: white;
        border-radius: 12px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .table-header {
        background: #f8fafc;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #e5e7eb;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .table-title {
        font-weight: 600;
        color: #1e293b;
      }

      .table-stats {
        color: #6b7280;
        font-size: 0.875rem;
      }

      table {
        width: 100%;
        border-collapse: collapse;
      }

      th,
      td {
        padding: 1rem 1.5rem;
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
      }

      th {
        background: #f8fafc;
        font-weight: 600;
        color: #374151;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      tr:hover {
        background: #f8fafc;
      }

      .user-avatar-table {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 500;
      }

      .user-info-cell {
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .user-details {
        display: flex;
        flex-direction: column;
      }

      .user-name {
        font-weight: 500;
        color: #1e293b;
      }

      .user-email {
        color: #6b7280;
        font-size: 0.875rem;
      }

      .status-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
      }

      .status-active {
        background: #dcfce7;
        color: #166534;
      }

      .status-inactive {
        background: #fef2f2;
        color: #991b1b;
      }

      .role-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
      }

      .role-admin {
        background: #fef3c7;
        color: #92400e;
      }

      .role-user {
        background: #e0e7ff;
        color: #3730a3;
      }

      .role-superuser {
        background: #fce7f3;
        color: #be185d;
      }

      .actions-cell {
        display: flex;
        gap: 0.5rem;
      }

      .pagination {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 1.5rem;
        background: #f8fafc;
        border-top: 1px solid #e5e7eb;
      }

      .pagination-info {
        color: #6b7280;
        font-size: 0.875rem;
      }

      .pagination-controls {
        display: flex;
        gap: 0.5rem;
      }

      .pagination-btn {
        padding: 0.5rem 1rem;
        border: 1px solid #e5e7eb;
        background: white;
        color: #374151;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s;
      }

      .pagination-btn:hover:not(:disabled) {
        background: #f3f4f6;
      }

      .pagination-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .pagination-btn.active {
        background: #3b82f6;
        color: white;
        border-color: #3b82f6;
      }

      .alert {
        padding: 1rem 1.5rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        display: none;
      }

      .alert-success {
        background: #f0fdf4;
        color: #166534;
        border: 1px solid #bbf7d0;
      }

      .alert-error {
        background: #fef2f2;
        color: #dc2626;
        border: 1px solid #fecaca;
      }

      .loading {
        text-align: center;
        padding: 2rem;
        color: #6b7280;
      }

      .loading-spinner {
        display: inline-block;
        width: 2rem;
        height: 2rem;
        border: 3px solid #e5e7eb;
        border-top: 3px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      @media (max-width: 768px) {
        .filters-row {
          grid-template-columns: 1fr;
        }

        .page-header {
          flex-direction: column;
          gap: 1rem;
          align-items: stretch;
        }

        .nav {
          flex-direction: column;
          gap: 0.5rem;
        }

        .header-content {
          flex-direction: column;
          gap: 1rem;
        }

        table {
          font-size: 0.875rem;
        }

        th,
        td {
          padding: 0.75rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="header">
      <div class="header-content">
        <div class="logo">
          <i class="fas fa-paw"></i>
          <span>宠物医疗AI平台</span>
        </div>
        <nav class="nav">
          <a href="index.html">首页</a>
          <a href="user-management.html" class="active">用户管理</a>
          <a href="api-key-management.html">API密钥</a>
          <a href="agent-management.html">智能体</a>
          <a href="ocr-service.html">OCR服务</a>
          <a href="system-monitoring.html">系统监控</a>
        </nav>
        <div class="user-info">
          <div class="user-avatar">
            <i class="fas fa-user"></i>
          </div>
          <span id="currentUserName">管理员</span>
          <button class="logout-btn" onclick="logout()">
            <i class="fas fa-sign-out-alt"></i>
            退出
          </button>
        </div>
      </div>
    </div>

    <div class="container">
      <div class="page-header">
        <h1 class="page-title">用户管理</h1>
        <div class="page-actions">
          <button class="btn btn-secondary" onclick="exportUsers()">
            <i class="fas fa-download"></i>
            导出用户
          </button>
          <button class="btn btn-primary" onclick="showCreateUserModal()">
            <i class="fas fa-plus"></i>
            创建用户
          </button>
        </div>
      </div>

      <div class="alert alert-success" id="successAlert"></div>
      <div class="alert alert-error" id="errorAlert"></div>

      <div class="search-filters">
        <div class="filters-row">
          <div class="form-group">
            <label class="form-label">搜索用户</label>
            <input
              type="text"
              class="form-input"
              id="searchInput"
              placeholder="搜索用户名、邮箱..."
            />
          </div>
          <div class="form-group">
            <label class="form-label">状态</label>
            <select class="form-select" id="statusFilter">
              <option value="">全部状态</option>
              <option value="true">激活</option>
              <option value="false">未激活</option>
            </select>
          </div>
          <div class="form-group">
            <label class="form-label">角色</label>
            <select class="form-select" id="roleFilter">
              <option value="">全部角色</option>
              <option value="superuser">超级管理员</option>
              <option value="admin">管理员</option>
              <option value="user">普通用户</option>
            </select>
          </div>
          <div class="form-group">
            <button class="btn btn-primary" onclick="searchUsers()">
              <i class="fas fa-search"></i>
              搜索
            </button>
          </div>
        </div>
      </div>

      <div class="users-table">
        <div class="table-header">
          <div class="table-title">用户列表</div>
          <div class="table-stats" id="tableStats">共 0 个用户</div>
        </div>

        <div id="loadingIndicator" class="loading">
          <div class="loading-spinner"></div>
          <div>加载中...</div>
        </div>

        <table id="usersTable" style="display: none">
          <thead>
            <tr>
              <th>用户信息</th>
              <th>状态</th>
              <th>角色</th>
              <th>注册时间</th>
              <th>最后登录</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody id="usersTableBody"></tbody>
        </table>

        <div class="pagination" id="paginationContainer" style="display: none">
          <div class="pagination-info" id="paginationInfo"></div>
          <div class="pagination-controls" id="paginationControls"></div>
        </div>
      </div>
    </div>

    <script>
      // API配置
      const API_BASE_URL = "http://localhost:8000";

      // 全局变量
      let currentPage = 1;
      let pageSize = 10;
      let totalUsers = 0;
      let users = [];
      let searchFilters = {
        search: "",
        status: "",
        role: "",
      };

      // DOM元素
      const searchInput = document.getElementById("searchInput");
      const statusFilter = document.getElementById("statusFilter");
      const roleFilter = document.getElementById("roleFilter");
      const usersTable = document.getElementById("usersTable");
      const usersTableBody = document.getElementById("usersTableBody");
      const loadingIndicator = document.getElementById("loadingIndicator");
      const tableStats = document.getElementById("tableStats");
      const paginationContainer = document.getElementById(
        "paginationContainer"
      );
      const paginationInfo = document.getElementById("paginationInfo");
      const paginationControls = document.getElementById("paginationControls");
      const successAlert = document.getElementById("successAlert");
      const errorAlert = document.getElementById("errorAlert");

      // 页面加载时初始化
      document.addEventListener("DOMContentLoaded", function () {
        checkAuth();
        loadUsers();
        setupEventListeners();
      });

      // 检查认证状态
      function checkAuth() {
        const token = localStorage.getItem("access_token");
        if (!token) {
          window.location.href = "auth-login.html";
          return;
        }

        // 可以添加token验证逻辑
        const currentUser = localStorage.getItem("current_user") || "管理员";
        document.getElementById("currentUserName").textContent = currentUser;
      }

      // 设置事件监听器
      function setupEventListeners() {
        // 搜索输入框实时搜索
        searchInput.addEventListener("input", debounce(searchUsers, 500));

        // 过滤器变化时搜索
        statusFilter.addEventListener("change", searchUsers);
        roleFilter.addEventListener("change", searchUsers);

        // 回车键搜索
        searchInput.addEventListener("keypress", function (e) {
          if (e.key === "Enter") {
            searchUsers();
          }
        });
      }

      // 防抖函数
      function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
          const later = () => {
            clearTimeout(timeout);
            func(...args);
          };
          clearTimeout(timeout);
          timeout = setTimeout(later, wait);
        };
      }

      // 显示提示信息
      function showAlert(message, type = "success") {
        hideAlerts();
        const alertElement = type === "success" ? successAlert : errorAlert;
        alertElement.textContent = message;
        alertElement.style.display = "block";

        // 自动隐藏
        setTimeout(() => {
          alertElement.style.display = "none";
        }, 5000);
      }

      function hideAlerts() {
        successAlert.style.display = "none";
        errorAlert.style.display = "none";
      }

      // 加载用户列表（演示模式）
      async function loadUsers() {
        try {
          showLoading(true);

          // 模拟网络延迟
          await new Promise((resolve) => setTimeout(resolve, 500));

          // 获取或初始化演示数据
          let demoUsers = JSON.parse(
            localStorage.getItem("demo_users") || "[]"
          );

          if (demoUsers.length === 0) {
            // 初始化演示用户数据
            demoUsers = [
              {
                id: 1,
                username: "管理员",
                email: "<EMAIL>",
                role: "admin",
                status: "active",
                created_at: "2024-01-15T10:30:00Z",
                last_login: "2024-01-20T14:25:00Z",
                tenant_id: 1,
                tenant_name: "默认租户",
              },
              {
                id: 2,
                username: "测试用户",
                email: "<EMAIL>",
                role: "user",
                status: "active",
                created_at: "2024-01-16T09:15:00Z",
                last_login: "2024-01-19T16:45:00Z",
                tenant_id: 1,
                tenant_name: "默认租户",
              },
              {
                id: 3,
                username: "演示用户",
                email: "<EMAIL>",
                role: "user",
                status: "inactive",
                created_at: "2024-01-17T11:20:00Z",
                last_login: "2024-01-18T13:30:00Z",
                tenant_id: 2,
                tenant_name: "演示租户",
              },
              {
                id: 4,
                username: "开发者",
                email: "<EMAIL>",
                role: "developer",
                status: "active",
                created_at: "2024-01-18T08:45:00Z",
                last_login: "2024-01-20T10:15:00Z",
                tenant_id: 1,
                tenant_name: "默认租户",
              },
            ];
            localStorage.setItem("demo_users", JSON.stringify(demoUsers));
          }

          // 应用搜索和过滤
          let filteredUsers = demoUsers.filter((user) => {
            const matchesSearch =
              !searchFilters.search ||
              user.username
                .toLowerCase()
                .includes(searchFilters.search.toLowerCase()) ||
              user.email
                .toLowerCase()
                .includes(searchFilters.search.toLowerCase());

            const matchesStatus =
              !searchFilters.status || user.status === searchFilters.status;
            const matchesRole =
              !searchFilters.role || user.role === searchFilters.role;

            return matchesSearch && matchesStatus && matchesRole;
          });

          // 分页处理
          totalUsers = filteredUsers.length;
          const startIndex = (currentPage - 1) * pageSize;
          const endIndex = startIndex + pageSize;
          users = filteredUsers.slice(startIndex, endIndex);

          renderUsersTable();
          renderPagination();
          updateTableStats();
        } catch (error) {
          console.error("加载用户列表失败:", error);
          showAlert("加载失败，请重试", "error");
          users = [];
          renderUsersTable();
        } finally {
          showLoading(false);
        }
      }

      // 显示/隐藏加载状态
      function showLoading(loading) {
        if (loading) {
          loadingIndicator.style.display = "block";
          usersTable.style.display = "none";
          paginationContainer.style.display = "none";
        } else {
          loadingIndicator.style.display = "none";
          usersTable.style.display = "table";
          paginationContainer.style.display = "flex";
        }
      }

      // 渲染用户表格
      function renderUsersTable() {
        if (users.length === 0) {
          usersTableBody.innerHTML = `
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 2rem; color: #6b7280;">
                            <i class="fas fa-users" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                            暂无用户数据
                        </td>
                    </tr>
                `;
          return;
        }

        usersTableBody.innerHTML = users
          .map(
            (user) => `
                <tr>
                    <td>
                        <div class="user-info-cell">
                            <div class="user-avatar-table">
                                ${getUserInitials(user.username)}
                            </div>
                            <div class="user-details">
                                <div class="user-name">${escapeHtml(
                                  user.username
                                )}</div>
                                <div class="user-email">${escapeHtml(
                                  user.email
                                )}</div>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="status-badge ${
                          user.is_active ? "status-active" : "status-inactive"
                        }">
                            ${user.is_active ? "激活" : "未激活"}
                        </span>
                    </td>
                    <td>
                        <span class="role-badge role-${user.role || "user"}">
                            ${getRoleDisplayName(user.role)}
                        </span>
                    </td>
                    <td>${formatDate(user.created_at)}</td>
                    <td>${
                      user.last_login ? formatDate(user.last_login) : "从未登录"
                    }</td>
                    <td>
                        <div class="actions-cell">
                            <button class="btn btn-sm btn-secondary" onclick="editUser('${
                              user.id
                            }')" title="编辑用户">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="viewUserPermissions('${
                              user.id
                            }')" title="权限管理">
                                <i class="fas fa-key"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteUser('${
                              user.id
                            }', '${escapeHtml(
              user.username
            )}')" title="删除用户">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `
          )
          .join("");
      }

      // 获取用户名首字母
      function getUserInitials(username) {
        if (!username) return "U";
        return username.charAt(0).toUpperCase();
      }

      // 获取角色显示名称
      function getRoleDisplayName(role) {
        const roleMap = {
          superuser: "超级管理员",
          admin: "管理员",
          user: "普通用户",
        };
        return roleMap[role] || "普通用户";
      }

      // 格式化日期
      function formatDate(dateString) {
        if (!dateString) return "-";
        const date = new Date(dateString);
        return date.toLocaleString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
        });
      }

      // HTML转义
      function escapeHtml(text) {
        const div = document.createElement("div");
        div.textContent = text;
        return div.innerHTML;
      }

      // 更新表格统计信息
      function updateTableStats() {
        tableStats.textContent = `共 ${totalUsers} 个用户`;
      }

      // 渲染分页
      function renderPagination() {
        const totalPages = Math.ceil(totalUsers / pageSize);

        if (totalPages <= 1) {
          paginationContainer.style.display = "none";
          return;
        }

        const startItem = (currentPage - 1) * pageSize + 1;
        const endItem = Math.min(currentPage * pageSize, totalUsers);

        paginationInfo.textContent = `显示 ${startItem}-${endItem} 项，共 ${totalUsers} 项`;

        let paginationHTML = "";

        // 上一页按钮
        paginationHTML += `
                <button class="pagination-btn" ${
                  currentPage === 1 ? "disabled" : ""
                } onclick="goToPage(${currentPage - 1})">
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;

        // 页码按钮
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
          paginationHTML += `<button class="pagination-btn" onclick="goToPage(1)">1</button>`;
          if (startPage > 2) {
            paginationHTML += `<span style="padding: 0 0.5rem;">...</span>`;
          }
        }

        for (let i = startPage; i <= endPage; i++) {
          paginationHTML += `
                    <button class="pagination-btn ${
                      i === currentPage ? "active" : ""
                    }" onclick="goToPage(${i})">
                        ${i}
                    </button>
                `;
        }

        if (endPage < totalPages) {
          if (endPage < totalPages - 1) {
            paginationHTML += `<span style="padding: 0 0.5rem;">...</span>`;
          }
          paginationHTML += `<button class="pagination-btn" onclick="goToPage(${totalPages})">${totalPages}</button>`;
        }

        // 下一页按钮
        paginationHTML += `
                <button class="pagination-btn" ${
                  currentPage === totalPages ? "disabled" : ""
                } onclick="goToPage(${currentPage + 1})">
                    <i class="fas fa-chevron-right"></i>
                </button>
            `;

        paginationControls.innerHTML = paginationHTML;
      }

      // 跳转到指定页面
      function goToPage(page) {
        if (page < 1 || page > Math.ceil(totalUsers / pageSize)) return;
        currentPage = page;
        loadUsers();
      }

      // 搜索用户
      function searchUsers() {
        searchFilters = {
          search: searchInput.value.trim(),
          status: statusFilter.value,
          role: roleFilter.value,
        };
        currentPage = 1;
        loadUsers();
      }

      // 创建用户
      function showCreateUserModal() {
        // 这里应该显示创建用户的模态框
        // 为了简化，直接跳转到一个新页面或显示简单的prompt
        const username = prompt("请输入用户名:");
        if (!username) return;

        const email = prompt("请输入邮箱:");
        if (!email) return;

        const password = prompt("请输入密码:");
        if (!password) return;

        createUser({ username, email, password });
      }

      // 创建用户API调用（演示模式）
      async function createUser(userData) {
        try {
          // 模拟网络延迟
          await new Promise((resolve) => setTimeout(resolve, 500));

          // 获取现有用户数据
          let demoUsers = JSON.parse(
            localStorage.getItem("demo_users") || "[]"
          );

          // 检查邮箱是否已存在
          if (demoUsers.some((user) => user.email === userData.email)) {
            throw new Error("邮箱已存在");
          }

          // 创建新用户
          const newUser = {
            id: Math.max(...demoUsers.map((u) => u.id), 0) + 1,
            username: userData.username,
            email: userData.email,
            role: userData.role || "user",
            status: "active",
            created_at: new Date().toISOString(),
            last_login: null,
            tenant_id: 1,
            tenant_name: "默认租户",
          };

          demoUsers.push(newUser);
          localStorage.setItem("demo_users", JSON.stringify(demoUsers));

          showAlert("用户创建成功");
          loadUsers();
        } catch (error) {
          console.error("创建用户失败:", error);
          showAlert("创建用户失败: " + error.message, "error");
        }
      }

      // 编辑用户
      function editUser(userId) {
        // 这里应该显示编辑用户的模态框
        showAlert("编辑用户功能开发中...", "error");
      }

      // 查看用户权限
      function viewUserPermissions(userId) {
        // 跳转到权限管理页面
        window.location.href = `permission-management.html?user_id=${userId}`;
      }

      // 删除用户（演示模式）
      async function deleteUser(userId, username) {
        if (!confirm(`确定要删除用户 "${username}" 吗？此操作不可恢复。`)) {
          return;
        }

        try {
          // 模拟网络延迟
          await new Promise((resolve) => setTimeout(resolve, 500));

          // 获取现有用户数据
          let demoUsers = JSON.parse(
            localStorage.getItem("demo_users") || "[]"
          );

          // 检查是否为管理员账户
          const userToDelete = demoUsers.find((user) => user.id == userId);
          if (userToDelete && userToDelete.email === "<EMAIL>") {
            throw new Error("不能删除管理员账户");
          }

          // 删除用户
          demoUsers = demoUsers.filter((user) => user.id != userId);
          localStorage.setItem("demo_users", JSON.stringify(demoUsers));

          showAlert("用户删除成功");
          loadUsers();
        } catch (error) {
          console.error("删除用户失败:", error);
          showAlert("删除用户失败: " + error.message, "error");
        }
      }

      // 导出用户
      function exportUsers() {
        showAlert("导出功能开发中...", "error");
      }

      // 退出登录
      function logout() {
        if (confirm("确定要退出登录吗？")) {
          localStorage.removeItem("access_token");
          localStorage.removeItem("refresh_token");
          localStorage.removeItem("current_user");
          window.location.href = "auth-login.html";
        }
      }
    </script>
  </body>
</html>
