<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 宠物医疗AI开放平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #334155;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav {
            display: flex;
            gap: 2rem;
        }

        .nav a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: background 0.2s;
        }

        .nav a:hover, .nav a.active {
            background: rgba(255,255,255,0.2);
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: bold;
            color: #1e293b;
        }

        .search-filters {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .filters-row {
            display: grid;
            grid-template-columns: 1fr 200px 200px 150px;
            gap: 1rem;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
            font-size: 0.875rem;
        }

        .form-input, .form-select {
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #475569;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .users-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .table-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1e293b;
        }

        .table-stats {
            color: #64748b;
            font-size: 0.875rem;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th {
            background: #f8fafc;
            padding: 0.75rem 1.5rem;
            text-align: left;
            font-weight: 600;
            color: #374151;
            font-size: 0.875rem;
            border-bottom: 1px solid #e2e8f0;
        }

        td {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            vertical-align: middle;
        }

        tr:last-child td {
            border-bottom: none;
        }

        tr:hover {
            background: #f8fafc;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .user-avatar {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 0.25rem;
        }

        .user-email {
            color: #64748b;
            font-size: 0.875rem;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-inactive {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .role-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .role-admin {
            background: #ddd6fe;
            color: #5b21b6;
        }

        .role-developer {
            background: #dbeafe;
            color: #1e40af;
        }

        .role-user {
            background: #e2e8f0;
            color: #475569;
        }

        .actions-menu {
            position: relative;
            display: inline-block;
        }

        .actions-btn {
            background: none;
            border: none;
            padding: 0.5rem;
            cursor: pointer;
            border-radius: 4px;
            color: #64748b;
        }

        .actions-btn:hover {
            background: #f1f5f9;
            color: #374151;
        }

        .actions-dropdown {
            position: absolute;
            right: 0;
            top: 100%;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            min-width: 150px;
            z-index: 100;
            display: none;
        }

        .actions-dropdown.show {
            display: block;
        }

        .dropdown-item {
            display: block;
            width: 100%;
            padding: 0.75rem 1rem;
            text-align: left;
            border: none;
            background: none;
            cursor: pointer;
            font-size: 0.875rem;
            color: #374151;
            transition: background 0.2s;
        }

        .dropdown-item:hover {
            background: #f8fafc;
        }

        .dropdown-item.danger {
            color: #dc2626;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }

        .pagination-btn {
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.875rem;
            color: #374151;
        }

        .pagination-btn:hover {
            background: #f8fafc;
        }

        .pagination-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #64748b;
            font-size: 0.875rem;
        }

        @media (max-width: 768px) {
            .filters-row {
                grid-template-columns: 1fr;
            }
            
            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            table {
                font-size: 0.875rem;
            }
            
            th, td {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-paw"></i>
                <span>宠物医疗AI开放平台</span>
            </div>
            <nav class="nav">
                <a href="01-dashboard.html">控制台</a>
                <a href="02-api-keys.html">API密钥</a>
                <a href="03-agents.html">智能体</a>
                <a href="04-docs.html">文档</a>
                <a href="05-users.html" class="active">用户管理</a>
            </nav>
        </div>
    </header>

    <div class="container">
        <div class="page-header">
            <h1 class="page-title">用户管理</h1>
            <button class="btn btn-primary">
                <i class="fas fa-plus"></i>
                添加用户
            </button>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">1,247</div>
                <div class="stat-label">总用户数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">1,089</div>
                <div class="stat-label">活跃用户</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">158</div>
                <div class="stat-label">待审核</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">89</div>
                <div class="stat-label">本月新增</div>
            </div>
        </div>

        <div class="search-filters">
            <div class="filters-row">
                <div class="form-group">
                    <label class="form-label">搜索用户</label>
                    <input type="text" class="form-input" placeholder="输入用户名或邮箱">
                </div>
                <div class="form-group">
                    <label class="form-label">用户状态</label>
                    <select class="form-select">
                        <option value="">全部状态</option>
                        <option value="active">活跃</option>
                        <option value="inactive">未激活</option>
                        <option value="pending">待审核</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">用户角色</label>
                    <select class="form-select">
                        <option value="">全部角色</option>
                        <option value="admin">管理员</option>
                        <option value="developer">开发者</option>
                        <option value="user">普通用户</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        搜索
                    </button>
                </div>
            </div>
        </div>

        <div class="users-table">
            <div class="table-header">
                <h3 class="table-title">用户列表</h3>
                <div class="table-stats">显示 1-10 条，共 1,247 条记录</div>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>用户信息</th>
                        <th>角色</th>
                        <th>状态</th>
                        <th>注册时间</th>
                        <th>最后登录</th>
                        <th>API调用</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <div class="user-info">
                                <div class="user-avatar">张</div>
                                <div class="user-details">
                                    <div class="user-name">张开发者</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="role-badge role-developer">开发者</span>
                        </td>
                        <td>
                            <span class="status-badge status-active">活跃</span>
                        </td>
                        <td>2024-01-15</td>
                        <td>2小时前</td>
                        <td>12,847</td>
                        <td>
                            <div class="actions-menu">
                                <button class="actions-btn" onclick="toggleDropdown(this)">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <div class="actions-dropdown">
                                    <button class="dropdown-item">查看详情</button>
                                    <button class="dropdown-item">编辑用户</button>
                                    <button class="dropdown-item">重置密码</button>
                                    <button class="dropdown-item danger">禁用用户</button>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="user-info">
                                <div class="user-avatar">李</div>
                                <div class="user-details">
                                    <div class="user-name">李医生</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="role-badge role-user">普通用户</span>
                        </td>
                        <td>
                            <span class="status-badge status-active">活跃</span>
                        </td>
                        <td>2024-01-12</td>
                        <td>1天前</td>
                        <td>3,456</td>
                        <td>
                            <div class="actions-menu">
                                <button class="actions-btn" onclick="toggleDropdown(this)">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <div class="actions-dropdown">
                                    <button class="dropdown-item">查看详情</button>
                                    <button class="dropdown-item">编辑用户</button>
                                    <button class="dropdown-item">重置密码</button>
                                    <button class="dropdown-item danger">禁用用户</button>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="user-info">
                                <div class="user-avatar">王</div>
                                <div class="user-details">
                                    <div class="user-name">王管理员</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="role-badge role-admin">管理员</span>
                        </td>
                        <td>
                            <span class="status-badge status-active">活跃</span>
                        </td>
                        <td>2024-01-01</td>
                        <td>30分钟前</td>
                        <td>856</td>
                        <td>
                            <div class="actions-menu">
                                <button class="actions-btn" onclick="toggleDropdown(this)">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <div class="actions-dropdown">
                                    <button class="dropdown-item">查看详情</button>
                                    <button class="dropdown-item">编辑用户</button>
                                    <button class="dropdown-item">重置密码</button>
                                    <button class="dropdown-item danger">禁用用户</button>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="user-info">
                                <div class="user-avatar">陈</div>
                                <div class="user-details">
                                    <div class="user-name">陈测试</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="role-badge role-developer">开发者</span>
                        </td>
                        <td>
                            <span class="status-badge status-pending">待审核</span>
                        </td>
                        <td>2024-01-14</td>
                        <td>从未登录</td>
                        <td>0</td>
                        <td>
                            <div class="actions-menu">
                                <button class="actions-btn" onclick="toggleDropdown(this)">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <div class="actions-dropdown">
                                    <button class="dropdown-item">审核通过</button>
                                    <button class="dropdown-item">查看详情</button>
                                    <button class="dropdown-item danger">拒绝申请</button>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="user-info">
                                <div class="user-avatar">刘</div>
                                <div class="user-details">
                                    <div class="user-name">刘宠物店</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="role-badge role-user">普通用户</span>
                        </td>
                        <td>
                            <span class="status-badge status-inactive">未激活</span>
                        </td>
                        <td>2024-01-13</td>
                        <td>从未登录</td>
                        <td>0</td>
                        <td>
                            <div class="actions-menu">
                                <button class="actions-btn" onclick="toggleDropdown(this)">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <div class="actions-dropdown">
                                    <button class="dropdown-item">发送激活邮件</button>
                                    <button class="dropdown-item">查看详情</button>
                                    <button class="dropdown-item">编辑用户</button>
                                    <button class="dropdown-item danger">删除用户</button>
                                </div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="pagination">
            <button class="pagination-btn" disabled>
                <i class="fas fa-chevron-left"></i>
            </button>
            <button class="pagination-btn active">1</button>
            <button class="pagination-btn">2</button>
            <button class="pagination-btn">3</button>
            <button class="pagination-btn">...</button>
            <button class="pagination-btn">125</button>
            <button class="pagination-btn">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>

    <script>
        function toggleDropdown(button) {
            // 关闭所有其他下拉菜单
            document.querySelectorAll('.actions-dropdown').forEach(dropdown => {
                if (dropdown !== button.nextElementSibling) {
                    dropdown.classList.remove('show');
                }
            });
            
            // 切换当前下拉菜单
            const dropdown = button.nextElementSibling;
            dropdown.classList.toggle('show');
        }

        // 点击外部关闭下拉菜单
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.actions-menu')) {
                document.querySelectorAll('.actions-dropdown').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }
        });
    </script>
</body>
</html>
