<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>宠物医疗AI开放平台</title>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: #f8fafc;
        color: #1e293b;
        line-height: 1.6;
      }

      /* 顶部导航栏 */
      .top-nav {
        background: #ffffff;
        border-bottom: 1px solid #e2e8f0;
        padding: 0 2rem;
        height: 56px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
      }

      .logo {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-size: 1.25rem;
        font-weight: 600;
        color: #1e293b;
      }

      .logo i {
        color: #3b82f6;
        font-size: 1.5rem;
      }

      .top-nav-menu {
        display: flex;
        gap: 2rem;
      }

      .top-nav-item {
        color: #64748b;
        text-decoration: none;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        transition: all 0.2s;
      }

      .top-nav-item:hover {
        color: #3b82f6;
        background: #f1f5f9;
      }

      .user-menu {
        display: flex;
        align-items: center;
        gap: 1rem;
        color: #64748b;
      }

      /* 主布局 */
      .main-layout {
        display: flex;
        margin-top: 56px;
        min-height: calc(100vh - 56px);
      }

      /* 左侧边栏 */
      .sidebar {
        width: 240px;
        background: #ffffff;
        border-right: 1px solid #e2e8f0;
        padding: 1.5rem 0;
        position: fixed;
        left: 0;
        top: 56px;
        bottom: 0;
        overflow-y: auto;
      }

      .sidebar-section {
        margin-bottom: 1.75rem;
      }

      .sidebar-title {
        font-size: 0.7rem;
        font-weight: 600;
        color: #64748b;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        padding: 0 1.25rem;
        margin-bottom: 0.75rem;
      }

      .sidebar-menu {
        list-style: none;
      }

      .sidebar-item {
        margin-bottom: 0.125rem;
      }

      .sidebar-link {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.625rem 1.25rem;
        color: #64748b;
        text-decoration: none;
        font-size: 0.8rem;
        font-weight: 500;
        transition: all 0.2s;
        border-left: 3px solid transparent;
      }

      .sidebar-link:hover {
        background: #f8fafc;
        color: #3b82f6;
      }

      .sidebar-link.active {
        background: #eff6ff;
        color: #3b82f6;
        border-left-color: #3b82f6;
      }

      .sidebar-link i {
        width: 1.25rem;
        text-align: center;
      }

      /* 主内容区域 */
      .main-content {
        flex: 1;
        margin-left: 240px;
        padding: 1.5rem;
        background: #f8fafc;
      }

      /* 页面容器 */
      .page {
        display: none;
      }

      .page.active {
        display: block;
      }

      .page-header {
        margin-bottom: 2rem;
      }

      .page-title {
        font-size: 1.75rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 0.5rem;
      }

      .page-description {
        color: #64748b;
        font-size: 1rem;
      }

      /* 卡片组件 */
      .card {
        background: #ffffff;
        border-radius: 6px;
        border: 1px solid #e2e8f0;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 1.5rem;
      }

      .card-header {
        padding: 1.25rem;
        border-bottom: 1px solid #e2e8f0;
        background: #f8fafc;
      }

      .card-title {
        font-size: 1rem;
        font-weight: 600;
        color: #1e293b;
      }

      .card-content {
        padding: 1.25rem;
      }

      /* 统计卡片 */
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.25rem;
        margin-bottom: 2rem;
      }

      .stat-card {
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        padding: 1.25rem;
        text-align: center;
      }

      .stat-value {
        font-size: 1.75rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 0.5rem;
      }

      .stat-label {
        color: #64748b;
        font-size: 0.875rem;
        font-weight: 500;
      }

      .stat-change {
        font-size: 0.75rem;
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.25rem;
      }

      .stat-change.positive {
        color: #10b981;
      }

      .stat-change.negative {
        color: #ef4444;
      }

      /* 按钮组件 */
      .btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.625rem 1.25rem;
        border: 1px solid transparent;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 500;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.2s;
      }

      .btn-primary {
        background: #3b82f6;
        color: #ffffff;
        border-color: #3b82f6;
      }

      .btn-primary:hover {
        background: #2563eb;
        border-color: #2563eb;
      }

      .btn-secondary {
        background: #ffffff;
        color: #374151;
        border-color: #d1d5db;
      }

      .btn-secondary:hover {
        background: #f9fafb;
        border-color: #9ca3af;
      }

      /* 表格组件 */
      .table-container {
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        overflow: hidden;
      }

      .table {
        width: 100%;
        border-collapse: collapse;
      }

      .table th {
        background: #f8fafc;
        padding: 0.75rem 1rem;
        text-align: left;
        font-weight: 600;
        color: #374151;
        font-size: 0.8rem;
        border-bottom: 1px solid #e2e8f0;
      }

      .table td {
        padding: 0.875rem 1rem;
        border-bottom: 1px solid #f1f5f9;
        font-size: 0.8rem;
      }

      .table tr:last-child td {
        border-bottom: none;
      }

      .table tr:hover {
        background: #f8fafc;
      }

      /* 表单组件 */
      .form-group {
        margin-bottom: 1.25rem;
      }

      .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #374151;
        font-size: 0.8rem;
      }

      .form-input,
      .form-select,
      .form-textarea {
        width: 100%;
        padding: 0.625rem;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 0.8rem;
        background: #ffffff;
      }

      .form-input:focus,
      .form-select:focus,
      .form-textarea:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      /* 状态标签 */
      .badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.625rem;
        border-radius: 10px;
        font-size: 0.7rem;
        font-weight: 500;
      }

      .badge-success {
        background: #dcfce7;
        color: #166534;
      }

      .badge-warning {
        background: #fef3c7;
        color: #92400e;
      }

      .badge-danger {
        background: #fee2e2;
        color: #991b1b;
      }

      .badge-info {
        background: #dbeafe;
        color: #1e40af;
      }

      /* 响应式设计 */
      @media (max-width: 1024px) {
        .sidebar {
          transform: translateX(-100%);
          transition: transform 0.3s;
        }

        .sidebar.open {
          transform: translateX(0);
        }

        .main-content {
          margin-left: 0;
        }

        .stats-grid {
          grid-template-columns: repeat(2, 1fr);
          gap: 1rem;
        }
      }

      @media (max-width: 768px) {
        .top-nav {
          padding: 0 1rem;
        }

        .top-nav-menu {
          display: none;
        }

        .main-content {
          padding: 1rem;
        }

        .stats-grid {
          grid-template-columns: 1fr;
          gap: 1rem;
        }

        .page-title {
          font-size: 1.5rem;
        }

        .card-header,
        .card-content {
          padding: 1rem;
        }
      }
    </style>
  </head>
  <body>
    <!-- 顶部导航栏 -->
    <nav class="top-nav">
      <div class="logo">
        <i class="fas fa-paw"></i>
        <span>宠物医疗AI开放平台</span>
      </div>
      <div class="top-nav-menu">
        <a href="#" class="top-nav-item">产品</a>
        <a href="#" class="top-nav-item">定价</a>
        <a href="#" class="top-nav-item">文档</a>
        <a href="#" class="top-nav-item">支持</a>
      </div>
      <div class="user-menu">
        <span>张开发者</span>
        <i class="fas fa-user-circle" style="font-size: 1.5rem"></i>
      </div>
    </nav>

    <!-- 主布局 -->
    <div class="main-layout">
      <!-- 左侧边栏 -->
      <aside class="sidebar">
        <div class="sidebar-section">
          <div class="sidebar-title">概览</div>
          <ul class="sidebar-menu">
            <li class="sidebar-item">
              <a
                href="#dashboard"
                class="sidebar-link active"
                data-page="dashboard"
              >
                <i class="fas fa-chart-line"></i>
                <span>控制台</span>
              </a>
            </li>
          </ul>
        </div>

        <div class="sidebar-section">
          <div class="sidebar-title">开发工具</div>
          <ul class="sidebar-menu">
            <li class="sidebar-item">
              <a href="#api-keys" class="sidebar-link" data-page="api-keys">
                <i class="fas fa-key"></i>
                <span>API密钥</span>
              </a>
            </li>
            <li class="sidebar-item">
              <a href="#agents" class="sidebar-link" data-page="agents">
                <i class="fas fa-robot"></i>
                <span>AI智能体</span>
              </a>
            </li>
            <li class="sidebar-item">
              <a href="#docs" class="sidebar-link" data-page="docs">
                <i class="fas fa-book"></i>
                <span>API文档</span>
              </a>
            </li>
          </ul>
        </div>

        <div class="sidebar-section">
          <div class="sidebar-title">管理</div>
          <ul class="sidebar-menu">
            <li class="sidebar-item">
              <a href="#users" class="sidebar-link" data-page="users">
                <i class="fas fa-users"></i>
                <span>用户管理</span>
              </a>
            </li>
            <li class="sidebar-item">
              <a href="#pets" class="sidebar-link" data-page="pets">
                <i class="fas fa-heart"></i>
                <span>宠物管理</span>
              </a>
            </li>
          </ul>
        </div>

        <div class="sidebar-section">
          <div class="sidebar-title">分析</div>
          <ul class="sidebar-menu">
            <li class="sidebar-item">
              <a href="#analytics" class="sidebar-link" data-page="analytics">
                <i class="fas fa-chart-bar"></i>
                <span>数据分析</span>
              </a>
            </li>
            <li class="sidebar-item">
              <a href="#billing" class="sidebar-link" data-page="billing">
                <i class="fas fa-credit-card"></i>
                <span>计费管理</span>
              </a>
            </li>
          </ul>
        </div>
      </aside>

      <!-- 主内容区域 -->
      <main class="main-content">
        <!-- 控制台页面 -->
        <div id="dashboard" class="page active">
          <div class="page-header">
            <h1 class="page-title">控制台</h1>
            <p class="page-description">
              欢迎使用宠物医疗AI开放平台！这里是您的API使用概览和服务监控中心。
            </p>
          </div>

          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-value">12,847</div>
              <div class="stat-label">今日API调用</div>
              <div class="stat-change positive">
                <i class="fas fa-arrow-up"></i>
                <span>+15.3% 较昨日</span>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-value">87,153</div>
              <div class="stat-label">剩余配额</div>
              <div class="stat-change positive">
                <i class="fas fa-info-circle"></i>
                <span>本月配额充足</span>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-value">0.12%</div>
              <div class="stat-label">错误率</div>
              <div class="stat-change positive">
                <i class="fas fa-arrow-down"></i>
                <span>-0.05% 较昨日</span>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-value">¥2,456</div>
              <div class="stat-label">本月费用</div>
              <div class="stat-change positive">
                <i class="fas fa-arrow-up"></i>
                <span>+8.7% 较上月</span>
              </div>
            </div>
          </div>

          <div
            style="
              display: grid;
              grid-template-columns: 2fr 1fr;
              gap: 2rem;
              margin-bottom: 2rem;
            "
          >
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">API调用趋势（最近7天）</h3>
              </div>
              <div class="card-content">
                <canvas id="apiChart" width="400" height="200"></canvas>
              </div>
            </div>

            <div class="card">
              <div class="card-header">
                <h3 class="card-title">服务使用分布</h3>
              </div>
              <div class="card-content">
                <canvas id="serviceChart" width="200" height="200"></canvas>
              </div>
            </div>
          </div>

          <div
            style="
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
              gap: 1.5rem;
            "
          >
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">AI问诊智能体</h3>
              </div>
              <div class="card-content">
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 1rem;
                  "
                >
                  <span class="badge badge-success">运行正常</span>
                  <span style="color: #64748b; font-size: 0.875rem"
                    >98.7% 成功率</span
                  >
                </div>
                <div
                  style="
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 1rem;
                    text-align: center;
                  "
                >
                  <div>
                    <div
                      style="
                        font-size: 1.5rem;
                        font-weight: 600;
                        color: #1e293b;
                      "
                    >
                      8,234
                    </div>
                    <div style="font-size: 0.75rem; color: #64748b">
                      今日调用
                    </div>
                  </div>
                  <div>
                    <div
                      style="
                        font-size: 1.5rem;
                        font-weight: 600;
                        color: #1e293b;
                      "
                    >
                      156ms
                    </div>
                    <div style="font-size: 0.75rem; color: #64748b">
                      平均响应
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="card">
              <div class="card-header">
                <h3 class="card-title">报告生成智能体</h3>
              </div>
              <div class="card-content">
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 1rem;
                  "
                >
                  <span class="badge badge-success">运行正常</span>
                  <span style="color: #64748b; font-size: 0.875rem"
                    >99.2% 成功率</span
                  >
                </div>
                <div
                  style="
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 1rem;
                    text-align: center;
                  "
                >
                  <div>
                    <div
                      style="
                        font-size: 1.5rem;
                        font-weight: 600;
                        color: #1e293b;
                      "
                    >
                      3,456
                    </div>
                    <div style="font-size: 0.75rem; color: #64748b">
                      今日调用
                    </div>
                  </div>
                  <div>
                    <div
                      style="
                        font-size: 1.5rem;
                        font-weight: 600;
                        color: #1e293b;
                      "
                    >
                      234ms
                    </div>
                    <div style="font-size: 0.75rem; color: #64748b">
                      平均响应
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="card">
              <div class="card-header">
                <h3 class="card-title">报告解读智能体</h3>
              </div>
              <div class="card-content">
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 1rem;
                  "
                >
                  <span class="badge badge-success">运行正常</span>
                  <span style="color: #64748b; font-size: 0.875rem"
                    >97.8% 成功率</span
                  >
                </div>
                <div
                  style="
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 1rem;
                    text-align: center;
                  "
                >
                  <div>
                    <div
                      style="
                        font-size: 1.5rem;
                        font-weight: 600;
                        color: #1e293b;
                      "
                    >
                      1,157
                    </div>
                    <div style="font-size: 0.75rem; color: #64748b">
                      今日调用
                    </div>
                  </div>
                  <div>
                    <div
                      style="
                        font-size: 1.5rem;
                        font-weight: 600;
                        color: #1e293b;
                      "
                    >
                      412ms
                    </div>
                    <div style="font-size: 0.75rem; color: #64748b">
                      平均响应
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- API密钥管理页面 -->
        <div id="api-keys" class="page">
          <div class="page-header">
            <div
              style="
                display: flex;
                justify-content: space-between;
                align-items: center;
              "
            >
              <div>
                <h1 class="page-title">API密钥管理</h1>
                <p class="page-description">
                  创建、管理和配置API密钥，设置权限和使用限制。
                </p>
              </div>
              <button class="btn btn-primary" onclick="openCreateKeyModal()">
                <i class="fas fa-plus"></i>
                创建新密钥
              </button>
            </div>
          </div>

          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-value">5</div>
              <div class="stat-label">活跃密钥</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">12,847</div>
              <div class="stat-label">今日调用</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">87,153</div>
              <div class="stat-label">剩余配额</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">0.12%</div>
              <div class="stat-label">错误率</div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">API密钥列表</h3>
            </div>
            <div class="card-content" style="padding: 0">
              <div class="table-container">
                <table class="table">
                  <thead>
                    <tr>
                      <th>密钥名称</th>
                      <th>密钥值</th>
                      <th>权限</th>
                      <th>状态</th>
                      <th>创建时间</th>
                      <th>最后使用</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>
                        <div style="font-weight: 600; color: #1e293b">
                          生产环境密钥
                        </div>
                      </td>
                      <td>
                        <div
                          style="
                            font-family: monospace;
                            background: #f8fafc;
                            padding: 0.5rem;
                            border-radius: 4px;
                            font-size: 0.875rem;
                            display: flex;
                            align-items: center;
                            gap: 0.5rem;
                          "
                        >
                          <span>sk-vet-prod-abc123...def789</span>
                          <button
                            onclick="copyToClipboard('sk-vet-prod-abc123def789xyz')"
                            style="
                              background: none;
                              border: none;
                              color: #3b82f6;
                              cursor: pointer;
                            "
                          >
                            <i class="fas fa-copy"></i>
                          </button>
                        </div>
                      </td>
                      <td>
                        <div
                          style="display: flex; gap: 0.25rem; flex-wrap: wrap"
                        >
                          <span class="badge badge-info">AI问诊</span>
                          <span class="badge badge-info">报告生成</span>
                          <span class="badge badge-info">报告解读</span>
                        </div>
                      </td>
                      <td>
                        <span class="badge badge-success">活跃</span>
                      </td>
                      <td>2024-01-15</td>
                      <td>2小时前</td>
                      <td>
                        <div style="display: flex; gap: 0.5rem">
                          <button
                            class="btn btn-secondary"
                            style="padding: 0.5rem 0.75rem; font-size: 0.75rem"
                          >
                            <i class="fas fa-edit"></i>
                            编辑
                          </button>
                          <button
                            class="btn btn-secondary"
                            style="
                              padding: 0.5rem 0.75rem;
                              font-size: 0.75rem;
                              color: #dc2626;
                              border-color: #dc2626;
                            "
                          >
                            <i class="fas fa-trash"></i>
                            删除
                          </button>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div style="font-weight: 600; color: #1e293b">
                          测试环境密钥
                        </div>
                      </td>
                      <td>
                        <div
                          style="
                            font-family: monospace;
                            background: #f8fafc;
                            padding: 0.5rem;
                            border-radius: 4px;
                            font-size: 0.875rem;
                            display: flex;
                            align-items: center;
                            gap: 0.5rem;
                          "
                        >
                          <span>sk-vet-test-xyz789...abc123</span>
                          <button
                            onclick="copyToClipboard('sk-vet-test-xyz789abc123def')"
                            style="
                              background: none;
                              border: none;
                              color: #3b82f6;
                              cursor: pointer;
                            "
                          >
                            <i class="fas fa-copy"></i>
                          </button>
                        </div>
                      </td>
                      <td>
                        <div
                          style="display: flex; gap: 0.25rem; flex-wrap: wrap"
                        >
                          <span class="badge badge-info">AI问诊</span>
                          <span class="badge badge-info">报告生成</span>
                        </div>
                      </td>
                      <td>
                        <span class="badge badge-success">活跃</span>
                      </td>
                      <td>2024-01-10</td>
                      <td>1天前</td>
                      <td>
                        <div style="display: flex; gap: 0.5rem">
                          <button
                            class="btn btn-secondary"
                            style="padding: 0.5rem 0.75rem; font-size: 0.75rem"
                          >
                            <i class="fas fa-edit"></i>
                            编辑
                          </button>
                          <button
                            class="btn btn-secondary"
                            style="
                              padding: 0.5rem 0.75rem;
                              font-size: 0.75rem;
                              color: #dc2626;
                              border-color: #dc2626;
                            "
                          >
                            <i class="fas fa-trash"></i>
                            删除
                          </button>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div style="font-weight: 600; color: #1e293b">
                          移动应用密钥
                        </div>
                      </td>
                      <td>
                        <div
                          style="
                            font-family: monospace;
                            background: #f8fafc;
                            padding: 0.5rem;
                            border-radius: 4px;
                            font-size: 0.875rem;
                            display: flex;
                            align-items: center;
                            gap: 0.5rem;
                          "
                        >
                          <span>sk-vet-mobile-def456...ghi012</span>
                          <button
                            onclick="copyToClipboard('sk-vet-mobile-def456ghi012jkl')"
                            style="
                              background: none;
                              border: none;
                              color: #3b82f6;
                              cursor: pointer;
                            "
                          >
                            <i class="fas fa-copy"></i>
                          </button>
                        </div>
                      </td>
                      <td>
                        <div
                          style="display: flex; gap: 0.25rem; flex-wrap: wrap"
                        >
                          <span class="badge badge-info">AI问诊</span>
                        </div>
                      </td>
                      <td>
                        <span class="badge badge-warning">未激活</span>
                      </td>
                      <td>2024-01-08</td>
                      <td>从未使用</td>
                      <td>
                        <div style="display: flex; gap: 0.5rem">
                          <button
                            class="btn btn-secondary"
                            style="padding: 0.5rem 0.75rem; font-size: 0.75rem"
                          >
                            <i class="fas fa-edit"></i>
                            编辑
                          </button>
                          <button
                            class="btn btn-secondary"
                            style="
                              padding: 0.5rem 0.75rem;
                              font-size: 0.75rem;
                              color: #dc2626;
                              border-color: #dc2626;
                            "
                          >
                            <i class="fas fa-trash"></i>
                            删除
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- AI智能体管理页面 -->
        <div id="agents" class="page">
          <div class="page-header">
            <h1 class="page-title">AI智能体管理</h1>
            <p class="page-description">
              管理系统级智能体和用户自定义智能体，监控性能和使用情况。
            </p>
          </div>

          <div
            style="
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
              gap: 1.5rem;
            "
          >
            <div class="card">
              <div
                class="card-header"
                style="background: #3b82f6; color: white"
              >
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                  "
                >
                  <div style="display: flex; align-items: center; gap: 0.75rem">
                    <i class="fas fa-stethoscope" style="font-size: 1.5rem"></i>
                    <h3 class="card-title" style="color: white">
                      AI问诊智能体
                    </h3>
                  </div>
                  <span class="badge badge-success">运行中</span>
                </div>
              </div>
              <div class="card-content">
                <p style="color: #64748b; margin-bottom: 1.5rem">
                  基于大语言模型的智能问诊系统，能够根据宠物症状提供专业的诊断建议和治疗方案。
                </p>

                <div
                  style="
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 1rem;
                    margin-bottom: 1.5rem;
                    text-align: center;
                  "
                >
                  <div>
                    <div
                      style="
                        font-size: 1.5rem;
                        font-weight: 600;
                        color: #1e293b;
                      "
                    >
                      8,234
                    </div>
                    <div style="font-size: 0.75rem; color: #64748b">
                      今日调用
                    </div>
                  </div>
                  <div>
                    <div
                      style="
                        font-size: 1.5rem;
                        font-weight: 600;
                        color: #1e293b;
                      "
                    >
                      98.7%
                    </div>
                    <div style="font-size: 0.75rem; color: #64748b">成功率</div>
                  </div>
                </div>

                <div style="margin-bottom: 1.5rem">
                  <div
                    style="
                      font-size: 0.875rem;
                      font-weight: 600;
                      color: #374151;
                      margin-bottom: 0.75rem;
                    "
                  >
                    核心功能
                  </div>
                  <div style="display: flex; flex-wrap: wrap; gap: 0.5rem">
                    <span class="badge badge-info">症状分析</span>
                    <span class="badge badge-info">诊断建议</span>
                    <span class="badge badge-info">治疗方案</span>
                    <span class="badge badge-info">流式输出</span>
                  </div>
                </div>

                <div style="display: flex; gap: 0.75rem">
                  <button class="btn btn-primary" style="flex: 1">
                    <i class="fas fa-play"></i>
                    测试调用
                  </button>
                  <button class="btn btn-secondary">
                    <i class="fas fa-cog"></i>
                    配置
                  </button>
                </div>
              </div>
            </div>

            <div class="card">
              <div
                class="card-header"
                style="background: #10b981; color: white"
              >
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                  "
                >
                  <div style="display: flex; align-items: center; gap: 0.75rem">
                    <i
                      class="fas fa-file-medical"
                      style="font-size: 1.5rem"
                    ></i>
                    <h3 class="card-title" style="color: white">
                      报告生成智能体
                    </h3>
                  </div>
                  <span class="badge badge-success">运行中</span>
                </div>
              </div>
              <div class="card-content">
                <p style="color: #64748b; margin-bottom: 1.5rem">
                  自动生成专业的宠物医疗报告，支持多种报告类型，包括体检报告、诊断报告等。
                </p>

                <div
                  style="
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 1rem;
                    margin-bottom: 1.5rem;
                    text-align: center;
                  "
                >
                  <div>
                    <div
                      style="
                        font-size: 1.5rem;
                        font-weight: 600;
                        color: #1e293b;
                      "
                    >
                      3,456
                    </div>
                    <div style="font-size: 0.75rem; color: #64748b">
                      今日调用
                    </div>
                  </div>
                  <div>
                    <div
                      style="
                        font-size: 1.5rem;
                        font-weight: 600;
                        color: #1e293b;
                      "
                    >
                      99.2%
                    </div>
                    <div style="font-size: 0.75rem; color: #64748b">成功率</div>
                  </div>
                </div>

                <div style="margin-bottom: 1.5rem">
                  <div
                    style="
                      font-size: 0.875rem;
                      font-weight: 600;
                      color: #374151;
                      margin-bottom: 0.75rem;
                    "
                  >
                    核心功能
                  </div>
                  <div style="display: flex; flex-wrap: wrap; gap: 0.5rem">
                    <span class="badge badge-info">多种模板</span>
                    <span class="badge badge-info">数据填充</span>
                    <span class="badge badge-info">PDF导出</span>
                    <span class="badge badge-info">自定义模板</span>
                  </div>
                </div>

                <div style="display: flex; gap: 0.75rem">
                  <button class="btn btn-primary" style="flex: 1">
                    <i class="fas fa-play"></i>
                    测试调用
                  </button>
                  <button class="btn btn-secondary">
                    <i class="fas fa-cog"></i>
                    配置
                  </button>
                </div>
              </div>
            </div>

            <div class="card">
              <div
                class="card-header"
                style="background: #f59e0b; color: white"
              >
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                  "
                >
                  <div style="display: flex; align-items: center; gap: 0.75rem">
                    <i class="fas fa-search" style="font-size: 1.5rem"></i>
                    <h3 class="card-title" style="color: white">
                      报告解读智能体
                    </h3>
                  </div>
                  <span class="badge badge-success">运行中</span>
                </div>
              </div>
              <div class="card-content">
                <p style="color: #64748b; margin-bottom: 1.5rem">
                  智能解读医疗报告和检查结果，支持图像识别和OCR文字提取，提供专业解释。
                </p>

                <div
                  style="
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 1rem;
                    margin-bottom: 1.5rem;
                    text-align: center;
                  "
                >
                  <div>
                    <div
                      style="
                        font-size: 1.5rem;
                        font-weight: 600;
                        color: #1e293b;
                      "
                    >
                      1,157
                    </div>
                    <div style="font-size: 0.75rem; color: #64748b">
                      今日调用
                    </div>
                  </div>
                  <div>
                    <div
                      style="
                        font-size: 1.5rem;
                        font-weight: 600;
                        color: #1e293b;
                      "
                    >
                      97.8%
                    </div>
                    <div style="font-size: 0.75rem; color: #64748b">成功率</div>
                  </div>
                </div>

                <div style="margin-bottom: 1.5rem">
                  <div
                    style="
                      font-size: 0.875rem;
                      font-weight: 600;
                      color: #374151;
                      margin-bottom: 0.75rem;
                    "
                  >
                    核心功能
                  </div>
                  <div style="display: flex; flex-wrap: wrap; gap: 0.5rem">
                    <span class="badge badge-info">图像识别</span>
                    <span class="badge badge-info">OCR提取</span>
                    <span class="badge badge-info">内容解读</span>
                    <span class="badge badge-info">异常标注</span>
                  </div>
                </div>

                <div style="display: flex; gap: 0.75rem">
                  <button class="btn btn-primary" style="flex: 1">
                    <i class="fas fa-play"></i>
                    测试调用
                  </button>
                  <button class="btn btn-secondary">
                    <i class="fas fa-cog"></i>
                    配置
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 其他页面占位符 -->
        <div id="docs" class="page">
          <div class="page-header">
            <h1 class="page-title">API文档</h1>
            <p class="page-description">
              交互式API文档，包含详细的接口说明、示例代码和在线测试工具。
            </p>
          </div>
          <div class="card">
            <div class="card-content">
              <p style="text-align: center; color: #64748b; padding: 3rem">
                API文档页面开发中...
              </p>
            </div>
          </div>
        </div>

        <div id="users" class="page">
          <div class="page-header">
            <h1 class="page-title">用户管理</h1>
            <p class="page-description">
              用户注册、认证、权限管理和使用情况监控。
            </p>
          </div>
          <div class="card">
            <div class="card-content">
              <p style="text-align: center; color: #64748b; padding: 3rem">
                用户管理页面开发中...
              </p>
            </div>
          </div>
        </div>

        <div id="pets" class="page">
          <div class="page-header">
            <h1 class="page-title">宠物管理</h1>
            <p class="page-description">
              宠物档案管理、医疗记录追踪和健康状态监控。
            </p>
          </div>
          <div class="card">
            <div class="card-content">
              <p style="text-align: center; color: #64748b; padding: 3rem">
                宠物管理页面开发中...
              </p>
            </div>
          </div>
        </div>

        <div id="analytics" class="page">
          <div class="page-header">
            <h1 class="page-title">数据分析</h1>
            <p class="page-description">
              API调用分析、错误监控、性能指标和业务数据分析。
            </p>
          </div>
          <div class="card">
            <div class="card-content">
              <p style="text-align: center; color: #64748b; padding: 3rem">
                数据分析页面开发中...
              </p>
            </div>
          </div>
        </div>

        <div id="billing" class="page">
          <div class="page-header">
            <h1 class="page-title">计费管理</h1>
            <p class="page-description">
              API调用计费、套餐管理、使用量监控和自动扣费。
            </p>
          </div>
          <div class="card">
            <div class="card-content">
              <p style="text-align: center; color: #64748b; padding: 3rem">
                计费管理页面开发中...
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>

    <script>
      // 路由管理
      class Router {
        constructor() {
          this.routes = {};
          this.currentPage = "dashboard";
          this.init();
        }

        init() {
          // 绑定侧边栏链接点击事件
          document.querySelectorAll(".sidebar-link").forEach((link) => {
            link.addEventListener("click", (e) => {
              e.preventDefault();
              const page = link.getAttribute("data-page");
              this.navigateTo(page);
            });
          });

          // 处理浏览器前进后退
          window.addEventListener("popstate", (e) => {
            const page = e.state?.page || "dashboard";
            this.showPage(page);
          });

          // 初始化页面
          const hash = window.location.hash.slice(1);
          const initialPage = hash || "dashboard";
          this.navigateTo(initialPage);
        }

        navigateTo(page) {
          // 更新URL
          window.history.pushState({ page }, "", `#${page}`);
          this.showPage(page);
        }

        showPage(page) {
          // 隐藏所有页面
          document.querySelectorAll(".page").forEach((p) => {
            p.classList.remove("active");
          });

          // 显示目标页面
          const targetPage = document.getElementById(page);
          if (targetPage) {
            targetPage.classList.add("active");
          }

          // 更新侧边栏活跃状态
          document.querySelectorAll(".sidebar-link").forEach((link) => {
            link.classList.remove("active");
          });

          const activeLink = document.querySelector(`[data-page="${page}"]`);
          if (activeLink) {
            activeLink.classList.add("active");
          }

          this.currentPage = page;

          // 页面切换后的回调
          this.onPageChange(page);
        }

        onPageChange(page) {
          // 如果是控制台页面，初始化图表
          if (page === "dashboard") {
            setTimeout(() => {
              this.initCharts();
            }, 100);
          }
        }

        initCharts() {
          // API调用趋势图
          const apiCtx = document.getElementById("apiChart");
          if (apiCtx && !apiCtx.chart) {
            apiCtx.chart = new Chart(apiCtx.getContext("2d"), {
              type: "line",
              data: {
                labels: [
                  "7天前",
                  "6天前",
                  "5天前",
                  "4天前",
                  "3天前",
                  "昨天",
                  "今天",
                ],
                datasets: [
                  {
                    label: "API调用次数",
                    data: [8500, 9200, 8800, 11200, 10500, 11200, 12847],
                    borderColor: "#3b82f6",
                    backgroundColor: "rgba(59, 130, 246, 0.1)",
                    tension: 0.4,
                    fill: true,
                  },
                ],
              },
              options: {
                responsive: true,
                plugins: {
                  legend: {
                    display: false,
                  },
                },
                scales: {
                  y: {
                    beginAtZero: true,
                  },
                },
              },
            });
          }

          // 服务使用分布图
          const serviceCtx = document.getElementById("serviceChart");
          if (serviceCtx && !serviceCtx.chart) {
            serviceCtx.chart = new Chart(serviceCtx.getContext("2d"), {
              type: "doughnut",
              data: {
                labels: ["AI问诊", "报告生成", "报告解读", "其他"],
                datasets: [
                  {
                    data: [65, 27, 9, 4],
                    backgroundColor: [
                      "#3b82f6",
                      "#10b981",
                      "#f59e0b",
                      "#e2e8f0",
                    ],
                  },
                ],
              },
              options: {
                responsive: true,
                plugins: {
                  legend: {
                    position: "bottom",
                  },
                },
              },
            });
          }
        }
      }

      // 工具函数
      function copyToClipboard(text) {
        navigator.clipboard
          .writeText(text)
          .then(() => {
            showToast("API密钥已复制到剪贴板", "success");
          })
          .catch(() => {
            showToast("复制失败，请手动复制", "error");
          });
      }

      function showToast(message, type = "info") {
        const toast = document.createElement("div");
        toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 24px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                transition: all 0.3s;
            `;

        switch (type) {
          case "success":
            toast.style.background = "#10b981";
            break;
          case "error":
            toast.style.background = "#ef4444";
            break;
          default:
            toast.style.background = "#3b82f6";
        }

        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
          toast.style.opacity = "0";
          toast.style.transform = "translateX(100%)";
          setTimeout(() => {
            document.body.removeChild(toast);
          }, 300);
        }, 3000);
      }

      function openCreateKeyModal() {
        showToast("创建API密钥功能开发中...", "info");
      }

      // 响应式侧边栏
      function toggleSidebar() {
        const sidebar = document.querySelector(".sidebar");
        sidebar.classList.toggle("open");
      }

      // 初始化应用
      document.addEventListener("DOMContentLoaded", () => {
        window.router = new Router();

        // 移动端菜单按钮
        if (window.innerWidth <= 1024) {
          const logo = document.querySelector(".logo");
          const menuBtn = document.createElement("button");
          menuBtn.innerHTML = '<i class="fas fa-bars"></i>';
          menuBtn.style.cssText = `
                    background: none;
                    border: none;
                    color: #3b82f6;
                    font-size: 1.25rem;
                    cursor: pointer;
                    padding: 0.5rem;
                    margin-left: 1rem;
                `;
          menuBtn.onclick = toggleSidebar;
          logo.parentNode.insertBefore(menuBtn, logo.nextSibling);
        }
      });
    </script>
  </body>
</html>
