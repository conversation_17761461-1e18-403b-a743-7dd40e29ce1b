<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宠物信息管理 - 宠物医疗AI开放平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #334155;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav {
            display: flex;
            gap: 2rem;
        }

        .nav a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: background 0.2s;
        }

        .nav a:hover, .nav a.active {
            background: rgba(255,255,255,0.2);
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: bold;
            color: #1e293b;
        }

        .pets-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .pet-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .pet-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .pet-header {
            position: relative;
            height: 200px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .pet-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            border: 3px solid rgba(255,255,255,0.3);
        }

        .pet-status {
            position: absolute;
            top: 1rem;
            right: 1rem;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            background: rgba(255,255,255,0.2);
            color: white;
        }

        .pet-content {
            padding: 1.5rem;
        }

        .pet-name {
            font-size: 1.25rem;
            font-weight: bold;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }

        .pet-breed {
            color: #64748b;
            font-size: 0.875rem;
            margin-bottom: 1rem;
        }

        .pet-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .info-item {
            text-align: center;
        }

        .info-label {
            font-size: 0.75rem;
            color: #64748b;
            margin-bottom: 0.25rem;
        }

        .info-value {
            font-weight: 600;
            color: #1e293b;
        }

        .pet-health {
            background: #f8fafc;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .health-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .health-items {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .health-tag {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .health-good {
            background: #dcfce7;
            color: #166534;
        }

        .health-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .health-danger {
            background: #fee2e2;
            color: #991b1b;
        }

        .pet-actions {
            display: flex;
            gap: 0.75rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border-radius: 6px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 0.875rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
            flex: 1;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #475569;
        }

        .btn-secondary:hover {
            background: #cbd5e1;
        }

        .add-pet-card {
            background: white;
            border: 2px dashed #cbd5e1;
            border-radius: 12px;
            padding: 3rem 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 400px;
        }

        .add-pet-card:hover {
            border-color: #3b82f6;
            background: #f8fafc;
        }

        .add-icon {
            width: 4rem;
            height: 4rem;
            background: #f1f5f9;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            font-size: 1.5rem;
            color: #64748b;
        }

        .add-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }

        .add-description {
            color: #64748b;
            font-size: 0.875rem;
        }

        .filters-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .filters-row {
            display: grid;
            grid-template-columns: 1fr 200px 200px 150px;
            gap: 1rem;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
            font-size: 0.875rem;
        }

        .form-input, .form-select {
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #64748b;
            font-size: 0.875rem;
        }

        .recent-records {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .records-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.75rem;
        }

        .record-item {
            display: flex;
            justify-content: between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f1f5f9;
            font-size: 0.875rem;
        }

        .record-item:last-child {
            border-bottom: none;
        }

        .record-date {
            color: #64748b;
            font-size: 0.75rem;
        }

        @media (max-width: 768px) {
            .pets-grid {
                grid-template-columns: 1fr;
            }
            
            .filters-row {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-paw"></i>
                <span>宠物医疗AI开放平台</span>
            </div>
            <nav class="nav">
                <a href="01-dashboard.html">控制台</a>
                <a href="02-api-keys.html">API密钥</a>
                <a href="03-agents.html">智能体</a>
                <a href="04-docs.html">文档</a>
                <a href="05-users.html">用户管理</a>
                <a href="06-pets.html" class="active">宠物管理</a>
            </nav>
        </div>
    </header>

    <div class="container">
        <div class="page-header">
            <h1 class="page-title">宠物信息管理</h1>
            <button class="btn btn-primary">
                <i class="fas fa-plus"></i>
                添加宠物
            </button>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">2,456</div>
                <div class="stat-label">总宠物数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">1,234</div>
                <div class="stat-label">犬类</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">987</div>
                <div class="stat-label">猫类</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">235</div>
                <div class="stat-label">其他</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">156</div>
                <div class="stat-label">本月新增</div>
            </div>
        </div>

        <div class="filters-section">
            <div class="filters-row">
                <div class="form-group">
                    <label class="form-label">搜索宠物</label>
                    <input type="text" class="form-input" placeholder="输入宠物名称或主人信息">
                </div>
                <div class="form-group">
                    <label class="form-label">宠物类型</label>
                    <select class="form-select">
                        <option value="">全部类型</option>
                        <option value="dog">犬类</option>
                        <option value="cat">猫类</option>
                        <option value="bird">鸟类</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">健康状态</label>
                    <select class="form-select">
                        <option value="">全部状态</option>
                        <option value="healthy">健康</option>
                        <option value="sick">患病</option>
                        <option value="recovering">康复中</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        搜索
                    </button>
                </div>
            </div>
        </div>

        <div class="pets-grid">
            <div class="add-pet-card" onclick="addNewPet()">
                <div class="add-icon">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="add-title">添加新宠物</div>
                <div class="add-description">
                    创建宠物档案，记录基本信息和医疗记录
                </div>
            </div>

            <div class="pet-card">
                <div class="pet-header">
                    <div class="pet-status">健康</div>
                    <div class="pet-avatar">
                        <i class="fas fa-dog"></i>
                    </div>
                </div>
                <div class="pet-content">
                    <div class="pet-name">小金</div>
                    <div class="pet-breed">金毛寻回犬 • 雄性</div>
                    
                    <div class="pet-info-grid">
                        <div class="info-item">
                            <div class="info-label">年龄</div>
                            <div class="info-value">3岁</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">体重</div>
                            <div class="info-value">25kg</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">主人</div>
                            <div class="info-value">张先生</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">登记日期</div>
                            <div class="info-value">2024-01-15</div>
                        </div>
                    </div>

                    <div class="pet-health">
                        <div class="health-title">
                            <i class="fas fa-heartbeat"></i>
                            健康状态
                        </div>
                        <div class="health-items">
                            <span class="health-tag health-good">疫苗完整</span>
                            <span class="health-tag health-good">体检正常</span>
                            <span class="health-tag health-warning">需要驱虫</span>
                        </div>
                    </div>

                    <div class="recent-records">
                        <div class="records-title">最近记录</div>
                        <div class="record-item">
                            <span>常规体检</span>
                            <span class="record-date">2024-01-10</span>
                        </div>
                        <div class="record-item">
                            <span>疫苗接种</span>
                            <span class="record-date">2024-01-05</span>
                        </div>
                    </div>

                    <div class="pet-actions">
                        <button class="btn btn-primary">
                            <i class="fas fa-eye"></i>
                            查看详情
                        </button>
                        <button class="btn btn-secondary">
                            <i class="fas fa-edit"></i>
                            编辑
                        </button>
                    </div>
                </div>
            </div>

            <div class="pet-card">
                <div class="pet-header">
                    <div class="pet-status">患病</div>
                    <div class="pet-avatar">
                        <i class="fas fa-cat"></i>
                    </div>
                </div>
                <div class="pet-content">
                    <div class="pet-name">咪咪</div>
                    <div class="pet-breed">英国短毛猫 • 雌性</div>
                    
                    <div class="pet-info-grid">
                        <div class="info-item">
                            <div class="info-label">年龄</div>
                            <div class="info-value">2岁</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">体重</div>
                            <div class="info-value">4.5kg</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">主人</div>
                            <div class="info-value">李女士</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">登记日期</div>
                            <div class="info-value">2024-01-12</div>
                        </div>
                    </div>

                    <div class="pet-health">
                        <div class="health-title">
                            <i class="fas fa-heartbeat"></i>
                            健康状态
                        </div>
                        <div class="health-items">
                            <span class="health-tag health-good">疫苗完整</span>
                            <span class="health-tag health-danger">急性胃肠炎</span>
                            <span class="health-tag health-warning">治疗中</span>
                        </div>
                    </div>

                    <div class="recent-records">
                        <div class="records-title">最近记录</div>
                        <div class="record-item">
                            <span>AI问诊</span>
                            <span class="record-date">2024-01-14</span>
                        </div>
                        <div class="record-item">
                            <span>血液检查</span>
                            <span class="record-date">2024-01-13</span>
                        </div>
                    </div>

                    <div class="pet-actions">
                        <button class="btn btn-primary">
                            <i class="fas fa-eye"></i>
                            查看详情
                        </button>
                        <button class="btn btn-secondary">
                            <i class="fas fa-edit"></i>
                            编辑
                        </button>
                    </div>
                </div>
            </div>

            <div class="pet-card">
                <div class="pet-header">
                    <div class="pet-status">康复中</div>
                    <div class="pet-avatar">
                        <i class="fas fa-dog"></i>
                    </div>
                </div>
                <div class="pet-content">
                    <div class="pet-name">旺财</div>
                    <div class="pet-breed">拉布拉多犬 • 雄性</div>
                    
                    <div class="pet-info-grid">
                        <div class="info-item">
                            <div class="info-label">年龄</div>
                            <div class="info-value">5岁</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">体重</div>
                            <div class="info-value">30kg</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">主人</div>
                            <div class="info-value">王先生</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">登记日期</div>
                            <div class="info-value">2024-01-08</div>
                        </div>
                    </div>

                    <div class="pet-health">
                        <div class="health-title">
                            <i class="fas fa-heartbeat"></i>
                            健康状态
                        </div>
                        <div class="health-items">
                            <span class="health-tag health-good">疫苗完整</span>
                            <span class="health-tag health-warning">骨折康复</span>
                            <span class="health-tag health-good">食欲正常</span>
                        </div>
                    </div>

                    <div class="recent-records">
                        <div class="records-title">最近记录</div>
                        <div class="record-item">
                            <span>复查X光</span>
                            <span class="record-date">2024-01-11</span>
                        </div>
                        <div class="record-item">
                            <span>康复训练</span>
                            <span class="record-date">2024-01-09</span>
                        </div>
                    </div>

                    <div class="pet-actions">
                        <button class="btn btn-primary">
                            <i class="fas fa-eye"></i>
                            查看详情
                        </button>
                        <button class="btn btn-secondary">
                            <i class="fas fa-edit"></i>
                            编辑
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function addNewPet() {
            alert('添加新宠物功能开发中...');
        }
    </script>
</body>
</html>
