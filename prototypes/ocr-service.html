<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>OCR服务 - 宠物医疗AI开放平台</title>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: #f8fafc;
        color: #334155;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem 2rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .header-content {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .logo {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.5rem;
        font-weight: bold;
      }

      .nav {
        display: flex;
        gap: 2rem;
      }

      .nav a {
        color: white;
        text-decoration: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        transition: background 0.2s;
      }

      .nav a:hover,
      .nav a.active {
        background: rgba(255, 255, 255, 0.2);
      }

      .container {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 0 2rem;
      }

      .page-header {
        text-align: center;
        margin-bottom: 3rem;
      }

      .page-title {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1e293b;
        margin-bottom: 1rem;
      }

      .page-subtitle {
        font-size: 1.125rem;
        color: #6b7280;
        max-width: 600px;
        margin: 0 auto;
      }

      .upload-section {
        background: white;
        border-radius: 16px;
        padding: 3rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
        text-align: center;
      }

      .upload-area {
        border: 2px dashed #d1d5db;
        border-radius: 12px;
        padding: 3rem;
        transition: all 0.2s;
        cursor: pointer;
        margin-bottom: 2rem;
      }

      .upload-area:hover,
      .upload-area.dragover {
        border-color: #3b82f6;
        background: #f8fafc;
      }

      .upload-icon {
        font-size: 3rem;
        color: #6b7280;
        margin-bottom: 1rem;
      }

      .upload-text {
        font-size: 1.125rem;
        color: #374151;
        margin-bottom: 0.5rem;
      }

      .upload-hint {
        color: #6b7280;
        font-size: 0.875rem;
      }

      .file-input {
        display: none;
      }

      .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        border: none;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
      }

      .btn-primary {
        background: #3b82f6;
        color: white;
      }

      .btn-primary:hover {
        background: #2563eb;
      }

      .btn-secondary {
        background: #6b7280;
        color: white;
      }

      .preview-section {
        display: none;
        margin-bottom: 2rem;
      }

      .preview-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
      }

      .preview-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      .card-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 1rem;
      }

      .image-preview {
        width: 100%;
        max-height: 400px;
        object-fit: contain;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }

      .result-content {
        background: #f8fafc;
        border-radius: 8px;
        padding: 1rem;
        font-family: "Monaco", "Menlo", monospace;
        font-size: 0.875rem;
        line-height: 1.6;
        white-space: pre-wrap;
        max-height: 400px;
        overflow-y: auto;
      }

      .loading {
        text-align: center;
        padding: 2rem;
        color: #6b7280;
      }

      .loading-spinner {
        display: inline-block;
        width: 2rem;
        height: 2rem;
        border: 3px solid #e5e7eb;
        border-top: 3px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .alert {
        padding: 1rem 1.5rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        display: none;
      }

      .alert-success {
        background: #f0fdf4;
        color: #166534;
        border: 1px solid #bbf7d0;
      }

      .alert-error {
        background: #fef2f2;
        color: #dc2626;
        border: 1px solid #fecaca;
      }

      .batch-upload {
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid #e5e7eb;
      }

      .batch-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 1rem;
      }

      .batch-results {
        display: none;
        margin-top: 2rem;
      }

      .batch-item {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        display: flex;
        gap: 1rem;
      }

      .batch-image {
        width: 100px;
        height: 100px;
        object-fit: cover;
        border-radius: 6px;
        border: 1px solid #e5e7eb;
      }

      .batch-content {
        flex: 1;
      }

      .batch-filename {
        font-weight: 500;
        color: #1e293b;
        margin-bottom: 0.5rem;
      }

      .batch-result {
        background: #f8fafc;
        border-radius: 6px;
        padding: 0.75rem;
        font-family: "Monaco", "Menlo", monospace;
        font-size: 0.75rem;
        line-height: 1.4;
        max-height: 100px;
        overflow-y: auto;
      }

      @media (max-width: 768px) {
        .preview-grid {
          grid-template-columns: 1fr;
        }

        .batch-item {
          flex-direction: column;
        }

        .batch-image {
          width: 100%;
          height: 200px;
        }
      }
    </style>
  </head>
  <body>
    <div class="header">
      <div class="header-content">
        <div class="logo">
          <i class="fas fa-paw"></i>
          <span>宠物医疗AI平台</span>
        </div>
        <nav class="nav">
          <a href="index.html">首页</a>
          <a href="user-management.html">用户管理</a>
          <a href="api-key-management.html">API密钥</a>
          <a href="agent-management.html">智能体</a>
          <a href="ocr-service.html" class="active">OCR服务</a>
          <a href="system-monitoring.html">系统监控</a>
        </nav>
        <div class="user-info">
          <span id="currentUserName">管理员</span>
          <button
            class="logout-btn"
            onclick="logout()"
            style="
              background: rgba(255, 255, 255, 0.2);
              border: none;
              color: white;
              padding: 0.5rem 1rem;
              border-radius: 6px;
              cursor: pointer;
            "
          >
            <i class="fas fa-sign-out-alt"></i>
            退出
          </button>
        </div>
      </div>
    </div>

    <div class="container">
      <div class="page-header">
        <h1 class="page-title">OCR文字识别服务</h1>
        <p class="page-subtitle">
          上传医疗报告图片，使用AI技术自动识别和提取文字内容，支持中文医学检查报告的专业识别。
        </p>
      </div>

      <div class="alert alert-success" id="successAlert"></div>
      <div class="alert alert-error" id="errorAlert"></div>

      <!-- 单图片上传 -->
      <div class="upload-section">
        <div
          class="upload-area"
          id="uploadArea"
          onclick="document.getElementById('fileInput').click()"
        >
          <div class="upload-icon">
            <i class="fas fa-cloud-upload-alt"></i>
          </div>
          <div class="upload-text">点击上传图片或拖拽图片到此处</div>
          <div class="upload-hint">支持 JPG、PNG、PDF 格式，最大 10MB</div>
        </div>

        <input
          type="file"
          id="fileInput"
          class="file-input"
          accept="image/*,.pdf"
          onchange="handleFileSelect(event)"
        />

        <div style="display: flex; gap: 1rem; justify-content: center">
          <button
            class="btn btn-primary"
            onclick="document.getElementById('fileInput').click()"
          >
            <i class="fas fa-upload"></i>
            选择文件
          </button>
          <button
            class="btn btn-secondary"
            onclick="processOCR()"
            id="processBtn"
            disabled
          >
            <i class="fas fa-magic"></i>
            开始识别
          </button>
        </div>

        <!-- 批量上传 -->
        <div class="batch-upload">
          <div class="batch-title">批量处理</div>
          <input
            type="file"
            id="batchFileInput"
            class="file-input"
            accept="image/*,.pdf"
            multiple
            onchange="handleBatchFileSelect(event)"
          />
          <button
            class="btn btn-primary"
            onclick="document.getElementById('batchFileInput').click()"
          >
            <i class="fas fa-images"></i>
            批量上传
          </button>
          <button
            class="btn btn-secondary"
            onclick="processBatchOCR()"
            id="batchProcessBtn"
            disabled
          >
            <i class="fas fa-magic"></i>
            批量识别
          </button>
        </div>
      </div>

      <!-- 预览和结果 -->
      <div class="preview-section" id="previewSection">
        <div class="preview-grid">
          <div class="preview-card">
            <div class="card-title">图片预览</div>
            <img id="imagePreview" class="image-preview" alt="图片预览" />
          </div>
          <div class="preview-card">
            <div class="card-title">识别结果</div>
            <div id="loadingIndicator" class="loading" style="display: none">
              <div class="loading-spinner"></div>
              <div>正在识别中...</div>
            </div>
            <div id="resultContent" class="result-content"></div>
          </div>
        </div>
      </div>

      <!-- 批量处理结果 -->
      <div class="batch-results" id="batchResults">
        <div class="card-title">批量处理结果</div>
        <div id="batchResultsContainer"></div>
      </div>
    </div>

    <script>
      // API配置
      const API_BASE_URL = "http://localhost:8004"; // OCR服务端口

      // 全局变量
      let selectedFile = null;
      let selectedFiles = [];

      // 页面加载时初始化
      document.addEventListener("DOMContentLoaded", function () {
        checkAuth();
        setupDragAndDrop();
      });

      // 检查认证状态
      function checkAuth() {
        const token = localStorage.getItem("access_token");
        if (!token) {
          window.location.href = "auth-login.html";
          return;
        }

        const currentUser = localStorage.getItem("current_user") || "管理员";
        document.getElementById("currentUserName").textContent = currentUser;
      }

      // 设置拖拽上传
      function setupDragAndDrop() {
        const uploadArea = document.getElementById("uploadArea");

        uploadArea.addEventListener("dragover", function (e) {
          e.preventDefault();
          uploadArea.classList.add("dragover");
        });

        uploadArea.addEventListener("dragleave", function (e) {
          e.preventDefault();
          uploadArea.classList.remove("dragover");
        });

        uploadArea.addEventListener("drop", function (e) {
          e.preventDefault();
          uploadArea.classList.remove("dragover");

          const files = e.dataTransfer.files;
          if (files.length > 0) {
            handleFile(files[0]);
          }
        });
      }

      // 处理文件选择
      function handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
          handleFile(file);
        }
      }

      // 处理单个文件
      function handleFile(file) {
        if (!validateFile(file)) {
          return;
        }

        selectedFile = file;
        showImagePreview(file);
        document.getElementById("processBtn").disabled = false;
      }

      // 处理批量文件选择
      function handleBatchFileSelect(event) {
        const files = Array.from(event.target.files);
        selectedFiles = files.filter((file) => validateFile(file));

        if (selectedFiles.length > 0) {
          document.getElementById("batchProcessBtn").disabled = false;
          showAlert(`已选择 ${selectedFiles.length} 个文件`, "success");
        }
      }

      // 验证文件
      function validateFile(file) {
        const maxSize = 10 * 1024 * 1024; // 10MB
        const allowedTypes = [
          "image/jpeg",
          "image/png",
          "image/gif",
          "application/pdf",
        ];

        if (file.size > maxSize) {
          showAlert("文件大小不能超过 10MB", "error");
          return false;
        }

        if (!allowedTypes.includes(file.type)) {
          showAlert("只支持 JPG、PNG、GIF、PDF 格式", "error");
          return false;
        }

        return true;
      }

      // 显示图片预览
      function showImagePreview(file) {
        const reader = new FileReader();
        reader.onload = function (e) {
          const imagePreview = document.getElementById("imagePreview");
          imagePreview.src = e.target.result;
          document.getElementById("previewSection").style.display = "block";
          document.getElementById("resultContent").textContent =
            '点击"开始识别"按钮进行OCR识别...';
        };
        reader.readAsDataURL(file);
      }

      // 处理OCR识别（演示模式）
      async function processOCR() {
        if (!selectedFile) {
          showAlert("请先选择文件", "error");
          return;
        }

        const loadingIndicator = document.getElementById("loadingIndicator");
        const resultContent = document.getElementById("resultContent");

        try {
          loadingIndicator.style.display = "block";
          resultContent.textContent = "";

          // 模拟网络延迟
          await new Promise((resolve) => setTimeout(resolve, 2000));

          // 生成模拟OCR结果
          const mockResult = generateMockOCRResult(selectedFile.name);

          resultContent.textContent = mockResult;
          showAlert("OCR识别完成", "success");
        } catch (error) {
          console.error("OCR识别失败:", error);
          resultContent.textContent = "识别失败: " + error.message;
          showAlert("OCR识别失败: " + error.message, "error");
        } finally {
          loadingIndicator.style.display = "none";
        }
      }

      // 生成模拟OCR结果
      function generateMockOCRResult(fileName) {
        const fileExt = fileName.toLowerCase().split(".").pop();

        if (fileExt === "pdf") {
          return `【宠物医疗检查报告】

检查日期：2024年1月20日
宠物姓名：小白
品种：金毛寻回犬
年龄：3岁
性别：雄性（已绝育）

检查项目：
1. 常规体检
2. 血液生化检查
3. X光检查

检查结果：
体温：38.2°C（正常）
心率：110次/分钟（正常）
呼吸：22次/分钟（正常）
体重：28.5kg

血液检查：
白细胞计数：8.2×10³/μL（正常）
红细胞计数：6.8×10⁶/μL（正常）
血红蛋白：145g/L（正常）
血小板：350×10³/μL（正常）

生化指标：
总蛋白：68g/L（正常）
白蛋白：35g/L（正常）
肌酐：85μmol/L（正常）
尿素氮：5.2mmol/L（正常）
ALT：42U/L（正常）
AST：38U/L（正常）

X光检查：
胸部X光：心肺结构正常，无异常阴影
腹部X光：内脏器官位置正常，无异物

诊断意见：
整体健康状况良好，各项指标均在正常范围内。

建议：
1. 继续保持规律饮食和适量运动
2. 定期进行健康检查
3. 注意口腔卫生，定期洁牙
4. 下次体检时间：6个月后

兽医签名：张医生
检查时间：2024年1月20日 14:30`;
        } else {
          return `【医疗检查报告识别结果】

报告类型：血液检查报告
检查日期：2024-01-20

患者信息：
姓名：小花（猫咪）
品种：英国短毛猫
年龄：2岁
性别：雌性

检查项目：血常规 + 生化全套

血常规结果：
• 白细胞计数 (WBC): 7.5 × 10³/μL ✓正常
• 红细胞计数 (RBC): 8.2 × 10⁶/μL ✓正常
• 血红蛋白 (HGB): 125 g/L ✓正常
• 红细胞压积 (HCT): 38% ✓正常
• 血小板计数 (PLT): 420 × 10³/μL ✓正常

生化指标：
• 总蛋白 (TP): 72 g/L ✓正常
• 白蛋白 (ALB): 38 g/L ✓正常
• 球蛋白 (GLOB): 34 g/L ✓正常
• 肌酐 (CREA): 95 μmol/L ✓正常
• 尿素氮 (BUN): 6.8 mmol/L ✓正常
• 丙氨酸转氨酶 (ALT): 45 U/L ✓正常
• 天冬氨酸转氨酶 (AST): 35 U/L ✓正常
• 碱性磷酸酶 (ALP): 78 U/L ✓正常

检查结论：
所有检查指标均在正常参考范围内，宠物健康状况良好。

医生建议：
1. 继续保持良好的饮食习惯
2. 定期运动，保持体重
3. 建议6个月后复查
4. 如有异常症状及时就医

检查医生：李兽医
报告日期：2024年1月20日`;
        }
      }

      // 批量OCR处理（演示模式）
      async function processBatchOCR() {
        if (selectedFiles.length === 0) {
          showAlert("请先选择文件", "error");
          return;
        }

        try {
          showAlert("正在批量处理，请稍候...", "success");

          // 模拟网络延迟
          await new Promise((resolve) => setTimeout(resolve, 3000));

          // 生成模拟批量结果
          const mockResults = selectedFiles.map((file, index) => ({
            extracted_text: generateMockOCRResult(file.name),
            confidence: 0.95 + Math.random() * 0.04, // 95-99%的置信度
            processing_time: 1.2 + Math.random() * 0.8, // 1.2-2.0秒处理时间
          }));

          displayBatchResults(mockResults);
          showAlert("批量OCR识别完成", "success");
        } catch (error) {
          console.error("批量OCR识别失败:", error);
          showAlert("批量OCR识别失败: " + error.message, "error");
        }
      }

      // 显示批量处理结果
      function displayBatchResults(results) {
        const container = document.getElementById("batchResultsContainer");
        const batchResults = document.getElementById("batchResults");

        container.innerHTML = results
          .map(
            (result, index) => `
                <div class="batch-item">
                    <img src="${URL.createObjectURL(
                      selectedFiles[index]
                    )}" class="batch-image" alt="图片${index + 1}">
                    <div class="batch-content">
                        <div class="batch-filename">${
                          selectedFiles[index].name
                        }</div>
                        <div class="batch-result">${
                          result.extracted_text || "未识别到文字内容"
                        }</div>
                    </div>
                </div>
            `
          )
          .join("");

        batchResults.style.display = "block";
      }

      // 显示提示信息
      function showAlert(message, type = "success") {
        const successAlert = document.getElementById("successAlert");
        const errorAlert = document.getElementById("errorAlert");

        // 隐藏所有提示
        successAlert.style.display = "none";
        errorAlert.style.display = "none";

        // 显示对应类型的提示
        const alertElement = type === "success" ? successAlert : errorAlert;
        alertElement.textContent = message;
        alertElement.style.display = "block";

        // 自动隐藏
        setTimeout(() => {
          alertElement.style.display = "none";
        }, 5000);
      }

      // 退出登录
      function logout() {
        if (confirm("确定要退出登录吗？")) {
          localStorage.removeItem("access_token");
          localStorage.removeItem("refresh_token");
          localStorage.removeItem("current_user");
          window.location.href = "auth-login.html";
        }
      }
    </script>
  </body>
</html>
