<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>API密钥管理 - 宠物医疗AI开放平台</title>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: #f8fafc;
        color: #334155;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem 2rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .header-content {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .logo {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.5rem;
        font-weight: bold;
      }

      .nav {
        display: flex;
        gap: 2rem;
      }

      .nav a {
        color: white;
        text-decoration: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        transition: background 0.2s;
      }

      .nav a:hover,
      .nav a.active {
        background: rgba(255, 255, 255, 0.2);
      }

      .container {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 0 2rem;
      }

      .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
      }

      .page-title {
        font-size: 2rem;
        font-weight: bold;
        color: #1e293b;
      }

      .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        border: none;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
      }

      .btn-primary {
        background: #3b82f6;
        color: white;
      }

      .btn-primary:hover {
        background: #2563eb;
      }

      .btn-secondary {
        background: #e2e8f0;
        color: #475569;
      }

      .btn-danger {
        background: #ef4444;
        color: white;
      }

      .card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .card-header {
        padding: 1.5rem;
        border-bottom: 1px solid #e2e8f0;
        background: #f8fafc;
      }

      .card-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1e293b;
      }

      .card-content {
        padding: 1.5rem;
      }

      .api-key-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem;
        border-bottom: 1px solid #e2e8f0;
      }

      .api-key-item:last-child {
        border-bottom: none;
      }

      .api-key-info {
        flex: 1;
      }

      .api-key-name {
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.5rem;
      }

      .api-key-value {
        font-family: "Monaco", "Menlo", monospace;
        background: #f1f5f9;
        padding: 0.5rem;
        border-radius: 6px;
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .api-key-meta {
        display: flex;
        gap: 1rem;
        font-size: 0.875rem;
        color: #64748b;
      }

      .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
      }

      .status-active {
        background: #dcfce7;
        color: #166534;
      }

      .status-inactive {
        background: #fee2e2;
        color: #991b1b;
      }

      .permissions-list {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
        margin-top: 0.5rem;
      }

      .permission-tag {
        background: #dbeafe;
        color: #1e40af;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
      }

      .actions {
        display: flex;
        gap: 0.5rem;
      }

      .btn-sm {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
      }

      .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
      }

      .modal-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        border-radius: 12px;
        width: 90%;
        max-width: 500px;
        max-height: 90vh;
        overflow-y: auto;
      }

      .modal-header {
        padding: 1.5rem;
        border-bottom: 1px solid #e2e8f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .modal-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1e293b;
      }

      .modal-body {
        padding: 1.5rem;
      }

      .form-group {
        margin-bottom: 1.5rem;
      }

      .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #374151;
      }

      .form-input {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 0.875rem;
      }

      .form-input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      .checkbox-group {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 0.75rem;
      }

      .checkbox-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .usage-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
        margin-bottom: 1.5rem;
      }

      .usage-stat {
        text-align: center;
        padding: 1rem;
        background: #f8fafc;
        border-radius: 8px;
      }

      .usage-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: #1e293b;
      }

      .usage-label {
        font-size: 0.875rem;
        color: #64748b;
        margin-top: 0.25rem;
      }

      .copy-btn {
        background: none;
        border: none;
        color: #3b82f6;
        cursor: pointer;
        padding: 0.25rem;
      }

      .copy-btn:hover {
        color: #2563eb;
      }

      @media (max-width: 768px) {
        .api-key-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 1rem;
        }

        .actions {
          width: 100%;
          justify-content: flex-end;
        }
      }
    </style>
  </head>
  <body>
    <header class="header">
      <div class="header-content">
        <div class="logo">
          <i class="fas fa-paw"></i>
          <span>宠物医疗AI开放平台</span>
        </div>
        <nav class="nav">
          <a href="01-dashboard.html">控制台</a>
          <a href="02-api-keys.html" class="active">API密钥</a>
          <a href="03-agents.html">智能体</a>
          <a href="04-docs.html">文档</a>
        </nav>
      </div>
    </header>

    <div class="container">
      <div class="page-header">
        <h1 class="page-title">API密钥管理</h1>
        <button class="btn btn-primary" onclick="openCreateModal()">
          <i class="fas fa-plus"></i>
          创建新密钥
        </button>
      </div>

      <div class="usage-stats">
        <div class="usage-stat">
          <div class="usage-value">5</div>
          <div class="usage-label">活跃密钥</div>
        </div>
        <div class="usage-stat">
          <div class="usage-value">12,847</div>
          <div class="usage-label">今日调用</div>
        </div>
        <div class="usage-stat">
          <div class="usage-value">87,153</div>
          <div class="usage-label">剩余配额</div>
        </div>
        <div class="usage-stat">
          <div class="usage-value">0.12%</div>
          <div class="usage-label">错误率</div>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <h3 class="card-title">API密钥列表</h3>
        </div>

        <div class="api-key-item">
          <div class="api-key-info">
            <div class="api-key-name">生产环境密钥</div>
            <div class="api-key-value">
              <span>sk-vet-prod-abc123...def789</span>
              <button
                class="copy-btn"
                onclick="copyToClipboard('sk-vet-prod-abc123def789xyz')"
              >
                <i class="fas fa-copy"></i>
              </button>
            </div>
            <div class="api-key-meta">
              <span>创建时间: 2024-01-15</span>
              <span>最后使用: 2小时前</span>
              <span class="status-badge status-active">活跃</span>
            </div>
            <div class="permissions-list">
              <span class="permission-tag">AI问诊</span>
              <span class="permission-tag">报告生成</span>
              <span class="permission-tag">报告解读</span>
            </div>
          </div>
          <div class="actions">
            <button class="btn btn-secondary btn-sm">
              <i class="fas fa-edit"></i>
              编辑
            </button>
            <button class="btn btn-danger btn-sm">
              <i class="fas fa-trash"></i>
              删除
            </button>
          </div>
        </div>

        <div class="api-key-item">
          <div class="api-key-info">
            <div class="api-key-name">测试环境密钥</div>
            <div class="api-key-value">
              <span>sk-vet-test-xyz789...abc123</span>
              <button
                class="copy-btn"
                onclick="copyToClipboard('sk-vet-test-xyz789abc123def')"
              >
                <i class="fas fa-copy"></i>
              </button>
            </div>
            <div class="api-key-meta">
              <span>创建时间: 2024-01-10</span>
              <span>最后使用: 1天前</span>
              <span class="status-badge status-active">活跃</span>
            </div>
            <div class="permissions-list">
              <span class="permission-tag">AI问诊</span>
              <span class="permission-tag">报告生成</span>
            </div>
          </div>
          <div class="actions">
            <button class="btn btn-secondary btn-sm">
              <i class="fas fa-edit"></i>
              编辑
            </button>
            <button class="btn btn-danger btn-sm">
              <i class="fas fa-trash"></i>
              删除
            </button>
          </div>
        </div>

        <div class="api-key-item">
          <div class="api-key-info">
            <div class="api-key-name">移动应用密钥</div>
            <div class="api-key-value">
              <span>sk-vet-mobile-def456...ghi012</span>
              <button
                class="copy-btn"
                onclick="copyToClipboard('sk-vet-mobile-def456ghi012jkl')"
              >
                <i class="fas fa-copy"></i>
              </button>
            </div>
            <div class="api-key-meta">
              <span>创建时间: 2024-01-08</span>
              <span>最后使用: 从未使用</span>
              <span class="status-badge status-inactive">未激活</span>
            </div>
            <div class="permissions-list">
              <span class="permission-tag">AI问诊</span>
            </div>
          </div>
          <div class="actions">
            <button class="btn btn-secondary btn-sm">
              <i class="fas fa-edit"></i>
              编辑
            </button>
            <button class="btn btn-danger btn-sm">
              <i class="fas fa-trash"></i>
              删除
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建API密钥模态框 -->
    <div id="createModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">创建新的API密钥</h3>
          <button
            onclick="closeCreateModal()"
            style="
              background: none;
              border: none;
              font-size: 1.5rem;
              cursor: pointer;
            "
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <form>
            <div class="form-group">
              <label class="form-label">密钥名称</label>
              <input
                type="text"
                class="form-input"
                placeholder="为您的API密钥起一个名称"
              />
            </div>

            <div class="form-group">
              <label class="form-label">权限设置</label>
              <div class="checkbox-group">
                <div class="checkbox-item">
                  <input type="checkbox" id="perm-diagnosis" checked />
                  <label for="perm-diagnosis">AI问诊智能体</label>
                </div>
                <div class="checkbox-item">
                  <input type="checkbox" id="perm-report" />
                  <label for="perm-report">报告生成智能体</label>
                </div>
                <div class="checkbox-item">
                  <input type="checkbox" id="perm-analysis" />
                  <label for="perm-analysis">报告解读智能体</label>
                </div>
                <div class="checkbox-item">
                  <input type="checkbox" id="perm-pets" />
                  <label for="perm-pets">宠物信息管理</label>
                </div>
                <div class="checkbox-item">
                  <input type="checkbox" id="perm-records" />
                  <label for="perm-records">医疗记录管理</label>
                </div>
                <div class="checkbox-item">
                  <input type="checkbox" id="perm-vaccines" />
                  <label for="perm-vaccines">疫苗记录管理</label>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">每日调用限制</label>
              <input
                type="number"
                class="form-input"
                value="10000"
                placeholder="设置每日最大调用次数"
              />
            </div>

            <div class="form-group">
              <label class="form-label">有效期</label>
              <select class="form-input">
                <option value="30">30天</option>
                <option value="90">90天</option>
                <option value="365">1年</option>
                <option value="0">永不过期</option>
              </select>
            </div>

            <div style="display: flex; gap: 1rem; justify-content: flex-end">
              <button
                type="button"
                class="btn btn-secondary"
                onclick="closeCreateModal()"
              >
                取消
              </button>
              <button type="submit" class="btn btn-primary">创建密钥</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <script>
      function openCreateModal() {
        document.getElementById("createModal").style.display = "block";
      }

      function closeCreateModal() {
        document.getElementById("createModal").style.display = "none";
      }

      function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
          // 创建一个临时的成功提示
          const toast = document.createElement("div");
          toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #10b981;
                    color: white;
                    padding: 12px 24px;
                    border-radius: 8px;
                    z-index: 10000;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                `;
          toast.textContent = "API密钥已复制到剪贴板";
          document.body.appendChild(toast);

          setTimeout(() => {
            document.body.removeChild(toast);
          }, 3000);
        });
      }

      // 点击模态框外部关闭
      window.onclick = function (event) {
        const modal = document.getElementById("createModal");
        if (event.target === modal) {
          modal.style.display = "none";
        }
      };
    </script>
  </body>
</html>
