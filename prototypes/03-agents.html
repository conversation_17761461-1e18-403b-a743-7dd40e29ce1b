<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能体管理 - 宠物医疗AI开放平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #334155;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav {
            display: flex;
            gap: 2rem;
        }

        .nav a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: background 0.2s;
        }

        .nav a:hover, .nav a.active {
            background: rgba(255,255,255,0.2);
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: bold;
            color: #1e293b;
        }

        .tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .tab {
            padding: 0.75rem 1.5rem;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 500;
            color: #64748b;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }

        .tab.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
        }

        .tab:hover {
            color: #3b82f6;
        }

        .agents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .agent-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .agent-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .agent-header {
            padding: 1.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            position: relative;
        }

        .agent-header.diagnosis {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .agent-header.report {
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }

        .agent-header.analysis {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
        }

        .agent-header.custom {
            background: linear-gradient(135deg, #43e97b, #38f9d7);
        }

        .agent-icon {
            width: 3rem;
            height: 3rem;
            background: rgba(255,255,255,0.2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .agent-title {
            font-size: 1.25rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .agent-description {
            opacity: 0.9;
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .agent-status {
            position: absolute;
            top: 1rem;
            right: 1rem;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-active {
            background: rgba(16, 185, 129, 0.2);
            color: #065f46;
        }

        .status-inactive {
            background: rgba(239, 68, 68, 0.2);
            color: #991b1b;
        }

        .agent-content {
            padding: 1.5rem;
        }

        .agent-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .metric {
            text-align: center;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #1e293b;
        }

        .metric-label {
            font-size: 0.75rem;
            color: #64748b;
            margin-top: 0.25rem;
        }

        .agent-features {
            margin-bottom: 1.5rem;
        }

        .features-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.75rem;
        }

        .feature-list {
            list-style: none;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            color: #64748b;
        }

        .feature-item i {
            color: #10b981;
            width: 1rem;
        }

        .agent-actions {
            display: flex;
            gap: 0.75rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border-radius: 6px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 0.875rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
            flex: 1;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #475569;
        }

        .btn-secondary:hover {
            background: #cbd5e1;
        }

        .create-agent {
            background: white;
            border: 2px dashed #cbd5e1;
            border-radius: 12px;
            padding: 3rem 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .create-agent:hover {
            border-color: #3b82f6;
            background: #f8fafc;
        }

        .create-icon {
            width: 4rem;
            height: 4rem;
            background: #f1f5f9;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: #64748b;
        }

        .create-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }

        .create-description {
            color: #64748b;
            font-size: 0.875rem;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: #64748b;
        }

        .empty-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .agents-grid {
                grid-template-columns: 1fr;
            }
            
            .agent-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-paw"></i>
                <span>宠物医疗AI开放平台</span>
            </div>
            <nav class="nav">
                <a href="01-dashboard.html">控制台</a>
                <a href="02-api-keys.html">API密钥</a>
                <a href="03-agents.html" class="active">智能体</a>
                <a href="04-docs.html">文档</a>
            </nav>
        </div>
    </header>

    <div class="container">
        <div class="page-header">
            <h1 class="page-title">AI智能体管理</h1>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="switchTab('system')">系统智能体</button>
            <button class="tab" onclick="switchTab('custom')">自定义智能体</button>
        </div>

        <!-- 系统智能体 -->
        <div id="system-tab" class="tab-content active">
            <div class="agents-grid">
                <div class="agent-card">
                    <div class="agent-header diagnosis">
                        <div class="agent-status status-active">运行中</div>
                        <div class="agent-icon">
                            <i class="fas fa-stethoscope"></i>
                        </div>
                        <div class="agent-title">AI问诊智能体</div>
                        <div class="agent-description">
                            基于大语言模型的智能问诊系统，能够根据宠物症状提供专业的诊断建议和治疗方案。
                        </div>
                    </div>
                    <div class="agent-content">
                        <div class="agent-metrics">
                            <div class="metric">
                                <div class="metric-value">8,234</div>
                                <div class="metric-label">今日调用</div>
                            </div>
                            <div class="metric">
                                <div class="metric-value">98.7%</div>
                                <div class="metric-label">成功率</div>
                            </div>
                        </div>
                        <div class="agent-features">
                            <div class="features-title">核心功能</div>
                            <ul class="feature-list">
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    症状分析与诊断建议
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    治疗方案推荐
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    紧急程度评估
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    流式输出支持
                                </li>
                            </ul>
                        </div>
                        <div class="agent-actions">
                            <button class="btn btn-primary">
                                <i class="fas fa-play"></i>
                                测试调用
                            </button>
                            <button class="btn btn-secondary">
                                <i class="fas fa-cog"></i>
                                配置
                            </button>
                        </div>
                    </div>
                </div>

                <div class="agent-card">
                    <div class="agent-header report">
                        <div class="agent-status status-active">运行中</div>
                        <div class="agent-icon">
                            <i class="fas fa-file-medical"></i>
                        </div>
                        <div class="agent-title">报告生成智能体</div>
                        <div class="agent-description">
                            自动生成专业的宠物医疗报告，支持多种报告类型，包括体检报告、诊断报告等。
                        </div>
                    </div>
                    <div class="agent-content">
                        <div class="agent-metrics">
                            <div class="metric">
                                <div class="metric-value">3,456</div>
                                <div class="metric-label">今日调用</div>
                            </div>
                            <div class="metric">
                                <div class="metric-value">99.2%</div>
                                <div class="metric-label">成功率</div>
                            </div>
                        </div>
                        <div class="agent-features">
                            <div class="features-title">核心功能</div>
                            <ul class="feature-list">
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    多种报告模板
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    数据自动填充
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    PDF格式导出
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    自定义模板支持
                                </li>
                            </ul>
                        </div>
                        <div class="agent-actions">
                            <button class="btn btn-primary">
                                <i class="fas fa-play"></i>
                                测试调用
                            </button>
                            <button class="btn btn-secondary">
                                <i class="fas fa-cog"></i>
                                配置
                            </button>
                        </div>
                    </div>
                </div>

                <div class="agent-card">
                    <div class="agent-header analysis">
                        <div class="agent-status status-active">运行中</div>
                        <div class="agent-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="agent-title">报告解读智能体</div>
                        <div class="agent-description">
                            智能解读医疗报告和检查结果，支持图像识别和OCR文字提取，提供专业解释。
                        </div>
                    </div>
                    <div class="agent-content">
                        <div class="agent-metrics">
                            <div class="metric">
                                <div class="metric-value">1,157</div>
                                <div class="metric-label">今日调用</div>
                            </div>
                            <div class="metric">
                                <div class="metric-value">97.8%</div>
                                <div class="metric-label">成功率</div>
                            </div>
                        </div>
                        <div class="agent-features">
                            <div class="features-title">核心功能</div>
                            <ul class="feature-list">
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    图像识别与OCR
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    报告内容解读
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    异常指标标注
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    建议措施推荐
                                </li>
                            </ul>
                        </div>
                        <div class="agent-actions">
                            <button class="btn btn-primary">
                                <i class="fas fa-play"></i>
                                测试调用
                            </button>
                            <button class="btn btn-secondary">
                                <i class="fas fa-cog"></i>
                                配置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 自定义智能体 -->
        <div id="custom-tab" class="tab-content">
            <div class="agents-grid">
                <div class="create-agent" onclick="createCustomAgent()">
                    <div class="create-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <div class="create-title">创建自定义智能体</div>
                    <div class="create-description">
                        使用自定义提示词和配置创建专属的AI智能体
                    </div>
                </div>

                <div class="agent-card">
                    <div class="agent-header custom">
                        <div class="agent-status status-active">运行中</div>
                        <div class="agent-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="agent-title">宠物营养顾问</div>
                        <div class="agent-description">
                            专门为宠物提供营养建议和饮食方案的自定义智能体。
                        </div>
                    </div>
                    <div class="agent-content">
                        <div class="agent-metrics">
                            <div class="metric">
                                <div class="metric-value">456</div>
                                <div class="metric-label">今日调用</div>
                            </div>
                            <div class="metric">
                                <div class="metric-value">96.5%</div>
                                <div class="metric-label">成功率</div>
                            </div>
                        </div>
                        <div class="agent-features">
                            <div class="features-title">自定义功能</div>
                            <ul class="feature-list">
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    营养成分分析
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    饮食方案制定
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    食物过敏检测
                                </li>
                            </ul>
                        </div>
                        <div class="agent-actions">
                            <button class="btn btn-primary">
                                <i class="fas fa-play"></i>
                                测试调用
                            </button>
                            <button class="btn btn-secondary">
                                <i class="fas fa-edit"></i>
                                编辑
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签的活跃状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 激活选中的标签
            event.target.classList.add('active');
        }

        function createCustomAgent() {
            alert('创建自定义智能体功能开发中...');
        }
    </script>
</body>
</html>
