<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>用户注册 - 宠物医疗AI开放平台</title>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #334155;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem;
      }

      .register-container {
        background: white;
        border-radius: 16px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        width: 100%;
        max-width: 450px;
      }

      .register-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        text-align: center;
      }

      .logo {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
      }

      .subtitle {
        opacity: 0.9;
        font-size: 0.875rem;
      }

      .register-form {
        padding: 2rem;
      }

      .form-group {
        margin-bottom: 1.5rem;
      }

      .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #374151;
      }

      .form-input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.2s, box-shadow 0.2s;
      }

      .form-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      .form-input.error {
        border-color: #ef4444;
      }

      .error-message {
        color: #ef4444;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: none;
      }

      .password-toggle {
        position: relative;
      }

      .password-toggle-btn {
        position: absolute;
        right: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #6b7280;
        cursor: pointer;
        padding: 0.25rem;
      }

      .password-toggle-btn:hover {
        color: #374151;
      }

      .password-strength {
        margin-top: 0.5rem;
        font-size: 0.75rem;
      }

      .strength-bar {
        height: 4px;
        background: #e5e7eb;
        border-radius: 2px;
        margin-top: 0.25rem;
        overflow: hidden;
      }

      .strength-fill {
        height: 100%;
        transition: width 0.3s, background-color 0.3s;
        width: 0%;
      }

      .strength-weak {
        background: #ef4444;
      }
      .strength-medium {
        background: #f59e0b;
      }
      .strength-strong {
        background: #10b981;
      }

      .terms-checkbox {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        margin-bottom: 1.5rem;
        font-size: 0.875rem;
      }

      .terms-checkbox input[type="checkbox"] {
        width: 1rem;
        height: 1rem;
        margin-top: 0.125rem;
      }

      .terms-text {
        line-height: 1.5;
        color: #6b7280;
      }

      .terms-text a {
        color: #667eea;
        text-decoration: none;
      }

      .terms-text a:hover {
        text-decoration: underline;
      }

      .register-btn {
        width: 100%;
        padding: 0.75rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: transform 0.2s, box-shadow 0.2s;
        margin-bottom: 1rem;
      }

      .register-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      }

      .register-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }

      .loading-spinner {
        display: none;
        width: 1rem;
        height: 1rem;
        border: 2px solid transparent;
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 0.5rem;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .login-link {
        text-align: center;
        padding-top: 1rem;
        border-top: 1px solid #e5e7eb;
        color: #6b7280;
        font-size: 0.875rem;
      }

      .login-link a {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;
      }

      .login-link a:hover {
        text-decoration: underline;
      }

      .alert {
        padding: 0.75rem 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        font-size: 0.875rem;
        display: none;
      }

      .alert-error {
        background: #fef2f2;
        color: #dc2626;
        border: 1px solid #fecaca;
      }

      .alert-success {
        background: #f0fdf4;
        color: #16a34a;
        border: 1px solid #bbf7d0;
      }

      .back-home {
        position: absolute;
        top: 2rem;
        left: 2rem;
        color: white;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        opacity: 0.9;
        transition: opacity 0.2s;
      }

      .back-home:hover {
        opacity: 1;
      }

      @media (max-width: 640px) {
        .register-container {
          margin: 1rem;
        }

        .back-home {
          position: static;
          margin-bottom: 2rem;
          justify-content: center;
        }
      }
    </style>
  </head>
  <body>
    <a href="index.html" class="back-home">
      <i class="fas fa-arrow-left"></i>
      返回首页
    </a>

    <div class="register-container">
      <div class="register-header">
        <div class="logo">
          <i class="fas fa-paw"></i>
          <span>宠物医疗AI平台</span>
        </div>
        <div class="subtitle">创建您的账户</div>
      </div>

      <div class="register-form">
        <div class="alert alert-error" id="errorAlert"></div>
        <div class="alert alert-success" id="successAlert"></div>

        <form id="registerForm">
          <div class="form-group">
            <label for="username" class="form-label">用户名</label>
            <input
              type="text"
              id="username"
              name="username"
              class="form-input"
              placeholder="请输入用户名"
              required
            />
            <div class="error-message" id="usernameError"></div>
          </div>

          <div class="form-group">
            <label for="email" class="form-label">邮箱地址</label>
            <input
              type="email"
              id="email"
              name="email"
              class="form-input"
              placeholder="请输入您的邮箱地址"
              required
            />
            <div class="error-message" id="emailError"></div>
          </div>

          <div class="form-group">
            <label for="password" class="form-label">密码</label>
            <div class="password-toggle">
              <input
                type="password"
                id="password"
                name="password"
                class="form-input"
                placeholder="请输入密码（至少8位）"
                required
              />
              <button
                type="button"
                class="password-toggle-btn"
                id="passwordToggle"
              >
                <i class="fas fa-eye"></i>
              </button>
            </div>
            <div class="password-strength">
              <div class="strength-bar">
                <div class="strength-fill" id="strengthFill"></div>
              </div>
              <div id="strengthText">密码强度：弱</div>
            </div>
            <div class="error-message" id="passwordError"></div>
          </div>

          <div class="form-group">
            <label for="confirmPassword" class="form-label">确认密码</label>
            <div class="password-toggle">
              <input
                type="password"
                id="confirmPassword"
                name="confirmPassword"
                class="form-input"
                placeholder="请再次输入密码"
                required
              />
              <button
                type="button"
                class="password-toggle-btn"
                id="confirmPasswordToggle"
              >
                <i class="fas fa-eye"></i>
              </button>
            </div>
            <div class="error-message" id="confirmPasswordError"></div>
          </div>

          <div class="terms-checkbox">
            <input type="checkbox" id="agreeTerms" name="agreeTerms" required />
            <div class="terms-text">
              我已阅读并同意 <a href="#" target="_blank">用户协议</a> 和
              <a href="#" target="_blank">隐私政策</a>
            </div>
          </div>

          <button type="submit" class="register-btn" id="registerBtn">
            <span class="loading-spinner" id="loadingSpinner"></span>
            <span id="registerBtnText">注册</span>
          </button>
        </form>

        <div class="login-link">
          已有账户？ <a href="auth-login.html">立即登录</a>
        </div>
      </div>
    </div>

    <script>
      // API配置
      const API_BASE_URL = "http://localhost:8000";

      // DOM元素
      const registerForm = document.getElementById("registerForm");
      const usernameInput = document.getElementById("username");
      const emailInput = document.getElementById("email");
      const passwordInput = document.getElementById("password");
      const confirmPasswordInput = document.getElementById("confirmPassword");
      const passwordToggle = document.getElementById("passwordToggle");
      const confirmPasswordToggle = document.getElementById(
        "confirmPasswordToggle"
      );
      const agreeTermsCheckbox = document.getElementById("agreeTerms");
      const registerBtn = document.getElementById("registerBtn");
      const registerBtnText = document.getElementById("registerBtnText");
      const loadingSpinner = document.getElementById("loadingSpinner");
      const errorAlert = document.getElementById("errorAlert");
      const successAlert = document.getElementById("successAlert");
      const strengthFill = document.getElementById("strengthFill");
      const strengthText = document.getElementById("strengthText");

      // 密码显示/隐藏切换
      passwordToggle.addEventListener("click", function () {
        togglePasswordVisibility(passwordInput, this);
      });

      confirmPasswordToggle.addEventListener("click", function () {
        togglePasswordVisibility(confirmPasswordInput, this);
      });

      function togglePasswordVisibility(input, button) {
        const type =
          input.getAttribute("type") === "password" ? "text" : "password";
        input.setAttribute("type", type);

        const icon = button.querySelector("i");
        icon.className =
          type === "password" ? "fas fa-eye" : "fas fa-eye-slash";
      }

      // 密码强度检测
      passwordInput.addEventListener("input", function () {
        const password = this.value;
        const strength = calculatePasswordStrength(password);
        updatePasswordStrength(strength);
      });

      function calculatePasswordStrength(password) {
        let score = 0;

        if (password.length >= 8) score += 1;
        if (password.length >= 12) score += 1;
        if (/[a-z]/.test(password)) score += 1;
        if (/[A-Z]/.test(password)) score += 1;
        if (/[0-9]/.test(password)) score += 1;
        if (/[^A-Za-z0-9]/.test(password)) score += 1;

        if (score <= 2) return "weak";
        if (score <= 4) return "medium";
        return "strong";
      }

      function updatePasswordStrength(strength) {
        const strengthMap = {
          weak: { width: "33%", class: "strength-weak", text: "密码强度：弱" },
          medium: {
            width: "66%",
            class: "strength-medium",
            text: "密码强度：中",
          },
          strong: {
            width: "100%",
            class: "strength-strong",
            text: "密码强度：强",
          },
        };

        const config = strengthMap[strength];
        strengthFill.style.width = config.width;
        strengthFill.className = `strength-fill ${config.class}`;
        strengthText.textContent = config.text;
      }

      // 表单验证
      function validateForm() {
        let isValid = true;

        // 清除之前的错误状态
        clearErrors();

        // 验证用户名
        const username = usernameInput.value.trim();
        if (!username) {
          showFieldError("username", "请输入用户名");
          isValid = false;
        } else if (username.length < 3) {
          showFieldError("username", "用户名长度至少3位");
          isValid = false;
        } else if (!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(username)) {
          showFieldError("username", "用户名只能包含字母、数字、下划线和中文");
          isValid = false;
        }

        // 验证邮箱
        const email = emailInput.value.trim();
        if (!email) {
          showFieldError("email", "请输入邮箱地址");
          isValid = false;
        } else if (!isValidEmail(email)) {
          showFieldError("email", "请输入有效的邮箱地址");
          isValid = false;
        }

        // 验证密码
        const password = passwordInput.value;
        if (!password) {
          showFieldError("password", "请输入密码");
          isValid = false;
        } else if (password.length < 8) {
          showFieldError("password", "密码长度至少8位");
          isValid = false;
        }

        // 验证确认密码
        const confirmPassword = confirmPasswordInput.value;
        if (!confirmPassword) {
          showFieldError("confirmPassword", "请确认密码");
          isValid = false;
        } else if (password !== confirmPassword) {
          showFieldError("confirmPassword", "两次输入的密码不一致");
          isValid = false;
        }

        // 验证协议同意
        if (!agreeTermsCheckbox.checked) {
          showAlert("请阅读并同意用户协议和隐私政策");
          isValid = false;
        }

        return isValid;
      }

      function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
      }

      function showFieldError(fieldName, message) {
        const input = document.getElementById(fieldName);
        const errorElement = document.getElementById(fieldName + "Error");

        input.classList.add("error");
        errorElement.textContent = message;
        errorElement.style.display = "block";
      }

      function clearErrors() {
        const inputs = document.querySelectorAll(".form-input");
        const errors = document.querySelectorAll(".error-message");

        inputs.forEach((input) => input.classList.remove("error"));
        errors.forEach((error) => {
          error.style.display = "none";
          error.textContent = "";
        });

        hideAlert();
      }

      function showAlert(message, type = "error") {
        const alertElement = type === "error" ? errorAlert : successAlert;
        const otherAlert = type === "error" ? successAlert : errorAlert;

        otherAlert.style.display = "none";
        alertElement.textContent = message;
        alertElement.style.display = "block";

        // 自动隐藏成功消息
        if (type === "success") {
          setTimeout(() => {
            alertElement.style.display = "none";
          }, 5000);
        }
      }

      function hideAlert() {
        errorAlert.style.display = "none";
        successAlert.style.display = "none";
      }

      function setLoading(loading) {
        registerBtn.disabled = loading;
        loadingSpinner.style.display = loading ? "inline-block" : "none";
        registerBtnText.textContent = loading ? "注册中..." : "注册";
      }

      // 注册处理（演示模式）
      async function handleRegister(formData) {
        try {
          // 模拟网络延迟
          await new Promise((resolve) => setTimeout(resolve, 1000));

          // 检查邮箱是否已存在（模拟）
          const existingEmails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
          ];
          if (existingEmails.includes(formData.email)) {
            throw new Error("该邮箱已被注册");
          }

          // 检查用户名是否已存在（模拟）
          const existingUsernames = ["管理员", "测试用户", "演示用户"];
          if (existingUsernames.includes(formData.username)) {
            throw new Error("该用户名已被使用");
          }

          // 注册成功
          showAlert("注册成功！请前往登录页面登录。", "success");

          // 延迟跳转到登录页面
          setTimeout(() => {
            window.location.href = "auth-login.html";
          }, 2000);
        } catch (error) {
          console.error("注册请求失败:", error);
          showAlert(error.message || "注册失败，请重试");
        }
      }

      // 表单提交事件
      registerForm.addEventListener("submit", async function (e) {
        e.preventDefault();

        if (!validateForm()) {
          return;
        }

        setLoading(true);

        const formData = {
          username: usernameInput.value.trim(),
          email: emailInput.value.trim(),
          password: passwordInput.value,
        };

        await handleRegister(formData);
        setLoading(false);
      });

      // 实时验证
      usernameInput.addEventListener("blur", function () {
        const username = this.value.trim();
        if (username && username.length < 3) {
          showFieldError("username", "用户名长度至少3位");
        } else if (username && !/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(username)) {
          showFieldError("username", "用户名只能包含字母、数字、下划线和中文");
        }
      });

      emailInput.addEventListener("blur", function () {
        const email = this.value.trim();
        if (email && !isValidEmail(email)) {
          showFieldError("email", "请输入有效的邮箱地址");
        }
      });

      confirmPasswordInput.addEventListener("blur", function () {
        const password = passwordInput.value;
        const confirmPassword = this.value;
        if (confirmPassword && password !== confirmPassword) {
          showFieldError("confirmPassword", "两次输入的密码不一致");
        }
      });

      // 清除错误状态当用户开始输入
      [usernameInput, emailInput, passwordInput, confirmPasswordInput].forEach(
        (input) => {
          input.addEventListener("input", function () {
            if (this.classList.contains("error")) {
              this.classList.remove("error");
              const errorId = this.id + "Error";
              document.getElementById(errorId).style.display = "none";
            }
          });
        }
      );
    </script>
  </body>
</html>
