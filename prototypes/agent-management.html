<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>智能体管理 - 宠物医疗AI开放平台</title>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: #f8fafc;
        color: #334155;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem 2rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .header-content {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .logo {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.5rem;
        font-weight: bold;
      }

      .nav {
        display: flex;
        gap: 2rem;
      }

      .nav a {
        color: white;
        text-decoration: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        transition: background 0.2s;
      }

      .nav a:hover,
      .nav a.active {
        background: rgba(255, 255, 255, 0.2);
      }

      .container {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 0 2rem;
      }

      .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
      }

      .page-title {
        font-size: 2rem;
        font-weight: bold;
        color: #1e293b;
      }

      .tabs {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 2rem;
        border-bottom: 1px solid #e5e7eb;
      }

      .tab {
        padding: 1rem 1.5rem;
        background: none;
        border: none;
        color: #6b7280;
        cursor: pointer;
        border-bottom: 2px solid transparent;
        transition: all 0.2s;
      }

      .tab.active {
        color: #3b82f6;
        border-bottom-color: #3b82f6;
      }

      .tab-content {
        display: none;
      }

      .tab-content.active {
        display: block;
      }

      .agents-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
      }

      .agent-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        transition: transform 0.2s, box-shadow 0.2s;
      }

      .agent-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .agent-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
      }

      .agent-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 12px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
      }

      .agent-info {
        flex: 1;
      }

      .agent-name {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.25rem;
      }

      .agent-type {
        font-size: 0.875rem;
        color: #6b7280;
      }

      .agent-description {
        color: #6b7280;
        font-size: 0.875rem;
        line-height: 1.5;
        margin-bottom: 1rem;
      }

      .agent-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
      }

      .status-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
      }

      .status-active {
        background: #dcfce7;
        color: #166534;
      }

      .status-inactive {
        background: #fef2f2;
        color: #991b1b;
      }

      .agent-stats {
        font-size: 0.875rem;
        color: #6b7280;
      }

      .agent-actions {
        display: flex;
        gap: 0.5rem;
      }

      .btn {
        padding: 0.5rem 1rem;
        border-radius: 6px;
        border: none;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
        font-size: 0.875rem;
      }

      .btn-primary {
        background: #3b82f6;
        color: white;
      }

      .btn-primary:hover {
        background: #2563eb;
      }

      .btn-secondary {
        background: #6b7280;
        color: white;
      }

      .btn-success {
        background: #10b981;
        color: white;
      }

      .btn-danger {
        background: #ef4444;
        color: white;
      }

      .system-agents {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
      }

      .system-agent-card {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        text-align: center;
      }

      .system-agent-icon {
        width: 4rem;
        height: 4rem;
        border-radius: 16px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        margin: 0 auto 1rem;
      }

      .system-agent-name {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.5rem;
      }

      .system-agent-description {
        color: #6b7280;
        font-size: 0.875rem;
        line-height: 1.5;
        margin-bottom: 1.5rem;
      }

      .execute-form {
        background: #f8fafc;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
        display: none;
      }

      .form-group {
        margin-bottom: 1rem;
      }

      .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #374151;
        font-size: 0.875rem;
      }

      .form-input {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #e5e7eb;
        border-radius: 6px;
        font-size: 0.875rem;
      }

      .form-textarea {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #e5e7eb;
        border-radius: 6px;
        font-size: 0.875rem;
        resize: vertical;
        min-height: 80px;
      }

      .alert {
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        display: none;
      }

      .alert-success {
        background: #f0fdf4;
        color: #166534;
        border: 1px solid #bbf7d0;
      }

      .alert-error {
        background: #fef2f2;
        color: #dc2626;
        border: 1px solid #fecaca;
      }

      .loading {
        text-align: center;
        padding: 2rem;
        color: #6b7280;
      }

      .loading-spinner {
        display: inline-block;
        width: 1.5rem;
        height: 1.5rem;
        border: 2px solid #e5e7eb;
        border-top: 2px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 0.5rem;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      @media (max-width: 768px) {
        .agents-grid {
          grid-template-columns: 1fr;
        }

        .system-agents {
          grid-template-columns: 1fr;
        }

        .page-header {
          flex-direction: column;
          gap: 1rem;
          align-items: stretch;
        }

        .tabs {
          overflow-x: auto;
        }
      }
    </style>
  </head>
  <body>
    <div class="header">
      <div class="header-content">
        <div class="logo">
          <i class="fas fa-paw"></i>
          <span>宠物医疗AI平台</span>
        </div>
        <nav class="nav">
          <a href="index.html">首页</a>
          <a href="user-management.html">用户管理</a>
          <a href="api-key-management.html">API密钥</a>
          <a href="agent-management.html" class="active">智能体</a>
          <a href="ocr-service.html">OCR服务</a>
          <a href="system-monitoring.html">系统监控</a>
        </nav>
        <div class="user-info">
          <span id="currentUserName">管理员</span>
          <button
            class="logout-btn"
            onclick="logout()"
            style="
              background: rgba(255, 255, 255, 0.2);
              border: none;
              color: white;
              padding: 0.5rem 1rem;
              border-radius: 6px;
              cursor: pointer;
            "
          >
            <i class="fas fa-sign-out-alt"></i>
            退出
          </button>
        </div>
      </div>
    </div>

    <div class="container">
      <div class="page-header">
        <h1 class="page-title">智能体管理</h1>
        <button class="btn btn-primary" onclick="showCreateAgentModal()">
          <i class="fas fa-plus"></i>
          创建智能体
        </button>
      </div>

      <div class="alert alert-success" id="successAlert"></div>
      <div class="alert alert-error" id="errorAlert"></div>

      <div class="tabs">
        <button class="tab active" onclick="switchTab('system')">
          系统智能体
        </button>
        <button class="tab" onclick="switchTab('custom')">自定义智能体</button>
      </div>

      <!-- 系统智能体 -->
      <div class="tab-content active" id="systemTab">
        <div class="system-agents">
          <div class="system-agent-card">
            <div class="system-agent-icon">
              <i class="fas fa-stethoscope"></i>
            </div>
            <div class="system-agent-name">AI问诊智能体</div>
            <div class="system-agent-description">
              基于专业兽医知识的AI问诊系统，能够根据宠物症状提供初步诊断建议和治疗方案。
            </div>
            <button
              class="btn btn-primary"
              onclick="toggleExecuteForm('diagnosis')"
            >
              <i class="fas fa-play"></i>
              执行问诊
            </button>
            <div class="execute-form" id="diagnosisForm">
              <div class="form-group">
                <label class="form-label">宠物信息</label>
                <input
                  type="text"
                  class="form-input"
                  placeholder="品种、年龄、性别等"
                  id="petInfo"
                />
              </div>
              <div class="form-group">
                <label class="form-label">症状描述</label>
                <textarea
                  class="form-textarea"
                  placeholder="详细描述宠物的症状..."
                  id="symptoms"
                ></textarea>
              </div>
              <button
                class="btn btn-success"
                onclick="executeSystemAgent('diagnosis')"
              >
                <i class="fas fa-robot"></i>
                开始问诊
              </button>
            </div>
          </div>

          <div class="system-agent-card">
            <div class="system-agent-icon">
              <i class="fas fa-eye"></i>
            </div>
            <div class="system-agent-name">AI视觉识别</div>
            <div class="system-agent-description">
              通过图像识别技术分析宠物照片，识别品种、健康状况、行为特征等信息。
            </div>
            <button
              class="btn btn-primary"
              onclick="toggleExecuteForm('vision')"
            >
              <i class="fas fa-play"></i>
              图像识别
            </button>
            <div class="execute-form" id="visionForm">
              <div class="form-group">
                <label class="form-label">上传图片</label>
                <input
                  type="file"
                  class="form-input"
                  accept="image/*"
                  id="imageFile"
                />
              </div>
              <div class="form-group">
                <label class="form-label">识别类型</label>
                <select class="form-input" id="recognitionType">
                  <option value="breed">品种识别</option>
                  <option value="health">健康检查</option>
                  <option value="behavior">行为分析</option>
                </select>
              </div>
              <button
                class="btn btn-success"
                onclick="executeSystemAgent('vision')"
              >
                <i class="fas fa-robot"></i>
                开始识别
              </button>
            </div>
          </div>

          <div class="system-agent-card">
            <div class="system-agent-icon">
              <i class="fas fa-file-medical"></i>
            </div>
            <div class="system-agent-name">报告生成智能体</div>
            <div class="system-agent-description">
              根据检查数据和诊断结果，自动生成专业的医疗报告和建议。
            </div>
            <button
              class="btn btn-primary"
              onclick="toggleExecuteForm('report')"
            >
              <i class="fas fa-play"></i>
              生成报告
            </button>
            <div class="execute-form" id="reportForm">
              <div class="form-group">
                <label class="form-label">检查数据</label>
                <textarea
                  class="form-textarea"
                  placeholder="输入检查数据..."
                  id="checkData"
                ></textarea>
              </div>
              <div class="form-group">
                <label class="form-label">报告类型</label>
                <select class="form-input" id="reportType">
                  <option value="diagnosis">诊断报告</option>
                  <option value="treatment">治疗方案</option>
                  <option value="follow-up">复查建议</option>
                </select>
              </div>
              <button
                class="btn btn-success"
                onclick="executeSystemAgent('report')"
              >
                <i class="fas fa-robot"></i>
                生成报告
              </button>
            </div>
          </div>

          <div class="system-agent-card">
            <div class="system-agent-icon">
              <i class="fas fa-search"></i>
            </div>
            <div class="system-agent-name">报告解读智能体</div>
            <div class="system-agent-description">
              解读医疗检查报告，将专业术语转换为易懂的说明，并提供相应建议。
            </div>
            <button
              class="btn btn-primary"
              onclick="toggleExecuteForm('analysis')"
            >
              <i class="fas fa-play"></i>
              解读报告
            </button>
            <div class="execute-form" id="analysisForm">
              <div class="form-group">
                <label class="form-label">医疗报告</label>
                <textarea
                  class="form-textarea"
                  placeholder="粘贴或输入医疗报告内容..."
                  id="reportContent"
                ></textarea>
              </div>
              <button
                class="btn btn-success"
                onclick="executeSystemAgent('analysis')"
              >
                <i class="fas fa-robot"></i>
                开始解读
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 自定义智能体 -->
      <div class="tab-content" id="customTab">
        <div id="customAgentsLoading" class="loading">
          <div class="loading-spinner"></div>
          <div>加载中...</div>
        </div>

        <div
          class="agents-grid"
          id="customAgentsContainer"
          style="display: none"
        >
          <!-- 自定义智能体将在这里动态生成 -->
        </div>
      </div>
    </div>

    <script>
      // API配置
      const API_BASE_URL = "http://localhost:8000";

      // 全局变量
      let customAgents = [];
      let currentTab = "system";

      // DOM元素
      const successAlert = document.getElementById("successAlert");
      const errorAlert = document.getElementById("errorAlert");
      const customAgentsContainer = document.getElementById(
        "customAgentsContainer"
      );
      const customAgentsLoading = document.getElementById(
        "customAgentsLoading"
      );

      // 页面加载时初始化
      document.addEventListener("DOMContentLoaded", function () {
        checkAuth();
        loadCustomAgents();
      });

      // 检查认证状态
      function checkAuth() {
        const token = localStorage.getItem("access_token");
        if (!token) {
          window.location.href = "auth-login.html";
          return;
        }

        const currentUser = localStorage.getItem("current_user") || "管理员";
        document.getElementById("currentUserName").textContent = currentUser;
      }

      // 切换标签页
      function switchTab(tab) {
        // 更新标签按钮状态
        document
          .querySelectorAll(".tab")
          .forEach((t) => t.classList.remove("active"));
        event.target.classList.add("active");

        // 更新内容显示
        document.querySelectorAll(".tab-content").forEach((content) => {
          content.classList.remove("active");
        });

        if (tab === "system") {
          document.getElementById("systemTab").classList.add("active");
        } else {
          document.getElementById("customTab").classList.add("active");
          if (customAgents.length === 0) {
            loadCustomAgents();
          }
        }

        currentTab = tab;
      }

      // 显示提示信息
      function showAlert(message, type = "success") {
        hideAlerts();
        const alertElement = type === "success" ? successAlert : errorAlert;
        alertElement.textContent = message;
        alertElement.style.display = "block";

        // 自动隐藏
        setTimeout(() => {
          alertElement.style.display = "none";
        }, 5000);
      }

      function hideAlerts() {
        successAlert.style.display = "none";
        errorAlert.style.display = "none";
      }

      // 切换执行表单显示
      function toggleExecuteForm(agentType) {
        const form = document.getElementById(agentType + "Form");
        const isVisible = form.style.display === "block";

        // 隐藏所有表单
        document.querySelectorAll(".execute-form").forEach((f) => {
          f.style.display = "none";
        });

        // 显示/隐藏目标表单
        form.style.display = isVisible ? "none" : "block";
      }

      // 执行系统智能体
      async function executeSystemAgent(agentType) {
        let requestData = {};

        try {
          // 根据智能体类型收集参数
          switch (agentType) {
            case "diagnosis":
              requestData = {
                pet_info: document.getElementById("petInfo").value,
                symptoms: document.getElementById("symptoms").value,
              };
              if (!requestData.symptoms) {
                throw new Error("请输入症状描述");
              }
              break;

            case "vision":
              const imageFile = document.getElementById("imageFile").files[0];
              if (!imageFile) {
                throw new Error("请选择图片文件");
              }
              // 这里应该上传图片并获取URL
              requestData = {
                image_url: "placeholder_image_url",
                recognition_type:
                  document.getElementById("recognitionType").value,
              };
              break;

            case "report":
              requestData = {
                check_data: document.getElementById("checkData").value,
                report_type: document.getElementById("reportType").value,
              };
              if (!requestData.check_data) {
                throw new Error("请输入检查数据");
              }
              break;

            case "analysis":
              requestData = {
                report_content: document.getElementById("reportContent").value,
              };
              if (!requestData.report_content) {
                throw new Error("请输入报告内容");
              }
              break;
          }

          // 模拟网络延迟
          await new Promise((resolve) => setTimeout(resolve, 2000));

          // 生成模拟响应
          let mockResult = generateMockAgentResult(agentType, requestData);

          showAlert("智能体执行成功");

          // 显示结果
          setTimeout(() => {
            alert(`执行结果：\n\n${mockResult}`);
          }, 500);
        } catch (error) {
          console.error("执行智能体失败:", error);
          showAlert("执行失败: " + error.message, "error");
        }
      }

      // 生成模拟智能体结果
      function generateMockAgentResult(agentType, requestData) {
        switch (agentType) {
          case "diagnosis":
            return `AI问诊结果：

宠物信息：${requestData.pet_info || "未提供"}
症状描述：${requestData.symptoms}

初步诊断：
根据您描述的症状，可能的原因包括：
1. 消化系统问题 - 建议调整饮食，观察2-3天
2. 轻微感冒 - 注意保暖，多休息
3. 应激反应 - 减少环境变化，提供安静环境

建议：
- 如症状持续或加重，请及时就医
- 保持充足饮水
- 观察食欲和精神状态变化

注意：此为AI初步分析，不能替代专业兽医诊断。`;

          case "vision":
            return `图像识别结果：

分析类型：${requestData.recognition_type}
图片文件：已上传

识别结果：
- 品种：金毛寻回犬（置信度：95%）
- 年龄估计：2-3岁成年犬
- 健康状况：整体良好
- 体型：标准体型，营养状况良好
- 毛发：光泽度良好，无明显皮肤问题

建议：
- 继续保持良好的护理习惯
- 定期梳理毛发
- 注意运动量和饮食平衡`;

          case "report":
            return `医疗报告生成：

检查数据：${requestData.check_data}
报告类型：${requestData.report_type}

生成的医疗报告：

【宠物医疗检查报告】

检查日期：${new Date().toLocaleDateString()}
检查项目：常规体检

检查结果：
- 体温：38.5°C（正常范围）
- 心率：120次/分钟（正常）
- 呼吸：24次/分钟（正常）
- 体重：25kg
- 精神状态：良好

诊断意见：
整体健康状况良好，各项指标正常。

治疗建议：
1. 继续保持规律饮食
2. 适量运动
3. 定期体检

复查建议：
建议6个月后进行下次常规体检。`;

          case "analysis":
            return `报告解读结果：

原始报告：${requestData.report_content.substring(0, 100)}...

通俗解读：

【简单说明】
您的宠物检查报告显示：

✅ 好消息：
- 主要健康指标都在正常范围内
- 没有发现明显的疾病征象
- 整体营养状况良好

⚠️ 需要注意：
- 某些指标接近临界值，需要观察
- 建议调整饮食结构
- 增加适量运动

📋 后续建议：
1. 保持现有的护理方式
2. 定期监测相关指标
3. 如有异常及时就医

【专业术语解释】
- 血常规：检查血液成分，评估整体健康
- 生化指标：反映内脏器官功能状态
- 尿检：评估泌尿系统和代谢状况`;

          default:
            return "智能体执行完成，结果正常。";
        }
      }

      // 加载自定义智能体（演示模式）
      async function loadCustomAgents() {
        try {
          customAgentsLoading.style.display = "block";
          customAgentsContainer.style.display = "none";

          // 模拟网络延迟
          await new Promise((resolve) => setTimeout(resolve, 500));

          // 获取或初始化演示数据
          let demoAgents = JSON.parse(
            localStorage.getItem("demo_custom_agents") || "[]"
          );

          if (demoAgents.length === 0) {
            // 初始化演示自定义智能体数据
            demoAgents = [
              {
                id: 1,
                name: "宠物营养顾问",
                description: "专业的宠物营养建议和饮食搭配指导",
                is_active: true,
                created_at: "2024-01-15T10:30:00Z",
                execution_count: 25,
                last_execution: "2024-01-20T14:25:00Z",
              },
              {
                id: 2,
                name: "行为训练师",
                description: "宠物行为分析和训练方案制定",
                is_active: true,
                created_at: "2024-01-16T09:15:00Z",
                execution_count: 18,
                last_execution: "2024-01-19T16:45:00Z",
              },
              {
                id: 3,
                name: "疫苗提醒助手",
                description: "疫苗接种时间提醒和健康档案管理",
                is_active: false,
                created_at: "2024-01-17T11:20:00Z",
                execution_count: 5,
                last_execution: "2024-01-18T13:30:00Z",
              },
            ];
            localStorage.setItem(
              "demo_custom_agents",
              JSON.stringify(demoAgents)
            );
          }

          customAgents = demoAgents;
          renderCustomAgents();
        } catch (error) {
          console.error("加载智能体列表失败:", error);
          showAlert("加载失败，请重试", "error");
          customAgents = [];
          renderCustomAgents();
        } finally {
          customAgentsLoading.style.display = "none";
          customAgentsContainer.style.display = "grid";
        }
      }

      // 渲染自定义智能体
      function renderCustomAgents() {
        if (customAgents.length === 0) {
          customAgentsContainer.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 3rem; color: #6b7280;">
                        <i class="fas fa-robot" style="font-size: 3rem; margin-bottom: 1rem; display: block;"></i>
                        <h3 style="margin-bottom: 0.5rem;">暂无自定义智能体</h3>
                        <p>点击"创建智能体"按钮来创建您的第一个智能体</p>
                    </div>
                `;
          return;
        }

        customAgentsContainer.innerHTML = customAgents
          .map(
            (agent) => `
                <div class="agent-card">
                    <div class="agent-header">
                        <div class="agent-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="agent-info">
                            <div class="agent-name">${escapeHtml(
                              agent.name
                            )}</div>
                            <div class="agent-type">自定义智能体</div>
                        </div>
                    </div>

                    <div class="agent-description">
                        ${escapeHtml(agent.description || "无描述")}
                    </div>

                    <div class="agent-meta">
                        <span class="status-badge ${
                          agent.is_active ? "status-active" : "status-inactive"
                        }">
                            ${agent.is_active ? "激活" : "未激活"}
                        </span>
                        <div class="agent-stats">
                            执行次数: ${agent.execution_count || 0}
                        </div>
                    </div>

                    <div class="agent-actions">
                        <button class="btn btn-primary" onclick="executeCustomAgent('${
                          agent.id
                        }')" ${!agent.is_active ? "disabled" : ""}>
                            <i class="fas fa-play"></i>
                            执行
                        </button>
                        <button class="btn btn-secondary" onclick="editAgent('${
                          agent.id
                        }')">
                            <i class="fas fa-edit"></i>
                            编辑
                        </button>
                        <button class="btn btn-danger" onclick="deleteAgent('${
                          agent.id
                        }', '${escapeHtml(agent.name)}')">
                            <i class="fas fa-trash"></i>
                            删除
                        </button>
                    </div>
                </div>
            `
          )
          .join("");
      }

      // HTML转义
      function escapeHtml(text) {
        const div = document.createElement("div");
        div.textContent = text;
        return div.innerHTML;
      }

      // 执行自定义智能体（演示模式）
      async function executeCustomAgent(agentId) {
        const input = prompt("请输入执行参数（可选）:");

        try {
          // 模拟网络延迟
          await new Promise((resolve) => setTimeout(resolve, 1500));

          // 查找智能体信息
          const agent = customAgents.find((a) => a.id == agentId);
          if (!agent) {
            throw new Error("智能体不存在");
          }

          if (!agent.is_active) {
            throw new Error("智能体已禁用");
          }

          // 生成模拟执行结果
          let mockResult = generateCustomAgentResult(agent, input);

          showAlert("智能体执行成功");

          // 显示结果
          setTimeout(() => {
            alert(`执行结果：\n\n${mockResult}`);
          }, 500);

          // 更新执行次数
          let demoAgents = JSON.parse(
            localStorage.getItem("demo_custom_agents") || "[]"
          );
          const agentIndex = demoAgents.findIndex((a) => a.id == agentId);
          if (agentIndex !== -1) {
            demoAgents[agentIndex].execution_count =
              (demoAgents[agentIndex].execution_count || 0) + 1;
            demoAgents[agentIndex].last_execution = new Date().toISOString();
            localStorage.setItem(
              "demo_custom_agents",
              JSON.stringify(demoAgents)
            );
          }

          // 刷新列表以更新执行次数
          loadCustomAgents();
        } catch (error) {
          console.error("执行智能体失败:", error);
          showAlert("执行失败: " + error.message, "error");
        }
      }

      // 生成自定义智能体模拟结果
      function generateCustomAgentResult(agent, input) {
        const inputText = input || "无特定输入";

        switch (agent.name) {
          case "宠物营养顾问":
            return `营养建议报告：

输入参数：${inputText}

专业营养建议：

🥘 饮食搭配建议：
- 优质蛋白质：鸡肉、鱼肉占比40%
- 碳水化合物：糙米、红薯占比30%
- 蔬菜纤维：胡萝卜、西兰花占比20%
- 健康脂肪：鱼油、亚麻籽油占比10%

📊 营养配比：
- 蛋白质：25-30%
- 脂肪：12-15%
- 纤维：3-5%
- 水分：充足供应

⚠️ 注意事项：
- 避免巧克力、洋葱等有毒食物
- 控制零食摄入量
- 根据年龄和活动量调整分量

建议：每日分2-3次喂食，定时定量。`;

          case "行为训练师":
            return `行为训练方案：

输入参数：${inputText}

训练计划：

🎯 训练目标：
- 基础服从性训练
- 社交行为改善
- 不良习惯纠正

📋 训练步骤：
1. 建立信任关系（1-2周）
2. 基础指令训练（2-4周）
3. 进阶技能培养（4-8周）
4. 巩固强化训练（持续）

🏆 训练技巧：
- 正向激励为主
- 及时奖励好行为
- 保持训练一致性
- 耐心和坚持

📅 训练计划：
- 每日训练15-20分钟
- 每周3-4次集中训练
- 结合日常生活场景

预期效果：4-6周内看到明显改善。`;

          case "疫苗提醒助手":
            return `疫苗接种提醒：

输入参数：${inputText}

疫苗计划：

💉 核心疫苗：
- 狂犬病疫苗：每年接种
- 犬瘟热疫苗：每年接种
- 细小病毒疫苗：每年接种
- 腺病毒疫苗：每年接种

📅 接种时间表：
- 幼犬：6-8周首次接种
- 成犬：每年定期接种
- 老年犬：根据健康状况调整

🔔 提醒设置：
- 接种前1个月提醒
- 接种前1周确认
- 接种后观察期提醒

📋 健康档案：
- 疫苗接种记录
- 健康检查记录
- 异常反应记录

下次接种时间：${new Date(
              Date.now() + 365 * 24 * 60 * 60 * 1000
            ).toLocaleDateString()}`;

          default:
            return `智能体执行完成：

输入参数：${inputText}

执行结果：
智能体 "${agent.name}" 已成功执行。

功能描述：${agent.description}

执行时间：${new Date().toLocaleString()}
执行状态：成功

如需更详细的功能，请联系管理员进行智能体配置。`;
        }
      }

      // 创建智能体
      function showCreateAgentModal() {
        showAlert("创建智能体功能开发中...", "error");
      }

      // 编辑智能体
      function editAgent(agentId) {
        showAlert("编辑智能体功能开发中...", "error");
      }

      // 删除智能体
      async function deleteAgent(agentId, agentName) {
        if (!confirm(`确定要删除智能体 "${agentName}" 吗？此操作不可恢复。`)) {
          return;
        }

        try {
          const token = localStorage.getItem("access_token");
          const response = await fetch(
            `${API_BASE_URL}/api/v1/agents/${agentId}`,
            {
              method: "DELETE",
              headers: {
                Authorization: `Bearer ${token}`,
                "Content-Type": "application/json",
              },
            }
          );

          const result = await response.json();

          if (response.ok && result.success) {
            showAlert("智能体删除成功");
            loadCustomAgents();
          } else {
            throw new Error(result.message || "删除智能体失败");
          }
        } catch (error) {
          console.error("删除智能体失败:", error);
          showAlert("删除智能体失败: " + error.message, "error");
        }
      }

      // 退出登录
      function logout() {
        if (confirm("确定要退出登录吗？")) {
          localStorage.removeItem("access_token");
          localStorage.removeItem("refresh_token");
          localStorage.removeItem("current_user");
          window.location.href = "auth-login.html";
        }
      }
    </script>
  </body>
</html>
