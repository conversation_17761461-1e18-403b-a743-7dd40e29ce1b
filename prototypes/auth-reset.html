<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>密码重置 - 宠物医疗AI开放平台</title>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #334155;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem;
      }

      .reset-container {
        background: white;
        border-radius: 16px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        width: 100%;
        max-width: 400px;
      }

      .reset-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        text-align: center;
      }

      .logo {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
      }

      .subtitle {
        opacity: 0.9;
        font-size: 0.875rem;
      }

      .reset-form {
        padding: 2rem;
      }

      .step-indicator {
        display: flex;
        justify-content: center;
        margin-bottom: 2rem;
      }

      .step {
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
        font-weight: 500;
        margin: 0 0.5rem;
        position: relative;
      }

      .step.active {
        background: #667eea;
        color: white;
      }

      .step.completed {
        background: #10b981;
        color: white;
      }

      .step.inactive {
        background: #e5e7eb;
        color: #6b7280;
      }

      .step:not(:last-child)::after {
        content: "";
        position: absolute;
        left: 100%;
        top: 50%;
        transform: translateY(-50%);
        width: 1rem;
        height: 2px;
        background: #e5e7eb;
      }

      .step.completed:not(:last-child)::after {
        background: #10b981;
      }

      .form-step {
        display: none;
      }

      .form-step.active {
        display: block;
      }

      .form-group {
        margin-bottom: 1.5rem;
      }

      .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #374151;
      }

      .form-input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.2s, box-shadow 0.2s;
      }

      .form-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      .form-input.error {
        border-color: #ef4444;
      }

      .error-message {
        color: #ef4444;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: none;
      }

      .form-description {
        color: #6b7280;
        font-size: 0.875rem;
        margin-bottom: 1.5rem;
        line-height: 1.5;
      }

      .action-btn {
        width: 100%;
        padding: 0.75rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: transform 0.2s, box-shadow 0.2s;
        margin-bottom: 1rem;
      }

      .action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      }

      .action-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }

      .loading-spinner {
        display: none;
        width: 1rem;
        height: 1rem;
        border: 2px solid transparent;
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 0.5rem;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .back-link {
        text-align: center;
        padding-top: 1rem;
        border-top: 1px solid #e5e7eb;
        color: #6b7280;
        font-size: 0.875rem;
      }

      .back-link a {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;
      }

      .back-link a:hover {
        text-decoration: underline;
      }

      .alert {
        padding: 0.75rem 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        font-size: 0.875rem;
        display: none;
      }

      .alert-error {
        background: #fef2f2;
        color: #dc2626;
        border: 1px solid #fecaca;
      }

      .alert-success {
        background: #f0fdf4;
        color: #16a34a;
        border: 1px solid #bbf7d0;
      }

      .alert-info {
        background: #eff6ff;
        color: #2563eb;
        border: 1px solid #bfdbfe;
      }

      .back-home {
        position: absolute;
        top: 2rem;
        left: 2rem;
        color: white;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        opacity: 0.9;
        transition: opacity 0.2s;
      }

      .back-home:hover {
        opacity: 1;
      }

      .verification-code-input {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
        margin-bottom: 1.5rem;
      }

      .code-digit {
        width: 3rem;
        height: 3rem;
        text-align: center;
        font-size: 1.25rem;
        font-weight: bold;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        transition: border-color 0.2s;
      }

      .code-digit:focus {
        outline: none;
        border-color: #667eea;
      }

      .resend-code {
        text-align: center;
        margin-bottom: 1.5rem;
      }

      .resend-btn {
        background: none;
        border: none;
        color: #667eea;
        cursor: pointer;
        font-size: 0.875rem;
        text-decoration: underline;
      }

      .resend-btn:disabled {
        color: #6b7280;
        cursor: not-allowed;
        text-decoration: none;
      }

      .countdown {
        color: #6b7280;
        font-size: 0.875rem;
      }

      @media (max-width: 640px) {
        .reset-container {
          margin: 1rem;
        }

        .back-home {
          position: static;
          margin-bottom: 2rem;
          justify-content: center;
        }
      }
    </style>
  </head>
  <body>
    <a href="index.html" class="back-home">
      <i class="fas fa-arrow-left"></i>
      返回首页
    </a>

    <div class="reset-container">
      <div class="reset-header">
        <div class="logo">
          <i class="fas fa-paw"></i>
          <span>宠物医疗AI平台</span>
        </div>
        <div class="subtitle">重置您的密码</div>
      </div>

      <div class="reset-form">
        <div class="step-indicator">
          <div class="step active" id="step1">1</div>
          <div class="step inactive" id="step2">2</div>
          <div class="step inactive" id="step3">3</div>
        </div>

        <div class="alert alert-error" id="errorAlert"></div>
        <div class="alert alert-success" id="successAlert"></div>
        <div class="alert alert-info" id="infoAlert"></div>

        <!-- 步骤1: 输入邮箱 -->
        <div class="form-step active" id="emailStep">
          <div class="form-description">
            请输入您注册时使用的邮箱地址，我们将向您发送验证码。
          </div>

          <form id="emailForm">
            <div class="form-group">
              <label for="email" class="form-label">邮箱地址</label>
              <input
                type="email"
                id="email"
                name="email"
                class="form-input"
                placeholder="请输入您的邮箱地址"
                required
              />
              <div class="error-message" id="emailError"></div>
            </div>

            <button type="submit" class="action-btn" id="sendCodeBtn">
              <span class="loading-spinner" id="sendCodeSpinner"></span>
              <span id="sendCodeText">发送验证码</span>
            </button>
          </form>
        </div>

        <!-- 步骤2: 验证码验证 -->
        <div class="form-step" id="verificationStep">
          <div class="form-description">
            我们已向您的邮箱发送了6位验证码，请在下方输入。
          </div>

          <form id="verificationForm">
            <div class="verification-code-input">
              <input type="text" class="code-digit" maxlength="1" id="code1" />
              <input type="text" class="code-digit" maxlength="1" id="code2" />
              <input type="text" class="code-digit" maxlength="1" id="code3" />
              <input type="text" class="code-digit" maxlength="1" id="code4" />
              <input type="text" class="code-digit" maxlength="1" id="code5" />
              <input type="text" class="code-digit" maxlength="1" id="code6" />
            </div>

            <div class="resend-code">
              <span class="countdown" id="countdown">60秒后可重新发送</span>
              <button type="button" class="resend-btn" id="resendBtn" disabled>
                重新发送验证码
              </button>
            </div>

            <button type="submit" class="action-btn" id="verifyCodeBtn">
              <span class="loading-spinner" id="verifyCodeSpinner"></span>
              <span id="verifyCodeText">验证</span>
            </button>
          </form>
        </div>

        <!-- 步骤3: 设置新密码 -->
        <div class="form-step" id="passwordStep">
          <div class="form-description">
            请设置您的新密码，密码长度至少8位，建议包含字母、数字和特殊字符。
          </div>

          <form id="passwordForm">
            <div class="form-group">
              <label for="newPassword" class="form-label">新密码</label>
              <input
                type="password"
                id="newPassword"
                name="newPassword"
                class="form-input"
                placeholder="请输入新密码（至少8位）"
                required
              />
              <div class="error-message" id="newPasswordError"></div>
            </div>

            <div class="form-group">
              <label for="confirmNewPassword" class="form-label"
                >确认新密码</label
              >
              <input
                type="password"
                id="confirmNewPassword"
                name="confirmNewPassword"
                class="form-input"
                placeholder="请再次输入新密码"
                required
              />
              <div class="error-message" id="confirmNewPasswordError"></div>
            </div>

            <button type="submit" class="action-btn" id="resetPasswordBtn">
              <span class="loading-spinner" id="resetPasswordSpinner"></span>
              <span id="resetPasswordText">重置密码</span>
            </button>
          </form>
        </div>

        <div class="back-link">
          <a href="auth-login.html">返回登录</a>
        </div>
      </div>
    </div>

    <script>
      // API配置
      const API_BASE_URL = "http://localhost:8000";

      // 当前步骤
      let currentStep = 1;
      let userEmail = "";
      let resetToken = "";
      let countdownTimer = null;

      // DOM元素
      const steps = {
        1: document.getElementById("emailStep"),
        2: document.getElementById("verificationStep"),
        3: document.getElementById("passwordStep"),
      };

      const stepIndicators = {
        1: document.getElementById("step1"),
        2: document.getElementById("step2"),
        3: document.getElementById("step3"),
      };

      const alerts = {
        error: document.getElementById("errorAlert"),
        success: document.getElementById("successAlert"),
        info: document.getElementById("infoAlert"),
      };

      // 步骤1元素
      const emailForm = document.getElementById("emailForm");
      const emailInput = document.getElementById("email");
      const sendCodeBtn = document.getElementById("sendCodeBtn");
      const sendCodeSpinner = document.getElementById("sendCodeSpinner");
      const sendCodeText = document.getElementById("sendCodeText");

      // 步骤2元素
      const verificationForm = document.getElementById("verificationForm");
      const codeInputs = [
        document.getElementById("code1"),
        document.getElementById("code2"),
        document.getElementById("code3"),
        document.getElementById("code4"),
        document.getElementById("code5"),
        document.getElementById("code6"),
      ];
      const verifyCodeBtn = document.getElementById("verifyCodeBtn");
      const verifyCodeSpinner = document.getElementById("verifyCodeSpinner");
      const verifyCodeText = document.getElementById("verifyCodeText");
      const resendBtn = document.getElementById("resendBtn");
      const countdown = document.getElementById("countdown");

      // 步骤3元素
      const passwordForm = document.getElementById("passwordForm");
      const newPasswordInput = document.getElementById("newPassword");
      const confirmNewPasswordInput =
        document.getElementById("confirmNewPassword");
      const resetPasswordBtn = document.getElementById("resetPasswordBtn");
      const resetPasswordSpinner = document.getElementById(
        "resetPasswordSpinner"
      );
      const resetPasswordText = document.getElementById("resetPasswordText");

      // 步骤切换
      function goToStep(step) {
        // 隐藏所有步骤
        Object.values(steps).forEach((stepElement) => {
          stepElement.classList.remove("active");
        });

        // 显示目标步骤
        steps[step].classList.add("active");

        // 更新步骤指示器
        Object.keys(stepIndicators).forEach((stepNum) => {
          const indicator = stepIndicators[stepNum];
          const num = parseInt(stepNum);

          if (num < step) {
            indicator.className = "step completed";
          } else if (num === step) {
            indicator.className = "step active";
          } else {
            indicator.className = "step inactive";
          }
        });

        currentStep = step;
        hideAlert();
      }

      // 通用函数
      function showAlert(message, type = "error") {
        hideAlert();
        const alertElement = alerts[type];
        if (alertElement) {
          alertElement.textContent = message;
          alertElement.style.display = "block";

          // 自动隐藏成功和信息消息
          if (type === "success" || type === "info") {
            setTimeout(() => {
              alertElement.style.display = "none";
            }, 5000);
          }
        }
      }

      function hideAlert() {
        Object.values(alerts).forEach((alert) => {
          alert.style.display = "none";
        });
      }

      function showFieldError(fieldName, message) {
        const input = document.getElementById(fieldName);
        const errorElement = document.getElementById(fieldName + "Error");

        if (input && errorElement) {
          input.classList.add("error");
          errorElement.textContent = message;
          errorElement.style.display = "block";
        }
      }

      function clearFieldError(fieldName) {
        const input = document.getElementById(fieldName);
        const errorElement = document.getElementById(fieldName + "Error");

        if (input && errorElement) {
          input.classList.remove("error");
          errorElement.style.display = "none";
        }
      }

      function setLoading(
        button,
        spinner,
        textElement,
        loading,
        loadingText,
        normalText
      ) {
        button.disabled = loading;
        spinner.style.display = loading ? "inline-block" : "none";
        textElement.textContent = loading ? loadingText : normalText;
      }

      function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
      }

      // 步骤1: 发送验证码
      emailForm.addEventListener("submit", async function (e) {
        e.preventDefault();

        const email = emailInput.value.trim();

        // 验证邮箱
        if (!email) {
          showFieldError("email", "请输入邮箱地址");
          return;
        }

        if (!isValidEmail(email)) {
          showFieldError("email", "请输入有效的邮箱地址");
          return;
        }

        clearFieldError("email");
        setLoading(
          sendCodeBtn,
          sendCodeSpinner,
          sendCodeText,
          true,
          "发送中...",
          "发送验证码"
        );

        try {
          // 模拟网络延迟
          await new Promise((resolve) => setTimeout(resolve, 1500));

          // 检查邮箱是否存在（演示模式）
          const validEmails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
          ];
          if (!validEmails.includes(email)) {
            throw new Error("该邮箱未注册");
          }

          userEmail = email;
          showAlert(
            "验证码已发送到您的邮箱，请查收（演示码：123456）",
            "success"
          );

          setTimeout(() => {
            goToStep(2);
            startCountdown();
          }, 1500);
        } catch (error) {
          console.error("发送验证码失败:", error);
          showAlert(error.message || "发送验证码失败，请稍后重试");
        }

        setLoading(
          sendCodeBtn,
          sendCodeSpinner,
          sendCodeText,
          false,
          "发送中...",
          "发送验证码"
        );
      });

      // 验证码输入处理
      codeInputs.forEach((input, index) => {
        input.addEventListener("input", function (e) {
          const value = e.target.value;

          // 只允许数字
          if (!/^\d*$/.test(value)) {
            e.target.value = "";
            return;
          }

          // 自动跳转到下一个输入框
          if (value && index < codeInputs.length - 1) {
            codeInputs[index + 1].focus();
          }
        });

        input.addEventListener("keydown", function (e) {
          // 退格键处理
          if (e.key === "Backspace" && !e.target.value && index > 0) {
            codeInputs[index - 1].focus();
          }
        });
      });

      // 倒计时
      function startCountdown() {
        let seconds = 60;
        resendBtn.disabled = true;

        countdownTimer = setInterval(() => {
          seconds--;
          countdown.textContent = `${seconds}秒后可重新发送`;

          if (seconds <= 0) {
            clearInterval(countdownTimer);
            countdown.style.display = "none";
            resendBtn.disabled = false;
            resendBtn.style.display = "inline";
          }
        }, 1000);
      }

      // 重新发送验证码
      resendBtn.addEventListener("click", async function () {
        setLoading(this, null, this, true, "发送中...", "重新发送验证码");

        try {
          // 模拟API调用
          await new Promise((resolve) => setTimeout(resolve, 1500));

          showAlert("验证码已重新发送", "info");
          countdown.style.display = "inline";
          resendBtn.style.display = "none";
          startCountdown();
        } catch (error) {
          showAlert("重新发送失败，请稍后重试");
        }

        setLoading(this, null, this, false, "发送中...", "重新发送验证码");
      });

      // 步骤2: 验证码验证
      verificationForm.addEventListener("submit", async function (e) {
        e.preventDefault();

        const code = codeInputs.map((input) => input.value).join("");

        if (code.length !== 6) {
          showAlert("请输入完整的6位验证码");
          return;
        }

        setLoading(
          verifyCodeBtn,
          verifyCodeSpinner,
          verifyCodeText,
          true,
          "验证中...",
          "验证"
        );

        try {
          // 模拟网络延迟
          await new Promise((resolve) => setTimeout(resolve, 1500));

          // 检查验证码（演示模式）
          if (code !== "123456") {
            throw new Error("验证码错误");
          }

          // 验证成功
          resetToken = "mock_reset_token_" + Date.now();
          showAlert("验证码验证成功", "success");

          setTimeout(() => {
            goToStep(3);
          }, 1500);
        } catch (error) {
          showAlert(error.message || "验证码错误或已过期，请重新输入");
        }

        setLoading(
          verifyCodeBtn,
          verifyCodeSpinner,
          verifyCodeText,
          false,
          "验证中...",
          "验证"
        );
      });

      // 步骤3: 重置密码
      passwordForm.addEventListener("submit", async function (e) {
        e.preventDefault();

        const newPassword = newPasswordInput.value;
        const confirmPassword = confirmNewPasswordInput.value;

        // 验证密码
        if (!newPassword) {
          showFieldError("newPassword", "请输入新密码");
          return;
        }

        if (newPassword.length < 8) {
          showFieldError("newPassword", "密码长度至少8位");
          return;
        }

        if (!confirmPassword) {
          showFieldError("confirmNewPassword", "请确认新密码");
          return;
        }

        if (newPassword !== confirmPassword) {
          showFieldError("confirmNewPassword", "两次输入的密码不一致");
          return;
        }

        clearFieldError("newPassword");
        clearFieldError("confirmNewPassword");

        setLoading(
          resetPasswordBtn,
          resetPasswordSpinner,
          resetPasswordText,
          true,
          "重置中...",
          "重置密码"
        );

        try {
          // 模拟API调用
          await new Promise((resolve) => setTimeout(resolve, 2000));

          showAlert("密码重置成功！正在跳转到登录页面...", "success");

          setTimeout(() => {
            window.location.href = "auth-login.html";
          }, 2000);
        } catch (error) {
          showAlert("密码重置失败，请稍后重试");
        }

        setLoading(
          resetPasswordBtn,
          resetPasswordSpinner,
          resetPasswordText,
          false,
          "重置中...",
          "重置密码"
        );
      });

      // 输入框错误清除
      emailInput.addEventListener("input", () => clearFieldError("email"));
      newPasswordInput.addEventListener("input", () =>
        clearFieldError("newPassword")
      );
      confirmNewPasswordInput.addEventListener("input", () =>
        clearFieldError("confirmNewPassword")
      );

      // 页面卸载时清理定时器
      window.addEventListener("beforeunload", function () {
        if (countdownTimer) {
          clearInterval(countdownTimer);
        }
      });
    </script>
  </body>
</html>
