<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>宠物医疗AI开放平台 - 产品原型</title>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #334155;
        min-height: 100vh;
        padding: 2rem;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
      }

      .header {
        text-align: center;
        margin-bottom: 3rem;
        color: white;
      }

      .logo {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 1rem;
      }

      .subtitle {
        font-size: 1.25rem;
        opacity: 0.9;
        margin-bottom: 0.5rem;
      }

      .description {
        font-size: 1rem;
        opacity: 0.8;
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
      }

      .pages-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
      }

      .page-card {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s, box-shadow 0.3s;
        text-decoration: none;
        color: inherit;
      }

      .page-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
      }

      .page-icon {
        width: 4rem;
        height: 4rem;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1.5rem;
      }

      .page-icon.dashboard {
        background: linear-gradient(135deg, #667eea, #764ba2);
      }

      .page-icon.api-keys {
        background: linear-gradient(135deg, #f093fb, #f5576c);
      }

      .page-icon.agents {
        background: linear-gradient(135deg, #4facfe, #00f2fe);
      }

      .page-icon.docs {
        background: linear-gradient(135deg, #43e97b, #38f9d7);
      }

      .page-icon.users {
        background: linear-gradient(135deg, #fa709a, #fee140);
      }

      .page-icon.pets {
        background: linear-gradient(135deg, #a8edea, #fed6e3);
      }

      .page-icon.apps {
        background: linear-gradient(135deg, #ff9a9e, #fecfef);
      }

      .page-icon.billing {
        background: linear-gradient(135deg, #ffecd2, #fcb69f);
      }

      .page-icon.community {
        background: linear-gradient(135deg, #a18cd1, #fbc2eb);
      }

      .page-icon.analytics {
        background: linear-gradient(135deg, #fad0c4, #ffd1ff);
      }

      .page-title {
        font-size: 1.25rem;
        font-weight: bold;
        color: #1e293b;
        margin-bottom: 0.75rem;
      }

      .page-description {
        color: #64748b;
        font-size: 0.875rem;
        line-height: 1.5;
        margin-bottom: 1rem;
      }

      .page-features {
        list-style: none;
      }

      .feature-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        color: #64748b;
      }

      .feature-item i {
        color: #10b981;
        width: 1rem;
      }

      .status-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
        margin-top: 1rem;
      }

      .status-completed {
        background: #dcfce7;
        color: #166534;
      }

      .status-wip {
        background: #fef3c7;
        color: #92400e;
      }

      .status-planned {
        background: #e2e8f0;
        color: #475569;
      }

      .footer {
        text-align: center;
        margin-top: 4rem;
        color: white;
        opacity: 0.8;
      }

      .tech-stack {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 1.5rem;
        margin-top: 2rem;
        color: white;
      }

      .tech-title {
        font-size: 1.125rem;
        font-weight: 600;
        margin-bottom: 1rem;
        text-align: center;
      }

      .tech-items {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
        justify-content: center;
      }

      .tech-item {
        background: rgba(255, 255, 255, 0.2);
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
      }

      @media (max-width: 768px) {
        .pages-grid {
          grid-template-columns: 1fr;
        }

        .container {
          padding: 0 1rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <div class="logo">
          <i class="fas fa-paw"></i>
          <span>宠物医疗AI开放平台</span>
        </div>
        <div class="subtitle">产品原型展示</div>
        <div class="description">
          基于现代Web技术构建的宠物医疗AI开放平台，提供完整的API服务、智能体管理、
          用户系统和开发者工具。以下是各个功能模块的原型页面展示。
        </div>
      </div>

      <div class="pages-grid">
        <a href="auth-login.html" class="page-card">
          <div class="page-icon dashboard">
            <i class="fas fa-sign-in-alt"></i>
          </div>
          <div class="page-title">用户认证系统</div>
          <div class="page-description">
            完整的用户登录、注册、密码重置功能，支持JWT认证和记住我功能。
          </div>
          <ul class="page-features">
            <li class="feature-item">
              <i class="fas fa-check"></i>
              实时API调用统计
            </li>
            <li class="feature-item">
              <i class="fas fa-check"></i>
              服务状态监控
            </li>
            <li class="feature-item">
              <i class="fas fa-check"></i>
              配额使用情况
            </li>
            <li class="feature-item">
              <i class="fas fa-check"></i>
              交互式图表展示
            </li>
          </ul>
          <span class="status-badge status-completed">已完成</span>
        </a>

        <a href="api-key-management.html" class="page-card">
          <div class="page-icon api-keys">
            <i class="fas fa-key"></i>
          </div>
          <div class="page-title">API密钥管理</div>
          <div class="page-description">
            创建、管理和配置API密钥，设置权限和使用限制，确保API访问安全。
          </div>
          <ul class="page-features">
            <li class="feature-item">
              <i class="fas fa-check"></i>
              密钥创建和删除
            </li>
            <li class="feature-item">
              <i class="fas fa-check"></i>
              权限精细控制
            </li>
            <li class="feature-item">
              <i class="fas fa-check"></i>
              使用统计监控
            </li>
            <li class="feature-item">
              <i class="fas fa-check"></i>
              安全复制功能
            </li>
          </ul>
          <span class="status-badge status-completed">已完成</span>
        </a>

        <a href="03-agents.html" class="page-card">
          <div class="page-icon agents">
            <i class="fas fa-robot"></i>
          </div>
          <div class="page-title">AI智能体管理</div>
          <div class="page-description">
            管理系统级智能体和用户自定义智能体，包括AI问诊、报告生成等功能。
          </div>
          <ul class="page-features">
            <li class="feature-item">
              <i class="fas fa-check"></i>
              系统智能体监控
            </li>
            <li class="feature-item">
              <i class="fas fa-check"></i>
              自定义智能体创建
            </li>
            <li class="feature-item">
              <i class="fas fa-check"></i>
              性能指标展示
            </li>
            <li class="feature-item">
              <i class="fas fa-check"></i>
              在线测试功能
            </li>
          </ul>
          <span class="status-badge status-completed">已完成</span>
        </a>

        <a href="04-docs.html" class="page-card">
          <div class="page-icon docs">
            <i class="fas fa-book"></i>
          </div>
          <div class="page-title">API文档</div>
          <div class="page-description">
            交互式API文档，包含详细的接口说明、示例代码和在线测试工具。
          </div>
          <ul class="page-features">
            <li class="feature-item">
              <i class="fas fa-check"></i>
              交互式文档界面
            </li>
            <li class="feature-item">
              <i class="fas fa-check"></i>
              多语言代码示例
            </li>
            <li class="feature-item">
              <i class="fas fa-check"></i>
              在线API测试
            </li>
            <li class="feature-item">
              <i class="fas fa-check"></i>
              OpenAI兼容接口
            </li>
          </ul>
          <span class="status-badge status-completed">已完成</span>
        </a>

        <a href="05-users.html" class="page-card">
          <div class="page-icon users">
            <i class="fas fa-users"></i>
          </div>
          <div class="page-title">用户管理</div>
          <div class="page-description">
            用户注册、认证、权限管理和使用情况监控的完整用户管理系统。
          </div>
          <ul class="page-features">
            <li class="feature-item">
              <i class="fas fa-check"></i>
              用户信息管理
            </li>
            <li class="feature-item">
              <i class="fas fa-check"></i>
              角色权限控制
            </li>
            <li class="feature-item">
              <i class="fas fa-check"></i>
              使用统计分析
            </li>
            <li class="feature-item">
              <i class="fas fa-check"></i>
              批量操作功能
            </li>
          </ul>
          <span class="status-badge status-completed">已完成</span>
        </a>

        <a href="06-pets.html" class="page-card">
          <div class="page-icon pets">
            <i class="fas fa-heart"></i>
          </div>
          <div class="page-title">宠物信息管理</div>
          <div class="page-description">
            宠物档案管理、医疗记录追踪和健康状态监控的专业宠物管理系统。
          </div>
          <ul class="page-features">
            <li class="feature-item">
              <i class="fas fa-check"></i>
              宠物档案管理
            </li>
            <li class="feature-item">
              <i class="fas fa-check"></i>
              医疗记录追踪
            </li>
            <li class="feature-item">
              <i class="fas fa-check"></i>
              健康状态监控
            </li>
            <li class="feature-item">
              <i class="fas fa-check"></i>
              疫苗记录管理
            </li>
          </ul>
          <span class="status-badge status-completed">已完成</span>
        </a>

        <div class="page-card" style="opacity: 0.7">
          <div class="page-icon apps">
            <i class="fas fa-mobile-alt"></i>
          </div>
          <div class="page-title">应用管理</div>
          <div class="page-description">
            第三方应用注册、审核和管理，支持OAuth认证和应用商店功能。
          </div>
          <ul class="page-features">
            <li class="feature-item">
              <i class="fas fa-clock"></i>
              应用注册审核
            </li>
            <li class="feature-item">
              <i class="fas fa-clock"></i>
              OAuth集成
            </li>
            <li class="feature-item">
              <i class="fas fa-clock"></i>
              应用商店
            </li>
            <li class="feature-item">
              <i class="fas fa-clock"></i>
              使用统计
            </li>
          </ul>
          <span class="status-badge status-planned">规划中</span>
        </div>

        <div class="page-card" style="opacity: 0.7">
          <div class="page-icon billing">
            <i class="fas fa-credit-card"></i>
          </div>
          <div class="page-title">计费和配额</div>
          <div class="page-description">
            API调用计费、套餐管理、使用量监控和自动扣费的完整计费系统。
          </div>
          <ul class="page-features">
            <li class="feature-item">
              <i class="fas fa-clock"></i>
              套餐管理
            </li>
            <li class="feature-item">
              <i class="fas fa-clock"></i>
              使用量监控
            </li>
            <li class="feature-item">
              <i class="fas fa-clock"></i>
              自动计费
            </li>
            <li class="feature-item">
              <i class="fas fa-clock"></i>
              发票管理
            </li>
          </ul>
          <span class="status-badge status-planned">规划中</span>
        </div>

        <div class="page-card" style="opacity: 0.7">
          <div class="page-icon community">
            <i class="fas fa-comments"></i>
          </div>
          <div class="page-title">开发者社区</div>
          <div class="page-description">
            开发者论坛、技术文档、示例代码和技术支持的社区平台。
          </div>
          <ul class="page-features">
            <li class="feature-item">
              <i class="fas fa-clock"></i>
              开发者论坛
            </li>
            <li class="feature-item">
              <i class="fas fa-clock"></i>
              示例代码库
            </li>
            <li class="feature-item">
              <i class="fas fa-clock"></i>
              技术支持
            </li>
            <li class="feature-item">
              <i class="fas fa-clock"></i>
              FAQ系统
            </li>
          </ul>
          <span class="status-badge status-planned">规划中</span>
        </div>

        <div class="page-card" style="opacity: 0.7">
          <div class="page-icon analytics">
            <i class="fas fa-chart-bar"></i>
          </div>
          <div class="page-title">监控和分析</div>
          <div class="page-description">
            API调用分析、错误监控、性能指标和业务数据分析的综合监控平台。
          </div>
          <ul class="page-features">
            <li class="feature-item">
              <i class="fas fa-clock"></i>
              实时监控
            </li>
            <li class="feature-item">
              <i class="fas fa-clock"></i>
              错误追踪
            </li>
            <li class="feature-item">
              <i class="fas fa-clock"></i>
              性能分析
            </li>
            <li class="feature-item">
              <i class="fas fa-clock"></i>
              业务报表
            </li>
          </ul>
          <span class="status-badge status-planned">规划中</span>
        </div>
      </div>

      <div class="tech-stack">
        <div class="tech-title">技术栈</div>
        <div class="tech-items">
          <span class="tech-item">FastAPI</span>
          <span class="tech-item">SQLAlchemy</span>
          <span class="tech-item">PostgreSQL</span>
          <span class="tech-item">Redis</span>
          <span class="tech-item">Docker</span>
          <span class="tech-item">OpenAI API</span>
          <span class="tech-item">JWT认证</span>
          <span class="tech-item">微服务架构</span>
        </div>
      </div>

      <div class="footer">
        <p>&copy; 2024 宠物医疗AI开放平台 - 产品原型展示</p>
        <p style="margin-top: 0.5rem; font-size: 0.875rem">
          基于现代Web技术构建的专业宠物医疗AI服务平台
        </p>
      </div>
    </div>
  </body>
</html>
