services:
  # 使用外部PostgreSQL数据库和外部Redis服务
  # 确保外部Redis正在运行: brew services start redis

  # User Service
  user_service:
    build:
      context: .
      dockerfile: services/user_service/Dockerfile
    container_name: vet_user_service
    ports:
      - "8001:8001"
    environment:
      - DATABASE_URL=*******************************************************/vet_platform
      - REDIS_URL=redis://host.docker.internal:6379/2
      - SECRET_KEY=vet-platform-unified-secret-key-for-all-services-2025
      - DEBUG=true
    depends_on: []
    networks:
      - vet_network
    volumes:
      - ./logs:/app/logs
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Pet Service
  pet_service:
    build:
      context: .
      dockerfile: services/pet_service/Dockerfile
    container_name: vet_pet_service
    ports:
      - "8002:8002"
    environment:
      - DATABASE_URL=*******************************************************/vet_platform
      - REDIS_URL=redis://host.docker.internal:6379/2
      - SECRET_KEY=vet-platform-unified-secret-key-for-all-services-2025
      - DEBUG=true
    depends_on: []
    networks:
      - vet_network
    volumes:
      - ./logs:/app/logs
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # App Service
  app_service:
    build:
      context: .
      dockerfile: services/app_service/Dockerfile
    container_name: vet_app_service
    ports:
      - "8003:8003"
    environment:
      - DATABASE_URL=*******************************************************/vet_platform
      - REDIS_URL=redis://host.docker.internal:6379/2
      - USER_SERVICE_URL=http://user_service:8001
      - PET_SERVICE_URL=http://pet_service:8002
      - SECRET_KEY=vet-platform-unified-secret-key-for-all-services-2025
      - JWT_SECRET_KEY=vet-platform-unified-secret-key-for-all-services-2025
      - DEBUG=true
    depends_on: []
    networks:
      - vet_network
    volumes:
      - ./logs:/app/logs
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # OCR服务 - 图片OCR识别服务
  ocr_service:
    build:
      context: .
      dockerfile: services/ocr_service/Dockerfile
    container_name: vet_ocr_service
    ports:
      - "8004:8004"
    environment:
      - USER_SERVICE_URL=http://user_service:8001
      - SECRET_KEY=vet-platform-unified-secret-key-for-all-services-2025
      - VL_QWEN_API_KEY=${VL_QWEN_API_KEY}
      - VL_QWEN_MODEL_NAME=${VL_QWEN_MODEL_NAME:-qwen-vl-plus}
      - VL_QWEN_BASE_URL=${VL_QWEN_BASE_URL:-https://dashscope.aliyuncs.com/compatible-mode/v1}
      - DEBUG=true
    networks:
      - vet_network
    volumes:
      - ./logs:/app/logs
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # API Gateway - 最后启动，依赖所有业务服务
  gateway:
    build:
      context: .
      dockerfile: gateway/Dockerfile
    container_name: vet_gateway
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=*******************************************************/vet_platform
      - REDIS_URL=redis://host.docker.internal:6379/2
      - USER_SERVICE_URL=http://user_service:8001
      - PET_SERVICE_URL=http://pet_service:8002
      - APP_SERVICE_URL=http://app_service:8003
      - OCR_SERVICE_URL=http://ocr_service:8004
      - SECRET_KEY=vet-platform-unified-secret-key-for-all-services-2025
      - REQUEST_TIMEOUT=120
      - AI_REQUEST_TIMEOUT=180
      - DEBUG=true
    depends_on:
      - user_service
      - pet_service
      - app_service
      - ocr_service
    networks:
      - vet_network
    volumes:
      - ./logs:/app/logs
    extra_hosts:
      - "host.docker.internal:host-gateway"

# 数据卷配置
volumes: {}

networks:
  vet_network:
    driver: bridge
