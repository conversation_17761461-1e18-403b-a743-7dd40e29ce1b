# 是否调试模式，调试下openai_proxy.py可以打印客户端请求的原始请求数据
DEBUG=true

PYTHONPATH=/Users/<USER>/work/code/vet_open_platform:$PYTHONPATH

# Database Configuration
# 远程PostgreSQL数据库连接（推荐）
# 格式: postgresql://username:password@hostname:port/database_name
DATABASE_URL=postgresql://vet_user:<EMAIL>:5432/vet_platform

# 或者使用本地Docker数据库（开发测试用）
# DATABASE_URL=postgresql://vet_user:vet_password@localhost:5432/vet_platform

# 分别配置数据库参数（可选，如果不使用DATABASE_URL）
DATABASE_HOST=your-postgres-host.com
DATABASE_PORT=5432
DATABASE_NAME=vet_platform
DATABASE_USER=vet_user
DATABASE_PASSWORD=vet_password
DATABASE_SCHEMA=vet

# Redis Configuration
# 远程Redis连接（推荐）
# 格式: redis://[password@]hostname:port/database_number
REDIS_URL=redis://<EMAIL>:6379/0

# 或者使用本地Redis
# REDIS_URL=redis://localhost:6379/0

# 分别配置Redis参数（可选）
REDIS_HOST=your-redis-host.com
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your_password

# JWT Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=宠物医疗AI开放平台
PROJECT_VERSION=1.0.0
DEBUG=true

# Service Ports
GATEWAY_PORT=8000
USER_SERVICE_PORT=8001
PET_SERVICE_PORT=8002
APP_SERVICE_PORT=8003
OCR_SERVICE_PORT=8004

# Service URLs
USER_SERVICE_URL=http://user_service:8001
PET_SERVICE_URL=http://pet_service:8002
APP_SERVICE_URL=http://app_service:8003
OCR_SERVICE_URL=http://ocr_service:8004

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# CORS Configuration
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# File Upload
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=./uploads

# Email Configuration (optional)
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=
SMTP_USER=
SMTP_PASSWORD=
EMAILS_FROM_EMAIL=
EMAILS_FROM_NAME=

# QwenVL OCR Configuration
VL_QWEN_API_KEY=your-qwen-api-key-here
VL_QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
# VL_QWEN_MODEL_NAME="qwen-vl-plus"
# VL_QWEN_MODEL_NAME="qwen2.5-vl-7b-instruct"
VL_QWEN_MODEL_NAME="qwen2.5-vl-32b-instruct"

# OCR Configuration
OCR_TASK_TIMEOUT=300
OCR_MAX_RETRY_COUNT=3
OCR_BATCH_SIZE=10

# Monitoring
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
