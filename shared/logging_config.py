"""
统一的loguru日志配置模块
为整个宠物医疗平台项目提供标准化的日志管理
"""
import os
import sys
from pathlib import Path
from typing import Optional
from loguru import logger


class LogConfig:
    """日志配置类"""

    # 日志级别映射
    LOG_LEVELS = {
        "DEBUG": "DEBUG",
        "INFO": "INFO",
        "WARNING": "WARNING",
        "ERROR": "ERROR",
        "CRITICAL": "CRITICAL"
    }

    # 控制台日志格式（彩色）
    CONSOLE_FORMAT = (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )

    # 文件日志格式
    FILE_FORMAT = (
        "{time:YYYY-MM-DD HH:mm:ss.SSS} | "
        "{level: <8} | "
        "{name}:{function}:{line} | "
        "{message}"
    )

    def __init__(self):
        # 创建日志目录
        self.logs_dir = Path("logs")
        self.logs_dir.mkdir(exist_ok=True)

        # 从环境变量获取日志级别
        self.log_level = os.getenv("LOG_LEVEL", "INFO").upper()
        if self.log_level not in self.LOG_LEVELS:
            self.log_level = "INFO"

    def setup_logger(self, service_name: str, enable_console: bool = True, enable_file: bool = True) -> None:
        """
        为指定服务设置logger

        Args:
            service_name: 服务名称（如：gateway, user_service等）
            enable_console: 是否启用控制台输出
            enable_file: 是否启用文件输出
        """
        # 移除默认的handler
        logger.remove()

        # 添加控制台输出
        if enable_console:
            logger.add(
                sys.stdout,
                format=self.CONSOLE_FORMAT,
                level=self.log_level,
                colorize=True,
                backtrace=True,
                diagnose=True,
                filter=lambda record: record["extra"].get(
                    "service") == service_name
            )

        # 添加文件输出
        if enable_file:
            # 当前日志文件
            current_log_file = self.logs_dir / f"{service_name}.log"
            logger.add(
                current_log_file,
                format=self.FILE_FORMAT,
                level=self.log_level,
                rotation="00:00",  # 每天午夜轮转
                retention="30 days",  # 保留30天
                compression="zip",  # 压缩旧日志
                encoding="utf-8",
                backtrace=True,
                diagnose=True,
                filter=lambda record: record["extra"].get(
                    "service") == service_name
            )

            # 错误日志单独文件
            error_log_file = self.logs_dir / f"{service_name}_error.log"
            logger.add(
                error_log_file,
                format=self.FILE_FORMAT,
                level="ERROR",
                rotation="10 MB",  # 错误日志按大小轮转
                retention="60 days",  # 错误日志保留更久
                compression="zip",
                encoding="utf-8",
                backtrace=True,
                diagnose=True,
                filter=lambda record: (
                    record["extra"].get("service") == service_name and
                    record["level"].no >= logger.level("ERROR").no
                )
            )

        # 绑定服务名称到logger上下文
        logger.configure(extra={"service": service_name})

        logger.info(f"✅ {service_name} 日志系统初始化完成")
        logger.info(f"📁 日志目录: {self.logs_dir.absolute()}")
        logger.info(f"📊 日志级别: {self.log_level}")


# 全局日志配置实例
log_config = LogConfig()


def setup_gateway_logger():
    """设置网关服务日志"""
    log_config.setup_logger("gateway")
    return logger.bind(service="gateway")


def setup_user_service_logger():
    """设置用户服务日志"""
    log_config.setup_logger("user_service")
    return logger.bind(service="user_service")


def setup_pet_service_logger():
    """设置宠物服务日志"""
    log_config.setup_logger("pet_service")
    return logger.bind(service="pet_service")


# LLM服务日志配置已移除 - 独立LLM服务模块已被移除
# 如需要LLM相关日志，请使用网关日志系统


def setup_app_service_logger():
    """设置应用服务日志"""
    log_config.setup_logger("app_service")
    return logger.bind(service="app_service")


def setup_ocr_service_logger():
    """设置OCR服务日志"""
    log_config.setup_logger("ocr_service")
    return logger.bind(service="ocr_service")


def get_logger(service_name: str):
    """
    获取指定服务的logger实例

    Args:
        service_name: 服务名称

    Returns:
        绑定了服务名称的logger实例
    """
    return logger.bind(service=service_name)


# 便捷的logger获取函数
def get_gateway_logger():
    """获取网关logger"""
    return logger.bind(service="gateway")


def get_user_service_logger():
    """获取用户服务logger"""
    return logger.bind(service="user_service")


def get_pet_service_logger():
    """获取宠物服务logger"""
    return logger.bind(service="pet_service")


# LLM服务logger已移除 - 使用网关logger替代


def get_app_service_logger():
    """获取应用服务logger"""
    return logger.bind(service="app_service")


# 用于测试的函数
def test_logging_system():
    """测试日志系统"""
    print("🧪 测试日志系统...")

    # 测试各个服务的logger
    services = ["gateway", "user_service",
                "pet_service", "app_service"]

    for service in services:
        log_config.setup_logger(service)
        service_logger = logger.bind(service=service)

        service_logger.debug(f"🔍 {service} DEBUG 级别测试")
        service_logger.info(f"ℹ️ {service} INFO 级别测试")
        service_logger.warning(f"⚠️ {service} WARNING 级别测试")
        service_logger.error(f"❌ {service} ERROR 级别测试")

    print("✅ 日志系统测试完成")


if __name__ == "__main__":
    test_logging_system()
