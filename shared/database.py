"""
数据库配置和会话管理
"""
import os
from typing import AsyncGenerator

# 修复TimeoutError重复基类问题
import warnings
warnings.filterwarnings("ignore", message=".*duplicate base class.*")

try:
    from sqlalchemy import create_engine
    from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
    from sqlalchemy.ext.declarative import declarative_base
    from sqlalchemy.orm import sessionmaker
except ImportError as e:
    print(f"SQLAlchemy导入错误: {e}")
    raise

# 延迟获取数据库URL的函数


def get_database_url():
    """获取数据库URL，支持运行时环境变量"""
    return os.getenv(
        "DATABASE_URL", "postgresql://postgres:123456@localhost:5432/vet_platform")


def get_async_database_url():
    """获取异步数据库URL"""
    return get_database_url().replace("postgresql://", "postgresql+asyncpg://")


def get_database_schema():
    """获取数据库schema，支持运行时环境变量"""
    return os.getenv("DATABASE_SCHEMA", "vet")


def get_table_args_with_schema(**kwargs):
    """
    获取包含schema配置的__table_args__

    Args:
        **kwargs: 其他table_args参数（如索引、约束等）

    Returns:
        tuple: 包含schema配置的__table_args__
    """
    schema = get_database_schema()

    # 如果有其他参数，将schema添加到字典中
    if kwargs:
        # 如果最后一个参数是字典，则合并schema
        args = list(kwargs.values()) if isinstance(kwargs, dict) else []
        schema_dict = {'schema': schema}

        # 检查是否已有字典参数
        if args and isinstance(args[-1], dict):
            args[-1].update(schema_dict)
            return tuple(args)
        else:
            return tuple(list(args) + [schema_dict])
    else:
        # 只有schema配置
        return ({'schema': schema},)


# 延迟创建数据库引擎
_engine = None
_async_engine = None


def get_engine():
    """获取同步数据库引擎"""
    global _engine
    if _engine is None:
        _engine = create_engine(
            get_database_url(),
            echo=False,
            # 连接池配置
            pool_size=10,                    # 连接池大小
            max_overflow=20,                 # 最大溢出连接数
            pool_timeout=30,                 # 获取连接超时时间
            pool_recycle=3600,               # 连接回收时间（1小时）
            pool_pre_ping=True               # 连接前ping检查
        )
    return _engine


def get_async_engine():
    """获取异步数据库引擎"""
    global _async_engine
    if _async_engine is None:
        # 基础连接参数
        connect_args = {
            "server_settings": {
                "application_name": "vet_platform",
                "search_path": "vet,public"  # 指定默认schema为vet，备用public
            }
        }

        # 检查是否需要添加SSL配置
        database_url = get_async_database_url()
        if "localhost" not in database_url and "127.0.0.1" not in database_url:
            # 远程数据库，禁用SSL以避免连接问题
            connect_args.update({
                "ssl": False,  # 禁用SSL
                "command_timeout": 60,  # 命令超时时间
                "server_settings": {
                    "application_name": "vet_platform",
                    "search_path": "vet,public",  # 指定默认schema为vet，备用public
                    "tcp_keepalives_idle": "600",
                    "tcp_keepalives_interval": "30",
                    "tcp_keepalives_count": "3"
                }
            })

        _async_engine = create_async_engine(
            database_url,
            echo=False,
            # 连接池配置
            pool_size=3,                     # 进一步减少连接池大小
            max_overflow=5,                  # 减少最大溢出连接数
            pool_timeout=60,                 # 增加获取连接超时时间
            pool_recycle=1800,               # 减少连接回收时间（30分钟）
            pool_pre_ping=True,              # 连接前ping检查
            # 异步连接配置
            connect_args=connect_args
        )
    return _async_engine

# 为了向后兼容，保留原有的变量名


@property
def engine():
    return get_engine()


@property
def async_engine():
    return get_async_engine()


# 创建会话制造器
SessionLocal = sessionmaker(
    autocommit=False, autoflush=False, bind=get_engine())
AsyncSessionLocal = async_sessionmaker(
    get_async_engine(), class_=AsyncSession, expire_on_commit=False
)

# 创建模型基类
Base = declarative_base()


def get_db():
    """
    获取数据库会话的依赖项
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """
    获取异步数据库会话的依赖项
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


async def init_db():
    """
    初始化数据库表
    """
    engine = get_async_engine()
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)


async def close_db():
    """
    关闭数据库连接
    """
    global _async_engine, _engine
    if _async_engine:
        await _async_engine.dispose()
        _async_engine = None
    if _engine:
        _engine.dispose()
        _engine = None


async def check_db_health() -> bool:
    """检查数据库连接健康状态"""
    try:
        from sqlalchemy import text
        async with AsyncSessionLocal() as session:
            result = await session.execute(text("SELECT 1"))
            return result.fetchone() is not None
    except Exception as e:
        print(f"数据库健康检查失败: {e}")
        return False


async def reset_db_connection():
    """重置数据库连接"""
    global _async_engine
    try:
        if _async_engine:
            await _async_engine.dispose()
            _async_engine = None
        # 重新获取引擎将创建新的连接池
        get_async_engine()
        print("数据库连接已重置")
        return True
    except Exception as e:
        print(f"重置数据库连接失败: {e}")
        return False
