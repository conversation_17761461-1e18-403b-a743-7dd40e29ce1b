"""
简化的Redis客户端 - 避免aioredis兼容性问题
只使用同步redis库，在异步环境中通过线程池使用
"""
import json
import os
from typing import Any, Optional
import asyncio
import concurrent.futures
from functools import wraps

import redis
from redis import Redis


def async_redis_operation(func):
    """装饰器：将同步Redis操作转换为异步"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor() as executor:
            return await loop.run_in_executor(executor, func, *args, **kwargs)
    return wrapper


class SimpleRedisClient:
    """简化的Redis客户端，避免aioredis兼容性问题"""

    def __init__(self):
        self.redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
        self.redis_password = os.getenv("REDIS_PASSWORD")
        self._client: Optional[Redis] = None

    @property
    def client(self) -> Redis:
        """获取Redis客户端"""
        if self._client is None:
            # 显式处理密码连接
            if self.redis_password:
                # 使用连接池创建客户端并传入密码
                pool = redis.ConnectionPool.from_url(
                    self.redis_url,
                    password=self.redis_password,
                    decode_responses=True
                )
                self._client = redis.Redis(connection_pool=pool)
            else:
                # 无密码时使用标准连接
                self._client = redis.from_url(
                    self.redis_url,
                    decode_responses=True
                )
        return self._client

    @property
    def sync_client(self) -> Redis:
        """获取同步Redis客户端（兼容性）"""
        return self.client

    @async_redis_operation
    def _sync_set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """同步设置键值"""
        if isinstance(value, (dict, list)):
            value = json.dumps(value)
        return self.client.set(key, value, ex=expire)

    @async_redis_operation
    def _sync_get(self, key: str) -> Any:
        """同步获取值"""
        value = self.client.get(key)
        if value is None:
            return None

        # 尝试解析JSON
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            return value

    @async_redis_operation
    def _sync_delete(self, *keys: str) -> int:
        """同步删除键"""
        return self.client.delete(*keys)

    @async_redis_operation
    def _sync_exists(self, key: str) -> bool:
        """同步检查键是否存在"""
        return bool(self.client.exists(key))

    @async_redis_operation
    def _sync_expire(self, key: str, seconds: int) -> bool:
        """同步设置过期时间"""
        return self.client.expire(key, seconds)

    @async_redis_operation
    def _sync_incr(self, key: str, amount: int = 1) -> int:
        """同步递增"""
        return self.client.incr(key, amount)

    @async_redis_operation
    def _sync_ping(self) -> bool:
        """同步ping测试"""
        return self.client.ping()

    @async_redis_operation
    def _sync_lpush(self, name: str, *values: Any) -> int:
        """同步列表左推"""
        serialized_values = []
        for value in values:
            if isinstance(value, (dict, list)):
                serialized_values.append(json.dumps(value))
            else:
                serialized_values.append(str(value))
        return self.client.lpush(name, *serialized_values)

    @async_redis_operation
    def _sync_llen(self, name: str) -> int:
        """同步获取列表长度"""
        return self.client.llen(name)

    @async_redis_operation
    def _sync_rpop(self, name: str) -> Any:
        """同步列表右弹出"""
        value = self.client.rpop(name)
        if value is None:
            return None
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            return value

    @async_redis_operation
    def _sync_setex(self, key: str, seconds: int, value: Any) -> bool:
        """同步设置键值并指定过期时间"""
        if isinstance(value, (dict, list)):
            value = json.dumps(value)
        return self.client.setex(key, seconds, value)

    # 异步接口
    async def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """设置键值对"""
        return await self._sync_set(key, value, expire)

    async def get(self, key: str, default: Any = None) -> Any:
        """获取值"""
        result = await self._sync_get(key)
        return result if result is not None else default

    async def delete(self, *keys: str) -> int:
        """删除键"""
        return await self._sync_delete(*keys)

    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        return await self._sync_exists(key)

    async def expire(self, key: str, seconds: int) -> bool:
        """设置过期时间"""
        return await self._sync_expire(key, seconds)

    async def incr(self, key: str, amount: int = 1) -> int:
        """递增"""
        return await self._sync_incr(key, amount)

    async def ping(self) -> bool:
        """测试连接"""
        return await self._sync_ping()

    async def lpush(self, name: str, *values: Any) -> int:
        """列表左推"""
        return await self._sync_lpush(name, *values)

    async def llen(self, name: str) -> int:
        """获取列表长度"""
        return await self._sync_llen(name)

    async def rpop(self, name: str) -> Any:
        """列表右弹出"""
        return await self._sync_rpop(name)

    async def setex(self, key: str, seconds: int, value: Any) -> bool:
        """设置键值并指定过期时间"""
        return await self._sync_setex(key, seconds, value)

    async def close(self):
        """关闭连接"""
        if self._client:
            self._client.close()
            self._client = None

    # 同步接口（用于非异步环境）
    def sync_set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """同步设置键值对"""
        if isinstance(value, (dict, list)):
            value = json.dumps(value)
        return self.client.set(key, value, ex=expire)

    def sync_get(self, key: str, default: Any = None) -> Any:
        """同步获取值"""
        value = self.client.get(key)
        if value is None:
            return default

        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            return value

    def sync_ping(self) -> bool:
        """同步ping测试"""
        return self.client.ping()

    def sync_setex(self, key: str, seconds: int, value: Any) -> bool:
        """同步设置键值并指定过期时间"""
        if isinstance(value, (dict, list)):
            value = json.dumps(value)
        return self.client.setex(key, seconds, value)


# 全局Redis客户端实例
redis_client = SimpleRedisClient()


# 缓存装饰器
def cache(expire: int = 300, key_prefix: str = "cache"):
    """
    缓存装饰器

    Args:
        expire: 缓存过期时间（秒）
        key_prefix: 缓存键前缀
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{func.__name__}:{hash(str(args) + str(kwargs))}"

            # 尝试从缓存获取
            cached_result = await redis_client.get(cache_key)
            if cached_result is not None:
                return cached_result

            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            await redis_client.set(cache_key, result, expire=expire)
            return result

        return wrapper
    return decorator
