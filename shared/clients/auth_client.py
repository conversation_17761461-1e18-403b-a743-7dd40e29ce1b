"""
认证服务客户端 - 供其他微服务使用
通过HTTP API调用用户服务的认证接口，避免直接数据库访问
"""
from typing import Optional, Dict, Any

import httpx

from shared.logging_config import get_logger

logger = get_logger(__name__)


class AuthClient:
    """认证服务客户端"""

    def __init__(self, user_service_url: str, timeout: float = 5.0):
        """
        初始化认证客户端

        Args:
            user_service_url: 用户服务的基础URL
            timeout: 请求超时时间（秒）
        """
        self.user_service_url = user_service_url.rstrip('/')
        self.timeout = timeout

    async def verify_jwt_token(self, token: str, token_type: str = "access") -> Optional[Dict[str, Any]]:
        """
        验证JWT令牌

        Args:
            token: JWT令牌字符串
            token_type: 令牌类型，默认为access

        Returns:
            验证成功返回用户信息字典，失败返回None
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.user_service_url}/internal/verify-jwt-token",
                    json={"token": token, "token_type": token_type}
                )

                if response.status_code == 200:
                    user_data = response.json()
                    logger.debug(f"JWT令牌验证成功，用户ID: {user_data.get('id')}")
                    return user_data
                elif response.status_code == 401:
                    logger.warning("JWT令牌验证失败：无效令牌")
                    return None
                elif response.status_code == 404:
                    logger.warning("JWT令牌验证失败：用户不存在")
                    return None
                else:
                    logger.error(
                        f"用户服务认证API错误: {response.status_code} - {response.text}")
                    return None

        except httpx.TimeoutException:
            logger.error("调用用户服务认证API超时")
            return None
        except httpx.ConnectError:
            logger.error("无法连接到用户服务")
            return None
        except Exception as e:
            logger.error(f"调用用户服务认证API失败: {e}")
            return None

    async def verify_api_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """
        验证API密钥

        Args:
            api_key: API密钥字符串

        Returns:
            验证成功返回用户信息字典，失败返回None
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.user_service_url}/internal/verify-api-key",
                    json={"api_key": api_key}
                )

                if response.status_code == 200:
                    user_data = response.json()
                    logger.debug(f"API密钥验证成功，用户ID: {user_data.get('id')}")
                    return user_data
                elif response.status_code == 401:
                    logger.warning("API密钥验证失败：无效或已过期")
                    return None
                else:
                    logger.error(
                        f"用户服务认证API错误: {response.status_code} - {response.text}")
                    return None

        except httpx.TimeoutException:
            logger.error("调用用户服务认证API超时")
            return None
        except httpx.ConnectError:
            logger.error("无法连接到用户服务")
            return None
        except Exception as e:
            logger.error(f"验证API密钥失败: {e}")
            return None

    async def verify_api_key_detailed(self, api_key: str) -> Dict[str, Any]:
        """
        详细验证API密钥，返回完整的验证结果

        Args:
            api_key: API密钥字符串

        Returns:
            包含详细验证信息的字典，包括错误类型和详细错误信息
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.user_service_url}/internal/verify-api-key-detailed",
                    json={"api_key": api_key}
                )

                if response.status_code == 200:
                    result = response.json()
                    logger.debug(f"API密钥详细验证成功，用户ID: {result.get('id')}")
                    return result
                else:
                    # 解析错误响应
                    try:
                        error_data = response.json()
                        logger.warning(f"API密钥详细验证失败: {error_data}")
                        return error_data
                    except:
                        # 如果无法解析JSON，返回通用错误
                        return {
                            "valid": False,
                            "error_type": "service_error",
                            "error_message": "认证服务错误",
                            "error_detail": f"HTTP {response.status_code}: {response.text}"
                        }

        except httpx.TimeoutException:
            logger.error("调用用户服务认证API超时")
            return {
                "valid": False,
                "error_type": "timeout_error",
                "error_message": "认证服务超时",
                "error_detail": "调用用户服务认证API超时，请稍后重试"
            }
        except httpx.ConnectError:
            logger.error("无法连接到用户服务")
            return {
                "valid": False,
                "error_type": "connection_error",
                "error_message": "认证服务不可用",
                "error_detail": "无法连接到用户服务，请检查服务状态"
            }
        except Exception as e:
            logger.error(f"验证API密钥失败: {e}")
            return {
                "valid": False,
                "error_type": "unknown_error",
                "error_message": "系统内部错误",
                "error_detail": f"验证API密钥时发生未知错误: {str(e)}"
            }

    async def verify_temporary_api_key(self, temp_api_key: str) -> Optional[Dict[str, Any]]:
        """
        验证临时API密钥并返回对应的真实API密钥信息

        Args:
            temp_api_key: 临时API密钥字符串

        Returns:
            验证成功返回包含真实API密钥和用户信息的字典，失败返回None
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.user_service_url}/internal/verify-temporary-api-key",
                    json={"temp_api_key": temp_api_key}
                )

                if response.status_code == 200:
                    data = response.json()
                    logger.debug(
                        f"临时API密钥验证成功，用户ID: {data.get('user_info', {}).get('id')}")
                    return data
                elif response.status_code == 401:
                    logger.warning("临时API密钥验证失败：无效或已过期")
                    return None
                else:
                    logger.error(
                        f"用户服务认证API错误: {response.status_code} - {response.text}")
                    return None

        except httpx.TimeoutException:
            logger.error("调用用户服务认证API超时")
            return None
        except httpx.ConnectError:
            logger.error("无法连接到用户服务")
            return None
        except Exception as e:
            logger.error(f"验证临时API密钥失败: {e}")
            return None

    async def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        通过用户ID获取用户信息

        Args:
            user_id: 用户ID

        Returns:
            用户信息字典或None
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.user_service_url}/internal/users/{user_id}"
                )

                if response.status_code == 200:
                    user_data = response.json()
                    logger.debug(f"获取用户信息成功，用户ID: {user_id}")
                    return user_data
                elif response.status_code == 404:
                    logger.warning(f"用户不存在，用户ID: {user_id}")
                    return None
                else:
                    logger.error(
                        f"获取用户信息失败: {response.status_code} - {response.text}")
                    return None

        except Exception as e:
            logger.error(f"获取用户信息失败: {e}")
            return None

    async def health_check(self) -> bool:
        """
        检查用户服务健康状态

        Returns:
            服务健康返回True，否则返回False
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.user_service_url}/internal/health"
                )
                return response.status_code == 200
        except Exception:
            return False


# 便捷函数
def create_auth_client(user_service_url: str, timeout: float = 5.0) -> AuthClient:
    """创建认证客户端实例"""
    return AuthClient(user_service_url, timeout)
