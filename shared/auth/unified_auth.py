"""
统一认证服务模块

该模块提供了统一的认证服务，整合了API密钥验证、缓存管理、速率限制等功能。
支持OpenAI兼容API的认证需求，同时遵循微服务架构原则。
"""
import time
from typing import Dict, Any, Optional

from fastapi import HTTPException, status

from shared.clients.auth_client import AuthClient
from shared.redis_simple import redis_client
from shared.logging_config import get_logger

logger = get_logger(__name__)


class UnifiedAuthService:
    """统一认证服务，整合所有认证方式和相关功能"""

    def __init__(self, auth_client: AuthClient):
        """
        初始化统一认证服务

        Args:
            auth_client: 认证客户端实例
        """
        self.auth_client = auth_client
        self.cache_ttl = 300  # 缓存5分钟
        self.cache_prefix = "unified_auth_cache"

    async def validate_openai_api_key(self, api_key: str) -> Dict[str, Any]:
        """
        OpenAI兼容的API密钥验证，整合缓存、速率限制、错误处理

        Args:
            api_key: API密钥字符串

        Returns:
            验证成功返回用户信息字典

        Raises:
            HTTPException: 验证失败时抛出相应的HTTP异常
        """

        # 1. 首先进行格式验证
        format_error = self._validate_api_key_format(api_key)
        if format_error:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={
                    "error": {
                        "message": format_error["message"],
                        "type": "invalid_request_error",
                        "code": "invalid_api_key",
                        "detail": format_error["detail"]
                    }
                }
            )

        # 2. 尝试从缓存获取
        cached_info = await self._get_cached_api_key_info(api_key)
        if cached_info:
            # 缓存的信息应该是有效的，检查速率限制
            if not await self._check_rate_limit(api_key, cached_info):
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail={
                        "error": {
                            "message": "API密钥请求频率超过限制，请稍后再试",
                            "type": "rate_limit_exceeded",
                            "code": "rate_limit_exceeded"
                        }
                    }
                )
            return cached_info

        # 3. 从认证服务获取详细验证结果
        validation_result = await self.auth_client.verify_api_key_detailed(api_key)

        # 4. 检查验证结果
        if not validation_result.get("valid", False):
            # 根据错误类型返回具体的错误信息
            error_type = validation_result.get("error_type", "unknown")
            error_message = validation_result.get("error_message", "API密钥验证失败")
            error_detail = validation_result.get(
                "error_detail", "请检查API密钥或联系管理员")

            # 根据错误类型设置适当的HTTP状态码
            if error_type in ["not_found", "disabled", "expired"]:
                status_code = status.HTTP_401_UNAUTHORIZED
                error_code = "invalid_api_key"
            elif error_type in ["user_deleted", "user_disabled"]:
                status_code = status.HTTP_403_FORBIDDEN
                error_code = "insufficient_permissions"
            else:  # service_error, timeout_error, connection_error等系统错误
                status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
                error_code = "internal_error"

            # 构建详细的错误响应
            error_response = {
                "error": {
                    "message": error_message,
                    "type": error_code,
                    "code": error_type,
                    "detail": error_detail
                }
            }

            # 对于过期的API key，添加过期时间信息
            if error_type == "expired" and "expired_at" in validation_result:
                error_response["error"]["expired_at"] = validation_result["expired_at"]

            raise HTTPException(status_code=status_code, detail=error_response)

        # 5. 检查速率限制
        if not await self._check_rate_limit(api_key, validation_result):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail={
                    "error": {
                        "message": "API密钥请求频率超过限制，请稍后再试",
                        "type": "rate_limit_exceeded",
                        "code": "rate_limit_exceeded"
                    }
                }
            )

        # 6. 缓存有效的API key信息
        await self._cache_api_key_info(api_key, validation_result)

        return validation_result

    def _validate_api_key_format(self, api_key: str) -> Optional[Dict[str, str]]:
        """
        验证API密钥格式

        Args:
            api_key: API密钥字符串

        Returns:
            如果格式有误返回错误信息字典，否则返回None
        """
        if not api_key:
            return {
                "message": "API密钥不能为空",
                "detail": "请提供有效的API密钥"
            }

        # 去除前后空格
        api_key = api_key.strip()

        if not api_key:
            return {
                "message": "API密钥不能为空",
                "detail": "请提供有效的API密钥"
            }

        if len(api_key) < 10:
            return {
                "message": "API密钥格式无效：密钥过短",
                "detail": f"API密钥长度至少需要10个字符，当前长度为{len(api_key)}个字符"
            }

        if not (api_key.startswith("sk-proj-") or api_key.startswith("sk-")):
            return {
                "message": "API密钥格式无效：前缀不正确",
                "detail": "API密钥必须以 'sk-proj-' 或 'sk-' 开头，请检查您的密钥格式"
            }

        # 格式正确
        return None

    async def _get_cached_api_key_info(self, api_key: str) -> Optional[Dict[str, Any]]:
        """从Redis缓存获取API key信息"""
        try:
            cache_key = f"{self.cache_prefix}:{api_key}"
            cached_info = await redis_client.get(cache_key)

            if cached_info:
                logger.debug(f"从缓存获取API key信息: {api_key[:10]}...")
                return cached_info

            return None

        except Exception as e:
            logger.error(f"从缓存获取API key信息失败: {e}")
            return None

    async def _cache_api_key_info(self, api_key: str, user_info: Dict[str, Any]):
        """缓存API key信息到Redis"""
        try:
            cache_key = f"{self.cache_prefix}:{api_key}"
            await redis_client.set(cache_key, user_info, expire=self.cache_ttl)
            logger.debug(f"API key信息已缓存: {api_key[:10]}...")

        except Exception as e:
            logger.error(f"缓存API key信息失败: {e}")

    async def _check_rate_limit(self, api_key: str, user_info: Dict[str, Any]) -> bool:
        """检查API key的速率限制"""
        try:
            current_time = int(time.time())
            current_minute = current_time // 60
            current_day = current_time // 86400

            # 检查每分钟限制
            minute_key = f"rate_limit:minute:{api_key}:{current_minute}"
            minute_count = await redis_client.get(minute_key, 0)

            if isinstance(minute_count, str):
                minute_count = int(minute_count)

            rate_limit_per_minute = user_info.get("rate_limit_per_minute", 60)
            if minute_count >= rate_limit_per_minute:
                logger.warning(
                    f"API key {api_key[:10]}... 超过每分钟速率限制: {minute_count}/{rate_limit_per_minute}")
                return False

            # 检查每日限制
            day_key = f"rate_limit:day:{api_key}:{current_day}"
            day_count = await redis_client.get(day_key, 0)

            if isinstance(day_count, str):
                day_count = int(day_count)

            rate_limit_per_day = user_info.get("rate_limit_per_day", 10000)
            if day_count >= rate_limit_per_day:
                logger.warning(
                    f"API key {api_key[:10]}... 超过每日速率限制: {day_count}/{rate_limit_per_day}")
                return False

            # 增加计数器
            await redis_client.incr(minute_key)
            await redis_client.expire(minute_key, 120)  # 2分钟过期

            await redis_client.incr(day_key)
            await redis_client.expire(day_key, 86400 * 2)  # 2天过期

            return True

        except Exception as e:
            logger.error(f"检查速率限制失败: {e}")
            # 如果Redis出错，允许请求通过，但记录错误
            return True

    async def clear_cache(self, api_key: str):
        """清除指定API key的缓存"""
        try:
            cache_key = f"{self.cache_prefix}:{api_key}"
            await redis_client.delete(cache_key)
            logger.debug(f"已清除API key缓存: {api_key[:10]}...")
        except Exception as e:
            logger.error(f"清除API key缓存失败: {e}")

    async def get_rate_limit_status(self, api_key: str) -> Dict[str, Any]:
        """获取API key的速率限制状态"""
        try:
            current_time = int(time.time())
            current_minute = current_time // 60
            current_day = current_time // 86400

            minute_key = f"rate_limit:minute:{api_key}:{current_minute}"
            day_key = f"rate_limit:day:{api_key}:{current_day}"

            minute_count = await redis_client.get(minute_key, 0)
            day_count = await redis_client.get(day_key, 0)

            if isinstance(minute_count, str):
                minute_count = int(minute_count)
            if isinstance(day_count, str):
                day_count = int(day_count)

            return {
                "minute_usage": minute_count,
                "day_usage": day_count,
                "current_minute": current_minute,
                "current_day": current_day
            }

        except Exception as e:
            logger.error(f"获取速率限制状态失败: {e}")
            return {
                "minute_usage": 0,
                "day_usage": 0,
                "current_minute": 0,
                "current_day": 0
            }


# 便捷函数
def create_unified_auth_service(user_service_url: str, timeout: float = 5.0) -> UnifiedAuthService:
    """创建统一认证服务实例"""
    auth_client = AuthClient(user_service_url, timeout)
    return UnifiedAuthService(auth_client)
