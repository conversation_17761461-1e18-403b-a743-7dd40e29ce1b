"""
基础模型包
"""

from .base import BaseModel, TimestampMixin, SoftDeleteMixin, AuditMixin, BaseAuditModel
from .response import (
    BaseResponse, SuccessResponse, ErrorResponse, PaginatedResponse,
    PaginationInfo, PaginatedData, create_success_response, create_error_response,
    create_paginated_response, success_response, error_response, paginated_response,
    ResponseStatus, ErrorCode, SuccessMessage, ErrorMessage
)

__all__ = [
    # 数据库模型基类
    "BaseModel",
    "TimestampMixin",
    "SoftDeleteMixin",
    "AuditMixin",
    "BaseAuditModel",

    # 响应模型
    "BaseResponse",
    "SuccessResponse",
    "ErrorResponse",
    "PaginatedResponse",
    "PaginationInfo",
    "PaginatedData",

    # 响应构造函数
    "create_success_response",
    "create_error_response",
    "create_paginated_response",

    # 兼容性函数
    "success_response",
    "error_response",
    "paginated_response",

    # 常量
    "ResponseStatus",
    "ErrorCode",
    "SuccessMessage",
    "ErrorMessage"
]
