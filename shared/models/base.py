"""
基础模型类和混入类
"""
from datetime import datetime, timezone
from typing import Any, Dict

from sqlalchemy import Column, DateTime, Integer, String, Boolean
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.sql import func

from shared.database import Base


class TimestampMixin:
    """添加时间戳字段的混入类"""

    created_at = Column(DateTime(timezone=True),
                        server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(
    ), onupdate=func.now(), nullable=False)


class SoftDeleteMixin:
    """软删除功能的混入类"""

    is_deleted = Column(Boolean, default=False, nullable=False)
    deleted_at = Column(DateTime(timezone=True), nullable=True)

    def soft_delete(self):
        """标记记录为已删除"""
        self.is_deleted = True
        self.deleted_at = datetime.now(timezone.utc)

    def restore(self):
        """恢复软删除的记录"""
        self.is_deleted = False
        self.deleted_at = None


class BaseModel(Base, TimestampMixin):
    """包含通用字段的基础模型类"""

    __abstract__ = True

    id = Column(Integer, primary_key=True, index=True)

    def to_dict(self) -> Dict[str, Any]:
        """将模型实例转换为字典"""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }

    def update(self, **kwargs):
        """使用提供的数据更新模型实例"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """从字典创建模型实例"""
        return cls(**{
            key: value for key, value in data.items()
            if hasattr(cls, key)
        })


class AuditMixin:
    """审计跟踪字段的混入类"""

    @declared_attr
    def created_by_id(cls):
        return Column(Integer, nullable=True)

    @declared_attr
    def updated_by_id(cls):
        return Column(Integer, nullable=True)

    created_by = Column(String(255), nullable=True)
    updated_by = Column(String(255), nullable=True)


class BaseAuditModel(BaseModel, AuditMixin, SoftDeleteMixin):
    """包含审计跟踪和软删除功能的基础模型"""

    __abstract__ = True
