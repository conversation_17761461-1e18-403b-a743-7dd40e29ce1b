"""
统一API响应模型

提供标准化的API响应格式，确保所有微服务返回一致的响应结构。
支持成功响应、错误响应、分页响应等多种场景。
"""
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, Generic, List, Optional, TypeVar, Union

from pydantic import BaseModel, Field

# 泛型类型变量，用于响应数据的类型
T = TypeVar('T')


class PaginationInfo(BaseModel):
    """分页信息模型"""
    total: int = Field(..., description="总记录数", ge=0)
    page: int = Field(..., description="当前页码", ge=1)
    size: int = Field(..., description="每页大小", ge=1)
    pages: int = Field(..., description="总页数", ge=1)


class BaseResponse(BaseModel, Generic[T]):
    """基础响应模型"""
    status: str = Field(..., description="响应状态: success|error")
    msg: str = Field(..., description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")
    timestamp: str = Field(..., description="响应时间戳")
    request_id: str = Field(..., description="请求唯一标识")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class SuccessResponse(BaseResponse[T]):
    """成功响应模型"""
    status: str = Field(default="success", description="响应状态")


class ErrorResponse(BaseResponse[None]):
    """错误响应模型"""
    status: str = Field(default="error", description="响应状态")
    data: None = Field(default=None, description="错误时数据为空")
    error_code: Optional[str] = Field(None, description="错误代码")
    error_details: Optional[Dict[str, Any]] = Field(None, description="错误详细信息")


class PaginatedData(BaseModel, Generic[T]):
    """分页数据模型"""
    items: List[T] = Field(..., description="数据项列表")
    pagination: PaginationInfo = Field(..., description="分页信息")


class PaginatedResponse(SuccessResponse[PaginatedData[T]]):
    """分页响应模型"""
    pass


# 便捷构造函数

def create_success_response(
    data: T,
    msg: str = "操作成功",
    request_id: Optional[str] = None
) -> SuccessResponse[T]:
    """
    创建成功响应
    
    Args:
        data: 响应数据
        msg: 响应消息
        request_id: 请求ID，如果不提供则自动生成
        
    Returns:
        SuccessResponse: 成功响应对象
    """
    return SuccessResponse[T](
        status="success",
        msg=msg,
        data=data,
        timestamp=datetime.now(timezone.utc).isoformat(),
        request_id=request_id or str(uuid.uuid4())
    )


def create_error_response(
    msg: str,
    error_code: Optional[str] = None,
    error_details: Optional[Dict[str, Any]] = None,
    request_id: Optional[str] = None
) -> ErrorResponse:
    """
    创建错误响应
    
    Args:
        msg: 错误消息
        error_code: 错误代码
        error_details: 错误详细信息
        request_id: 请求ID，如果不提供则自动生成
        
    Returns:
        ErrorResponse: 错误响应对象
    """
    return ErrorResponse(
        status="error",
        msg=msg,
        data=None,
        error_code=error_code,
        error_details=error_details,
        timestamp=datetime.now(timezone.utc).isoformat(),
        request_id=request_id or str(uuid.uuid4())
    )


def create_paginated_response(
    items: List[T],
    total: int,
    page: int,
    size: int,
    msg: str = "获取成功",
    request_id: Optional[str] = None
) -> PaginatedResponse[T]:
    """
    创建分页响应
    
    Args:
        items: 数据项列表
        total: 总记录数
        page: 当前页码
        size: 每页大小
        msg: 响应消息
        request_id: 请求ID，如果不提供则自动生成
        
    Returns:
        PaginatedResponse: 分页响应对象
    """
    # 计算总页数
    pages = (total + size - 1) // size if total > 0 else 1
    
    pagination_info = PaginationInfo(
        total=total,
        page=page,
        size=size,
        pages=pages
    )
    
    paginated_data = PaginatedData[T](
        items=items,
        pagination=pagination_info
    )
    
    return PaginatedResponse[T](
        status="success",
        msg=msg,
        data=paginated_data,
        timestamp=datetime.now(timezone.utc).isoformat(),
        request_id=request_id or str(uuid.uuid4())
    )


# 兼容性别名，用于渐进式迁移
def success_response(data: T, msg: str = "操作成功") -> SuccessResponse[T]:
    """兼容性函数：创建成功响应"""
    return create_success_response(data, msg)


def error_response(msg: str, error_code: Optional[str] = None) -> ErrorResponse:
    """兼容性函数：创建错误响应"""
    return create_error_response(msg, error_code)


def paginated_response(
    items: List[T], 
    total: int, 
    page: int, 
    size: int, 
    msg: str = "获取成功"
) -> PaginatedResponse[T]:
    """兼容性函数：创建分页响应"""
    return create_paginated_response(items, total, page, size, msg)


# 响应状态常量
class ResponseStatus:
    """响应状态常量"""
    SUCCESS = "success"
    ERROR = "error"


# 常用错误代码
class ErrorCode:
    """常用错误代码"""
    # 通用错误
    INTERNAL_ERROR = "INTERNAL_ERROR"
    INVALID_REQUEST = "INVALID_REQUEST"
    VALIDATION_ERROR = "VALIDATION_ERROR"
    
    # 认证授权错误
    UNAUTHORIZED = "UNAUTHORIZED"
    FORBIDDEN = "FORBIDDEN"
    TOKEN_EXPIRED = "TOKEN_EXPIRED"
    INVALID_TOKEN = "INVALID_TOKEN"
    
    # 资源错误
    NOT_FOUND = "NOT_FOUND"
    ALREADY_EXISTS = "ALREADY_EXISTS"
    CONFLICT = "CONFLICT"
    
    # 业务逻辑错误
    BUSINESS_ERROR = "BUSINESS_ERROR"
    OPERATION_FAILED = "OPERATION_FAILED"
    
    # 外部服务错误
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    TIMEOUT = "TIMEOUT"
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"


# 常用成功消息
class SuccessMessage:
    """常用成功消息"""
    CREATED = "创建成功"
    UPDATED = "更新成功"
    DELETED = "删除成功"
    RETRIEVED = "获取成功"
    OPERATION_SUCCESS = "操作成功"
    LOGIN_SUCCESS = "登录成功"
    LOGOUT_SUCCESS = "退出成功"


# 常用错误消息
class ErrorMessage:
    """常用错误消息"""
    INTERNAL_ERROR = "内部服务器错误"
    INVALID_REQUEST = "请求参数无效"
    UNAUTHORIZED = "未授权访问"
    FORBIDDEN = "权限不足"
    NOT_FOUND = "资源不存在"
    ALREADY_EXISTS = "资源已存在"
    VALIDATION_ERROR = "数据验证失败"
    SERVICE_UNAVAILABLE = "服务不可用"
    TIMEOUT = "请求超时"
