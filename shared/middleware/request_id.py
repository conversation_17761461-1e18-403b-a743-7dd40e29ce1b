"""
请求ID中间件

为每个HTTP请求生成唯一的请求ID，用于日志追踪和响应标识。
支持从请求头中读取现有的请求ID，或自动生成新的请求ID。
"""
import uuid
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from shared.logging_config import get_logger

logger = get_logger(__name__)


class RequestIDMiddleware(BaseHTTPMiddleware):
    """
    请求ID中间件
    
    为每个请求生成或提取唯一的请求ID，并将其存储在请求状态中。
    请求ID可用于日志追踪、错误响应和调试。
    """
    
    def __init__(
        self,
        app,
        header_name: str = "X-Request-ID",
        generate_if_missing: bool = True
    ):
        """
        初始化请求ID中间件
        
        Args:
            app: FastAPI应用实例
            header_name: 请求ID的HTTP头名称
            generate_if_missing: 如果请求头中没有请求ID，是否自动生成
        """
        super().__init__(app)
        self.header_name = header_name
        self.generate_if_missing = generate_if_missing
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        处理请求，生成或提取请求ID
        
        Args:
            request: HTTP请求对象
            call_next: 下一个中间件或路由处理器
            
        Returns:
            Response: HTTP响应对象
        """
        # 尝试从请求头中获取请求ID
        request_id = request.headers.get(self.header_name)
        
        # 如果没有请求ID且允许生成，则创建新的请求ID
        if not request_id and self.generate_if_missing:
            request_id = str(uuid.uuid4())
        
        # 将请求ID存储在请求状态中
        if request_id:
            request.state.request_id = request_id
        
        # 记录请求开始日志
        logger.info(
            f"请求开始: {request.method} {request.url.path} "
            f"(请求ID: {request_id})"
        )
        
        # 调用下一个处理器
        response = await call_next(request)
        
        # 将请求ID添加到响应头中
        if request_id:
            response.headers[self.header_name] = request_id
        
        # 记录请求完成日志
        logger.info(
            f"请求完成: {request.method} {request.url.path} "
            f"状态码: {response.status_code} (请求ID: {request_id})"
        )
        
        return response


def add_request_id_middleware(app, header_name: str = "X-Request-ID"):
    """
    添加请求ID中间件到FastAPI应用
    
    Args:
        app: FastAPI应用实例
        header_name: 请求ID的HTTP头名称
    """
    app.add_middleware(
        RequestIDMiddleware,
        header_name=header_name,
        generate_if_missing=True
    )
    
    logger.info(f"已添加请求ID中间件 (头名称: {header_name})")


# 便捷函数：从请求中获取请求ID
def get_request_id(request: Request) -> str:
    """
    从请求对象中获取请求ID
    
    Args:
        request: HTTP请求对象
        
    Returns:
        str: 请求ID，如果不存在则返回默认值
    """
    return getattr(request.state, 'request_id', 'unknown')


# 便捷函数：生成新的请求ID
def generate_request_id() -> str:
    """
    生成新的请求ID
    
    Returns:
        str: 新的UUID格式的请求ID
    """
    return str(uuid.uuid4())
