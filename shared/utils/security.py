"""
认证和授权的安全工具
"""
import os
from datetime import datetime, timedelta, timezone
from typing import Optional

from jose import JWTError, jwt
from passlib.context import CryptContext

# 密码哈希配置
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT设置
SECRET_KEY = os.getenv(
    "SECRET_KEY", "your-super-secret-key-change-this-in-production")
ALGORITHM = os.getenv("ALGORITHM", "HS256")
ACCESS_TOKEN_EXPIRE_MINUTES = int(
    os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
REFRESH_TOKEN_EXPIRE_DAYS = int(os.getenv("REFRESH_TOKEN_EXPIRE_DAYS", "7"))


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码与其哈希值是否匹配"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """对密码进行哈希加密"""
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """创建JWT访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

    # 添加过期时间和令牌类型到载荷中
    to_encode.update({"exp": expire, "type": "access"})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def create_refresh_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """创建JWT刷新令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)

    # 添加过期时间和令牌类型到载荷中
    to_encode.update({"exp": expire, "type": "refresh"})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_token(token: str, token_type: str = "access") -> Optional[dict]:
    """验证JWT令牌并返回载荷"""
    try:
        # 解码JWT令牌
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])

        # 检查令牌类型是否匹配
        if payload.get("type") != token_type:
            return None

        # 检查令牌是否过期
        exp = payload.get("exp")
        if exp is None or datetime.now(timezone.utc) > datetime.fromtimestamp(exp, tz=timezone.utc):
            return None

        return payload
    except JWTError:
        return None
    except Exception:
        return None


def generate_api_key() -> str:
    """生成OpenAI格式的随机API密钥"""
    import secrets
    import string

    # OpenAI API密钥格式: sk-proj-{48个字符} 或 sk-{48个字符}
    # 我们使用sk-proj-格式以保持兼容性
    alphabet = string.ascii_letters + string.digits
    api_key = ''.join(secrets.choice(alphabet) for _ in range(48))
    return f"sk-proj-{api_key}"


def generate_temporary_api_key() -> str:
    """生成临时API密钥（以tt_开头）"""
    import secrets
    import string

    # 临时API密钥格式: tt-{48个字符}
    alphabet = string.ascii_letters + string.digits
    api_key = ''.join(secrets.choice(alphabet) for _ in range(48))
    return f"tt-{api_key}"


def verify_api_key(api_key: str) -> bool:
    """验证API密钥格式（OpenAI兼容）"""
    return (api_key.startswith("sk-proj-") and len(api_key) == 56) or \
        (api_key.startswith("sk-") and len(api_key) >= 50)


def verify_temporary_api_key(api_key: str) -> bool:
    """验证临时API密钥格式（tt_开头）"""
    return api_key.startswith("tt-") and len(api_key) == 51


def is_temporary_api_key(api_key: str) -> bool:
    """判断是否为临时API密钥"""
    return api_key.startswith("tt-")


def is_real_api_key(api_key: str) -> bool:
    """判断是否为真实API密钥"""
    return api_key.startswith("sk-")
