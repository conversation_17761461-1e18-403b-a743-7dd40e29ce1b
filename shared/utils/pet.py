import os
from typing import Optional

import httpx


async def get_pet_by_id(pet_id: int,authorization: str) -> Optional[dict]:
    """
    通过HTTP API从宠物服务获取宠物信息

    Args:
        pet_id: 宠物ID

    Returns:
        包含宠物信息的字典，如果找不到宠物则返回None
    """
    try:
        # 获取宠物服务URL
        pet_service_url = os.getenv("PET_SERVICE_URL", "http://localhost:8002")

        # 构建请求URL - 使用内部API端点
        url = f"{pet_service_url}/internal/pets/{pet_id}"

        print(f"正在请求宠物服务: {url}")

        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(url, headers=[("Authorization", authorization)])

            if response.status_code == 200:
                pet_data = response.json()
                print(f"宠物服务返回: {pet_data}")
                return pet_data.get("data")
            elif response.status_code == 404:
                print(f"宠物服务返回404: 宠物不存在")
                return None
            else:
                print(f"宠物服务返回错误: {response.status_code} - {response.text}")
                return None

    except httpx.RequestError as e:
        print(f"请求宠物服务失败: {e}")
        return None
    except Exception as e:
        print(f"获取宠物信息时出错: {e}")
        return None


async def save_or_update_by_tenant_pet_id(pet_data: dict) -> Optional[dict]:
    """
    通过HTTP API向宠物服务创建或更新宠物信息

    Args:
        pet_data: 包含宠物信息的字典，必须包含tenant_pet_id

    Returns:
        包含操作结果和宠物信息的字典，如果操作失败则返回None
    """
    try:
        # 获取宠物服务URL
        pet_service_url = os.getenv("PET_SERVICE_URL", "http://localhost:8002")

        # 构建请求URL - 使用内部API端点
        url = f"{pet_service_url}/internal/pets/save_or_update_by_tenant_pet_id"

        print(f"正在请求宠物服务: {url}")
        print(f"请求数据: {pet_data}")

        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.post(url, json=pet_data)

            if response.status_code == 200:
                result_data = response.json()
                print(f"宠物服务返回: {result_data}")
                return result_data
            else:
                print(f"宠物服务返回错误: {response.status_code} - {response.text}")
                return None

    except httpx.RequestError as e:
        print(f"请求宠物服务失败: {e}")
        return None
    except Exception as e:
        print(f"upsert宠物信息时出错: {e}")
        return None
