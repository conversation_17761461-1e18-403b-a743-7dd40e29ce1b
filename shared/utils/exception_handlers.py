"""
统一异常处理器

提供标准化的异常处理机制，确保所有微服务返回一致的错误响应格式。
支持FastAPI的HTTPException、Pydantic验证错误等常见异常类型。
"""
import traceback
from typing import Any, Dict, Optional

from fastapi import HTTPException, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError
from sqlalchemy.exc import IntegrityError, SQLAlchemyError

from shared.logging_config import get_logger
from shared.models.response import create_error_response, ErrorCode, ErrorMessage

logger = get_logger(__name__)


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """
    处理FastAPI的HTTPException

    Args:
        request: 请求对象
        exc: HTTP异常

    Returns:
        JSONResponse: 统一格式的错误响应
    """
    # 提取请求ID（如果存在）
    request_id = getattr(request.state, 'request_id', None)

    # 根据状态码确定错误代码
    error_code_map = {
        400: ErrorCode.INVALID_REQUEST,
        401: ErrorCode.UNAUTHORIZED,
        403: ErrorCode.FORBIDDEN,
        404: ErrorCode.NOT_FOUND,
        409: ErrorCode.CONFLICT,
        422: ErrorCode.VALIDATION_ERROR,
        500: ErrorCode.INTERNAL_ERROR,
        502: ErrorCode.SERVICE_UNAVAILABLE,
        503: ErrorCode.SERVICE_UNAVAILABLE,
        504: ErrorCode.TIMEOUT
    }

    error_code = error_code_map.get(exc.status_code, ErrorCode.INTERNAL_ERROR)

    # 处理detail字段（可能是字符串或字典）
    if isinstance(exc.detail, dict):
        msg = exc.detail.get('message', str(exc.detail))
        error_details = exc.detail
    else:
        msg = str(exc.detail)
        error_details = None

    # 记录错误日志
    logger.error("HTTP异常 {}: {} (请求ID: {})", exc.status_code, msg, request_id)

    # 创建统一错误响应
    error_response = create_error_response(
        msg=msg,
        error_code=error_code,
        error_details=error_details,
        request_id=request_id
    )

    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.dict()
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """
    处理Pydantic请求验证错误

    Args:
        request: 请求对象
        exc: 验证异常

    Returns:
        JSONResponse: 统一格式的错误响应
    """
    request_id = getattr(request.state, 'request_id', None)

    # 格式化验证错误信息
    error_details = []
    for error in exc.errors():
        error_details.append({
            "field": ".".join(str(loc) for loc in error["loc"]),
            "message": error["msg"],
            "type": error["type"]
        })

    msg = f"请求数据验证失败: {len(error_details)}个字段错误"

    # 记录错误日志
    logger.warning("请求验证失败: {} (请求ID: {})", msg, request_id)

    # 创建统一错误响应
    error_response = create_error_response(
        msg=msg,
        error_code=ErrorCode.VALIDATION_ERROR,
        error_details={"validation_errors": error_details},
        request_id=request_id
    )

    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=error_response.dict()
    )


async def pydantic_validation_exception_handler(request: Request, exc: ValidationError) -> JSONResponse:
    """
    处理Pydantic模型验证错误

    Args:
        request: 请求对象
        exc: Pydantic验证异常

    Returns:
        JSONResponse: 统一格式的错误响应
    """
    request_id = getattr(request.state, 'request_id', None)

    # 格式化验证错误信息
    error_details = []
    for error in exc.errors():
        error_details.append({
            "field": ".".join(str(loc) for loc in error["loc"]),
            "message": error["msg"],
            "type": error["type"]
        })

    msg = f"数据模型验证失败: {len(error_details)}个字段错误"

    # 记录错误日志
    logger.warning("Pydantic验证失败: {} (请求ID: {})", msg, request_id)

    # 创建统一错误响应
    error_response = create_error_response(
        msg=msg,
        error_code=ErrorCode.VALIDATION_ERROR,
        error_details={"validation_errors": error_details},
        request_id=request_id
    )

    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=error_response.dict()
    )


async def sqlalchemy_exception_handler(request: Request, exc: SQLAlchemyError) -> JSONResponse:
    """
    处理SQLAlchemy数据库错误

    Args:
        request: 请求对象
        exc: SQLAlchemy异常

    Returns:
        JSONResponse: 统一格式的错误响应
    """
    request_id = getattr(request.state, 'request_id', None)

    # 根据异常类型确定错误信息
    if isinstance(exc, IntegrityError):
        msg = "数据完整性约束违反"
        error_code = ErrorCode.CONFLICT
        status_code = status.HTTP_409_CONFLICT
    else:
        msg = "数据库操作失败"
        error_code = ErrorCode.INTERNAL_ERROR
        status_code = status.HTTP_500_INTERNAL_SERVER_ERROR

    # 记录错误日志（包含详细的异常信息）
    logger.error("数据库异常: {} (请求ID: {})", str(exc), request_id, exc_info=True)

    # 临时添加：打印到控制台以便调试
    print(f"🚨 数据库异常: {type(exc).__name__}: {str(exc)}")
    print(f"🔍 请求ID: {request_id}")
    import traceback
    traceback.print_exc()

    # 创建统一错误响应（不暴露数据库内部错误信息）
    error_response = create_error_response(
        msg=msg,
        error_code=error_code,
        request_id=request_id
    )

    return JSONResponse(
        status_code=status_code,
        content=error_response.dict()
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    处理所有未捕获的异常

    Args:
        request: 请求对象
        exc: 通用异常

    Returns:
        JSONResponse: 统一格式的错误响应
    """
    request_id = getattr(request.state, 'request_id', None)

    # 记录详细的错误日志（避免格式化问题）
    logger.error(
        "未处理的异常: {}: {} (请求ID: {})",
        type(exc).__name__, str(exc), request_id,
        exc_info=True
    )

    # 临时添加：打印到控制台以便调试
    print(f"🚨 未处理的异常: {type(exc).__name__}: {str(exc)}")
    print(f"🔍 请求ID: {request_id}")
    import traceback
    traceback.print_exc()

    # 创建统一错误响应（不暴露内部错误信息）
    error_response = create_error_response(
        msg=ErrorMessage.INTERNAL_ERROR,
        error_code=ErrorCode.INTERNAL_ERROR,
        request_id=request_id
    )

    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=error_response.dict()
    )


def register_exception_handlers(app):
    """
    注册所有异常处理器到FastAPI应用

    Args:
        app: FastAPI应用实例
    """
    # 注册各种异常处理器
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError,
                              validation_exception_handler)
    app.add_exception_handler(
        ValidationError, pydantic_validation_exception_handler)
    app.add_exception_handler(SQLAlchemyError, sqlalchemy_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)

    logger.info("已注册统一异常处理器")


# 自定义业务异常类
class BusinessException(Exception):
    """
    业务逻辑异常

    用于表示业务规则违反等可预期的错误情况
    """

    def __init__(
        self,
        message: str,
        error_code: str = ErrorCode.BUSINESS_ERROR,
        status_code: int = status.HTTP_400_BAD_REQUEST,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details
        super().__init__(message)


async def business_exception_handler(request: Request, exc: BusinessException) -> JSONResponse:
    """
    处理业务异常

    Args:
        request: 请求对象
        exc: 业务异常

    Returns:
        JSONResponse: 统一格式的错误响应
    """
    request_id = getattr(request.state, 'request_id', None)

    # 记录业务异常日志
    logger.warning("业务异常: {} (请求ID: {})", exc.message, request_id)

    # 创建统一错误响应
    error_response = create_error_response(
        msg=exc.message,
        error_code=exc.error_code,
        error_details=exc.details,
        request_id=request_id
    )

    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.dict()
    )


def register_all_exception_handlers(app):
    """
    注册所有异常处理器（包括自定义业务异常）

    Args:
        app: FastAPI应用实例
    """
    # 注册业务异常处理器
    app.add_exception_handler(BusinessException, business_exception_handler)

    # 注册其他异常处理器
    register_exception_handlers(app)

    logger.info("已注册所有异常处理器（包括业务异常）")
