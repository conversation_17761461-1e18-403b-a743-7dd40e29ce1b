"""
认证工具和依赖项
"""
import os
import httpx
from typing import Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from shared.database import get_async_db
from shared.utils.security import verify_token, verify_api_key
from shared.logging_config import get_logger

logger = get_logger(__name__)

security = HTTPBearer()


async def get_user_by_id_from_service(user_id: str) -> Optional[dict]:
    """通过HTTP API从用户服务获取用户信息"""
    try:
        # 获取用户服务URL
        user_service_url = os.getenv(
            "USER_SERVICE_URL", "http://localhost:8001")

        # 构建请求URL - 使用内部API端点
        url = f"{user_service_url}/internal/users/{user_id}"

        logger.debug(f"正在请求用户服务: {url}")

        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(url)

            if response.status_code == 200:
                user_data = response.json()
                logger.debug(f"用户服务返回用户ID: {user_data.get('id')}")
                return user_data
            elif response.status_code == 404:
                logger.warning("用户服务返回404: 用户不存在")
                return None
            else:
                logger.error(
                    f"用户服务返回错误: {response.status_code} - {response.text}")
                return None

    except httpx.RequestError as e:
        logger.error(f"请求用户服务失败: {e}")
        return None
    except Exception as e:
        logger.error(f"获取用户信息时出错: {e}")
        return None


class AuthenticationError(HTTPException):
    """Authentication error exception"""

    def __init__(self, detail: str = "Could not validate credentials"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"},
        )


class AuthorizationError(HTTPException):
    """Authorization error exception"""

    def __init__(self, detail: str = "Not enough permissions"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
        )


async def get_current_user_from_token(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_db)
):
    """从JWT令牌获取当前用户"""
    token = credentials.credentials

    # 使用AuthClient进行验证
    try:
        from shared.clients.auth_client import create_auth_client

        user_service_url = os.getenv(
            "USER_SERVICE_URL", "http://localhost:8001")
        auth_client = create_auth_client(user_service_url)

        user_info = await auth_client.verify_jwt_token(token, "access")

        if not user_info:
            raise AuthenticationError("无效的令牌或用户不存在")

        if not user_info.get('is_active'):
            raise AuthenticationError("用户未激活")

        return user_info
    except Exception as e:
        logger.error(f"JWT令牌验证失败: {e}")
        raise AuthenticationError(f"认证失败: {e}")


async def get_current_active_user(
    current_user=Depends(get_current_user_from_token)
):
    """获取当前活跃用户"""
    # 兼容字典和对象格式的用户数据
    is_active = current_user.get('is_active') if isinstance(
        current_user, dict) else current_user.is_active
    if not is_active:
        raise AuthenticationError("用户未激活")
    return current_user


async def get_current_superuser(
    current_user=Depends(get_current_active_user)
):
    """获取当前超级用户"""
    # 兼容字典和对象格式的用户数据
    is_superuser = current_user.get('is_superuser') if isinstance(
        current_user, dict) else current_user.is_superuser
    if not is_superuser:
        raise AuthorizationError("权限不足")
    return current_user


async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(
        HTTPBearer(auto_error=False))
):
    """获取当前用户（可选）- 如果没有有效凭据则返回None"""
    if not credentials:
        return None

    try:
        token = credentials.credentials
        logger.debug(f"尝试验证令牌: {token[:20]}...")

        # 首先尝试JWT令牌验证
        payload = verify_token(token, "access")
        if payload:
            user_id = payload.get("sub")
            if user_id:
                try:
                    logger.debug(f"正在通过API查找用户ID: {user_id}")
                    user_info = await get_user_by_id_from_service(user_id)

                    if user_info:
                        if user_info.get('is_active'):
                            logger.debug(
                                f"用户验证成功: {user_info.get('username')}")
                            return user_info
                        else:
                            logger.warning("用户未激活")
                    else:
                        logger.warning(f"未找到用户ID: {user_id}")
                except Exception as e:
                    logger.error(f"查找用户时出错: {e}")

        # 如果JWT验证失败，尝试API密钥验证
        from shared.utils.security import is_temporary_api_key, verify_temporary_api_key
        from shared.clients.auth_client import create_auth_client

        user_service_url = os.getenv("USER_SERVICE_URL", "http://localhost:8001")
        auth_client = create_auth_client(user_service_url)

        # 首先检查是否为临时API密钥
        if is_temporary_api_key(token) and verify_temporary_api_key(token):
            logger.debug(f"尝试验证临时API密钥: {token[:20]}...")
            temp_key_data = await auth_client.verify_temporary_api_key(token)
            if temp_key_data:
                # 返回真实API密钥和用户信息，并标记为临时密钥认证
                user_info = temp_key_data.get("user_info", {})
                user_info['current_api_key'] = temp_key_data.get("real_api_key")
                user_info['temp_api_key'] = token
                user_info['auth_type'] = 'temporary_api_key'
                logger.debug(f"临时API密钥验证成功: {user_info.get('username')}")
                return user_info

        # 然后尝试真实API密钥验证
        elif verify_api_key(token):
            logger.debug(f"尝试验证真实API密钥: {token[:20]}...")
            user = await auth_client.verify_api_key(token)
            if user:
                # 添加当前API密钥信息
                user['current_api_key'] = token
                user['auth_type'] = 'api_key'
                logger.debug(f"真实API密钥验证成功: {user.get('username')}")
                return user

        return None

    except Exception as e:
        logger.error(f"可选用户验证失败: {e}")
        return None


async def get_current_user_from_api_key(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """从API密钥获取当前用户（支持真实API密钥和临时API密钥）"""
    if not credentials:
        raise AuthenticationError("缺少认证凭据")

    try:
        api_key = credentials.credentials
        logger.debug(f"尝试验证API密钥: {api_key[:20]}...")

        from shared.utils.security import is_temporary_api_key, verify_temporary_api_key
        from shared.clients.auth_client import create_auth_client

        user_service_url = os.getenv("USER_SERVICE_URL", "http://localhost:8001")
        auth_client = create_auth_client(user_service_url)

        # 首先检查是否为临时API密钥
        if is_temporary_api_key(api_key):
            if not verify_temporary_api_key(api_key):
                raise AuthenticationError("无效的临时API密钥格式")

            logger.debug(f"验证临时API密钥: {api_key[:20]}...")
            temp_key_data = await auth_client.verify_temporary_api_key(api_key)
            if not temp_key_data:
                raise AuthenticationError("无效的临时API密钥或已过期")

            user_info = temp_key_data.get("user_info", {})
            if not user_info.get('is_active'):
                raise AuthenticationError("用户未激活")

            # 添加API密钥信息到用户信息中
            user_info['current_api_key'] = temp_key_data.get("real_api_key")
            user_info['temp_api_key'] = api_key
            user_info['auth_type'] = 'temporary_api_key'
            logger.debug(f"临时API密钥验证成功: {user_info.get('username')}")
            return user_info

        # 然后验证真实API密钥
        else:
            if not verify_api_key(api_key):
                raise AuthenticationError("无效的API密钥格式")

            logger.debug(f"验证真实API密钥: {api_key[:20]}...")
            user_info = await auth_client.verify_api_key(api_key)
            if not user_info:
                raise AuthenticationError("无效的API密钥或用户不存在")

            if not user_info.get('is_active'):
                raise AuthenticationError("用户未激活")

            # 添加API密钥信息到用户信息中
            user_info['current_api_key'] = api_key
            user_info['auth_type'] = 'api_key'
            logger.debug(f"真实API密钥验证成功: {user_info.get('username')}")
            return user_info

    except AuthenticationError:
        raise
    except Exception as e:
        logger.error(f"API密钥验证失败: {e}")
        raise AuthenticationError(f"认证失败: {e}")


async def get_current_active_user_from_api_key(
    current_user=Depends(get_current_user_from_api_key)
):
    """获取当前活跃用户（只支持API密钥认证）"""
    # 兼容字典和对象格式的用户数据
    is_active = current_user.get('is_active') if isinstance(
        current_user, dict) else current_user.is_active
    if not is_active:
        raise AuthenticationError("用户未激活")
    return current_user
