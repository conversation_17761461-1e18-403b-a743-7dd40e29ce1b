"""
图片处理工具类
"""
import base64
import httpx
import io
from typing import Optional, <PERSON>ple
from PIL import Image

from shared.logging_config import get_logger

logger = get_logger(__name__)


class ImageUtils:
    """图片处理工具类"""
    
    @staticmethod
    async def url_to_base64(
        url: str, 
        timeout: int = 30,
        max_size_mb: int = 10
    ) -> Optional[str]:
        """
        将在线URL图片转换为base64格式
        
        Args:
            url: 图片URL地址
            timeout: 请求超时时间（秒）
            max_size_mb: 最大文件大小限制（MB）
            
        Returns:
            base64编码的图片字符串，失败时返回None
        """
        try:
            logger.info(f"开始下载图片: {url}")
            
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.get(url)
                response.raise_for_status()
                
                # 检查内容类型
                content_type = response.headers.get('content-type', '')
                if not content_type.startswith('image/'):
                    logger.error(f"URL不是图片类型: {content_type}")
                    return None
                
                # 检查文件大小
                content_length = len(response.content)
                max_size_bytes = max_size_mb * 1024 * 1024
                if content_length > max_size_bytes:
                    logger.error(f"图片文件过大: {content_length} bytes > {max_size_bytes} bytes")
                    return None
                
                # 转换为base64
                base64_string = base64.b64encode(response.content).decode('utf-8')
                logger.info(f"图片转换成功，大小: {content_length} bytes")
                
                return base64_string
                
        except httpx.TimeoutException:
            logger.error(f"下载图片超时: {url}")
            return None
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP错误 {e.response.status_code}: {url}")
            return None
        except Exception as e:
            logger.error(f"下载图片失败: {url}, 错误: {str(e)}")
            return None
    
    @staticmethod
    async def url_to_base64_with_format(
        url: str, 
        timeout: int = 30,
        max_size_mb: int = 10
    ) -> Optional[str]:
        """
        将在线URL图片转换为带格式前缀的base64格式（data:image/xxx;base64,xxx）
        
        Args:
            url: 图片URL地址
            timeout: 请求超时时间（秒）
            max_size_mb: 最大文件大小限制（MB）
            
        Returns:
            带格式前缀的base64编码字符串，失败时返回None
        """
        try:
            logger.info(f"开始下载图片: {url}")
            
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.get(url)
                response.raise_for_status()
                
                # 检查内容类型
                content_type = response.headers.get('content-type', '')
                if not content_type.startswith('image/'):
                    logger.error(f"URL不是图片类型: {content_type}")
                    return None
                
                # 检查文件大小
                content_length = len(response.content)
                max_size_bytes = max_size_mb * 1024 * 1024
                if content_length > max_size_bytes:
                    logger.error(f"图片文件过大: {content_length} bytes > {max_size_bytes} bytes")
                    return None
                
                # 转换为base64
                base64_string = base64.b64encode(response.content).decode('utf-8')
                
                # 添加格式前缀
                formatted_base64 = f"data:{content_type};base64,{base64_string}"
                
                logger.info(f"图片转换成功，大小: {content_length} bytes")
                
                return formatted_base64
                
        except httpx.TimeoutException:
            logger.error(f"下载图片超时: {url}")
            return None
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP错误 {e.response.status_code}: {url}")
            return None
        except Exception as e:
            logger.error(f"下载图片失败: {url}, 错误: {str(e)}")
            return None
    
    @staticmethod
    def base64_to_image(base64_string: str) -> Optional[Image.Image]:
        """
        将base64字符串转换为PIL Image对象
        
        Args:
            base64_string: base64编码的图片字符串
            
        Returns:
            PIL Image对象，失败时返回None
        """
        try:
            # 如果包含格式前缀，去除前缀
            if base64_string.startswith('data:'):
                base64_string = base64_string.split(',')[1]
            
            # 解码base64
            image_data = base64.b64decode(base64_string)
            
            # 转换为PIL Image
            image = Image.open(io.BytesIO(image_data))
            
            logger.info(f"base64转换为图片成功，尺寸: {image.size}")
            
            return image
            
        except Exception as e:
            logger.error(f"base64转换为图片失败: {str(e)}")
            return None
    
    @staticmethod
    def get_image_info(base64_string: str) -> Optional[dict]:
        """
        获取base64图片的基本信息
        
        Args:
            base64_string: base64编码的图片字符串
            
        Returns:
            包含图片信息的字典，失败时返回None
        """
        try:
            image = ImageUtils.base64_to_image(base64_string)
            if image is None:
                return None
            
            info = {
                'width': image.width,
                'height': image.height,
                'format': image.format,
                'mode': image.mode,
                'size_bytes': len(base64.b64decode(base64_string.split(',')[1] if base64_string.startswith('data:') else base64_string))
            }
            
            logger.info(f"获取图片信息成功: {info}")
            
            return info
            
        except Exception as e:
            logger.error(f"获取图片信息失败: {str(e)}")
            return None
    
    @staticmethod
    async def resize_image_from_url(
        url: str,
        max_width: int = 1024,
        max_height: int = 1024,
        quality: int = 85,
        timeout: int = 30
    ) -> Optional[str]:
        """
        从URL下载图片并调整大小后转换为base64
        
        Args:
            url: 图片URL地址
            max_width: 最大宽度
            max_height: 最大高度
            quality: JPEG质量（1-100）
            timeout: 请求超时时间（秒）
            
        Returns:
            调整大小后的base64编码字符串，失败时返回None
        """
        try:
            # 先获取原始base64
            base64_string = await ImageUtils.url_to_base64(url, timeout)
            if base64_string is None:
                return None
            
            # 转换为PIL Image
            image = ImageUtils.base64_to_image(base64_string)
            if image is None:
                return None
            
            # 计算新尺寸（保持宽高比）
            width, height = image.size
            if width <= max_width and height <= max_height:
                # 不需要调整大小
                return base64_string
            
            ratio = min(max_width / width, max_height / height)
            new_width = int(width * ratio)
            new_height = int(height * ratio)
            
            # 调整大小
            resized_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # 转换回base64
            buffer = io.BytesIO()
            format_name = image.format if image.format else 'JPEG'
            if format_name == 'JPEG':
                resized_image.save(buffer, format=format_name, quality=quality)
            else:
                resized_image.save(buffer, format=format_name)
            
            resized_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            logger.info(f"图片调整大小成功: {width}x{height} -> {new_width}x{new_height}")
            
            return resized_base64
            
        except Exception as e:
            logger.error(f"调整图片大小失败: {str(e)}")
            return None
