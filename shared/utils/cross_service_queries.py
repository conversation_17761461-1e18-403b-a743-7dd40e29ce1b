"""
跨微服务查询工具
用于处理微服务间的数据关联查询，替代SQLAlchemy的relationship

⚠️ 警告：此模块违反微服务架构原则，计划在下个版本中废弃
建议使用HTTP API调用替代直接数据库查询
"""
import warnings
from typing import List, Dict, Any, Optional, Union
from sqlalchemy import text, select
from sqlalchemy.ext.asyncio import AsyncSession

from shared.logging_config import get_logger

logger = get_logger(__name__)

# 发出废弃警告
warnings.warn(
    "cross_service_queries模块违反微服务架构原则，将在下个版本中移除。"
    "请使用HTTP API调用替代直接数据库查询。详见文档：docs/microservices-architecture-refactoring.md",
    DeprecationWarning,
    stacklevel=2
)


class CrossServiceQueryHelper:
    """跨服务查询助手"""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_user_permissions(self, user_id: int) -> List[Dict[str, Any]]:
        """获取用户权限列表"""
        try:
            # 通过联表查询获取用户的所有权限
            # 使用原生SQL避免跨服务的ORM关系依赖
            query = text("""
                SELECT p.id, p.name, p.display_name, p.description, p.category, p.is_system
                FROM permissions p
                INNER JOIN user_permissions up ON p.id = up.permission_id
                WHERE up.user_id = :user_id
            """)

            result = await self.db.execute(query, {"user_id": user_id})
            permissions = []
            # 将查询结果转换为字典格式，便于JSON序列化
            for row in result:
                permissions.append({
                    "id": row.id,
                    "name": row.name,
                    "display_name": row.display_name,
                    "description": row.description,
                    "category": row.category,
                    "is_system": row.is_system
                })

            return permissions

        except Exception as e:
            logger.error(f"获取用户权限失败: {e}")
            return []

    async def get_users_by_permission(self, permission_name: str) -> List[Dict[str, Any]]:
        """根据权限名称获取用户列表"""
        try:
            query = text("""
                SELECT u.id, u.username, u.email, u.is_active, u.is_superuser
                FROM users u
                INNER JOIN user_permissions up ON u.id = up.user_id
                INNER JOIN permissions p ON up.permission_id = p.id
                WHERE p.name = :permission_name AND u.is_active = true
            """)

            result = await self.db.execute(query, {"permission_name": permission_name})
            users = []
            for row in result:
                users.append({
                    "id": row.id,
                    "username": row.username,
                    "email": row.email,
                    "is_active": row.is_active,
                    "is_superuser": row.is_superuser
                })

            return users

        except Exception as e:
            logger.error(f"根据权限获取用户失败: {e}")
            return []

    async def get_user_pets(self, user_id: int) -> List[Dict[str, Any]]:
        """获取用户的宠物列表"""
        try:
            query = text("""
                SELECT id, name, species, breed, gender, birth_date, 
                       weight, status, is_neutered, is_vaccinated, avatar_url
                FROM pets 
                WHERE owner_id = :user_id
                ORDER BY created_at DESC
            """)

            result = await self.db.execute(query, {"user_id": user_id})
            pets = []
            for row in result:
                pets.append({
                    "id": row.id,
                    "name": row.name,
                    "species": row.species,
                    "breed": row.breed,
                    "gender": row.gender,
                    "birth_date": row.birth_date,
                    "weight": row.weight,
                    "status": row.status,
                    "is_neutered": row.is_neutered,
                    "is_vaccinated": row.is_vaccinated,
                    "avatar_url": row.avatar_url
                })

            return pets

        except Exception as e:
            logger.error(f"获取用户宠物失败: {e}")
            return []

    async def get_user_agents(self, user_id: int) -> List[Dict[str, Any]]:
        """获取用户的智能体列表"""
        try:
            query = text("""
                SELECT id, name, display_name, description, agent_type, 
                       system_category, status, is_public, usage_count
                FROM agents 
                WHERE owner_id = :user_id
                ORDER BY created_at DESC
            """)

            result = await self.db.execute(query, {"user_id": user_id})
            agents = []
            for row in result:
                agents.append({
                    "id": row.id,
                    "name": row.name,
                    "display_name": row.display_name,
                    "description": row.description,
                    "agent_type": row.agent_type,
                    "system_category": row.system_category,
                    "status": row.status,
                    "is_public": row.is_public,
                    "usage_count": row.usage_count
                })

            return agents

        except Exception as e:
            logger.error(f"获取用户智能体失败: {e}")
            return []

    async def get_pet_with_owner(self, pet_id: int) -> Optional[Dict[str, Any]]:
        """
        获取宠物及其主人信息

        ⚠️ 废弃警告：此方法违反微服务架构原则
        建议替代方案：
        1. 先调用宠物服务API获取宠物信息
        2. 再调用用户服务API获取主人信息
        """
        logger.warning("get_pet_with_owner方法已废弃，请使用微服务API调用替代")
        try:
            query = text("""
                SELECT p.id as pet_id, p.name as pet_name, p.species, p.breed,
                       u.id as owner_id, u.username, u.email, u.first_name, u.last_name
                FROM pets p
                INNER JOIN users u ON p.owner_id = u.id
                WHERE p.id = :pet_id
            """)

            result = await self.db.execute(query, {"pet_id": pet_id})
            row = result.first()

            if row:
                return {
                    "pet": {
                        "id": row.pet_id,
                        "name": row.pet_name,
                        "species": row.species,
                        "breed": row.breed
                    },
                    "owner": {
                        "id": row.owner_id,
                        "username": row.username,
                        "email": row.email,
                        "first_name": row.first_name,
                        "last_name": row.last_name
                    }
                }

            return None

        except Exception as e:
            logger.error(f"获取宠物及主人信息失败: {e}")
            return None

    async def get_agent_with_owner(self, agent_id: int) -> Optional[Dict[str, Any]]:
        """获取智能体及其创建者信息"""
        try:
            query = text("""
                SELECT a.id as agent_id, a.name as agent_name, a.display_name, a.description,
                       u.id as owner_id, u.username, u.email
                FROM agents a
                INNER JOIN users u ON a.owner_id = u.id
                WHERE a.id = :agent_id
            """)

            result = await self.db.execute(query, {"agent_id": agent_id})
            row = result.first()

            if row:
                return {
                    "agent": {
                        "id": row.agent_id,
                        "name": row.agent_name,
                        "display_name": row.display_name,
                        "description": row.description
                    },
                    "owner": {
                        "id": row.owner_id,
                        "username": row.username,
                        "email": row.email
                    }
                }

            return None

        except Exception as e:
            logger.error(f"获取智能体及创建者信息失败: {e}")
            return None

    async def check_user_has_permission(self, user_id: int, permission_name: str) -> bool:
        """检查用户是否有特定权限"""
        try:
            # 首先检查是否是超级用户，超级用户拥有所有权限
            user_query = text(
                "SELECT is_superuser FROM users WHERE id = :user_id")
            user_result = await self.db.execute(user_query, {"user_id": user_id})
            user_row = user_result.first()

            if user_row and user_row.is_superuser:
                return True

            # 检查用户是否具有指定的具体权限
            # 通过联表查询user_permissions和permissions表
            query = text("""
                SELECT 1 FROM user_permissions up
                INNER JOIN permissions p ON up.permission_id = p.id
                WHERE up.user_id = :user_id AND p.name = :permission_name
                LIMIT 1
            """)

            result = await self.db.execute(query, {
                "user_id": user_id,
                "permission_name": permission_name
            })

            # 如果查询到结果，说明用户有该权限
            return result.first() is not None

        except Exception as e:
            logger.error(f"检查用户权限失败: {e}")
            return False

    async def get_user_summary(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        获取用户汇总信息（包含宠物和智能体数量）

        ⚠️ 废弃警告：此方法违反微服务架构原则
        建议替代方案：
        1. 调用用户服务API获取用户基本信息
        2. 调用宠物服务API获取宠物数量
        3. 调用应用服务API获取智能体数量
        """
        logger.warning("get_user_summary方法已废弃，请使用微服务API调用替代")
        try:
            # 使用子查询统计用户相关的各种数据数量
            # 这种方式避免了多次查询，提高了性能
            query = text("""
                SELECT
                    u.id, u.username, u.email, u.first_name, u.last_name,
                    u.is_active, u.is_superuser,
                    (SELECT COUNT(*) FROM pets WHERE owner_id = u.id) as pet_count,
                    (SELECT COUNT(*) FROM agents WHERE owner_id = u.id) as agent_count,
                    (SELECT COUNT(*) FROM user_permissions WHERE user_id = u.id) as permission_count
                FROM users u
                WHERE u.id = :user_id
            """)

            result = await self.db.execute(query, {"user_id": user_id})
            row = result.first()

            if row:
                # 构建用户汇总信息字典
                return {
                    "id": row.id,
                    "username": row.username,
                    "email": row.email,
                    "first_name": row.first_name,
                    "last_name": row.last_name,
                    "is_active": row.is_active,
                    "is_superuser": row.is_superuser,
                    "pet_count": row.pet_count,
                    "agent_count": row.agent_count,
                    "permission_count": row.permission_count
                }

            return None

        except Exception as e:
            logger.error(f"获取用户汇总信息失败: {e}")
            return None


# 便捷函数
async def get_cross_service_helper(db: AsyncSession) -> CrossServiceQueryHelper:
    """获取跨服务查询助手实例"""
    return CrossServiceQueryHelper(db)
