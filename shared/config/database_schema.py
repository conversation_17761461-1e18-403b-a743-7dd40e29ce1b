"""
数据库Schema配置管理模块

统一管理整个项目中所有SQLAlchemy模型的schema配置，
确保所有微服务使用一致的数据库schema设置。
"""
import os
from typing import Dict, Any, Tuple, Union


def get_database_schema() -> str:
    """
    获取数据库schema名称
    
    从环境变量DATABASE_SCHEMA读取，默认为'vet'
    
    Returns:
        str: 数据库schema名称
    """
    return os.getenv("DATABASE_SCHEMA", "vet")


def create_table_args(*args, **kwargs) -> Tuple:
    """
    创建包含schema配置的__table_args__
    
    这个函数会自动添加schema配置到__table_args__中，
    同时保留其他的索引、约束等配置。
    
    Args:
        *args: 其他table_args参数（如Index、UniqueConstraint等）
        **kwargs: 额外的配置参数
        
    Returns:
        tuple: 包含schema配置的完整__table_args__
        
    Examples:
        # 只有schema配置
        __table_args__ = create_table_args()
        
        # 包含索引的配置
        __table_args__ = create_table_args(
            Index('ix_user_email', 'email'),
            UniqueConstraint('username', name='uq_username')
        )
        
        # 包含其他配置的情况
        __table_args__ = create_table_args(
            Index('ix_user_email', 'email'),
            mysql_engine='InnoDB'
        )
    """
    schema = get_database_schema()
    
    # 构建配置字典
    config_dict = {'schema': schema}
    
    # 如果有额外的kwargs配置，添加到配置字典中
    if kwargs:
        config_dict.update(kwargs)
    
    # 如果有其他参数（索引、约束等），将它们和配置字典组合
    if args:
        return args + (config_dict,)
    else:
        return (config_dict,)


def get_foreign_key_with_schema(table_name: str, column_name: str = "id") -> str:
    """
    获取包含schema的外键引用字符串
    
    Args:
        table_name: 目标表名
        column_name: 目标列名，默认为'id'
        
    Returns:
        str: 格式为 'schema.table_name.column_name' 的外键引用
        
    Examples:
        # 引用users表的id列
        user_id = Column(Integer, ForeignKey(get_foreign_key_with_schema('users')))
        
        # 引用agents表的id列
        agent_id = Column(Integer, ForeignKey(get_foreign_key_with_schema('agents')))
    """
    schema = get_database_schema()
    return f"{schema}.{table_name}.{column_name}"


def get_table_name_with_schema(table_name: str) -> str:
    """
    获取包含schema的完整表名
    
    Args:
        table_name: 表名
        
    Returns:
        str: 格式为 'schema.table_name' 的完整表名
    """
    schema = get_database_schema()
    return f"{schema}.{table_name}"


# 为了向后兼容，提供一些别名函数
def get_schema() -> str:
    """获取数据库schema名称（别名函数）"""
    return get_database_schema()


def table_args(*args, **kwargs) -> Tuple:
    """创建__table_args__（别名函数）"""
    return create_table_args(*args, **kwargs)


def fk_ref(table_name: str, column_name: str = "id") -> str:
    """获取外键引用（别名函数）"""
    return get_foreign_key_with_schema(table_name, column_name)


# 常用的表名常量（可选，用于避免硬编码表名）
class TableNames:
    """常用表名常量"""
    # User Service
    USERS = "users"
    TENANTS = "tenants"
    PERMISSIONS = "permissions"
    API_KEYS = "api_keys"
    USER_SESSIONS = "user_sessions"
    TENANT_ROLES = "tenant_roles"
    
    # Pet Service
    PETS = "pets"
    BREEDS = "breeds"
    MEDICAL_RECORDS = "medical_records"
    VACCINATIONS = "vaccinations"
    
    # App Service
    AGENTS = "agents"
    AGENT_PERMISSIONS = "agent_permissions"
    AGENT_EXECUTIONS = "agent_executions"
    CONVERSATIONS = "conversations"
    MESSAGES = "messages"
    CONVERSATION_CONTEXTS = "conversation_contexts"
    
    # Gateway
    API_REQUEST_LOGS = "api_request_logs"


# 导出主要函数
__all__ = [
    'get_database_schema',
    'create_table_args',
    'get_foreign_key_with_schema',
    'get_table_name_with_schema',
    'get_schema',
    'table_args',
    'fk_ref',
    'TableNames'
]
