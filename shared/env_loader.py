"""
简化的环境配置加载器

通过环境变量 ENVIRONMENT 自动加载对应的配置文件
支持的环境: development, testing, staging, production
默认环境: development

使用方法:
    from shared.env_loader import load_env
    load_env()  # 自动根据ENVIRONMENT环境变量加载对应配置
"""

import os
from pathlib import Path
from typing import Optional

try:
    from dotenv import load_dotenv
except ImportError:
    print("⚠️ python-dotenv未安装，请运行: pip install python-dotenv")
    load_dotenv = None

# 全局标志，避免重复加载和打印
_env_loaded = False
_current_environment = None


def load_env(environment: Optional[str] = None, project_root: Optional[Path] = None, force_reload: bool = False) -> str:
    """
    加载环境配置文件

    Args:
        environment: 指定环境名称，如果不指定则从环境变量ENVIRONMENT获取
        project_root: 项目根目录，默认自动检测
        force_reload: 是否强制重新加载，默认False

    Returns:
        str: 实际加载的环境名称
    """
    global _env_loaded, _current_environment

    # 如果已经加载过且不强制重新加载，直接返回当前环境
    if _env_loaded and not force_reload and _current_environment:
        return _current_environment

    if load_dotenv is None:
        print("❌ 无法加载环境配置，python-dotenv未安装")
        return "unknown"

    # 确定项目根目录
    if project_root is None:
        # 从当前文件向上查找项目根目录
        current_path = Path(__file__).parent
        while current_path.parent != current_path:
            if (current_path / ".env").exists():
                project_root = current_path
                break
            current_path = current_path.parent
        else:
            project_root = Path(__file__).parent.parent

    # 确定环境名称
    if environment is None:
        # 优先从环境变量获取
        environment = os.getenv("ENVIRONMENT")

        # 如果环境变量没有设置，尝试从默认.env文件读取
        if not environment:
            default_env_file = project_root / ".env"
            if default_env_file.exists():
                try:
                    with open(default_env_file, 'r', encoding='utf-8') as f:
                        for line in f:
                            line = line.strip()
                            if line.startswith("ENVIRONMENT="):
                                environment = line.split(
                                    "=", 1)[1].strip().strip('"\'')
                                break
                except Exception:
                    pass

        # 默认环境
        if not environment:
            environment = "development"

    # 支持的环境列表
    supported_environments = ["development",
                              "testing", "staging", "production"]
    if environment not in supported_environments:
        print(f"⚠️ 不支持的环境: {environment}, 使用默认环境: development")
        environment = "development"

    # 查找配置文件
    config_files = [
        project_root / f".env.{environment}",  # 项目根目录的环境配置
        project_root / ".env"  # 默认配置文件
    ]

    loaded_file = None
    for config_file in config_files:
        if config_file.exists():
            try:
                load_dotenv(config_file, override=True)
                loaded_file = config_file
                break
            except Exception as e:
                print(f"⚠️ 加载配置文件失败: {config_file}, 错误: {e}")
                continue

    # 只在首次加载或强制重新加载时打印消息，并且考虑reload模式
    if (not _env_loaded or force_reload):
        if loaded_file:
            print(f"✅ 已加载 {environment} 环境配置: {loaded_file}")
        else:
            print(f"⚠️ 未找到 {environment} 环境配置文件，使用系统环境变量")

    # 设置环境变量以便其他代码识别当前环境
    os.environ["ENVIRONMENT"] = environment

    # 标记为已加载
    _env_loaded = True
    _current_environment = environment

    return environment


def get_current_environment() -> str:
    """获取当前环境名称"""
    global _current_environment
    if _current_environment:
        return _current_environment
    return os.getenv("ENVIRONMENT", "development")


def is_development() -> bool:
    """判断是否为开发环境"""
    return get_current_environment() == "development"


def is_testing() -> bool:
    """判断是否为测试环境"""
    return get_current_environment() == "testing"


def is_staging() -> bool:
    """判断是否为预发布环境"""
    return get_current_environment() == "staging"


def is_production() -> bool:
    """判断是否为生产环境"""
    return get_current_environment() == "production"


# 自动加载环境配置（当模块被导入时）
# 这样其他模块只需要 import shared.env_loader 就会自动加载配置
_current_env = load_env()


if __name__ == "__main__":
    # 命令行测试
    import sys
    # export ENVIRONMENT=development
    # export ENVIRONMENT=production

    if len(sys.argv) > 1:
        env = sys.argv[1]
        loaded_env = load_env(env)
        print(f"指定环境: {env}")
        print(f"实际加载: {loaded_env}")
    else:
        print(f"当前环境: {get_current_environment()}")
        print(f"是否开发环境: {is_development()}")
        print(f"是否生产环境: {is_production()}")
