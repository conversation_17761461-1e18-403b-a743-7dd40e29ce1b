# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
app/logs/*
logs/
logs/*.log
logs/*.zip

# Local development settings
# 但保留示例文件
!.env.example

data/ai_vet.db*

.chainlit/
.files/

*.db


uvicorn.pid

uploads/*

logs/*
run/*
/md/
