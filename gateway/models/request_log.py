"""
API请求日志数据模型

该模块定义了API请求日志的数据库模型，用于存储所有通过网关的请求记录。
支持详细的请求分析、用户行为追踪、性能监控和计费统计。
"""
import uuid
from datetime import datetime, timezone
from enum import Enum
from typing import Optional

from sqlalchemy import (
    Boolean, Column, DateTime, Float, Integer, String, Text,
    Index, Enum as SQLEnum, JSON
)
from sqlalchemy.dialects.postgresql import UUID, INET
from sqlalchemy.sql import func

from shared.models.base import BaseModel
from shared.config.database_schema import create_table_args


class RequestType(str, Enum):
    """请求类型枚举"""
    BUSINESS_API = "business_api"      # 业务API请求 (/api/v1/*)
    OPENAI_API = "openai_api"          # OpenAI兼容API请求 (/v1/*)
    PUBLIC_API = "public_api"          # 公开API请求 (健康检查等)
    STATIC_RESOURCE = "static"         # 静态资源请求


class AuthType(str, Enum):
    """认证类型枚举"""
    JWT_TOKEN = "jwt_token"            # JWT令牌认证
    API_KEY = "api_key"                # API密钥认证
    PUBLIC = "public"                  # 公开访问（无需认证）


class APIRequestLog(BaseModel):
    """API请求日志模型

    记录所有通过网关的API请求详细信息，用于：
    - API使用量统计和分析
    - 用户行为追踪
    - 性能监控和优化
    - 计费数据收集
    - 安全审计和问题排查
    """

    __tablename__ = "api_request_logs"

    # 请求唯一标识
    request_id = Column(UUID(as_uuid=True), default=uuid.uuid4,
                        unique=True, index=True, nullable=False, comment="请求唯一ID")

    # 时间信息
    request_start_time = Column(DateTime(
        timezone=True), server_default=func.now(), nullable=False, comment="请求开始时间")
    request_end_time = Column(DateTime(timezone=True),
                              nullable=True, comment="请求结束时间")
    processing_time_ms = Column(Float, nullable=True, comment="处理耗时(毫秒)")

    # 请求基础信息
    method = Column(String(10), nullable=False, index=True, comment="HTTP方法")
    path = Column(String(500), nullable=False, index=True, comment="请求路径")
    full_url = Column(Text, nullable=True, comment="完整URL")
    query_params = Column(JSON, nullable=True, comment="查询参数")

    # 请求分类
    request_type = Column(SQLEnum(RequestType),
                          nullable=False, index=True, comment="请求类型")

    # 客户端信息
    client_ip = Column(INET, nullable=True, index=True, comment="客户端IP地址")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    referer = Column(String(500), nullable=True, comment="来源页面")

    # 认证信息
    auth_type = Column(SQLEnum(AuthType), nullable=True,
                       index=True, comment="认证类型")
    user_id = Column(Integer, nullable=True, index=True, comment="用户ID")
    user_email = Column(String(255), nullable=True, index=True, comment="用户邮箱")
    api_key_id = Column(Integer, nullable=True, index=True, comment="API密钥ID")
    api_key_name = Column(String(100), nullable=True, comment="API密钥名称")

    # 请求内容信息
    content_type = Column(String(100), nullable=True, comment="请求内容类型")
    content_length = Column(Integer, nullable=True, comment="请求内容长度")

    # 响应信息
    status_code = Column(Integer, nullable=True, index=True, comment="HTTP状态码")
    response_content_type = Column(
        String(100), nullable=True, comment="响应内容类型")
    response_content_length = Column(Integer, nullable=True, comment="响应内容长度")

    # 错误信息
    error_type = Column(String(100), nullable=True, index=True, comment="错误类型")
    error_message = Column(Text, nullable=True, comment="错误消息")

    # 业务相关信息
    is_successful = Column(Boolean, nullable=True,
                           index=True, comment="请求是否成功")
    is_cached = Column(Boolean, default=False, comment="响应是否来自缓存")

    # 额外的元数据
    extra_metadata = Column(JSON, nullable=True, comment="额外的元数据信息")

    # 索引定义
    __table_args__ = create_table_args(
        # 复合索引用于常见查询
        Index('idx_user_time', 'user_id', 'request_start_time'),
        Index('idx_api_key_time', 'api_key_id', 'request_start_time'),
        Index('idx_type_time', 'request_type', 'request_start_time'),
        Index('idx_status_time', 'status_code', 'request_start_time'),
        Index('idx_ip_time', 'client_ip', 'request_start_time'),
        Index('idx_path_time', 'path', 'request_start_time'),

        # 用于性能分析的索引
        Index('idx_processing_time', 'processing_time_ms'),
        Index('idx_error_analysis', 'error_type',
              'status_code', 'request_start_time'),

        # 用于计费统计的索引
        Index('idx_billing_analysis', 'user_id', 'request_type',
              'is_successful', 'request_start_time'),

        # 分区键（如果使用分区表）
        Index('idx_partition_key', 'request_start_time'),
    )

    def __repr__(self):
        return (
            f"<APIRequestLog(id={self.id}, request_id='{self.request_id}', "
            f"method='{self.method}', path='{self.path}', "
            f"status_code={self.status_code}, user_id={self.user_id})>"
        )

    @property
    def is_openai_request(self) -> bool:
        """判断是否为OpenAI兼容API请求"""
        return self.request_type == RequestType.OPENAI_API

    @property
    def is_business_request(self) -> bool:
        """判断是否为业务API请求"""
        return self.request_type == RequestType.BUSINESS_API

    @property
    def processing_time_seconds(self) -> Optional[float]:
        """获取处理时间（秒）"""
        if self.processing_time_ms is not None:
            return self.processing_time_ms / 1000.0
        return None

    @property
    def duration(self) -> Optional[float]:
        """获取请求持续时间（秒）"""
        if self.request_start_time and self.request_end_time:
            delta = self.request_end_time - self.request_start_time
            return delta.total_seconds()
        return None

    def to_dict(self) -> dict:
        """转换为字典格式"""
        result = super().to_dict()

        # 转换特殊类型
        if self.request_id:
            result['request_id'] = str(self.request_id)
        if self.client_ip:
            result['client_ip'] = str(self.client_ip)
        if self.request_start_time:
            result['request_start_time'] = self.request_start_time.isoformat()
        if self.request_end_time:
            result['request_end_time'] = self.request_end_time.isoformat()

        return result

    @classmethod
    def create_from_request_data(
        cls,
        request_id: str,
        method: str,
        path: str,
        request_type: RequestType,
        client_ip: str = None,
        user_agent: str = None,
        **kwargs
    ) -> "APIRequestLog":
        """从请求数据创建日志记录"""
        return cls(
            request_id=request_id,
            method=method,
            path=path,
            request_type=request_type,
            client_ip=client_ip,
            user_agent=user_agent,
            **kwargs
        )

    def update_response_data(
        self,
        status_code: int,
        processing_time_ms: float,
        response_content_type: str = None,
        response_content_length: int = None,
        error_type: str = None,
        error_message: str = None
    ):
        """更新响应数据"""
        self.request_end_time = datetime.now(timezone.utc)
        self.status_code = status_code
        self.processing_time_ms = processing_time_ms
        self.response_content_type = response_content_type
        self.response_content_length = response_content_length
        self.is_successful = 200 <= status_code < 400

        if error_type:
            self.error_type = error_type
        if error_message:
            self.error_message = error_message
