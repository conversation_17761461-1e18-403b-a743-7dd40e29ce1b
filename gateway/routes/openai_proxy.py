"""
OpenAI兼容API代理路由模块

该模块实现了完整的OpenAI API规范代理功能，包括：
- 聊天完成API（支持流式和非流式响应）
- 模型列表API
- 文本嵌入API
- 使用统计API
- 使用统一认证服务进行API密钥认证
- 动态模型路由和负载均衡
"""
import json
import time
from typing import Dict, Any, Optional, AsyncGenerator
import uuid

from openai import AsyncOpenAI
from fastapi import APIRouter, Request, HTTPException, status, Depends
from fastapi.responses import StreamingResponse, JSONResponse
from pydantic import BaseModel, Field

from gateway.config.model_routes import model_route_manager
from gateway.config.settings import settings
from gateway.utils import (
    OpenAIProxyConfig, OpenAIErrorHandler, OpenAIResponseFormatter,
    DebugLogger
)
from shared.auth import create_unified_auth_service
from shared.logging_config import get_gateway_logger

logger = get_gateway_logger()
router = APIRouter(prefix="/v1", tags=["OpenAI兼容API"])

# 创建统一认证服务实例
unified_auth_service = create_unified_auth_service(settings.USER_SERVICE_URL)


# OpenAI API 数据模型
class ChatMessage(BaseModel):
    role: str = Field(..., description="消息角色: system, user, assistant")
    content: str = Field(..., description="消息内容")
    name: Optional[str] = Field(None, description="消息发送者名称")


class ChatCompletionRequest(BaseModel):
    model: str = Field(..., description="模型名称")
    messages: list[ChatMessage] = Field(..., description="对话消息列表")
    temperature: Optional[float] = Field(0.7, ge=0, le=2, description="温度参数")
    max_tokens: Optional[int] = Field(None, ge=1, description="最大token数")
    top_p: Optional[float] = Field(1.0, ge=0, le=1, description="top_p参数")
    frequency_penalty: Optional[float] = Field(
        0, ge=-2, le=2, description="频率惩罚")
    presence_penalty: Optional[float] = Field(
        0, ge=-2, le=2, description="存在惩罚")
    stream: Optional[bool] = Field(None, description="是否流式输出")
    stop: Optional[list[str]] = Field(None, description="停止词列表")
    user: Optional[str] = Field(None, description="用户标识")


class ModelInfo(BaseModel):
    id: str
    object: str = "model"
    created: int
    owned_by: str


class ModelsResponse(BaseModel):
    object: str = "list"
    data: list[ModelInfo]


class OpenAIProxy:
    """OpenAI API代理类 - 使用OpenAI SDK"""

    def __init__(self):
        # OpenAI SDK客户端缓存
        self._openai_clients = {}

    def standardize_model_content(self, content: str) -> str:
        """
        标准化底层模型返回的内容格式

        主要功能：
        1. 检测缺少开头 <think> 标签但有结尾 </think> 标签的内容
        2. 自动在思考内容开头添加 <think> 标签
        3. 保持其他内容不变

        Args:
            content (str): 原始模型返回的内容

        Returns:
            str: 标准化后的内容

        Examples:
            输入: "好的，我现在需要介绍猫咪常见的疾病。首先，要涵盖基本的传染病...</think>"
            输出: "<think>好的，我现在需要介绍猫咪常见的疾病。首先，要涵盖基本的传染病...</think>"
        """
        if not content or not isinstance(content, str):
            return content

        # 检查是否有结尾的 </think> 标签但没有开头的 <think> 标签
        has_end_think = "</think>" in content
        has_start_think = "<think>" in content

        if has_end_think and not has_start_think:
            # 找到第一个 </think> 标签的位置
            end_think_pos = content.find("</think>")

            if end_think_pos > 0:
                # 提取 </think> 之前的内容作为思考部分
                think_content = content[:end_think_pos].strip()
                # 提取 </think> 之后的内容
                remaining_content = content[end_think_pos:].strip()

                # 重新组合内容，在思考部分前添加 <think> 标签
                standardized_content = f"<think>{think_content}</think>"

                # 如果 </think> 后还有其他内容，添加到结果中
                if len(remaining_content) > len("</think>"):
                    additional_content = remaining_content[len(
                        "</think>"):].strip()
                    if additional_content:
                        standardized_content += f"\n\n{additional_content}"

                logger.debug(f"内容标准化完成: 添加了缺失的 <think> 开头标签")
                return standardized_content

        # 如果不需要标准化，返回原内容
        return content

    def _get_openai_client(self, service_url: str, api_key: str) -> AsyncOpenAI:
        """获取或创建OpenAI客户端实例"""
        client_key = f"{service_url}:{api_key}"
        if client_key not in self._openai_clients:
            self._openai_clients[client_key] = AsyncOpenAI(
                base_url=f"{service_url}/v1",
                api_key=api_key,
                timeout=OpenAIProxyConfig.OPENAI_CLIENT_TIMEOUT
            )
        return self._openai_clients[client_key]

    async def forward_chat_completion(
        self,
        request_data: ChatCompletionRequest,
        user_info: Dict[str, Any],
        request_id: str
    ) -> Any:
        """使用OpenAI SDK转发聊天完成请求"""

        # 记录请求日志
        await self._log_request(request_data, user_info, request_id)

        # 准备转发的请求数据
        forward_data, service_url, api_key = self._prepare_forward_data(
            request_data, user_info)

        try:
            # 获取OpenAI客户端
            openai_client = self._get_openai_client(service_url, api_key)

            # 转换请求数据为OpenAI SDK格式
            messages = [
                {"role": msg.role, "content": msg.content}
                for msg in request_data.messages
            ]

            # 判断是否为流式响应（默认为非流式）
            is_stream = request_data.stream is True

            if is_stream:
                # 流式响应
                return await self._handle_stream_response_with_sdk(
                    openai_client, forward_data, messages, request_id
                )
            else:
                # 非流式响应
                return await self._handle_regular_response_with_sdk(
                    openai_client, forward_data, messages, request_id
                )

        except Exception as e:
            logger.error(f"使用OpenAI SDK转发聊天完成请求时发生错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail={
                    "error": {
                        "message": "内部服务器错误",
                        "type": "server_error",
                        "code": "server_error"
                    }
                }
            )

    def _prepare_forward_data(
        self,
        request_data: ChatCompletionRequest,
        user_info: Dict[str, Any]
    ) -> tuple[Dict[str, Any], str, str]:
        """准备转发数据，返回(转发数据, 服务URL, API Key)"""

        forward_data = request_data.model_dump(exclude_unset=True)
        original_model = forward_data.get("model", "gpt-3.5-turbo")

        # 获取模型配置
        model_config = model_route_manager.get_model_config(original_model)
        if not model_config:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "error": {
                        "message": f"不支持的模型 '{original_model}'",
                        "type": "invalid_request_error",
                        "code": "model_not_found"
                    }
                }
            )

        # 获取后端实际模型名称
        backend_model = model_route_manager.get_backend_model_name(
            original_model)
        forward_data["model"] = backend_model

        # 添加用户信息到请求中
        forward_data["user"] = str(user_info.get("id", "unknown"))

        # 记录转发的详细参数（用于调试）
        logger.debug(f"转发参数: model={forward_data.get('model')}, "
                     f"messages_count={len(forward_data.get('messages', []))}, "
                     f"temperature={forward_data.get('temperature')}, "
                     f"max_tokens={forward_data.get('max_tokens')}")

        return forward_data, model_config.service_url, model_config.get_api_key()

    async def _handle_regular_response_with_sdk(
        self,
        openai_client: AsyncOpenAI,
        forward_data: Dict[str, Any],
        messages: list,
        request_id: str
    ) -> JSONResponse:
        """使用OpenAI SDK处理非流式响应"""

        try:
            response = await openai_client.chat.completions.create(
                model=forward_data["model"],
                messages=messages,
                temperature=forward_data.get("temperature", 0.7),
                max_tokens=forward_data.get("max_tokens"),
                top_p=forward_data.get("top_p", 1.0),
                frequency_penalty=forward_data.get("frequency_penalty", 0),
                presence_penalty=forward_data.get("presence_penalty", 0),
                stop=forward_data.get("stop"),
                user=forward_data.get("user"),
                stream=False
            )

            # 转换为标准OpenAI响应格式，并应用内容标准化
            response_data = OpenAIResponseFormatter.format_chat_completion_response(
                response, self.standardize_model_content
            )

            # Debug模式下记录响应详情
            if settings.DEBUG:
                logger.debug(f"🔍 准备记录响应日志 [ID: {request_id}]")
            self._log_response(response_data, request_id, is_stream=False)

            return JSONResponse(
                content=response_data,
                status_code=200,
                headers={"X-Request-ID": request_id}
            )

        except Exception as e:
            logger.error(f"OpenAI SDK非流式响应错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail={
                    "error": {
                        "message": f"后端服务错误: {str(e)}",
                        "type": "service_error",
                        "code": "service_error"
                    }
                }
            )

    async def _handle_stream_response_with_sdk(
        self,
        openai_client: AsyncOpenAI,
        forward_data: Dict[str, Any],
        messages: list,
        request_id: str
    ) -> StreamingResponse:
        """使用OpenAI SDK处理流式响应"""

        async def stream_generator() -> AsyncGenerator[str, None]:
            try:
                # Debug模式下记录流式响应开始
                if settings.DEBUG:
                    self._log_response({}, request_id, is_stream=True)

                stream = await openai_client.chat.completions.create(
                    model=forward_data["model"],
                    messages=messages,
                    temperature=forward_data.get("temperature", 0.7),
                    max_tokens=forward_data.get("max_tokens"),
                    top_p=forward_data.get("top_p", 1.0),
                    frequency_penalty=forward_data.get("frequency_penalty", 0),
                    presence_penalty=forward_data.get("presence_penalty", 0),
                    stop=forward_data.get("stop"),
                    user=forward_data.get("user"),
                    stream=True
                )

                # 流式内容标准化状态跟踪
                collected_content = ""
                chunk_count = 0
                first_chunk_metadata = None
                think_tag_added = False  # 是否已添加<think>标签

                async for chunk in stream:
                    chunk_count += 1

                    # 转换为标准SSE格式
                    chunk_data = {
                        "id": chunk.id,
                        "object": chunk.object,
                        "created": chunk.created,
                        "model": chunk.model,
                        "choices": []
                    }

                    # 保存第一个数据块的元数据
                    if first_chunk_metadata is None:
                        first_chunk_metadata = {
                            "id": chunk.id,
                            "object": chunk.object,
                            "created": chunk.created,
                            "model": chunk.model
                        }

                    for choice in chunk.choices:
                        choice_data = {
                            "index": choice.index,
                            "delta": {},
                            "finish_reason": choice.finish_reason
                        }

                        # 处理role信息
                        if choice.delta.role is not None:
                            choice_data["delta"]["role"] = choice.delta.role

                        # 处理content信息
                        if choice.delta.content is not None:
                            original_content = choice.delta.content
                            collected_content += original_content

                            # 在第一个包含内容的数据块前添加<think>标签
                            if not think_tag_added:
                                # 先发送<think>标签作为单独的数据块
                                think_chunk = {
                                    "id": chunk.id,
                                    "object": chunk.object,
                                    "created": chunk.created,
                                    "model": chunk.model,
                                    "choices": [{
                                        "index": choice.index,
                                        "delta": {"content": "<think>"},
                                        "finish_reason": None
                                    }]
                                }
                                yield f"data: {json.dumps(think_chunk)}\n\n"
                                think_tag_added = True

                                if settings.DEBUG:
                                    logger.debug(f"🔧 流式模式下添加<think>标签")

                            choice_data["delta"]["content"] = original_content

                        chunk_data["choices"].append(choice_data)

                    # 立即发送数据块（真正的流式响应）
                    yield f"data: {json.dumps(chunk_data)}\n\n"

                    # Debug模式下记录数据块
                    if settings.DEBUG and chunk_count <= 3:  # 只打印前3个块的详情
                        logger.debug(
                            f"🌊 流式数据块 {chunk_count}: {json.dumps(chunk_data)[:100]}...")

                # Debug模式下打印完整的流式响应内容
                if settings.DEBUG:
                    logger.debug("=" * 80)
                    logger.debug(f"🌊 流式响应完成 [ID: {request_id}]")
                    logger.debug("=" * 80)
                    logger.debug(f"📊 总数据块数: {chunk_count}")
                    logger.debug(f"📝 原始内容长度: {len(collected_content)} 字符")
                    logger.debug(f"💬 原始内容: {collected_content}")
                    logger.debug(f"🔧 <think>标签已添加: {think_tag_added}")

                    # 检查是否需要标准化（仅用于日志记录）
                    standardized_content = self.standardize_model_content(
                        collected_content)
                    if standardized_content != collected_content:
                        logger.debug(f"✨ 流式模式下应用了内容标准化")
                        logger.debug(f"🔧 标准化后内容: {standardized_content}")
                    else:
                        logger.debug("✅ 内容无需标准化")

                    logger.debug("=" * 80)

                yield "data: [DONE]\n\n"

            except Exception as e:
                logger.error(f"OpenAI SDK流式响应错误: {e}")
                error_data = {
                    "error": {
                        "message": f"流式处理错误: {str(e)}",
                        "type": "stream_error",
                        "code": "stream_error"
                    }
                }
                yield f"data: {json.dumps(error_data)}\n\n"
                yield "data: [DONE]\n\n"

        return StreamingResponse(
            stream_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Request-ID": request_id,
            }
        )

    async def get_models(self, user_info: Dict[str, Any]) -> ModelsResponse:
        """获取可用模型列表"""

        # 记录用户请求（使用user_info参数）
        logger.debug(
            f"Getting models for user {user_info.get('id', 'unknown')}")

        # 直接从模型路由管理器获取所有支持的模型
        all_models = model_route_manager.get_all_models()

        model_list = []
        for model_name, config in all_models.items():
            model_list.append(ModelInfo(
                id=model_name,
                created=int(time.time()),
                owned_by=config.owned_by
            ))

        return ModelsResponse(data=model_list)

    async def _log_request(
        self,
        request_data: ChatCompletionRequest,
        user_info: Dict[str, Any],
        request_id: str
    ):
        """记录请求日志"""

        # 确定实际的流式状态
        actual_stream = request_data.stream is True

        log_data = {
            "request_id": request_id,
            "user_id": user_info.get("id"),
            "model": request_data.model,
            "message_count": len(request_data.messages),
            "stream": actual_stream,
            "stream_raw": request_data.stream,  # 原始值
            "timestamp": time.time(),
            "type": "openai_request"
        }

        logger.info(f"OpenAI API Request: {json.dumps(log_data)}")

        # Debug模式下打印详细的用户输入和参数
        if settings.DEBUG:
            DebugLogger.log_request_details(
                request_data, user_info, request_id)

        # 这里可以添加到Redis或数据库进行持久化
        # await redis_client.lpush("openai_requests", json.dumps(log_data))

    def _log_response(
        self,
        response_data: Dict[str, Any],
        request_id: str,
        is_stream: bool = False
    ):
        """记录响应日志"""

        # Debug模式下打印详细的模型输出结果
        if settings.DEBUG:
            logger.debug("=" * 80)
            logger.debug(f"📤 DEBUG模式 - 响应详情 [ID: {request_id}]")
            logger.debug("=" * 80)

            if not is_stream and response_data:
                # 非流式响应的详细信息
                logger.debug(f"🆔 响应ID: {response_data.get('id', 'N/A')}")
                logger.debug(f"🤖 模型: {response_data.get('model', 'N/A')}")
                logger.debug(f"⏰ 创建时间: {response_data.get('created', 'N/A')}")

                # 打印选择结果
                choices = response_data.get('choices', [])
                logger.debug(f"🎯 选择数量: {len(choices)}")

                for i, choice in enumerate(choices):
                    message = choice.get('message', {})
                    content = message.get('content', '')
                    finish_reason = choice.get('finish_reason', 'N/A')

                    logger.debug(f"  选择 {i+1}:")
                    logger.debug(f"    🏁 结束原因: {finish_reason}")
                    logger.debug(f"    📝 内容长度: {len(content)} 字符")

                    # 打印内容预览（前200字符）
                    content_preview = content[:200] + \
                        "..." if len(content) > 200 else content
                    logger.debug(f"    💬 内容预览: {content_preview}")

                    # 如果内容较长，打印完整内容
                    if len(content) > 200:
                        logger.debug(f"    📄 完整内容: {content}")

                # 打印使用统计
                usage = response_data.get('usage', {})
                if usage:
                    logger.debug(f"📊 Token使用统计:")
                    logger.debug(
                        f"    📥 输入Token: {usage.get('prompt_tokens', 'N/A')}")
                    logger.debug(
                        f"    📤 输出Token: {usage.get('completion_tokens', 'N/A')}")
                    logger.debug(
                        f"    📈 总Token: {usage.get('total_tokens', 'N/A')}")

            elif is_stream:
                logger.debug("🌊 流式响应开始...")

            logger.debug("=" * 80)

    async def close(self):
        """关闭OpenAI客户端"""
        for client in self._openai_clients.values():
            await client.close()


# 全局代理实例
openai_proxy = OpenAIProxy()


# 依赖函数：OpenAI标准API Key认证
async def get_current_user_for_openai(request: Request):
    """OpenAI标准API Key认证 - 支持数据库验证和Redis缓存"""

    # 获取Authorization头
    auth_header = request.headers.get("Authorization", "")

    if not auth_header:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "error": {
                    "message": "您没有提供API密钥。您需要在Authorization头中使用Bearer认证提供API密钥（例如：Authorization: Bearer YOUR_KEY）。",
                    "type": "invalid_request_error",
                    "code": "invalid_api_key"
                }
            }
        )

    if not auth_header.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "error": {
                    "message": "无效的授权头格式。期望格式为 'Bearer YOUR_KEY'。",
                    "type": "invalid_request_error",
                    "code": "invalid_api_key"
                }
            }
        )

    api_key = auth_header.replace("Bearer ", "").strip()

    # 只进行基本的格式检查，详细验证交给统一认证服务
    if not api_key or len(api_key) < OpenAIProxyConfig.API_KEY_MIN_LENGTH:
        OpenAIErrorHandler.raise_http_exception(
            message="无效的API密钥格式。API密钥过短或为空。",
            error_type="invalid_request_error",
            code="invalid_api_key",
            status_code=status.HTTP_401_UNAUTHORIZED
        )

    # 使用统一认证服务进行详细验证（包括格式、状态、过期等）
    try:
        user_info = await unified_auth_service.validate_openai_api_key(api_key)

        # 记录成功的API key使用
        logger.info(
            f"OpenAI API认证成功: 用户 {user_info.get('email')} (ID: {user_info.get('id')})")

        # 构建用户信息对象
        authenticated_user = {
            "id": user_info["id"],
            "user_id": user_info["id"],  # 兼容字段
            "email": user_info["email"],
            "username": user_info.get("username"),
            "tier": user_info.get("tier", "standard"),
            "api_key": api_key,
            "api_key_id": user_info.get("api_key_id"),
            "api_key_name": user_info.get("api_key_name"),
            "auth_type": "api_key",  # 添加认证类型
            "rate_limits": {
                "per_minute": user_info.get("rate_limit_per_minute", OpenAIProxyConfig.RATE_LIMIT_PER_MINUTE_DEFAULT),
                "per_day": user_info.get("rate_limit_per_day", OpenAIProxyConfig.RATE_LIMIT_PER_DAY_DEFAULT)
            }
        }

        # 将用户信息设置到请求状态中，供日志中间件使用
        request.state.user = authenticated_user
        request.state.auth_type = "api_key"

        return authenticated_user

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"API key验证过程中发生未知错误: {e}")
        OpenAIErrorHandler.raise_http_exception(
            message="内部服务器错误",
            error_type="server_error",
            code="server_error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/chat/completions")
async def chat_completions(
    request: Request,
    request_data: ChatCompletionRequest,
    user_info: Dict[str, Any] = Depends(get_current_user_for_openai)
):
    """
    OpenAI兼容的聊天完成API
    支持流式和非流式响应
    """

    request_id = str(uuid.uuid4())
    logger.info(
        f"Chat completion request {request_id} from user {user_info.get('id')}")

    # Debug模式下打印原始请求数据
    if settings.DEBUG:
        try:
            # 获取原始请求体
            body = await request.body()
            raw_data = json.loads(body.decode('utf-8'))
            logger.debug(
                f"🔍 原始请求数据 [ID: {request_id}]: {json.dumps(raw_data, ensure_ascii=False)}")
            logger.debug(f"🌊 原始stream参数: {raw_data.get('stream', '未设置')}")
            logger.debug(f"📝 解析后stream参数: {request_data.stream}")
        except Exception as e:
            logger.debug(f"⚠️ 无法解析原始请求数据: {e}")

    return await openai_proxy.forward_chat_completion(request_data, user_info, request_id)


@router.get("/models", response_model=ModelsResponse)
async def list_models(
    user_info: Dict[str, Any] = Depends(get_current_user_for_openai)
):
    """
    获取可用模型列表
    """

    logger.info(f"Models request from user {user_info.get('id')}")
    return await openai_proxy.get_models(user_info)


@router.post("/embeddings")
async def create_embeddings(
    request: Request,
    user_info: Dict[str, Any] = Depends(get_current_user_for_openai)
):
    """
    创建文本嵌入向量
    """

    request_id = str(uuid.uuid4())
    logger.info(
        f"Embeddings request {request_id} from user {user_info.get('id')}")

    try:
        # 获取请求体
        request_data = await request.json()

        # 获取模型配置
        model_name = request_data.get(
            "model", OpenAIProxyConfig.DEFAULT_EMBEDDING_MODEL)
        model_config = model_route_manager.get_model_config(model_name)

        if not model_config:
            # 如果没有找到指定模型，使用默认的第一个模型
            all_models = model_route_manager.get_all_models()
            if all_models:
                model_config = list(all_models.values())[0]
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={
                        "error": {
                            "message": f"模型 '{model_name}' 不支持嵌入向量功能",
                            "type": "invalid_request_error",
                            "code": "model_not_found"
                        }
                    }
                )

        # 使用OpenAI SDK转发到对应的模型服务
        openai_client = openai_proxy._get_openai_client(
            model_config.service_url, model_config.get_api_key()
        )

        response = await openai_client.embeddings.create(
            model=request_data.get(
                "model", OpenAIProxyConfig.DEFAULT_EMBEDDING_MODEL),
            input=request_data.get("input", "")
        )

        # 转换为标准OpenAI响应格式
        response_data = OpenAIResponseFormatter.format_embeddings_response(
            response)

        return JSONResponse(
            content=response_data,
            status_code=200,
            headers={"X-Request-ID": request_id}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"嵌入向量端点错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": {
                    "message": "内部服务器错误",
                    "type": "server_error",
                    "code": "server_error"
                }
            }
        )


@router.get("/usage")
async def get_usage(
    user_info: Dict[str, Any] = Depends(get_current_user_for_openai)
):
    """
    获取用户使用统计
    """

    logger.info(f"Usage request from user {user_info.get('id')}")

    # 这里可以从Redis或数据库获取用户使用统计
    # 目前返回模拟数据
    return {
        "object": "usage",
        "user_id": str(user_info.get("id")),
        "current_month": {
            "requests": 0,
            "tokens": 0,
            "cost": 0.0
        },
        "limits": {
            "max_requests_per_month": 1000,
            "max_tokens_per_month": 100000
        }
    }
