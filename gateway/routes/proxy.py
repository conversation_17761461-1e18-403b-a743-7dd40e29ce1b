"""
API网关代理路由模块

该模块实现了请求转发到后端服务的代理功能，包括：
- 动态路由匹配和服务发现
- 请求头和响应头的智能处理
- 错误处理和超时管理
- 客户端IP地址的正确传递
- 请求链路追踪支持
"""
from typing import Dict

import httpx
from fastapi import APIRouter, Request, HTTPException, status
from fastapi.responses import Response, StreamingResponse
import json

from gateway.config.settings import settings
from shared.logging_config import get_gateway_logger

logger = get_gateway_logger()
router = APIRouter()


class ServiceProxy:
    """服务代理类

    负责将网关接收到的请求转发到相应的后端微服务，
    处理请求头、响应头的转换和错误处理。
    """

    def __init__(self):
        """初始化服务代理"""
        # 使用默认超时时间初始化客户端
        self.client = httpx.AsyncClient(timeout=settings.REQUEST_TIMEOUT)
        self.service_routes = settings.SERVICE_ROUTES

        # AI相关端点列表（需要更长超时时间）
        self.ai_endpoints = {
            "/api/v1/system-agents/diagnosis",
            "/api/v1/system-agents/vision",
            "/api/v1/system-agents/report-generation",
            "/api/v1/system-agents/report-analysis",
            "/api/v1/agents"  # 智能体执行也可能需要较长时间
        }

    async def forward_request(
        self,
        request: Request,
        target_url: str,
        path: str
    ) -> Response:
        """转发请求到目标服务

        Args:
            request: FastAPI请求对象
            target_url: 目标服务的基础URL
            path: 请求路径

        Returns:
            Response: 来自后端服务的响应
        """

        try:
            # 准备请求头（排除逐跳头）
            headers = self._prepare_headers(request)
            logger.info(f"🔍 准备的请求头: {headers}")

            # 获取请求体（如果存在）
            body = None
            if request.method in ["POST", "PUT", "PATCH"]:
                body = await request.body()
                logger.info(f"🔍 请求体长度: {len(body) if body else 0}")

            # 构造完整URL
            full_url = f"{target_url}{path}"
            logger.info(f"🔍 完整目标URL: {full_url}")
            logger.info(f"🔍 请求方法: {request.method}")
            logger.info(f"🔍 查询参数: {dict(request.query_params)}")

            # 检查是否需要流式响应
            is_stream_request = self._is_stream_request(request, body)
            logger.info(f"🔍 是否流式请求: {is_stream_request}")

            # 根据请求路径确定超时时间
            timeout = self._get_timeout_for_path(path)
            logger.info(f"🚀 向 {full_url} 发起请求 (超时: {timeout}秒)")

            # 如果是流式请求，使用流式转发
            if is_stream_request:
                return await self._forward_stream_request(
                    full_url, request.method, headers,
                    request.query_params, body, timeout
                )

            # 普通请求处理
            # 为这个请求创建临时客户端（如果需要不同的超时时间）
            if timeout != settings.REQUEST_TIMEOUT:
                async with httpx.AsyncClient(timeout=timeout) as temp_client:
                    response = await temp_client.request(
                        method=request.method,
                        url=full_url,
                        headers=headers,
                        params=request.query_params,
                        content=body
                    )
            else:
                response = await self.client.request(
                    method=request.method,
                    url=full_url,
                    headers=headers,
                    params=request.query_params,
                    content=body
                )

            logger.info(f"✅ 收到响应: {response.status_code}")

            # 准备响应头
            response_headers = self._prepare_response_headers(response.headers)

            # 返回响应
            return Response(
                content=response.content,
                status_code=response.status_code,
                headers=response_headers,
                media_type=response.headers.get("content-type")
            )

        except httpx.TimeoutException:
            logger.error(f"转发请求到 {target_url}{path} 超时")
            raise HTTPException(
                status_code=status.HTTP_504_GATEWAY_TIMEOUT,
                detail="服务超时"
            )
        except httpx.ConnectError:
            logger.error(
                f"转发请求到 {target_url}{path} 连接错误")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="服务不可用"
            )
        except Exception as e:
            logger.error(
                f"转发请求到 {target_url}{path} 时发生错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="网关错误"
            )

    def _prepare_headers(self, request: Request) -> Dict[str, str]:
        """准备转发的请求头

        Args:
            request: FastAPI请求对象

        Returns:
            Dict[str, str]: 处理后的请求头字典
        """

        # 需要排除的请求头（逐跳头）
        excluded_headers = {
            'host', 'connection', 'upgrade', 'proxy-authenticate',
            'proxy-authorization', 'te', 'trailers', 'transfer-encoding'
        }

        headers = {}
        for name, value in request.headers.items():
            if name.lower() not in excluded_headers:
                headers[name] = value

        # 添加转发相关的请求头
        headers['X-Forwarded-For'] = self._get_client_ip(request)
        headers['X-Forwarded-Proto'] = request.url.scheme
        headers['X-Forwarded-Host'] = request.headers.get('host', '')

        # 添加请求ID（如果可用）
        if hasattr(request.state, 'request_id'):
            headers['X-Request-ID'] = request.state.request_id

        return headers

    def _is_stream_request(self, request: Request, body: bytes = None) -> bool:
        """检查是否为流式请求

        Args:
            request: FastAPI请求对象
            body: 请求体内容

        Returns:
            bool: 是否为流式请求
        """
        # 检查Accept头是否包含text/event-stream
        accept_header = request.headers.get("accept", "").lower()
        if "text/event-stream" in accept_header:
            logger.info("🔍 检测到Accept: text/event-stream头")
            return True

        # 检查请求体中是否包含stream=true参数
        if body:
            try:
                body_str = body.decode('utf-8')
                if '"stream"' in body_str and '"true"' in body_str:
                    logger.info("🔍 检测到请求体中包含stream=true")
                    return True
                # 也检查JSON格式
                try:
                    body_json = json.loads(body_str)
                    if body_json.get("stream") is True:
                        logger.info("🔍 检测到JSON请求体中stream=true")
                        return True
                except json.JSONDecodeError:
                    pass
            except UnicodeDecodeError:
                pass

        # 检查查询参数中是否包含stream=true
        if request.query_params.get("stream") == "true":
            logger.info("🔍 检测到查询参数stream=true")
            return True

        return False

    async def _forward_stream_request(
        self,
        url: str,
        method: str,
        headers: Dict[str, str],
        params,
        body: bytes = None,
        timeout: int = None
    ) -> StreamingResponse:
        """转发流式请求

        Args:
            url: 目标URL
            method: HTTP方法
            headers: 请求头
            params: 查询参数
            body: 请求体
            timeout: 超时时间

        Returns:
            StreamingResponse: 流式响应
        """
        logger.info(f"🌊 开始流式转发到: {url}")

        async def stream_generator():
            try:
                # 创建流式HTTP客户端
                timeout_config = httpx.Timeout(
                    timeout or settings.AI_REQUEST_TIMEOUT)
                async with httpx.AsyncClient(timeout=timeout_config) as client:
                    async with client.stream(
                        method=method,
                        url=url,
                        headers=headers,
                        params=params,
                        content=body
                    ) as response:
                        logger.info(f"🌊 流式响应状态码: {response.status_code}")

                        # 如果响应不成功，读取错误信息并返回
                        if response.status_code >= 400:
                            error_content = await response.aread()
                            logger.error(
                                f"🌊 流式请求失败: {response.status_code} - {error_content}")
                            yield f"data: {json.dumps({'error': f'HTTP {response.status_code}', 'detail': error_content.decode()})}\n\n"
                            return

                        # 流式转发响应内容
                        async for chunk in response.aiter_bytes():
                            if chunk:
                                yield chunk

            except httpx.TimeoutException:
                logger.error(f"🌊 流式请求超时: {url}")
                yield f"data: {json.dumps({'error': 'timeout', 'detail': '请求超时'})}\n\n"
            except httpx.ConnectError:
                logger.error(f"🌊 流式请求连接错误: {url}")
                yield f"data: {json.dumps({'error': 'connection_error', 'detail': '连接失败'})}\n\n"
            except Exception as e:
                logger.error(f"🌊 流式请求异常: {e}")
                yield f"data: {json.dumps({'error': 'internal_error', 'detail': str(e)})}\n\n"

        # 设置流式响应头
        response_headers = {
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }

        return StreamingResponse(
            stream_generator(),
            media_type="text/event-stream",
            headers=response_headers
        )

    def _prepare_response_headers(self, response_headers) -> Dict[str, str]:
        """准备响应头

        Args:
            response_headers: 原始响应头

        Returns:
            Dict[str, str]: 处理后的响应头字典
        """

        # 需要排除的响应头
        excluded_headers = {
            'connection', 'upgrade', 'proxy-authenticate',
            'proxy-authorization', 'te', 'trailers', 'transfer-encoding'
        }

        headers = {}
        for name, value in response_headers.items():
            if name.lower() not in excluded_headers:
                headers[name] = value

        return headers

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址

        Args:
            request: FastAPI请求对象

        Returns:
            str: 客户端IP地址
        """
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        return request.client.host if request.client else "unknown"

    def get_target_service(self, path: str) -> str:
        """根据路径获取目标服务URL

        Args:
            path: 请求路径

        Returns:
            str: 目标服务URL，如果未找到则返回None
        """

        for route_prefix, service_url in self.service_routes.items():
            if path.startswith(route_prefix):
                return service_url

        return None

    def _get_timeout_for_path(self, path: str) -> int:
        """根据请求路径获取超时时间

        Args:
            path: 请求路径

        Returns:
            int: 超时时间（秒）
        """
        # 检查是否是AI相关端点
        for ai_endpoint in self.ai_endpoints:
            if path.startswith(ai_endpoint):
                return settings.AI_REQUEST_TIMEOUT

        # 检查是否是智能体执行端点（包含execute的路径）
        if "/agents/" in path and "/execute" in path:
            return settings.AI_REQUEST_TIMEOUT

        # 默认超时时间
        return settings.REQUEST_TIMEOUT

    async def close(self):
        """关闭HTTP客户端"""
        await self.client.aclose()


# 全局代理实例
proxy = ServiceProxy()


@router.api_route("/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"])
async def proxy_request(request: Request, path: str):
    """将请求代理到相应的后端服务

    Args:
        request: FastAPI请求对象
        path: 请求路径

    Returns:
        Response: 来自后端服务的响应
    """

    full_path = f"/{path}"

    # 详细的请求日志
    logger.info(f"🔍 收到请求: {request.method} {full_path}")
    logger.info(f"🔍 请求头: {dict(request.headers)}")
    logger.info(f"🔍 查询参数: {dict(request.query_params)}")

    # 查找目标服务
    target_service = proxy.get_target_service(full_path)

    logger.info(f"🔍 路径 {full_path} 的目标服务: {target_service}")
    logger.info(f"🔍 可用路由: {proxy.service_routes}")

    if not target_service:
        logger.warning(f"❌ 未找到路径对应的服务: {full_path}")
        logger.warning(
            f"❌ 可用路由: {list(proxy.service_routes.keys())}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="未找到服务"
        )

    # 转发请求
    logger.info(
        f"🚀 转发 {request.method} {full_path} 到 {target_service}")
    try:
        response = await proxy.forward_request(request, target_service, full_path)
        logger.info(
            f"✅ 成功转发请求，响应状态码: {response.status_code}")
        return response
    except Exception as e:
        logger.error(f"❌ 转发请求失败: {e}")
        raise
