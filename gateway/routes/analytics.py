"""
API分析和统计路由

提供API使用情况分析、请求统计和性能监控的端点。
"""
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict

from fastapi import APIRouter, Depends, Query, HTTPException
from pydantic import BaseModel, Field

from gateway.middleware.auth import get_current_user
from gateway.models.request_log import RequestType
from gateway.services.request_log_service import request_log_service
from shared.logging_config import get_gateway_logger

logger = get_gateway_logger()
router = APIRouter(prefix="/analytics", tags=["分析统计"])


class RequestStatsResponse(BaseModel):
    """请求统计响应模型"""
    total_requests: int = Field(..., description="总请求数")
    successful_requests: int = Field(..., description="成功请求数")
    failed_requests: int = Field(..., description="失败请求数")
    success_rate: float = Field(..., description="成功率（百分比）")
    avg_response_time_ms: float = Field(..., description="平均响应时间（毫秒）")
    period: Dict[str, Optional[str]] = Field(..., description="统计时间段")


class ServiceStatsResponse(BaseModel):
    """服务统计响应模型"""
    buffer_size: int = Field(..., description="缓冲区大小")
    total_requests: int = Field(..., description="总处理请求数")
    successful_inserts: int = Field(..., description="成功插入数")
    failed_inserts: int = Field(..., description="失败插入数")
    last_flush: float = Field(..., description="最后刷新时间戳")
    flush_interval: int = Field(..., description="刷新间隔（秒）")
    batch_size: int = Field(..., description="批量大小")


@router.get("/request-stats", response_model=RequestStatsResponse)
async def get_request_stats(
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    user_id: Optional[int] = Query(None, description="用户ID"),
    request_type: Optional[RequestType] = Query(None, description="请求类型"),
    current_user: dict = Depends(get_current_user)
):
    """获取请求统计信息
    
    Args:
        start_time: 开始时间
        end_time: 结束时间
        user_id: 用户ID（管理员可查看所有用户，普通用户只能查看自己的）
        request_type: 请求类型
        current_user: 当前用户信息
        
    Returns:
        RequestStatsResponse: 请求统计信息
    """
    try:
        # 权限检查：普通用户只能查看自己的统计
        if not current_user.get("is_superuser", False):
            if user_id and user_id != current_user.get("id"):
                raise HTTPException(
                    status_code=403,
                    detail="无权限查看其他用户的统计信息"
                )
            # 强制设置为当前用户ID
            user_id = current_user.get("id")
        
        # 默认时间范围：最近24小时
        if not start_time:
            start_time = datetime.now(timezone.utc) - timedelta(hours=24)
        if not end_time:
            end_time = datetime.now(timezone.utc)
        
        # 获取统计数据
        stats = await request_log_service.get_request_stats(
            start_time=start_time,
            end_time=end_time,
            user_id=user_id,
            request_type=request_type
        )
        
        return RequestStatsResponse(**stats)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取请求统计失败: {e}")
        raise HTTPException(
            status_code=500,
            detail="获取统计信息失败"
        )


@router.get("/service-stats", response_model=ServiceStatsResponse)
async def get_service_stats(
    current_user: dict = Depends(get_current_user)
):
    """获取日志服务统计信息
    
    Args:
        current_user: 当前用户信息
        
    Returns:
        ServiceStatsResponse: 服务统计信息
    """
    try:
        # 只有管理员可以查看服务统计
        if not current_user.get("is_superuser", False):
            raise HTTPException(
                status_code=403,
                detail="无权限查看服务统计信息"
            )
        
        # 获取服务统计
        stats = await request_log_service.get_stats()
        
        return ServiceStatsResponse(**stats)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取服务统计失败: {e}")
        raise HTTPException(
            status_code=500,
            detail="获取服务统计失败"
        )


@router.post("/flush-logs")
async def flush_logs(
    current_user: dict = Depends(get_current_user)
):
    """手动刷新日志缓冲区
    
    Args:
        current_user: 当前用户信息
        
    Returns:
        dict: 操作结果
    """
    try:
        # 只有管理员可以手动刷新
        if not current_user.get("is_superuser", False):
            raise HTTPException(
                status_code=403,
                detail="无权限执行此操作"
            )
        
        # 执行刷新
        await request_log_service.flush_buffer()
        
        return {
            "message": "日志缓冲区刷新成功",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"刷新日志缓冲区失败: {e}")
        raise HTTPException(
            status_code=500,
            detail="刷新操作失败"
        )


@router.delete("/cleanup-logs")
async def cleanup_old_logs(
    days_to_keep: int = Query(90, ge=1, le=365, description="保留天数"),
    current_user: dict = Depends(get_current_user)
):
    """清理旧的日志记录
    
    Args:
        days_to_keep: 保留天数（1-365天）
        current_user: 当前用户信息
        
    Returns:
        dict: 操作结果
    """
    try:
        # 只有管理员可以清理日志
        if not current_user.get("is_superuser", False):
            raise HTTPException(
                status_code=403,
                detail="无权限执行此操作"
            )
        
        # 执行清理
        await request_log_service.cleanup_old_logs(days_to_keep=days_to_keep)
        
        return {
            "message": f"成功清理{days_to_keep}天前的日志记录",
            "days_kept": days_to_keep,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清理旧日志失败: {e}")
        raise HTTPException(
            status_code=500,
            detail="清理操作失败"
        )


@router.get("/request-types")
async def get_request_types():
    """获取可用的请求类型列表
    
    Returns:
        dict: 请求类型列表
    """
    return {
        "request_types": [
            {
                "value": request_type.value,
                "name": request_type.value,
                "description": {
                    "business_api": "业务API请求",
                    "openai_api": "OpenAI兼容API请求",
                    "public_api": "公开API请求",
                    "static": "静态资源请求"
                }.get(request_type.value, request_type.value)
            }
            for request_type in RequestType
        ]
    }


@router.get("/health")
async def analytics_health():
    """分析服务健康检查
    
    Returns:
        dict: 健康状态
    """
    try:
        # 检查日志服务状态
        stats = await request_log_service.get_stats()
        
        return {
            "status": "healthy",
            "service": "analytics",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "buffer_size": stats.get("buffer_size", 0),
            "total_requests": stats.get("total_requests", 0)
        }
        
    except Exception as e:
        logger.error(f"分析服务健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "service": "analytics",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "error": str(e)
        }
