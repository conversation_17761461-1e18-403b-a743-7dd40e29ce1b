"""
模型路由映射配置
支持动态配置模型名称到后端服务的映射关系
"""
import os
from typing import Dict, Optional
from dataclasses import dataclass

# 默认后端服务API Key (所有模型共用)
DEFAULT_BACKEND_API_KEY = "y03ZvBwVvHAqAo0nosjdlVGCfCNYGlgm8U"


@dataclass
class ModelConfig:
    """模型配置信息"""
    name: str                    # 模型名称
    service_url: str            # 后端服务URL
    description: str = ""       # 模型描述
    max_tokens: Optional[int] = None  # 最大token限制
    owned_by: str = "vet-ai-platform"  # 模型所有者
    # 后端服务API Key (可选，默认使用DEFAULT_BACKEND_API_KEY)
    api_key: Optional[str] = None

    def get_api_key(self) -> str:
        """获取API Key，如果未配置则使用默认值"""
        return self.api_key or DEFAULT_BACKEND_API_KEY


class ModelRouteManager:
    """模型路由管理器"""

    def __init__(self):
        self._model_configs: Dict[str, ModelConfig] = {}
        self._load_default_configs()

    def _load_default_configs(self):
        """
        加载默认模型配置

        所有模型请求现在直接路由到远程生产服务。
        如需要本地开发测试，可配置指向公开可用的OpenAI兼容API服务。
        """

        # 企业自有模型配置 - 主要生产模型
        self.register_model(ModelConfig(
            name="ds-vet-answer-32B",
            service_url="http://************:8888",
            description="宠物医疗专用32B参数模型",
            max_tokens=4096,  # ds-vet-answer-32B模型最大token数8196
            owned_by="vet-ai-platform"
        ))

        # self.register_model(ModelConfig(
        #     name="ds-vet-answer-72B",
        #     service_url="http://************:8888",
        #     description="宠物医疗专用72B参数模型",
        #     max_tokens=8192,
        #     owned_by="vet-ai-platform"
        # ))

        # 可选：如需要免费开发测试，可配置公开API服务
        # 示例：配置指向免费的OpenAI兼容服务
        # self.register_model(ModelConfig(
        #     name="free-test-model",
        #     service_url="https://api.openai.com",  # 或其他免费服务
        #     description="免费测试模型（需要有效API Key）",
        #     max_tokens=4096,
        #     owned_by="external-provider",
        #     api_key="your-free-api-key"  # 需要配置有效的API Key
        # ))

    def register_model(self, config: ModelConfig):
        """注册模型配置"""
        self._model_configs[config.name] = config

    def get_model_config(self, model_name: str) -> Optional[ModelConfig]:
        """获取模型配置"""
        return self._model_configs.get(model_name)

    def get_all_models(self) -> Dict[str, ModelConfig]:
        """获取所有模型配置"""
        return self._model_configs.copy()

    def get_backend_model_name(self, model_name: str) -> str:
        """获取后端实际模型名称（现在直接返回原模型名称）"""
        config = self.get_model_config(model_name)
        if not config:
            return model_name

        # 直接返回模型名称，不再进行映射
        return model_name

    def is_model_supported(self, model_name: str) -> bool:
        """检查模型是否支持"""
        return model_name in self._model_configs

    def get_service_url(self, model_name: str) -> Optional[str]:
        """获取模型对应的服务URL"""
        config = self.get_model_config(model_name)
        return config.service_url if config else None

    def get_api_key(self, model_name: str) -> Optional[str]:
        """获取模型对应的API Key"""
        config = self.get_model_config(model_name)
        return config.api_key if config else None


# 全局模型路由管理器实例
model_route_manager = ModelRouteManager()


def load_model_configs_from_env():
    """从环境变量加载模型配置（可选功能）"""

    # 示例：从环境变量加载自定义模型
    custom_model_name = os.getenv("CUSTOM_MODEL_NAME")
    custom_model_url = os.getenv("CUSTOM_MODEL_URL")
    custom_model_key = os.getenv("CUSTOM_MODEL_API_KEY")

    if all([custom_model_name, custom_model_url, custom_model_key]):
        model_route_manager.register_model(ModelConfig(
            name=custom_model_name,
            service_url=custom_model_url,
            api_key=custom_model_key,
            description="从环境变量加载的自定义模型",
            owned_by="custom"
        ))


def load_model_configs_from_database():
    """从数据库加载模型配置（未来扩展功能）"""
    # TODO: 实现从数据库加载模型配置
    # 这里可以连接数据库，动态加载模型配置
    pass


# 初始化时加载配置
load_model_configs_from_env()
