"""
API网关配置模块

该模块定义了宠物医疗平台API网关的所有配置参数，包括服务路由、数据库连接、
认证设置、限流配置等。所有配置项都支持通过环境变量进行覆盖。
"""
import os
from typing import Dict, List

from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """API网关配置类

    使用Pydantic BaseSettings自动从环境变量加载配置，
    支持类型验证和默认值设置。
    """

    # API基础配置
    API_V1_STR: str = "/api/v1"  # API版本前缀
    PROJECT_NAME: str = "宠物医疗AI开放平台 - 网关服务"  # 项目名称
    PROJECT_VERSION: str = "1.0.0"  # 项目版本
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"  # 调试模式开关

    # 网关服务配置
    GATEWAY_PORT: int = int(os.getenv("GATEWAY_PORT", "8000"))  # 网关监听端口

    # 后端微服务URL配置
    # 用户服务：处理用户管理、认证、权限等功能
    USER_SERVICE_URL: str = os.getenv(
        "USER_SERVICE_URL", "http://localhost:8001")
    # 宠物服务：处理宠物信息、医疗记录、疫苗等功能
    PET_SERVICE_URL: str = os.getenv(
        "PET_SERVICE_URL", "http://localhost:8002")
    # 应用服务：处理第三方应用、AI代理等功能
    APP_SERVICE_URL: str = os.getenv(
        "APP_SERVICE_URL", "http://localhost:8003")
    # OCR服务：处理图片OCR识别功能
    OCR_SERVICE_URL: str = os.getenv(
        "OCR_SERVICE_URL", "http://localhost:8004")

    # 数据存储配置（网关层使用，用于缓存和会话管理）
    DATABASE_URL: str = os.getenv(
        "DATABASE_URL",
        "postgresql://postgres:123456@localhost:5432/vet_platform"
    )  # PostgreSQL数据库连接字符串
    REDIS_URL: str = os.getenv(
        "REDIS_URL", "redis://localhost:6379/0")  # Redis缓存连接字符串

    # JWT认证配置
    SECRET_KEY: str = os.getenv(
        "SECRET_KEY", "your-super-secret-key-change-this-in-production")  # JWT签名密钥
    ALGORITHM: str = os.getenv("ALGORITHM", "HS256")  # JWT签名算法

    # 跨域资源共享(CORS)配置
    # 允许访问API的前端域名列表
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",  # React开发服务器
        "http://localhost:8080",  # Vue开发服务器
        "http://localhost:8000"   # 本地测试
    ]

    # 限流配置
    RATE_LIMIT_PER_MINUTE: int = int(
        os.getenv("RATE_LIMIT_PER_MINUTE", "60"))  # 每分钟请求限制
    RATE_LIMIT_BURST: int = int(os.getenv("RATE_LIMIT_BURST", "10"))  # 突发请求限制

    # 请求超时配置
    REQUEST_TIMEOUT: int = int(
        os.getenv("REQUEST_TIMEOUT", "120"))  # 后端服务请求超时时间（秒）

    # AI相关端点的超时配置（更长的超时时间）
    AI_REQUEST_TIMEOUT: int = int(
        os.getenv("AI_REQUEST_TIMEOUT", "180"))  # AI处理请求超时时间（秒）

    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")  # 日志级别
    LOG_FORMAT: str = os.getenv("LOG_FORMAT", "json")  # 日志格式

    # 服务路由配置
    # 定义API路径前缀到后端服务的映射关系
    SERVICE_ROUTES: Dict[str, str] = {
        # 用户服务路由
        "/api/v1/auth": USER_SERVICE_URL,           # 认证相关接口
        "/api/v1/users": USER_SERVICE_URL,          # 用户管理接口
        "/api/v1/permissions": USER_SERVICE_URL,    # 权限管理接口
        "/api/v1/api-keys": USER_SERVICE_URL,       # API密钥管理接口

        # 租户管理路由（新增）
        "/api/v1/tenants": USER_SERVICE_URL,        # 租户管理接口
        "/api/v1/tenant-management": USER_SERVICE_URL,  # 租户管理扩展接口

        # 宠物服务路由
        "/api/v1/pets": PET_SERVICE_URL,            # 宠物信息管理接口
        "/api/v1/medical-records": PET_SERVICE_URL,  # 医疗记录接口
        "/api/v1/vaccinations": PET_SERVICE_URL,    # 疫苗记录接口
        "/api/v1/breeds": PET_SERVICE_URL,          # 宠物品种接口

        # 应用服务路由
        "/api/v1/applications": APP_SERVICE_URL,    # 第三方应用管理接口
        "/api/v1/agents": APP_SERVICE_URL,          # AI代理管理接口
        "/api/v1/system-agents": APP_SERVICE_URL,   # 系统级智能体专用接口
        "/api/v1/conversations": APP_SERVICE_URL,   # 会话管理接口（包含消息和上下文管理）
        "/api/v1/beta-cat-agents": APP_SERVICE_URL,   # 贝塔猫智能体专用接口
        "/api/v1/xiao-pei-agents": APP_SERVICE_URL,   # 小佩智能体专用接口

        # OCR服务路由
        "/api/v1/ocr": OCR_SERVICE_URL,             # OCR识别接口
    }

    # 服务健康检查端点配置
    # 用于监控各个后端服务的健康状态
    SERVICE_HEALTH_ENDPOINTS: Dict[str, str] = {
        "user_service": f"{USER_SERVICE_URL}/health",  # 用户服务健康检查
        "pet_service": f"{PET_SERVICE_URL}/health",    # 宠物服务健康检查
        "app_service": f"{APP_SERVICE_URL}/health",    # 应用服务健康检查
        "ocr_service": f"{OCR_SERVICE_URL}/health",    # OCR服务健康检查
        # 注意：LLM服务通过模型路由动态管理，不在此处配置健康检查
    }

    class Config:
        """Pydantic配置类"""
        case_sensitive = True  # 环境变量名称大小写敏感


# 全局配置实例
# 在应用启动时创建，供整个应用使用
settings = Settings()
