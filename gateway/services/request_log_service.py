"""
API请求日志服务

该模块提供API请求日志的数据库操作服务，包括：
- 异步批量插入优化
- 错误处理和重试机制
- 性能监控和统计
- 数据清理和归档
"""
import asyncio
import time
import uuid
from collections import deque
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any

from sqlalchemy import select, func, and_
from sqlalchemy.exc import SQLAlchemyError

from gateway.models.request_log import APIRequestLog, RequestType, AuthType
from shared.database import AsyncSessionLocal
from shared.logging_config import get_gateway_logger

logger = get_gateway_logger()


class RequestLogService:
    """API请求日志服务类

    提供高性能的请求日志记录服务，支持：
    - 异步批量插入
    - 内存缓冲和定时刷新
    - 错误处理和重试
    - 性能优化
    """

    def __init__(self, batch_size: int = 100, flush_interval: int = 5):
        """初始化请求日志服务

        Args:
            batch_size: 批量插入的大小
            flush_interval: 刷新间隔（秒）
        """
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        self._buffer = deque()
        self._last_flush = time.time()
        self._flush_lock = asyncio.Lock()
        self._stats = {
            'total_requests': 0,
            'successful_inserts': 0,
            'failed_inserts': 0,
            'buffer_size': 0
        }

    async def log_request(
        self,
        request_id: str,
        method: str,
        path: str,
        request_type: RequestType,
        client_ip: str = None,
        user_agent: str = None,
        auth_type: AuthType = None,
        user_id: int = None,
        user_email: str = None,
        api_key_id: int = None,
        api_key_name: str = None,
        # 响应信息（可选，用于一次性创建完整记录）
        status_code: int = None,
        processing_time_ms: float = None,
        response_content_type: str = None,
        response_content_length: int = None,
        error_type: str = None,
        error_message: str = None,
        **kwargs
    ) -> APIRequestLog:
        """记录API请求日志

        Args:
            request_id: 请求唯一ID
            method: HTTP方法
            path: 请求路径
            request_type: 请求类型
            client_ip: 客户端IP
            user_agent: 用户代理
            auth_type: 认证类型
            user_id: 用户ID
            user_email: 用户邮箱
            api_key_id: API密钥ID
            api_key_name: API密钥名称
            **kwargs: 其他参数

        Returns:
            APIRequestLog: 创建的日志记录对象
        """
        try:
            # 创建日志记录对象
            log_entry = APIRequestLog(
                request_id=uuid.UUID(request_id) if isinstance(
                    request_id, str) else request_id,
                method=method,
                path=path,
                request_type=request_type,
                client_ip=client_ip,
                user_agent=user_agent,
                auth_type=auth_type,
                user_id=user_id,
                user_email=user_email,
                api_key_id=api_key_id,
                api_key_name=api_key_name,
                # 响应信息（如果提供）
                status_code=status_code,
                processing_time_ms=processing_time_ms,
                response_content_type=response_content_type,
                response_content_length=response_content_length,
                error_type=error_type,
                error_message=error_message,
                is_successful=(200 <= status_code <
                               400) if status_code else None,
                **kwargs
            )

            # 如果提供了响应信息，设置结束时间
            if status_code is not None:
                log_entry.request_end_time = datetime.now(timezone.utc)

            # 添加到缓冲区
            self._buffer.append(log_entry)
            self._stats['total_requests'] += 1
            self._stats['buffer_size'] = len(self._buffer)

            # 检查是否需要刷新
            await self._check_and_flush()

            return log_entry

        except Exception as e:
            logger.error(f"创建请求日志失败: {e}")
            self._stats['failed_inserts'] += 1
            raise

    async def update_response(
        self,
        request_id: str,
        status_code: int,
        processing_time_ms: float,
        response_content_type: str = None,
        response_content_length: int = None,
        error_type: str = None,
        error_message: str = None
    ):
        """更新请求的响应信息

        Args:
            request_id: 请求ID
            status_code: HTTP状态码
            processing_time_ms: 处理时间（毫秒）
            response_content_type: 响应内容类型
            response_content_length: 响应内容长度
            error_type: 错误类型
            error_message: 错误消息
        """
        try:
            # 首先尝试在缓冲区中查找
            for log_entry in self._buffer:
                if str(log_entry.request_id) == str(request_id):
                    log_entry.update_response_data(
                        status_code=status_code,
                        processing_time_ms=processing_time_ms,
                        response_content_type=response_content_type,
                        response_content_length=response_content_length,
                        error_type=error_type,
                        error_message=error_message
                    )
                    return

            # 如果缓冲区中没有找到，直接更新数据库
            await self._update_response_in_db(
                request_id=request_id,
                status_code=status_code,
                processing_time_ms=processing_time_ms,
                response_content_type=response_content_type,
                response_content_length=response_content_length,
                error_type=error_type,
                error_message=error_message
            )

        except Exception as e:
            logger.error(f"更新响应信息失败 {request_id}: {e}")

    async def _update_response_in_db(
        self,
        request_id: str,
        status_code: int,
        processing_time_ms: float,
        response_content_type: str = None,
        response_content_length: int = None,
        error_type: str = None,
        error_message: str = None
    ):
        """直接在数据库中更新响应信息"""
        try:
            async with AsyncSessionLocal() as db:
                # 查找记录
                query = select(APIRequestLog).where(
                    APIRequestLog.request_id == uuid.UUID(request_id)
                )
                result = await db.execute(query)
                log_entry = result.scalar_one_or_none()

                if log_entry:
                    # 更新响应数据
                    log_entry.update_response_data(
                        status_code=status_code,
                        processing_time_ms=processing_time_ms,
                        response_content_type=response_content_type,
                        response_content_length=response_content_length,
                        error_type=error_type,
                        error_message=error_message
                    )

                    await db.commit()
                    logger.debug(f"数据库中更新响应信息成功: {request_id}")
                else:
                    logger.warning(f"未找到请求记录: {request_id}")

        except Exception as e:
            logger.error(f"数据库更新响应信息失败 {request_id}: {e}")

    async def _check_and_flush(self):
        """检查并刷新缓冲区"""
        current_time = time.time()
        should_flush = (
            len(self._buffer) >= self.batch_size or
            (current_time - self._last_flush) >= self.flush_interval
        )

        if should_flush:
            await self.flush_buffer()

    async def flush_buffer(self):
        """刷新缓冲区到数据库"""
        async with self._flush_lock:
            if not self._buffer:
                return

            # 获取当前缓冲区的所有记录
            batch = list(self._buffer)
            self._buffer.clear()

            try:
                await self._batch_insert(batch)
                self._stats['successful_inserts'] += len(batch)
                self._last_flush = time.time()
                self._stats['buffer_size'] = len(self._buffer)

                logger.debug(f"批量插入成功: {len(batch)} 条记录")

            except Exception as e:
                logger.error(f"批量插入失败: {e}")
                # 将失败的记录重新加入缓冲区
                self._buffer.extendleft(reversed(batch))
                self._stats['failed_inserts'] += len(batch)
                raise

    async def _batch_insert(self, batch: List[APIRequestLog]):
        """批量插入记录到数据库"""
        if not batch:
            return

        try:
            async with AsyncSessionLocal() as db:
                db.add_all(batch)
                await db.commit()

        except SQLAlchemyError as e:
            logger.error(f"数据库批量插入失败: {e}")
            raise
        except Exception as e:
            logger.error(f"批量插入过程中发生未知错误: {e}")
            raise

    async def get_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        return {
            **self._stats,
            'last_flush': self._last_flush,
            'flush_interval': self.flush_interval,
            'batch_size': self.batch_size
        }

    async def get_request_stats(
        self,
        start_time: datetime = None,
        end_time: datetime = None,
        user_id: int = None,
        request_type: RequestType = None
    ) -> Dict[str, Any]:
        """获取请求统计信息

        Args:
            start_time: 开始时间
            end_time: 结束时间
            user_id: 用户ID
            request_type: 请求类型

        Returns:
            Dict: 统计信息
        """
        try:
            async with AsyncSessionLocal() as db:
                # 构建查询条件
                conditions = []

                if start_time:
                    conditions.append(
                        APIRequestLog.request_start_time >= start_time)
                if end_time:
                    conditions.append(
                        APIRequestLog.request_start_time <= end_time)
                if user_id:
                    conditions.append(APIRequestLog.user_id == user_id)
                if request_type:
                    conditions.append(
                        APIRequestLog.request_type == request_type)

                # 基础统计查询
                base_query = select(APIRequestLog)
                if conditions:
                    base_query = base_query.where(and_(*conditions))

                # 总请求数
                total_query = select(func.count(APIRequestLog.id))
                if conditions:
                    total_query = total_query.where(and_(*conditions))

                total_result = await db.execute(total_query)
                total_requests = total_result.scalar()

                # 成功请求数
                success_query = select(func.count(APIRequestLog.id)).where(
                    APIRequestLog.is_successful == True
                )
                if conditions:
                    success_query = success_query.where(and_(*conditions))

                success_result = await db.execute(success_query)
                successful_requests = success_result.scalar()

                # 平均响应时间
                avg_time_query = select(
                    func.avg(APIRequestLog.processing_time_ms))
                if conditions:
                    avg_time_query = avg_time_query.where(and_(*conditions))

                avg_time_result = await db.execute(avg_time_query)
                avg_response_time = avg_time_result.scalar()

                return {
                    'total_requests': total_requests or 0,
                    'successful_requests': successful_requests or 0,
                    'failed_requests': (total_requests or 0) - (successful_requests or 0),
                    'success_rate': (successful_requests / total_requests * 100) if total_requests else 0,
                    'avg_response_time_ms': float(avg_response_time) if avg_response_time else 0,
                    'period': {
                        'start_time': start_time.isoformat() if start_time else None,
                        'end_time': end_time.isoformat() if end_time else None
                    }
                }

        except Exception as e:
            logger.error(f"获取请求统计失败: {e}")
            return {}

    async def cleanup_old_logs(self, days_to_keep: int = 90):
        """清理旧的日志记录

        Args:
            days_to_keep: 保留天数
        """
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_to_keep)

            async with AsyncSessionLocal() as db:
                # 删除旧记录
                delete_query = select(APIRequestLog).where(
                    APIRequestLog.request_start_time < cutoff_date
                )

                result = await db.execute(delete_query)
                old_logs = result.scalars().all()

                for log in old_logs:
                    await db.delete(log)

                await db.commit()

                logger.info(f"清理了 {len(old_logs)} 条旧日志记录（{days_to_keep}天前）")

        except Exception as e:
            logger.error(f"清理旧日志失败: {e}")


# 全局请求日志服务实例
request_log_service = RequestLogService()
