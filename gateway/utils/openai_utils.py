"""
OpenAI代理工具模块

该模块提供了OpenAI代理相关的工具函数和常量定义，
用于减少代码重复和提高代码可维护性。
"""
import json
from typing import Dict, Any, Optional

from fastapi import HTTPException, status
from fastapi.responses import J<PERSON><PERSON>esponse

from shared.logging_config import get_gateway_logger

logger = get_gateway_logger()


class OpenAIProxyConfig:
    """OpenAI代理配置常量"""

    # 缓存配置
    CACHE_TTL = 300  # 5分钟
    CACHE_PREFIX = "unified_auth_cache"

    # 速率限制默认值
    RATE_LIMIT_PER_MINUTE_DEFAULT = 60
    RATE_LIMIT_PER_DAY_DEFAULT = 10000

    # API密钥配置
    API_KEY_MIN_LENGTH = 10
    API_KEY_PREVIEW_LENGTH = 10

    # 内容处理配置
    CHUNK_SIZE = 10  # 流式响应数据块大小
    CONTENT_PREVIEW_LENGTH = 100  # 内容预览长度

    # 调试配置
    DEBUG_SEPARATOR = "=" * 80

    # 超时配置
    OPENAI_CLIENT_TIMEOUT = 300.0
    HTTP_CLIENT_TIMEOUT = 60.0

    # 默认模型
    DEFAULT_MODEL = "gpt-3.5-turbo"
    DEFAULT_EMBEDDING_MODEL = "text-embedding-ada-002"


class OpenAIErrorHandler:
    """OpenAI错误处理工具类"""

    @staticmethod
    def create_error_response(
        message: str,
        error_type: str,
        code: str,
        detail: Optional[str] = None,
        status_code: int = status.HTTP_400_BAD_REQUEST,
        extra_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        创建标准的OpenAI错误响应格式

        Args:
            message: 错误消息
            error_type: 错误类型
            code: 错误代码
            detail: 详细错误信息
            status_code: HTTP状态码
            extra_data: 额外数据

        Returns:
            标准格式的错误响应字典
        """
        error_response = {
            "error": {
                "message": message,
                "type": error_type,
                "code": code
            }
        }

        if detail:
            error_response["error"]["detail"] = detail

        if extra_data:
            error_response["error"].update(extra_data)

        return error_response

    @staticmethod
    def raise_http_exception(
        message: str,
        error_type: str,
        code: str,
        detail: Optional[str] = None,
        status_code: int = status.HTTP_400_BAD_REQUEST,
        extra_data: Optional[Dict[str, Any]] = None
    ):
        """
        抛出标准格式的HTTP异常

        Args:
            message: 错误消息
            error_type: 错误类型
            code: 错误代码
            detail: 详细错误信息
            status_code: HTTP状态码
            extra_data: 额外数据
        """
        error_response = OpenAIErrorHandler.create_error_response(
            message, error_type, code, detail, status_code, extra_data
        )
        raise HTTPException(status_code=status_code, detail=error_response)


class OpenAIResponseFormatter:
    """OpenAI响应格式化工具类"""

    @staticmethod
    def format_chat_completion_response(
        response,
        content_standardizer=None
    ) -> Dict[str, Any]:
        """
        格式化聊天完成响应

        Args:
            response: OpenAI响应对象
            content_standardizer: 内容标准化函数

        Returns:
            标准格式的响应字典
        """
        response_data = {
            "id": response.id,
            "object": response.object,
            "created": response.created,
            "model": response.model,
            "choices": [],
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            }
        }

        for choice in response.choices:
            choice_data = {
                "index": choice.index,
                "message": {
                    "role": choice.message.role,
                    "content": choice.message.content
                },
                "finish_reason": choice.finish_reason
            }

            # 应用内容标准化
            if content_standardizer and choice.message.content:
                choice_data["message"]["content"] = content_standardizer(
                    choice.message.content
                )

            response_data["choices"].append(choice_data)

        return response_data

    @staticmethod
    def format_embeddings_response(response) -> Dict[str, Any]:
        """
        格式化嵌入向量响应

        Args:
            response: OpenAI嵌入响应对象

        Returns:
            标准格式的响应字典
        """
        return {
            "object": "list",
            "data": [
                {
                    "object": "embedding",
                    "index": i,
                    "embedding": embedding_item.embedding
                }
                for i, embedding_item in enumerate(response.data)
            ],
            "model": response.model,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "total_tokens": response.usage.total_tokens
            }
        }


class DebugLogger:
    """调试日志工具类"""

    @staticmethod
    def log_section(title: str, request_id: str = ""):
        """
        记录调试分节

        Args:
            title: 分节标题
            request_id: 请求ID
        """
        # loguru logger 不需要检查级别，直接调用即可
        logger.debug(OpenAIProxyConfig.DEBUG_SEPARATOR)
        if request_id:
            logger.debug(f"{title} [ID: {request_id}]")
        else:
            logger.debug(title)
        logger.debug(OpenAIProxyConfig.DEBUG_SEPARATOR)

    @staticmethod
    def log_request_details(request_data, user_info: Dict[str, Any], request_id: str):
        """
        记录请求详情

        Args:
            request_data: 请求数据
            user_info: 用户信息
            request_id: 请求ID
        """
        # loguru logger 会根据配置的级别自动过滤，无需手动检查

        DebugLogger.log_section("🔍 DEBUG模式 - 请求详情", request_id)

        # 打印用户信息
        logger.debug(
            f"👤 用户信息: ID={user_info.get('id')}, Email={user_info.get('email', 'N/A')}")

        # 打印模型和基本参数
        logger.debug(f"🤖 模型: {request_data.model}")
        logger.debug(f"🌊 流式响应: {request_data.stream}")
        logger.debug(f"🌡️ Temperature: {request_data.temperature}")
        logger.debug(f"📏 Max Tokens: {request_data.max_tokens}")
        logger.debug(f"🎯 Top P: {request_data.top_p}")

        # 打印对话消息
        logger.debug(f"💬 对话消息 ({len(request_data.messages)} 条):")
        for i, message in enumerate(request_data.messages, 1):
            role_emoji = {"system": "⚙️", "user": "👤", "assistant": "🤖"}.get(
                message.role, "❓")
            content_preview = (
                message.content[:OpenAIProxyConfig.CONTENT_PREVIEW_LENGTH] + "..."
                if len(message.content) > OpenAIProxyConfig.CONTENT_PREVIEW_LENGTH
                else message.content
            )
            logger.debug(
                f"  {i}. {role_emoji} {message.role}: {content_preview}")

        # 如果有用户问题，特别标注
        user_messages = [
            msg for msg in request_data.messages if msg.role == "user"]
        if user_messages:
            latest_user_msg = user_messages[-1]
            logger.debug(f"❓ 用户最新问题: {latest_user_msg.content}")


class APIKeyFormatter:
    """API密钥格式化工具类"""

    @staticmethod
    def format_for_log(api_key: str) -> str:
        """
        格式化API密钥用于日志输出

        Args:
            api_key: 完整的API密钥

        Returns:
            格式化后的API密钥（用于日志安全显示）
        """
        if not api_key or len(api_key) < OpenAIProxyConfig.API_KEY_PREVIEW_LENGTH:
            return "invalid_key"
        return f"{api_key[:OpenAIProxyConfig.API_KEY_PREVIEW_LENGTH]}..."

    @staticmethod
    def validate_format(api_key: str) -> bool:
        """
        验证API密钥格式

        Args:
            api_key: API密钥字符串

        Returns:
            格式是否有效
        """
        if not api_key:
            return False

        # 检查长度
        if len(api_key) < OpenAIProxyConfig.API_KEY_MIN_LENGTH:
            return False

        # 检查前缀
        if not (api_key.startswith("sk-proj-") or api_key.startswith("sk-")):
            return False

        return True


def create_json_response(
    content: Dict[str, Any],
    status_code: int = 200,
    headers: Optional[Dict[str, str]] = None
) -> JSONResponse:
    """
    创建JSON响应

    Args:
        content: 响应内容
        status_code: HTTP状态码
        headers: 响应头

    Returns:
        JSONResponse对象
    """
    response_headers = headers or {}
    return JSONResponse(
        content=content,
        status_code=status_code,
        headers=response_headers
    )
