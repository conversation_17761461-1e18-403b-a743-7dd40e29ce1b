"""
OpenAI兼容API计费和使用统计中间件模块

该模块实现了OpenAI兼容API的计费和使用统计功能，包括：
- 用户使用限制检查
- Token使用量统计
- 成本计算
- 使用日志记录
支持多种用户等级的差异化限制和定价策略。
"""
import json
import time
from typing import Dict, Any, Optional

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from shared.redis_simple import redis_client
from shared.logging_config import get_gateway_logger

logger = get_gateway_logger()


class OpenAIBillingMiddleware(BaseHTTPMiddleware):
    """OpenAI兼容API计费和使用统计中间件类

    负责处理OpenAI兼容API的计费相关功能：
    1. 用户使用限制检查（请求数量、Token数量）
    2. 使用统计记录（月度统计、详细日志）
    3. 成本计算（基于用户等级的差异化定价）
    4. 超限保护（防止用户超出配额）
    """

    def __init__(self, app):
        """初始化计费中间件

        Args:
            app: FastAPI应用实例
        """
        super().__init__(app)
        # OpenAI兼容API端点路径列表
        self.openai_paths = [
            "/v1/chat/completions",  # 聊天完成端点
            "/v1/embeddings",        # 嵌入向量端点
            "/v1/models"             # 模型列表端点
        ]

    async def dispatch(self, request: Request, call_next):
        """处理请求的计费中间件主入口

        Args:
            request: FastAPI请求对象
            call_next: 下一个中间件或路由处理器

        Returns:
            响应对象
        """

        # 检查是否是OpenAI兼容API请求
        if not self._is_openai_request(request):
            return await call_next(request)

        # 记录请求开始时间，用于计算处理耗时
        start_time = time.time()

        # 获取用户信息（从认证中间件传递）
        user_info = await self._get_user_info(request)

        # 检查用户使用限制
        if user_info and not await self._check_user_limits(user_info):
            return Response(
                content=json.dumps({
                    "error": {
                        "message": "使用限制已超出",
                        "type": "quota_exceeded_error",
                        "code": "quota_exceeded"
                    }
                }),
                status_code=429,
                media_type="application/json"
            )

        # 处理请求
        response = await call_next(request)

        # 记录使用统计（仅在请求成功时）
        if user_info and response.status_code == 200:
            await self._record_usage(
                user_info=user_info,
                request=request,
                response=response,
                duration=time.time() - start_time
            )

        return response

    def _is_openai_request(self, request: Request) -> bool:
        """检查是否是OpenAI兼容API请求

        Args:
            request: FastAPI请求对象

        Returns:
            bool: 是否为OpenAI兼容API请求
        """
        return any(request.url.path.startswith(path) for path in self.openai_paths)

    async def _get_user_info(self, request: Request) -> Optional[Dict[str, Any]]:
        """从请求中获取用户信息

        Args:
            request: FastAPI请求对象

        Returns:
            Optional[Dict[str, Any]]: 用户信息字典，包含ID、邮箱、等级等
        """

        # 从请求状态中获取用户信息（由认证中间件设置）
        if hasattr(request.state, 'user'):
            user = request.state.user
            return {
                "id": user.id,
                "email": user.email,
                "tier": getattr(user, 'tier', 'free')  # 默认为免费用户
            }

        return None

    async def _check_user_limits(self, user_info: Dict[str, Any]) -> bool:
        """检查用户使用限制

        Args:
            user_info: 用户信息字典

        Returns:
            bool: 是否在限制范围内
        """

        user_id = user_info["id"]
        tier = user_info.get("tier", "free")

        # 获取当前月份的使用统计
        current_month = time.strftime("%Y-%m")
        usage_key = f"openai_usage:{user_id}:{current_month}"

        try:
            usage_data = await redis_client.get(usage_key)
            if usage_data:
                usage = json.loads(usage_data)
            else:
                usage = {"requests": 0, "tokens": 0}

            # 根据用户等级检查限制
            limits = self._get_user_limits(tier)

            # 检查请求数量限制
            if usage["requests"] >= limits["max_requests"]:
                logger.warning(f"用户 {user_id} 超出请求数量限制")
                return False

            # 检查Token数量限制
            if usage["tokens"] >= limits["max_tokens"]:
                logger.warning(f"用户 {user_id} 超出Token数量限制")
                return False

            return True

        except Exception as e:
            logger.error(f"检查用户限制时发生错误: {e}")
            # 在错误情况下允许请求通过，避免服务中断
            return True

    def _get_user_limits(self, tier: str) -> Dict[str, int]:
        """获取用户等级对应的使用限制

        Args:
            tier: 用户等级（free/premium/enterprise）

        Returns:
            Dict[str, int]: 包含最大请求数和最大Token数的限制字典
        """

        # 不同用户等级的使用限制配置
        limits = {
            "free": {
                "max_requests": 100,      # 免费用户：每月100次请求
                "max_tokens": 10000       # 免费用户：每月10K Token
            },
            "premium": {
                "max_requests": 1000,     # 高级用户：每月1000次请求
                "max_tokens": 100000      # 高级用户：每月100K Token
            },
            "enterprise": {
                "max_requests": 10000,    # 企业用户：每月10000次请求
                "max_tokens": 1000000     # 企业用户：每月1M Token
            }
        }

        return limits.get(tier, limits["free"])

    async def _record_usage(
        self,
        user_info: Dict[str, Any],
        request: Request,
        response: Response,
        duration: float
    ):
        """记录用户使用统计

        Args:
            user_info: 用户信息字典
            request: FastAPI请求对象
            response: FastAPI响应对象
            duration: 请求处理耗时（秒）
        """

        user_id = user_info["id"]
        current_month = time.strftime("%Y-%m")

        # 估算Token使用量（简化版本，实际应该从LLM服务响应中解析）
        estimated_tokens = await self._estimate_tokens(request, response)

        # 更新月度使用统计
        usage_key = f"openai_usage:{user_id}:{current_month}"

        try:
            # 获取当前使用统计
            usage_data = await redis_client.get(usage_key)
            if usage_data:
                usage = json.loads(usage_data)
            else:
                usage = {"requests": 0, "tokens": 0, "cost": 0.0}

            # 更新统计数据
            usage["requests"] += 1
            usage["tokens"] += estimated_tokens
            usage["cost"] += self._calculate_cost(
                estimated_tokens, user_info.get("tier", "free"))

            # 保存到Redis（设置过期时间为3个月）
            await redis_client.set(
                usage_key,
                json.dumps(usage),
                expire=90 * 24 * 3600  # 90天过期
            )

            # 记录详细使用日志
            log_data = {
                "user_id": user_id,
                "path": request.url.path,
                "method": request.method,
                "tokens": estimated_tokens,
                "duration": duration,
                "timestamp": time.time(),
                "tier": user_info.get("tier", "free")
            }

            # 保存详细日志到Redis列表（用于审计和分析）
            await redis_client.lpush(
                f"openai_logs:{user_id}",
                json.dumps(log_data)
            )

            # 限制日志列表长度，保留最近1000条记录
            await redis_client.ltrim(f"openai_logs:{user_id}", 0, 999)

        except Exception as e:
            logger.error(f"记录使用统计时发生错误: {e}")

    async def _estimate_tokens(self, request: Request, response: Response) -> int:
        """估算Token使用量

        Args:
            request: FastAPI请求对象
            response: FastAPI响应对象（当前未使用，预留用于从响应中解析实际Token数）

        Returns:
            int: 估算的Token数量

        Note:
            这是一个简化的估算方法，实际应用中应该从LLM服务的响应中获取准确的Token数量
        """

        try:
            if request.url.path == "/v1/chat/completions":
                # 对于聊天完成API，估算输入和输出Token
                if hasattr(request.state, 'request_body'):
                    body = request.state.request_body
                    if isinstance(body, dict) and "messages" in body:
                        # 简单估算：每个字符约0.75个Token（基于GPT模型的经验值）
                        input_text = " ".join(
                            [msg.get("content", "") for msg in body["messages"]])
                        input_tokens = len(input_text) * 0.75

                        # 输出Token估算（假设平均响应长度）
                        output_tokens = 100  # 默认估算值

                        return int(input_tokens + output_tokens)

            elif request.url.path == "/v1/embeddings":
                # 对于嵌入向量API，主要计算输入Token
                if hasattr(request.state, 'request_body'):
                    body = request.state.request_body
                    if isinstance(body, dict) and "input" in body:
                        input_text = body["input"]
                        if isinstance(input_text, list):
                            input_text = " ".join(input_text)
                        return int(len(input_text) * 0.75)

            # 默认返回值（用于未知端点或解析失败的情况）
            return 50

        except Exception as e:
            logger.error(f"估算Token使用量时发生错误: {e}")
            return 50

    def _calculate_cost(self, tokens: int, tier: str) -> float:
        """计算使用成本

        Args:
            tokens: Token数量
            tier: 用户等级

        Returns:
            float: 计算出的成本（美元）
        """

        # 简化的定价模型（每1000个Token的价格，单位：美元）
        pricing = {
            "free": 0.0,        # 免费用户不收费
            "premium": 0.002,   # 高级用户：$0.002 per 1K tokens
            "enterprise": 0.001  # 企业用户：$0.001 per 1K tokens（批量优惠）
        }

        price_per_1k = pricing.get(tier, pricing["free"])
        return (tokens / 1000) * price_per_1k
