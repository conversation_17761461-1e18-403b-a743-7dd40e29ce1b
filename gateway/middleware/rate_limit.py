"""
API网关限流中间件模块

该模块实现了基于滑动窗口算法的限流中间件，支持：
- 基于用户ID、API密钥或IP地址的限流
- 滑动窗口算法确保精确的限流控制
- 突发请求限制防止短时间内的大量请求
- 自动降级策略确保服务可用性
"""
import time

from fastapi import Request
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from shared.redis_simple import redis_client
from gateway.config.settings import settings
from shared.logging_config import get_gateway_logger

logger = get_gateway_logger()


class RateLimitMiddleware(BaseHTTPMiddleware):
    """API网关限流中间件类

    使用滑动窗口算法实现精确的限流控制，支持：
    1. 基于不同客户端标识的限流（用户ID > API密钥 > IP地址）
    2. 可配置的每分钟请求限制和突发请求限制
    3. Redis存储请求历史，支持分布式部署
    4. 优雅的错误处理和降级策略
    """

    def __init__(self, app, calls_per_minute: int = None, burst_limit: int = None):
        """初始化限流中间件

        Args:
            app: FastAPI应用实例
            calls_per_minute: 每分钟允许的请求数，默认从配置读取
            burst_limit: 突发请求限制，默认从配置读取
        """
        super().__init__(app)
        self.calls_per_minute = calls_per_minute or settings.RATE_LIMIT_PER_MINUTE
        self.burst_limit = burst_limit or settings.RATE_LIMIT_BURST
        self.window_size = 60  # 滑动窗口大小：60秒

    async def dispatch(self, request: Request, call_next):
        """处理请求的限流中间件主入口

        Args:
            request: FastAPI请求对象
            call_next: 下一个中间件或路由处理器

        Returns:
            响应对象或429限流错误响应
        """

        # 获取客户端标识符（用于区分不同的请求来源）
        client_id = self._get_client_id(request)

        # 检查是否超出限流限制
        is_allowed, remaining, reset_time = await self._check_rate_limit(client_id)

        if not is_allowed:
            logger.warning(f"客户端 {client_id} 超出限流限制")
            return JSONResponse(
                status_code=429,
                content={
                    "detail": "请求频率超出限制",
                    "type": "rate_limit_error",
                    "retry_after": reset_time
                },
                headers={
                    "X-RateLimit-Limit": str(self.calls_per_minute),
                    "X-RateLimit-Remaining": str(remaining),
                    "X-RateLimit-Reset": str(reset_time),
                    "Retry-After": str(reset_time)
                }
            )

        # 处理请求
        response = await call_next(request)

        # 在响应中添加限流信息头
        response.headers["X-RateLimit-Limit"] = str(self.calls_per_minute)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(reset_time)

        return response

    def _get_client_id(self, request: Request) -> str:
        """获取客户端标识符用于限流

        优先级：用户ID > API密钥 > IP地址

        Args:
            request: FastAPI请求对象

        Returns:
            str: 客户端标识符
        """
        # 尝试从请求状态中获取用户ID（由认证中间件设置）
        if hasattr(request.state, 'user') and request.state.user:
            user_info = request.state.user
            if user_info.get('auth_type') == 'token':
                return f"user:{user_info.get('user_id')}"
            elif user_info.get('auth_type') == 'api_key':
                return f"api_key:{user_info.get('api_key')}"

        # 回退到IP地址标识
        client_ip = self._get_client_ip(request)
        return f"ip:{client_ip}"

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址

        支持代理环境下的真实IP获取

        Args:
            request: FastAPI请求对象

        Returns:
            str: 客户端IP地址
        """
        # 检查代理转发的IP头（当网关位于代理后面时）
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        # 检查真实IP头
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        # 回退到直连IP
        return request.client.host if request.client else "unknown"

    async def _check_rate_limit(self, client_id: str) -> tuple[bool, int, int]:
        """使用滑动窗口算法检查限流

        滑动窗口算法确保在任意60秒窗口内的请求数不超过限制，
        比固定窗口算法更加精确和公平。

        Args:
            client_id: 客户端标识符

        Returns:
            tuple[bool, int, int]: (是否允许请求, 剩余请求数, 重置时间戳)
        """
        current_time = int(time.time())
        window_start = current_time - self.window_size

        # Redis有序集合键，用于存储该客户端的请求时间戳
        key = f"rate_limit:{client_id}"

        try:
            # 使用Redis管道提高性能
            pipe = redis_client.sync_client.pipeline()

            # 移除窗口外的旧请求记录
            pipe.zremrangebyscore(key, 0, window_start)

            # 统计当前窗口内的请求数量
            pipe.zcard(key)

            # 添加当前请求的时间戳
            pipe.zadd(key, {str(current_time): current_time})

            # 设置键的过期时间（防止内存泄漏）
            pipe.expire(key, self.window_size * 2)

            results = pipe.execute()
            current_count = results[1] + 1  # +1 包含当前请求

            # 检查是否超出限制
            is_allowed = current_count <= self.calls_per_minute
            remaining = max(0, self.calls_per_minute - current_count)
            reset_time = current_time + self.window_size

            # 如果超出限制，移除刚才添加的请求记录
            if not is_allowed:
                redis_client.sync_client.zrem(key, str(current_time))

            return is_allowed, remaining, reset_time

        except Exception as e:
            logger.error(f"客户端 {client_id} 限流检查失败: {e}")
            # Redis故障时采用开放策略，允许请求通过
            return True, self.calls_per_minute, current_time + self.window_size

    async def _check_burst_limit(self, client_id: str) -> bool:
        """检查突发请求限制

        防止短时间内的大量请求对系统造成冲击，
        在较短的时间窗口内限制请求数量。

        Args:
            client_id: 客户端标识符

        Returns:
            bool: 是否允许请求
        """
        current_time = int(time.time())
        burst_window = 10  # 突发检查窗口：10秒
        window_start = current_time - burst_window

        key = f"burst_limit:{client_id}"

        try:
            # 统计突发窗口内的请求数量
            count = redis_client.sync_client.zcount(
                key, window_start, current_time)

            if count >= self.burst_limit:
                return False

            # 添加当前请求到突发窗口
            redis_client.sync_client.zadd(
                key, {str(current_time): current_time})
            redis_client.sync_client.expire(key, burst_window * 2)

            return True

        except Exception as e:
            logger.error(f"客户端 {client_id} 突发限制检查失败: {e}")
            # 错误时允许请求通过
            return True
