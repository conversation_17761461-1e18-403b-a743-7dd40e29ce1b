"""
API网关认证中间件模块

该模块实现了API网关的认证中间件，支持JWT令牌和API密钥两种认证方式。
中间件会自动处理公开端点的跳过认证，以及OpenAI兼容端点的特殊认证逻辑。
"""
import os
from typing import Optional

from fastapi import Request, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from shared.clients.auth_client import create_auth_client
from shared.logging_config import get_gateway_logger
from shared.redis_simple import redis_client
from shared.utils.security import verify_token, verify_api_key

logger = get_gateway_logger()

# 创建认证客户端实例
user_service_url = os.getenv("USER_SERVICE_URL", "http://localhost:8001")
auth_client = create_auth_client(user_service_url)


class AuthMiddleware(BaseHTTPMiddleware):
    """API网关认证中间件类

    负责处理所有进入网关的请求认证，支持：
    1. JWT令牌认证（用于用户会话）
    2. API密钥认证（用于第三方应用）
    3. 公开端点的认证跳过
    4. OpenAI兼容端点的特殊处理
    """

    # 无需认证的公开端点列表
    PUBLIC_ENDPOINTS = {
        "/health",  # 健康检查端点
        "/api/v1/auth/login",  # 用户登录端点
        "/api/v1/auth/register",  # 用户注册端点
        "/api/v1/auth/refresh",  # 令牌刷新端点
        "/docs",  # API文档端点（根路径）
        "/redoc",  # ReDoc文档端点（根路径）
        "/static-offline-docs",  # 离线文档端点（根路径）
        "/openapi.json",  # OpenAPI规范端点（根路径）
        "/api/v1/docs",  # API文档端点（API版本路径）
        "/api/v1/redoc",  # ReDoc文档端点（API版本路径）
        "/api/v1/openapi.json",  # OpenAPI规范端点（API版本路径）
        # App Service 公开端点
        "/api/v1/agents",  # 智能体列表（可查看公开智能体）
    }

    # OpenAI兼容API端点列表（使用自己的API密钥认证）
    OPENAI_ENDPOINTS = {
        "/v1/chat/completions",  # 聊天完成端点
        "/v1/models",  # 模型列表端点
        "/v1/embeddings",  # 嵌入向量端点
        "/v1/usage",  # 使用统计端点
        "/v1/agents",  # 智能体端点
        "/v1/ocr"  # OCR识别端点
    }

    async def dispatch(self, request: Request, call_next):
        """处理请求的认证中间件主入口

        Args:
            request: FastAPI请求对象
            call_next: 下一个中间件或路由处理器

        Returns:
            响应对象
        """

        # 跳过公开端点的认证检查
        if self._is_public_endpoint(request.url.path):
            return await call_next(request)

        # 跳过OpenAI端点的JWT认证（它们有自己的API密钥认证）
        if self._is_openai_endpoint(request.url.path):
            return await call_next(request)

        # 提取Authorization请求头
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            return self._unauthorized_response("缺少授权请求头")

        # 解析Authorization请求头格式
        try:
            scheme, credentials = auth_header.split(" ", 1)
            if scheme.lower() != "bearer":
                return self._unauthorized_response("无效的授权方案")
        except ValueError:
            return self._unauthorized_response("无效的授权请求头格式")

        # 验证凭据（JWT令牌或API密钥）
        user_info = await self._verify_credentials(credentials)
        if not user_info:
            return self._unauthorized_response("无效的凭据")

        # 将用户信息添加到请求状态中，供后续处理使用
        request.state.user = user_info
        request.state.auth_type = user_info.get("auth_type", "token")

        # 记录认证成功的请求日志
        logger.info(
            f"认证成功的请求: {request.method} {request.url.path} "
            f"用户ID: {user_info.get('user_id', 'unknown')} "
            f"认证方式: {user_info.get('auth_type', 'unknown')}"
        )

        return await call_next(request)

    def _is_public_endpoint(self, path: str) -> bool:
        """检查端点是否为公开端点

        Args:
            path: 请求路径

        Returns:
            bool: 是否为公开端点
        """
        # 精确匹配公开端点
        if path in self.PUBLIC_ENDPOINTS:
            return True

        # 模式匹配文档相关端点
        if path.startswith("/docs") or path.startswith("/redoc") or path.startswith("/static-offline-docs"):
            return True

        # 智能体相关的GET请求（查看公开智能体）
        if path.startswith("/api/v1/agents/") and path.count("/") == 4:
            # 匹配 /api/v1/agents/{agent_id} 的GET请求
            return True

        return False

    async def _verify_credentials(self, credentials: str) -> Optional[dict]:
        """验证JWT令牌或API密钥

        Args:
            credentials: 从Authorization头中提取的凭据字符串

        Returns:
            Optional[dict]: 验证成功返回用户信息字典，失败返回None
        """

        # 首先尝试验证JWT令牌
        token_payload = verify_token(credentials, "access")
        if token_payload:
            user_id = token_payload.get("sub")
            if user_id:
                # 检查用户信息是否已缓存在Redis中
                user_cache_key = f"user:{user_id}"
                cached_user = await redis_client.get(user_cache_key)

                if cached_user:
                    return {
                        "user_id": int(user_id),
                        "auth_type": "token",
                        "cached": True,
                        **cached_user
                    }

                # 从用户服务获取用户信息
                try:
                    user_info = await auth_client.verify_jwt_token(credentials, "access")
                    if user_info:
                        # 缓存用户信息
                        # 缓存5分钟
                        await redis_client.setex(user_cache_key, 300, user_info)
                        return {
                            "auth_type": "token",
                            "cached": False,
                            **user_info
                        }
                except Exception as e:
                    logger.error(f"JWT令牌验证失败: {e}")

        # 首先检查是否为临时API密钥
        from shared.utils.security import is_temporary_api_key, verify_temporary_api_key

        if is_temporary_api_key(credentials) and verify_temporary_api_key(credentials):
            # 检查临时API密钥信息是否已缓存
            temp_key_cache_key = f"temp_api_key:{credentials}"
            cached_temp_key = await redis_client.get(temp_key_cache_key)

            if cached_temp_key:
                return {
                    "api_key": cached_temp_key.get("real_api_key"),  # 返回真实API密钥
                    "temp_api_key": credentials,  # 保留临时密钥信息
                    "auth_type": "temporary_api_key",
                    "cached": True,
                    **cached_temp_key.get("user_info", {})
                }

            # 从用户服务验证临时API密钥
            try:
                temp_key_data = await auth_client.verify_temporary_api_key(credentials)
                if temp_key_data:
                    # 缓存临时API密钥信息（缓存时间较短，5分钟）
                    await redis_client.setex(temp_key_cache_key, 300, temp_key_data)
                    return {
                        "api_key": temp_key_data.get("real_api_key"),  # 返回真实API密钥
                        "temp_api_key": credentials,  # 保留临时密钥信息
                        "auth_type": "temporary_api_key",
                        "cached": False,
                        **temp_key_data.get("user_info", {})
                    }
            except Exception as e:
                logger.error(f"临时API密钥验证失败: {e}")

        # 尝试验证真实API密钥
        elif verify_api_key(credentials):
            # 检查API密钥信息是否已缓存
            api_key_cache_key = f"api_key:{credentials}"
            cached_api_key = await redis_client.get(api_key_cache_key)

            if cached_api_key:
                return {
                    "api_key": credentials,
                    "auth_type": "api_key",
                    "cached": True,
                    **cached_api_key
                }

            # 从用户服务验证API密钥
            try:
                user_info = await auth_client.verify_api_key(credentials)
                if user_info:
                    # 缓存API密钥信息
                    # 缓存10分钟
                    await redis_client.setex(api_key_cache_key, 600, user_info)
                    return {
                        "api_key": credentials,
                        "auth_type": "api_key",
                        "cached": False,
                        **user_info
                    }
            except Exception as e:
                logger.error(f"API密钥验证失败: {e}")

        return None

    def _unauthorized_response(self, detail: str) -> JSONResponse:
        """返回未授权响应

        Args:
            detail: 错误详情描述

        Returns:
            JSONResponse: 401未授权响应
        """
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={
                "detail": detail,
                "type": "authentication_error"
            },
            headers={"WWW-Authenticate": "Bearer"}
        )

    def _is_openai_endpoint(self, path: str) -> bool:
        """检查端点是否为OpenAI兼容API端点

        Args:
            path: 请求路径

        Returns:
            bool: 是否为OpenAI端点
        """
        return any(path.startswith(endpoint) for endpoint in self.OPENAI_ENDPOINTS)


# 依赖函数用于路由中的认证检查
async def get_current_user(request: Request) -> dict:
    """获取当前认证用户信息

    Args:
        request: FastAPI请求对象

    Returns:
        dict: 用户信息

    Raises:
        HTTPException: 认证失败时抛出异常
    """
    from fastapi import HTTPException, status

    # 从请求状态中获取用户信息（由认证中间件设置）
    user_info = getattr(request.state, 'user', None)

    if not user_info:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未认证的用户",
            headers={"WWW-Authenticate": "Bearer"}
        )

    return user_info
