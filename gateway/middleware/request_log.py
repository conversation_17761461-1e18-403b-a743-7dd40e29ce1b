"""
API网关请求日志中间件模块

该模块实现了全面的请求日志记录功能，支持：
- 请求和响应的详细信息收集
- 数据库持久化存储（PostgreSQL）
- 异步批量插入优化性能
- 敏感信息过滤和脱敏处理
- 实时监控指标统计
- 用户使用情况跟踪和计费数据收集
"""
import time
import uuid
from typing import Dict, Any, Optional
from urllib.parse import urlparse, parse_qs

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from shared.logging_config import get_gateway_logger
from gateway.models.request_log import RequestType, AuthType
from gateway.services.request_log_service import request_log_service

logger = get_gateway_logger()


class RequestLogMiddleware(BaseHTTPMiddleware):
    """API网关请求日志中间件类

    负责记录所有通过网关的请求到数据库，用于：
    1. API使用情况跟踪和分析
    2. 计费数据收集和统计
    3. 实时监控和告警
    4. 安全审计和问题排查
    5. 性能分析和优化
    6. 用户行为分析
    """

    def __init__(self, app):
        """初始化请求日志中间件

        Args:
            app: FastAPI应用实例
        """
        super().__init__(app)

        # 定义请求类型映射
        self.request_type_patterns = {
            '/v1/': RequestType.OPENAI_API,
            '/api/v1/': RequestType.BUSINESS_API,
            '/health': RequestType.PUBLIC_API,
            '/docs': RequestType.PUBLIC_API,
            '/redoc': RequestType.PUBLIC_API,
            '/openapi.json': RequestType.PUBLIC_API,
        }

        # 需要跳过日志记录的路径
        self.skip_logging_paths = {
            '/health',
            '/metrics',
            '/favicon.ico'
        }

    async def dispatch(self, request: Request, call_next):
        """处理请求的日志中间件主入口

        Args:
            request: FastAPI请求对象
            call_next: 下一个中间件或路由处理器

        Returns:
            响应对象（包含请求ID和处理时间头）
        """

        # 检查是否需要跳过日志记录
        if self._should_skip_logging(request.url.path):
            return await call_next(request)

        # 生成唯一的请求ID用于链路追踪
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id

        # 记录请求开始时间
        start_time = time.time()

        # 处理请求
        response = await call_next(request)

        # 计算请求处理时间
        process_time_ms = (time.time() - start_time) * 1000

        # 在请求处理完成后创建完整的日志记录（此时用户信息已经可用）
        try:
            await self._create_complete_request_log(
                request=request,
                response=response,
                request_id=request_id,
                processing_time_ms=process_time_ms
            )
        except Exception as e:
            logger.error(f"创建请求日志失败 {request_id}: {e}")

        # 在响应头中添加请求ID和处理时间
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Process-Time"] = f"{process_time_ms:.1f}ms"

        # 记录简化的应用日志
        logger.info(
            f"API请求: {request.method} {request.url.path} - "
            f"状态码: {response.status_code} - "
            f"耗时: {process_time_ms:.1f}ms"
        )

        return response

    def _should_skip_logging(self, path: str) -> bool:
        """检查是否应该跳过日志记录

        Args:
            path: 请求路径

        Returns:
            bool: 是否跳过
        """
        return path in self.skip_logging_paths

    def _determine_request_type(self, path: str) -> RequestType:
        """确定请求类型

        Args:
            path: 请求路径

        Returns:
            RequestType: 请求类型
        """
        for pattern, request_type in self.request_type_patterns.items():
            if path.startswith(pattern):
                return request_type

        # 默认为静态资源
        return RequestType.STATIC_RESOURCE

    def _determine_auth_type(self, user_info: Dict[str, Any]) -> Optional[AuthType]:
        """确定认证类型

        Args:
            user_info: 用户信息

        Returns:
            AuthType: 认证类型
        """
        if not user_info:
            return AuthType.PUBLIC

        auth_type = user_info.get("auth_type", "")
        if auth_type == "api_key":
            return AuthType.API_KEY
        elif auth_type == "token" or auth_type == "jwt":
            return AuthType.JWT_TOKEN

        # 如果有API Key信息，也认为是API Key认证
        if user_info.get("api_key") or user_info.get("api_key_id"):
            return AuthType.API_KEY

        return AuthType.PUBLIC

    async def _create_complete_request_log(
        self,
        request: Request,
        response: Response,
        request_id: str,
        processing_time_ms: float
    ):
        """创建完整的请求日志记录（包含请求和响应信息）

        Args:
            request: FastAPI请求对象
            response: FastAPI响应对象
            request_id: 请求ID
            processing_time_ms: 处理时间（毫秒）
        """
        # 从请求状态中获取用户信息（由认证中间件或路由依赖设置）
        user_info = getattr(request.state, 'user', None)

        # 获取客户端IP地址
        client_ip = self._get_client_ip(request)

        # 确定请求类型
        request_type = self._determine_request_type(request.url.path)

        # 确定认证类型
        auth_type = self._determine_auth_type(user_info)

        # 解析查询参数
        query_params = dict(
            request.query_params) if request.query_params else None

        # 提取用户信息
        user_id = None
        user_email = None
        api_key_id = None
        api_key_name = None

        if user_info:
            user_id = user_info.get("user_id") or user_info.get("id")
            user_email = user_info.get("email")
            api_key_id = user_info.get("api_key_id")
            api_key_name = user_info.get("api_key_name")

        # 确定错误信息
        error_type = None
        error_message = None

        if response.status_code >= 400:
            if response.status_code >= 500:
                error_type = "server_error"
            elif response.status_code >= 400:
                error_type = "client_error"

            error_message = f"HTTP {response.status_code}"

        # 创建完整的日志记录
        await request_log_service.log_request(
            request_id=request_id,
            method=request.method,
            path=request.url.path,
            full_url=str(request.url),
            query_params=query_params,
            request_type=request_type,
            client_ip=client_ip,
            user_agent=request.headers.get("user-agent"),
            referer=request.headers.get("referer"),
            auth_type=auth_type,
            user_id=user_id,
            user_email=user_email,
            api_key_id=api_key_id,
            api_key_name=api_key_name,
            content_type=request.headers.get("content-type"),
            content_length=self._safe_int(
                request.headers.get("content-length")),
            # 响应信息
            status_code=response.status_code,
            processing_time_ms=processing_time_ms,
            response_content_type=response.headers.get("content-type"),
            response_content_length=self._safe_int(
                response.headers.get("content-length")),
            error_type=error_type,
            error_message=error_message
        )

    def _safe_int(self, value: str) -> Optional[int]:
        """安全地转换字符串为整数

        Args:
            value: 字符串值

        Returns:
            int: 转换后的整数，如果转换失败返回None
        """
        if value is None:
            return None
        try:
            return int(value)
        except (ValueError, TypeError):
            return None

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址

        支持代理环境下的真实IP获取

        Args:
            request: FastAPI请求对象

        Returns:
            str: 客户端IP地址
        """
        # 检查代理转发的IP头（当网关位于代理后面时）
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        # 检查真实IP头
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        # 回退到直连IP
        return request.client.host if request.client else "unknown"
