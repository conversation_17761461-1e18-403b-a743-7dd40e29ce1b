"""
API网关主应用程序模块

该模块是宠物医疗平台API网关的主入口，负责：
- FastAPI应用程序的创建和配置
- 中间件的注册和顺序管理
- 路由的注册和优先级设置
- 应用程序生命周期管理
- 健康检查和服务状态监控
- 全局异常处理
"""
from contextlib import asynccontextmanager

import httpx
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi_offline import FastAPIOffline

from gateway.config.settings import settings
from gateway.middleware.auth import AuthMiddleware
from gateway.middleware.rate_limit import RateLimitMiddleware
from gateway.middleware.request_log import RequestLogMiddleware
# 加载环境变量
from gateway.routes.analytics import router as analytics_router
from gateway.routes.openai_proxy import router as openai_router, openai_proxy
from gateway.routes.proxy import router as proxy_router, proxy
from shared.logging_config import setup_gateway_logger
from shared.redis_simple import redis_client


# 初始化网关日志系统
logger = setup_gateway_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用程序生命周期事件管理器

    处理应用程序启动和关闭时的初始化和清理工作

    Args:
        app: FastAPI应用程序实例
    """
    # 应用程序启动阶段
    logger.info("正在启动API网关...")
    try:
        # 测试Redis连接
        await redis_client.set("gateway_health", "ok", expire=10)
        health_status = await redis_client.get("gateway_health")
        if health_status == "ok":
            logger.info("Redis连接成功")
        else:
            logger.warning("Redis连接失败")

        # 测试后端服务连接
        await test_backend_services()

        logger.info("API网关启动成功")
    except Exception as e:
        logger.error(f"API网关启动失败: {e}")
        raise

    yield

    # 应用程序关闭阶段
    logger.info("正在关闭API网关...")
    try:
        await proxy.close()
        await openai_proxy.close()
        await redis_client.close()
        logger.info("API网关关闭完成")
    except Exception as e:
        logger.error(f"关闭过程中发生错误: {e}")


async def test_backend_services():
    """测试与后端服务的连接

    检查所有配置的后端服务是否可达，
    并记录连接状态用于启动时的健康检查。
    """

    async with httpx.AsyncClient(timeout=5.0) as client:
        for service_name, health_url in settings.SERVICE_HEALTH_ENDPOINTS.items():
            try:
                response = await client.get(health_url)
                if response.status_code == 200:
                    logger.info(f"✅ {service_name} 服务健康")
                else:
                    logger.warning(
                        f"⚠️ {service_name} 服务返回状态码 {response.status_code}")
            except Exception as e:
                logger.warning(f"❌ {service_name} 服务不可达: {e}")


# 创建FastAPI应用程序实例
app = FastAPIOffline(
    title=settings.PROJECT_NAME,
    version=settings.PROJECT_VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=f"{settings.API_V1_STR}/docs",
    redoc_url=f"{settings.API_V1_STR}/redoc",
    lifespan=lifespan
)

# 添加CORS中间件（跨域资源共享）
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加自定义中间件（注意：顺序很重要！）
# 中间件的执行顺序是后进先出（LIFO），所以最后添加的中间件最先执行
app.add_middleware(RequestLogMiddleware)  # 请求日志记录（最外层）
app.add_middleware(RateLimitMiddleware)  # 限流控制
app.add_middleware(AuthMiddleware)  # 认证验证（最内层）


# 健康检查端点
@app.get("/health")
async def health_check():
    """网关健康检查端点

    检查网关自身和所有后端服务的健康状态，
    用于负载均衡器和监控系统的健康检查。

    Returns:
        JSONResponse: 包含详细健康状态信息的响应
    """
    try:
        # 测试Redis连接
        await redis_client.set("health_check", "ok", expire=5)
        redis_status = await redis_client.get("health_check")

        # 测试后端服务连接状态
        service_status = {}
        async with httpx.AsyncClient(timeout=2.0) as client:
            for service_name, health_url in settings.SERVICE_HEALTH_ENDPOINTS.items():
                try:
                    response = await client.get(health_url)
                    service_status[service_name] = {
                        "status": "healthy" if response.status_code == 200 else "unhealthy",
                        "response_time": response.elapsed.total_seconds()
                    }
                except Exception as e:
                    service_status[service_name] = {
                        "status": "unreachable",
                        "error": str(e)
                    }

        # 确定整体健康状态
        all_healthy = all(
            service["status"] == "healthy"
            for service in service_status.values()
        )

        return JSONResponse(
            status_code=200 if all_healthy else 503,
            content={
                "status": "healthy" if all_healthy else "degraded",
                "service": "api_gateway",
                "version": settings.PROJECT_VERSION,
                "redis": "connected" if redis_status == "ok" else "disconnected",
                "backend_services": service_status
            }
        )
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "service": "api_gateway",
                "version": settings.PROJECT_VERSION,
                "error": str(e)
            }
        )


# 服务状态端点
@app.get("/status")
async def service_status():
    """详细的服务状态信息

    提供比健康检查更详细的服务状态信息，
    包括网关自身的运行状态和所有后端服务的详细信息。

    Returns:
        dict: 包含网关和后端服务详细状态的字典
    """
    try:
        status_info = {
            "gateway": {
                "version": settings.PROJECT_VERSION,
                "uptime": "unknown",  # TODO: 实现运行时间跟踪
                "requests_per_minute": 0  # TODO: 从Redis获取实时请求统计
            },
            "services": {}
        }

        # 获取各个后端服务的详细状态
        async with httpx.AsyncClient(timeout=5.0) as client:
            for service_name, health_url in settings.SERVICE_HEALTH_ENDPOINTS.items():
                try:
                    response = await client.get(health_url)
                    if response.status_code == 200:
                        service_data = response.json()
                        status_info["services"][service_name] = service_data
                    else:
                        status_info["services"][service_name] = {
                            "status": "unhealthy",
                            "status_code": response.status_code
                        }
                except Exception as e:
                    status_info["services"][service_name] = {
                        "status": "unreachable",
                        "error": str(e)
                    }

        return status_info

    except Exception as e:
        logger.error(f"状态检查失败: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )


# 注册路由器（注意顺序：特定路由优先于通用路由）
# 分析统计路由器（管理功能）
app.include_router(analytics_router, prefix=settings.API_V1_STR)

# OpenAI兼容API路由器（特定路径，优先匹配）
app.include_router(openai_router)

# 通用代理路由器（通配符路径，最后匹配）
app.include_router(proxy_router)


# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器

    捕获所有未被处理的异常，记录详细错误信息，
    并返回统一的错误响应格式。

    Args:
        request: 发生异常的请求对象
        exc: 异常实例

    Returns:
        JSONResponse: 统一格式的错误响应
    """
    logger.error(f"未处理的异常: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "detail": "网关内部错误",
            "type": "gateway_error"
        }
    )


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.GATEWAY_PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
