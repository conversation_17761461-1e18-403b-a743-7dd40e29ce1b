#!/usr/bin/env python3
"""
网关服务数据库表初始化脚本
"""
from shared.logging_config import get_logger
from shared.database import get_async_engine, Base
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
sys.path.insert(0, str(project_root))


logger = get_logger(__name__)


async def init_gateway_tables(drop_existing: bool = False):
    """初始化网关服务相关的数据库表"""
    try:
        logger.info("开始初始化网关服务数据库表...")

        # 导入网关服务的所有模型
        from ..models.request_log import APIRequestLog

        logger.info("网关服务模型导入成功")

        # 获取数据库引擎
        engine = get_async_engine()

        # 如果需要，先删除现有表
        if drop_existing:
            logger.warning("删除现有网关服务表...")
            async with engine.begin() as conn:
                # 只删除网关服务相关的表
                await conn.run_sync(lambda sync_conn: Base.metadata.drop_all(
                    sync_conn,
                    tables=[
                        APIRequestLog.__table__
                    ]
                ))
            logger.info("现有网关服务表已删除")

        # 创建表
        logger.info("创建网关服务表...")
        async with engine.begin() as conn:
            # 只创建网关服务相关的表
            await conn.run_sync(lambda sync_conn: Base.metadata.create_all(
                sync_conn,
                tables=[
                    APIRequestLog.__table__
                ]
            ))

        logger.info("✅ 网关服务数据库表初始化完成")
        return True

    except Exception as e:
        logger.error(f"❌ 网关服务数据库表初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="网关服务数据库初始化")
    parser.add_argument("--drop", action="store_true", help="删除现有表后重新创建")

    args = parser.parse_args()

    logger.info("🎯 网关服务数据库初始化工具")
    logger.info("=" * 50)

    # 初始化表
    success = await init_gateway_tables(drop_existing=args.drop)
    if not success:
        sys.exit(1)

    logger.info("🎉 网关服务数据库初始化完成！")


if __name__ == "__main__":
    asyncio.run(main())
