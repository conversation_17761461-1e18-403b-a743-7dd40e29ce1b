# 开发环境配置
# Development Environment Configuration

# 环境标识
ENVIRONMENT=development

# 调试模式
DEBUG=true

# 项目路径配置
PYTHONPATH=/Users/<USER>/work/code/vet_open_platform:$PYTHONPATH

# 数据库配置 - 开发环境
DATABASE_URL=*******************************************************/vet_platform
DATABASE_HOST=*********
DATABASE_PORT=5432
DATABASE_NAME=vet_platform
DATABASE_USER=vet_platform_rw
DATABASE_PASSWORD=VetPlatform
DATABASE_SCHEMA=vet

# Redis配置 - 开发环境
REDIS_URL=redis://localhost:6379/2
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=2
REDIS_PASSWORD=

# JWT配置 - 开发环境（较短过期时间便于测试）
SECRET_KEY=dev_Q3w5E7r9T1y3U5i7O9p1A3s5D7f9G1h3J5k7L9z1X3c5V7b9N1m3Q5w7E9r1T3y
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_DAYS=7

# API配置
API_V1_STR=/api/v1
PROJECT_NAME="VetPlatform-Development"
PROJECT_VERSION=1.0.0-dev

# 服务端口配置
GATEWAY_PORT=8000
USER_SERVICE_PORT=8001
PET_SERVICE_PORT=8002
APP_SERVICE_PORT=8003
OCR_SERVICE_PORT=8004

# 服务URL配置 - 开发环境
USER_SERVICE_URL=http://localhost:8001
PET_SERVICE_URL=http://localhost:8002
APP_SERVICE_URL=http://localhost:8003
OCR_SERVICE_URL=http://localhost:8004

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# 日志配置 - 开发环境（详细日志）
LOG_LEVEL=DEBUG
LOG_FORMAT=json

# CORS配置 - 开发环境（宽松配置）
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080", "http://localhost:8000", "http://127.0.0.1:3000"]

# 限流配置 - 开发环境（宽松限制）
RATE_LIMIT_PER_MINUTE=120
RATE_LIMIT_BURST=20

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=./uploads

# 邮件配置（开发环境可选）
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=
SMTP_USER=
SMTP_PASSWORD=
EMAILS_FROM_EMAIL=
EMAILS_FROM_NAME=

# 监控配置
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# OpenAI配置 - 开发环境
OPENAI_API_KEY=y03ZvBwVvHAqAo0nosjdlVGCfCNYGlgm8U
OPENAI_BASE_URL=http://************:8888/v1

# 阿里qwen-vl配置
VL_QWEN_API_KEY=sk-e6b74c85f16a4f5c9737d4828a7e8d04
VL_QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
VL_QWEN_MODEL_NAME="qwen2.5-vl-32b-instruct"
