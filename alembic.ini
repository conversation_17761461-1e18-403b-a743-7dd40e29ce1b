# 宠物医疗平台微服务架构数据库迁移配置
#
# 该配置支持统一的数据库迁移管理，覆盖所有微服务的数据库表：
# - User Service: 用户管理、权限、租户
# - Pet Service: 宠物信息、医疗记录、疫苗接种
# - App Service: 智能体、会话管理
# - Gateway: API请求日志
#
# 使用方法：
# 1. 生成迁移: alembic revision --autogenerate -m "描述"
# 2. 执行迁移: alembic upgrade head
# 3. 回滚迁移: alembic downgrade -1

[alembic]
# 迁移脚本路径
script_location = migrations

# 迁移文件命名模板 - 使用默认格式
# file_template = %%(rev)s_%%(slug)s

# 系统路径配置 - 确保能找到项目模块
prepend_sys_path = .

# 时区配置 - 使用上海时区
timezone = Asia/Shanghai

# slug字段最大长度
truncate_slug_length = 40

# 在revision命令期间运行环境
revision_environment = true

# 允许检测.pyc和.pyo文件作为版本
sourceless = false

# 版本号格式 - 4位数字
version_num_format = %%04d

# 版本路径分隔符
version_path_separator = os

# 输出编码
output_encoding = utf-8

# 数据库连接URL - 从环境变量读取，这里是默认值
# 实际运行时会被env.py中的配置覆盖
sqlalchemy.url = postgresql://vet_user:vet_password@localhost:5432/vet_platform


[post_write_hooks]
# post_write_hooks defines scripts or Python functions that are run
# on newly generated revision scripts.  See the documentation for further
# detail and examples

# format using "black" - use the console_scripts runner, against the "black" entrypoint
# hooks = black
# black.type = console_scripts
# black.entrypoint = black
# black.options = -l 79 REVISION_SCRIPT_FILENAME

# Logging configuration
[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S
