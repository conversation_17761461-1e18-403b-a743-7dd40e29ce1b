#!/usr/bin/env python3
"""
宠物服务数据库表初始化脚本
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent.parent
sys.path.insert(0, str(project_root))


from shared.database import get_async_engine, Base
from shared.logging_config import get_logger

logger = get_logger(__name__)


async def init_pet_service_tables(drop_existing: bool = False):
    """初始化宠物服务相关的数据库表"""
    try:
        logger.info("开始初始化宠物服务数据库表...")
        
        # 导入宠物服务的所有模型
        from ..models.pet import Pet, MedicalRecord, Vaccination, Breed
        
        logger.info("宠物服务模型导入成功")
        
        # 获取数据库引擎
        engine = get_async_engine()
        
        # 如果需要，先删除现有表
        if drop_existing:
            logger.warning("删除现有宠物服务表...")
            async with engine.begin() as conn:
                # 只删除宠物服务相关的表
                await conn.run_sync(lambda sync_conn: Base.metadata.drop_all(
                    sync_conn, 
                    tables=[
                        Vaccination.__table__,
                        MedicalRecord.__table__,
                        Pet.__table__,
                        Breed.__table__
                    ]
                ))
            logger.info("现有宠物服务表已删除")
        
        # 创建表
        logger.info("创建宠物服务表...")
        async with engine.begin() as conn:
            # 只创建宠物服务相关的表
            await conn.run_sync(lambda sync_conn: Base.metadata.create_all(
                sync_conn,
                tables=[
                    Breed.__table__,
                    Pet.__table__,
                    MedicalRecord.__table__,
                    Vaccination.__table__
                ]
            ))
        
        logger.info("✅ 宠物服务数据库表初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 宠物服务数据库表初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def init_default_breeds():
    """初始化默认品种数据"""
    try:
        from shared.database import AsyncSessionLocal
        from ..models.pet import Breed
        from sqlalchemy import select
        
        logger.info("开始初始化默认品种数据...")
        
        # 默认品种列表
        default_breeds = [
            # 犬类品种
            {"name": "金毛犬", "species": "犬", "origin_country": "英国", "size_category": "大型"},
            {"name": "拉布拉多", "species": "犬", "origin_country": "加拿大", "size_category": "大型"},
            {"name": "哈士奇", "species": "犬", "origin_country": "俄罗斯", "size_category": "大型"},
            {"name": "边境牧羊犬", "species": "犬", "origin_country": "英国", "size_category": "中型"},
            {"name": "德国牧羊犬", "species": "犬", "origin_country": "德国", "size_category": "大型"},
            {"name": "比熊犬", "species": "犬", "origin_country": "法国", "size_category": "小型"},
            {"name": "泰迪犬", "species": "犬", "origin_country": "法国", "size_category": "小型"},
            {"name": "柯基犬", "species": "犬", "origin_country": "英国", "size_category": "小型"},
            {"name": "中华田园犬", "species": "犬", "origin_country": "中国", "size_category": "中型"},
            
            # 猫类品种
            {"name": "英国短毛猫", "species": "猫", "origin_country": "英国", "size_category": "中型"},
            {"name": "美国短毛猫", "species": "猫", "origin_country": "美国", "size_category": "中型"},
            {"name": "波斯猫", "species": "猫", "origin_country": "伊朗", "size_category": "中型"},
            {"name": "暹罗猫", "species": "猫", "origin_country": "泰国", "size_category": "中型"},
            {"name": "布偶猫", "species": "猫", "origin_country": "美国", "size_category": "大型"},
            {"name": "缅因猫", "species": "猫", "origin_country": "美国", "size_category": "大型"},
            {"name": "苏格兰折耳猫", "species": "猫", "origin_country": "英国", "size_category": "中型"},
            {"name": "中华田园猫", "species": "猫", "origin_country": "中国", "size_category": "中型"},
            
            # 其他宠物
            {"name": "荷兰猪", "species": "啮齿动物", "origin_country": "南美洲", "size_category": "小型"},
            {"name": "仓鼠", "species": "啮齿动物", "origin_country": "叙利亚", "size_category": "小型"},
            {"name": "龙猫", "species": "啮齿动物", "origin_country": "南美洲", "size_category": "小型"},
            {"name": "兔子", "species": "兔形目", "origin_country": "欧洲", "size_category": "小型"}
        ]
        
        async with AsyncSessionLocal() as db:
            created_count = 0
            for breed_data in default_breeds:
                # 检查品种是否已存在
                result = await db.execute(
                    select(Breed).where(
                        Breed.name == breed_data["name"],
                        Breed.species == breed_data["species"]
                    )
                )
                existing_breed = result.scalar_one_or_none()
                
                if not existing_breed:
                    breed = Breed(**breed_data)
                    db.add(breed)
                    created_count += 1
                    logger.info(f"创建品种: {breed_data['species']} - {breed_data['name']}")
            
            if created_count > 0:
                await db.commit()
                logger.info(f"✅ 成功创建 {created_count} 个默认品种")
            else:
                logger.info("✅ 所有默认品种已存在，无需创建")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 初始化默认品种失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="宠物服务数据库初始化")
    parser.add_argument("--drop", action="store_true", help="删除现有表后重新创建")
    parser.add_argument("--tables-only", action="store_true", help="只创建表，不初始化数据")
    
    args = parser.parse_args()
    
    logger.info("🎯 宠物服务数据库初始化工具")
    logger.info("=" * 50)
    
    # 初始化表
    success = await init_pet_service_tables(drop_existing=args.drop)
    if not success:
        sys.exit(1)
    
    if not args.tables_only:
        # 初始化默认数据
        success = await init_default_breeds()
        if not success:
            sys.exit(1)
    
    logger.info("🎉 宠物服务数据库初始化完成！")


if __name__ == "__main__":
    asyncio.run(main())
