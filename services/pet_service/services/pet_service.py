"""
宠物服务业务逻辑
"""
from typing import Optional, List, Tuple

from fastapi import HTTPException, status
from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from ..models.pet import Pet, MedicalRecord, Vaccination, Breed
from ..schemas.pet import (
    PetCreate, PetUpdate, MedicalRecordCreate, MedicalRecordUpdate,
    VaccinationCreate, BreedCreate
)


class PetService:
    """宠物服务类"""

    @staticmethod
    async def create_pet(
            db: AsyncSession,
            pet_data: PetCreate,
            current_user_id: int
    ) -> Pet:
        """创建宠物"""
        # 检查用户是否有权限为指定用户创建宠物
        if pet_data.owner_id != current_user_id:
            # 这里可以添加管理员权限检查
            pass

        # 检查芯片号唯一性
        if pet_data.microchip_id:
            existing_pet = await db.execute(
                select(Pet).where(Pet.microchip_id == pet_data.microchip_id)
            )
            if existing_pet.scalar_one_or_none():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="芯片号已存在"
                )

        # 创建宠物
        pet_dict = pet_data.model_dump()
        pet = Pet(**pet_dict)

        db.add(pet)
        await db.commit()
        await db.refresh(pet)

        return pet

    @staticmethod
    async def get_pet_by_id(
            db: AsyncSession,
            pet_id: int,
            current_user_id: int,
            is_superuser: bool = False
    ) -> Optional[Pet]:
        """根据ID获取宠物"""
        query = select(Pet).where(Pet.id == pet_id)

        # 非管理员只能查看自己的宠物
        if not is_superuser:
            query = query.where(Pet.owner_id == current_user_id)

        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def save_or_update_by_tenant_pet_id(
            db: AsyncSession,
            pet_data: PetUpdate
    ) -> tuple[Pet, bool]:
        """
        根据tenant_pet_id创建或更新宠物

        Returns:
            tuple[Pet, bool]: (宠物对象, 是否为新创建)
        """
        if not pet_data.tenant_pet_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="tenant_pet_id不能为空"
            )

        # 查询是否已存在
        existing_pet = await db.execute(
            select(Pet).where(Pet.tenant_pet_id == pet_data.tenant_pet_id)
        )
        pet = existing_pet.scalar_one_or_none()

        if pet:
            # 更新现有宠物
            is_created = False

            # 检查是否需要更新
            update_data = pet_data.model_dump()
            has_changes = False

            for field, value in update_data.items():
                if hasattr(pet, field):
                    current_value = getattr(pet, field)

                    # 特殊处理JSON字段的比较
                    if field == 'other_info':
                        # 对于JSON字段，进行深度比较
                        if current_value != value:
                            has_changes = True
                            setattr(pet, field, value)
                    else:
                        # 对于其他字段，直接比较
                        if current_value != value:
                            has_changes = True
                            setattr(pet, field, value)

            # 如果没有任何变化，跳过数据库操作
            if not has_changes:
                return pet, False

        else:
            # 创建新宠物
            is_created = True

            # 创建宠物
            pet_dict = pet_data.model_dump()
            pet = Pet(**pet_dict)

            db.add(pet)

        await db.commit()
        await db.refresh(pet)
        return pet, is_created

    @staticmethod
    async def get_pets(
            db: AsyncSession,
            current_user_id: int,
            is_superuser: bool = False,
            page: int = 1,
            size: int = 10,
            search: Optional[str] = None,
            species: Optional[str] = None,
            status: Optional[str] = None,
            owner_id: Optional[int] = None
    ) -> Tuple[List[Pet], int]:
        """获取宠物列表"""
        # 构建基础查询
        query = select(Pet)
        count_query = select(func.count(Pet.id))

        # 权限过滤
        if not is_superuser:
            query = query.where(Pet.owner_id == current_user_id)
            count_query = count_query.where(Pet.owner_id == current_user_id)
        elif owner_id:
            query = query.where(Pet.owner_id == owner_id)
            count_query = count_query.where(Pet.owner_id == owner_id)

        # 搜索过滤
        if search:
            search_pattern = f"%{search}%"
            search_condition = or_(
                Pet.name.ilike(search_pattern),
                Pet.species.ilike(search_pattern),
                Pet.breed.ilike(search_pattern),
                Pet.color.ilike(search_pattern)
            )
            query = query.where(search_condition)
            count_query = count_query.where(search_condition)

        # 物种过滤
        if species:
            query = query.where(Pet.species == species)
            count_query = count_query.where(Pet.species == species)

        # 状态过滤
        if status:
            query = query.where(Pet.status == status)
            count_query = count_query.where(Pet.status == status)

        # 分页
        offset = (page - 1) * size
        query = query.offset(offset).limit(size).order_by(Pet.created_at.desc())

        # 执行查询
        pets_result = await db.execute(query)
        pets = pets_result.scalars().all()

        count_result = await db.execute(count_query)
        total = count_result.scalar()

        return list(pets), total

    @staticmethod
    async def update_pet(
            db: AsyncSession,
            pet_id: int,
            pet_update: PetUpdate,
            current_user_id: int,
            is_superuser: bool = False
    ) -> Optional[Pet]:
        """更新宠物信息"""
        # 获取宠物
        pet = await PetService.get_pet_by_id(db, pet_id, current_user_id, is_superuser)
        if not pet:
            return None

        # 检查芯片号唯一性
        if pet_update.microchip_id and pet_update.microchip_id != pet.microchip_id:
            existing_pet = await db.execute(
                select(Pet).where(
                    and_(
                        Pet.microchip_id == pet_update.microchip_id,
                        Pet.id != pet_id
                    )
                )
            )
            if existing_pet.scalar_one_or_none():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="芯片号已存在"
                )

        # 更新字段
        update_data = pet_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(pet, field, value)

        await db.commit()
        await db.refresh(pet)

        return pet

    @staticmethod
    async def delete_pet(
            db: AsyncSession,
            pet_id: int,
            current_user_id: int,
            is_superuser: bool = False
    ) -> bool:
        """删除宠物"""
        pet = await PetService.get_pet_by_id(db, pet_id, current_user_id, is_superuser)
        if not pet:
            return False

        await db.delete(pet)
        await db.commit()

        return True


class MedicalRecordService:
    """医疗记录服务类"""

    @staticmethod
    async def create_medical_record(
            db: AsyncSession,
            record_data: MedicalRecordCreate,
            current_user_id: int,
            is_superuser: bool = False
    ) -> MedicalRecord:
        """创建医疗记录"""
        # 检查宠物是否存在且用户有权限
        pet = await PetService.get_pet_by_id(
            db, record_data.pet_id, current_user_id, is_superuser
        )
        if not pet:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="宠物不存在或无权限访问"
            )

        # 创建医疗记录
        record_dict = record_data.model_dump()
        record = MedicalRecord(**record_dict)

        db.add(record)
        await db.commit()
        await db.refresh(record)

        return record

    @staticmethod
    async def get_medical_records(
            db: AsyncSession,
            pet_id: int,
            current_user_id: int,
            is_superuser: bool = False,
            page: int = 1,
            size: int = 10
    ) -> Tuple[List[MedicalRecord], int]:
        """获取医疗记录列表"""
        # 检查宠物权限
        pet = await PetService.get_pet_by_id(db, pet_id, current_user_id, is_superuser)
        if not pet:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="宠物不存在或无权限访问"
            )

        # 构建查询
        query = select(MedicalRecord).where(MedicalRecord.pet_id == pet_id)
        count_query = select(func.count(MedicalRecord.id)).where(MedicalRecord.pet_id == pet_id)

        # 分页
        offset = (page - 1) * size
        query = query.offset(offset).limit(size).order_by(MedicalRecord.visit_date.desc())

        # 执行查询
        records_result = await db.execute(query)
        records = records_result.scalars().all()

        count_result = await db.execute(count_query)
        total = count_result.scalar()

        return list(records), total

    @staticmethod
    async def update_medical_record(
            db: AsyncSession,
            record_id: int,
            record_update: MedicalRecordUpdate,
            current_user_id: int,
            is_superuser: bool = False
    ) -> Optional[MedicalRecord]:
        """更新医疗记录"""
        # 获取医疗记录
        query = select(MedicalRecord).options(selectinload(MedicalRecord.pet)).where(
            MedicalRecord.id == record_id
        )
        result = await db.execute(query)
        record = result.scalar_one_or_none()

        if not record:
            return None

        # 检查权限
        if not is_superuser and record.pet.owner_id != current_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限访问此医疗记录"
            )

        # 更新字段
        update_data = record_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(record, field, value)

        await db.commit()
        await db.refresh(record)

        return record


class VaccinationService:
    """疫苗接种服务类"""

    @staticmethod
    async def create_vaccination(
            db: AsyncSession,
            vaccination_data: VaccinationCreate,
            current_user_id: int,
            is_superuser: bool = False
    ) -> Vaccination:
        """创建疫苗接种记录"""
        # 检查宠物是否存在且用户有权限
        pet = await PetService.get_pet_by_id(
            db, vaccination_data.pet_id, current_user_id, is_superuser
        )
        if not pet:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="宠物不存在或无权限访问"
            )

        # 创建疫苗接种记录
        vaccination_dict = vaccination_data.model_dump()
        vaccination = Vaccination(**vaccination_dict)

        db.add(vaccination)
        await db.commit()
        await db.refresh(vaccination)

        return vaccination

    @staticmethod
    async def get_vaccinations(
            db: AsyncSession,
            pet_id: int,
            current_user_id: int,
            is_superuser: bool = False,
            page: int = 1,
            size: int = 10
    ) -> Tuple[List[Vaccination], int]:
        """获取疫苗接种记录列表"""
        # 检查宠物权限
        pet = await PetService.get_pet_by_id(db, pet_id, current_user_id, is_superuser)
        if not pet:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="宠物不存在或无权限访问"
            )

        # 构建查询
        query = select(Vaccination).where(Vaccination.pet_id == pet_id)
        count_query = select(func.count(Vaccination.id)).where(Vaccination.pet_id == pet_id)

        # 分页
        offset = (page - 1) * size
        query = query.offset(offset).limit(size).order_by(Vaccination.vaccination_date.desc())

        # 执行查询
        vaccinations_result = await db.execute(query)
        vaccinations = vaccinations_result.scalars().all()

        count_result = await db.execute(count_query)
        total = count_result.scalar()

        return list(vaccinations), total


class BreedService:
    """品种服务类"""

    @staticmethod
    async def create_breed(db: AsyncSession, breed_data: BreedCreate) -> Breed:
        """创建品种"""
        # 检查品种名称唯一性
        existing_breed = await db.execute(
            select(Breed).where(
                and_(
                    Breed.name == breed_data.name,
                    Breed.species == breed_data.species
                )
            )
        )
        if existing_breed.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该物种的品种名称已存在"
            )

        # 创建品种
        breed_dict = breed_data.model_dump()
        breed = Breed(**breed_dict)

        db.add(breed)
        await db.commit()
        await db.refresh(breed)

        return breed

    @staticmethod
    async def get_breeds(
            db: AsyncSession,
            page: int = 1,
            size: int = 10,
            search: Optional[str] = None,
            species: Optional[str] = None
    ) -> Tuple[List[Breed], int]:
        """获取品种列表"""
        # 构建查询
        query = select(Breed)
        count_query = select(func.count(Breed.id))

        # 搜索过滤
        if search:
            search_pattern = f"%{search}%"
            search_condition = or_(
                Breed.name.ilike(search_pattern),
                Breed.species.ilike(search_pattern),
                Breed.origin_country.ilike(search_pattern)
            )
            query = query.where(search_condition)
            count_query = count_query.where(search_condition)

        # 物种过滤
        if species:
            query = query.where(Breed.species == species)
            count_query = count_query.where(Breed.species == species)

        # 分页
        offset = (page - 1) * size
        query = query.offset(offset).limit(size).order_by(Breed.name)

        # 执行查询
        breeds_result = await db.execute(query)
        breeds = breeds_result.scalars().all()

        count_result = await db.execute(count_query)
        total = count_result.scalar()

        return list(breeds), total
