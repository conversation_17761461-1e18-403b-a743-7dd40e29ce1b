#!/usr/bin/env python3
"""
宠物服务主应用 - 统一响应格式
"""
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi_offline import FastAPIOffline

from shared.database import init_db, close_db
from shared.logging_config import setup_pet_service_logger
from shared.redis_simple import redis_client
from shared.utils.exception_handlers import register_all_exception_handlers
from shared.middleware.request_id import add_request_id_middleware
from .api.breeds import router as breeds_router
from .api.internal import router as internal_router
from .api.medical_records import router as medical_records_router
from .api.pets import router as pets_router
from .api.vaccinations import router as vaccinations_router
from .config import settings


# 初始化宠物服务日志系统
logger = setup_pet_service_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用程序生命周期事件"""
    # 启动阶段
    logger.info("正在启动宠物服务...")
    try:
        # 导入本服务的模型
        from .models.pet import Pet, MedicalRecord, Vaccination, Breed
        logger.info("宠物服务模型导入成功")

        await init_db()
        logger.info("数据库初始化完成")

        # 测试Redis连接
        await redis_client.set("health_check", "ok", expire=10)
        health_status = await redis_client.get("health_check")
        if health_status == "ok":
            logger.info("Redis连接成功")
        else:
            logger.warning("Redis连接失败")

        logger.info("宠物服务启动成功")
    except Exception as e:
        logger.error(f"宠物服务启动失败: {e}")
        raise

    yield

    # 关闭阶段
    logger.info("正在关闭宠物服务...")
    try:
        await close_db()
        await redis_client.close()
        logger.info("宠物服务关闭完成")
    except Exception as e:
        logger.error(f"关闭过程中出错: {e}")


# 创建FastAPI应用程序
app = FastAPIOffline(
    title=settings.PROJECT_NAME,
    version=settings.PROJECT_VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=f"{settings.API_V1_STR}/docs",
    redoc_url=f"{settings.API_V1_STR}/redoc",
    lifespan=lifespan
)

# 添加请求ID中间件（在其他中间件之前）
add_request_id_middleware(app)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册统一异常处理器
register_all_exception_handlers(app)


# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查端点 - 使用统一响应格式"""
    from shared.models.response import create_success_response, create_error_response

    try:
        # 测试Redis连接
        await redis_client.set("health_check", "ok", expire=5)
        redis_status = await redis_client.get("health_check")

        health_data = {
            "service": "pet_service",
            "version": settings.PROJECT_VERSION,
            "status": "healthy",
            "components": {
                "redis": "connected" if redis_status == "ok" else "disconnected"
            }
        }

        return create_success_response(
            data=health_data,
            msg="健康检查完成"
        )
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return create_error_response(
            msg="健康检查失败",
            error_code="HEALTH_CHECK_FAILED",
            error_details={"error": str(e)}
        )


# 包含路由器
app.include_router(
    pets_router,
    prefix=f"{settings.API_V1_STR}/pets",
    tags=["宠物管理"]
)

app.include_router(
    medical_records_router,
    prefix=f"{settings.API_V1_STR}",
    tags=["医疗记录"]
)

app.include_router(
    vaccinations_router,
    prefix=f"{settings.API_V1_STR}",
    tags=["疫苗接种"]
)

app.include_router(
    breeds_router,
    prefix=f"{settings.API_V1_STR}/breeds",
    tags=["品种管理"]
)

app.include_router(
    internal_router,
    prefix="/internal",
    tags=["内部API"]
)


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.SERVICE_PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
