"""
内部API端点 - 用于服务间调用，不需要认证，统一响应格式
"""
from fastapi import APIRouter, Depends, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from shared.database import get_async_db
from shared.middleware.request_id import get_request_id
from shared.models.response import (
    SuccessResponse, create_success_response, ErrorCode, SuccessMessage
)
from shared.utils.auth import get_current_user_optional
from shared.utils.exception_handlers import BusinessException
from ..services import PetService

router = APIRouter()


@router.get("/pets/{pet_id}", response_model=SuccessResponse[dict])
async def get_pet_internal(
        request: Request,
        pet_id: int,
        current_user=Depends(get_current_user_optional),
        db: AsyncSession = Depends(get_async_db)
):
    """
    内部API：根据宠物ID获取宠物信息

    此端点专门用于服务间调用，不需要认证
    """
    # 查询宠物
    pet = await PetService.get_pet_by_id(
        db=db,
        pet_id=pet_id,
        current_user_id=current_user.get('id'),
        is_superuser=current_user.get('is_superuser', False)
    )

    if not pet:
        raise BusinessException(
            message="宠物不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 构建宠物基本信息
    pet_data = {
        "id": pet.id,
        "name": pet.name,
        "species": pet.species,
        "breed": pet.breed,
        "gender": pet.gender.value if pet.gender else None,
        "birth_date": pet.birth_date.isoformat() if pet.birth_date else None,
        "age_years": pet.age_years,
        "age_months": pet.age_months,
        "color": pet.color,
        "weight": pet.weight,
        "height": pet.height,
        "microchip_id": pet.microchip_id,
        "registration_number": pet.registration_number,
        "status": pet.status.value if pet.status else None,
        "is_neutered": pet.is_neutered,
        "is_vaccinated": pet.is_vaccinated,
        "description": pet.description,
        "special_needs": pet.special_needs,
        "avatar_url": pet.avatar_url,
        "other_info": pet.other_info,
        "owner_id": pet.owner_id,
        "tenant_pet_id": pet.tenant_pet_id,
        "created_at": pet.created_at.isoformat() if pet.created_at else None,
        "updated_at": pet.updated_at.isoformat() if pet.updated_at else None
    }

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=pet_data,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.post("/pets/save_or_update_by_tenant_pet_id", response_model=SuccessResponse[dict])
async def save_or_update_by_tenant_pet_id(
        request: Request,
        pet_data: dict,
        db: AsyncSession = Depends(get_async_db)
):
    """
    内部API：根据tenant_pet_id创建或更新宠物信息

    此端点专门用于服务间调用，不需要认证
    """
    try:
        # 导入PetUpsert schema和PetService
        from ..schemas.pet import PetUpdate
        from ..services.pet_service import PetService

        # 验证请求数据
        if not pet_data.get('tenant_pet_id'):
            raise BusinessException(
                message="tenant_pet_id不能为空",
                error_code=ErrorCode.VALIDATION_ERROR,
                status_code=status.HTTP_400_BAD_REQUEST
            )

        # 转换为PetUpsert模型进行验证
        try:
            pet_upsert = PetUpdate(**pet_data)
        except Exception as e:
            raise BusinessException(
                message="请求数据格式错误",
                error_code=ErrorCode.VALIDATION_ERROR,
                status_code=status.HTTP_400_BAD_REQUEST,
                details={"validation_error": str(e)}
            )

        # 执行upsert操作
        pet, is_created = await PetService.save_or_update_by_tenant_pet_id(
            db=db,
            pet_data=pet_upsert
        )

        # 构建响应数据
        response_data = {
            "operation": "created" if is_created else "updated",
            "pet": {
                "id": pet.id,
                "name": pet.name,
                "species": pet.species,
                "breed": pet.breed,
                "gender": pet.gender.value if pet.gender else None,
                "birth_date": pet.birth_date.isoformat() if pet.birth_date else None,
                "age_years": pet.age_years,
                "age_months": pet.age_months,
                "color": pet.color,
                "weight": pet.weight,
                "height": pet.height,
                "microchip_id": pet.microchip_id,
                "registration_number": pet.registration_number,
                "status": pet.status.value if pet.status else None,
                "is_neutered": pet.is_neutered,
                "is_vaccinated": pet.is_vaccinated,
                "description": pet.description,
                "special_needs": pet.special_needs,
                "avatar_url": pet.avatar_url,
                "other_info": pet.other_info,
                "owner_id": pet.owner_id,
                "tenant_pet_id": pet.tenant_pet_id,
                "created_at": pet.created_at.isoformat() if pet.created_at else None,
                "updated_at": pet.updated_at.isoformat() if pet.updated_at else None
            }
        }

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一成功响应格式
        return create_success_response(
            data=response_data,
            msg=SuccessMessage.CREATED if is_created else SuccessMessage.UPDATED,
            request_id=request_id
        )

    except BusinessException:
        raise
    except Exception as e:
        raise BusinessException(
            message="宠物upsert操作失败",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details={"error": str(e)}
        )
