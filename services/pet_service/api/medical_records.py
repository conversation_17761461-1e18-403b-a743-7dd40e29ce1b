"""
医疗记录管理API端点 - 统一响应格式
"""
from fastapi import APIRouter, Depends, Query, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from shared.database import get_async_db
from shared.utils.auth import get_current_active_user
from shared.models.response import (
    SuccessResponse, PaginatedResponse, create_success_response,
    create_paginated_response, ErrorCode, SuccessMessage
)
from shared.middleware.request_id import get_request_id
from shared.utils.exception_handlers import BusinessException

from ..schemas.pet import (
    MedicalRecord as MedicalRecordSchema,
    MedicalRecordCreate,
    MedicalRecordUpdate
)
from ..services.pet_service import MedicalRecordService

router = APIRouter()


@router.get("/pets/{pet_id}/medical-records", response_model=PaginatedResponse[MedicalRecordSchema])
async def get_medical_records(
    request: Request,
    pet_id: int,
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    current_user=Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取宠物医疗记录列表"""

    records, total = await MedicalRecordService.get_medical_records(
        db=db,
        pet_id=pet_id,
        current_user_id=current_user.get('id'),
        is_superuser=current_user.get('is_superuser', False),
        page=page,
        size=size
    )

    # 转换为Pydantic模型
    record_items = [MedicalRecordSchema.model_validate(
        record) for record in records]

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一分页响应格式
    return create_paginated_response(
        items=record_items,
        total=total,
        page=page,
        size=size,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.post("/pets/{pet_id}/medical-records", response_model=SuccessResponse[MedicalRecordSchema], status_code=status.HTTP_201_CREATED)
async def create_medical_record(
    request: Request,
    pet_id: int,
    record_create: MedicalRecordCreate,
    current_user=Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """创建医疗记录"""

    # 确保pet_id一致
    if record_create.pet_id != pet_id:
        raise BusinessException(
            message="路径中的宠物ID与请求体中的宠物ID不一致",
            error_code=ErrorCode.INVALID_REQUEST,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    try:
        record = await MedicalRecordService.create_medical_record(
            db=db,
            record_data=record_create,
            current_user_id=current_user.get('id'),
            is_superuser=current_user.get('is_superuser', False)
        )
    except Exception as e:
        raise BusinessException(
            message="创建医疗记录失败",
            error_code=ErrorCode.OPERATION_FAILED,
            details={"original_error": str(e)}
        )

    # 转换为Pydantic模型
    record_data = MedicalRecordSchema.model_validate(record)

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=record_data,
        msg=SuccessMessage.CREATED,
        request_id=request_id
    )


@router.get("/medical-records/{record_id}", response_model=SuccessResponse[MedicalRecordSchema])
async def get_medical_record(
    request: Request,
    record_id: int,
    current_user=Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取医疗记录详情"""

    from sqlalchemy import select
    from sqlalchemy.orm import selectinload
    from ..models.pet import MedicalRecord

    # 获取医疗记录
    query = select(MedicalRecord).options(selectinload(MedicalRecord.pet)).where(
        MedicalRecord.id == record_id
    )
    result = await db.execute(query)
    record = result.scalar_one_or_none()

    if not record:
        raise BusinessException(
            message="医疗记录不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 检查权限
    if not current_user.get('is_superuser', False) and record.pet.owner_id != current_user.get('id'):
        raise BusinessException(
            message="无权限访问此医疗记录",
            error_code=ErrorCode.FORBIDDEN,
            status_code=status.HTTP_403_FORBIDDEN
        )

    # 转换为Pydantic模型
    record_data = MedicalRecordSchema.model_validate(record)

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=record_data,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.put("/medical-records/{record_id}", response_model=SuccessResponse[MedicalRecordSchema])
async def update_medical_record(
    request: Request,
    record_id: int,
    record_update: MedicalRecordUpdate,
    current_user=Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """更新医疗记录"""

    try:
        record = await MedicalRecordService.update_medical_record(
            db=db,
            record_id=record_id,
            record_update=record_update,
            current_user_id=current_user.get('id'),
            is_superuser=current_user.get('is_superuser', False)
        )
    except Exception as e:
        raise BusinessException(
            message="更新医疗记录失败",
            error_code=ErrorCode.OPERATION_FAILED,
            details={"original_error": str(e)}
        )

    if not record:
        raise BusinessException(
            message="医疗记录不存在或无权限访问",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 转换为Pydantic模型
    record_data = MedicalRecordSchema.model_validate(record)

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=record_data,
        msg=SuccessMessage.UPDATED,
        request_id=request_id
    )


@router.delete("/medical-records/{record_id}", response_model=SuccessResponse[None])
async def delete_medical_record(
    request: Request,
    record_id: int,
    current_user=Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """删除医疗记录"""

    from sqlalchemy import select
    from sqlalchemy.orm import selectinload
    from ..models.pet import MedicalRecord

    # 获取医疗记录
    query = select(MedicalRecord).options(selectinload(MedicalRecord.pet)).where(
        MedicalRecord.id == record_id
    )
    result = await db.execute(query)
    record = result.scalar_one_or_none()

    if not record:
        raise BusinessException(
            message="医疗记录不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 检查权限
    if not current_user.get('is_superuser', False) and record.pet.owner_id != current_user.get('id'):
        raise BusinessException(
            message="无权限删除此医疗记录",
            error_code=ErrorCode.FORBIDDEN,
            status_code=status.HTTP_403_FORBIDDEN
        )

    try:
        await db.delete(record)
        await db.commit()
    except Exception as e:
        raise BusinessException(
            message="删除医疗记录失败",
            error_code=ErrorCode.OPERATION_FAILED,
            details={"original_error": str(e)}
        )

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式（无数据）
    return create_success_response(
        data=None,
        msg=SuccessMessage.DELETED,
        request_id=request_id
    )
