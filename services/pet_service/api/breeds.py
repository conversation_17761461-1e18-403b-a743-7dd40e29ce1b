"""
品种管理API端点 - 统一响应格式
"""
from typing import Optional

from fastapi import APIRouter, Depends, Query, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from shared.database import get_async_db
from shared.utils.auth import get_current_active_user, get_current_superuser
from shared.models.response import (
    SuccessResponse, PaginatedResponse, create_success_response,
    create_paginated_response, ErrorCode, SuccessMessage
)
from shared.middleware.request_id import get_request_id
from shared.utils.exception_handlers import BusinessException

from ..schemas.pet import (
    Breed as BreedSchema,
    BreedCreate,
    BreedUpdate
)
from ..services.pet_service import BreedService

router = APIRouter()


@router.get("/", response_model=PaginatedResponse[BreedSchema])
async def get_breeds(
    request: Request,
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    species: Optional[str] = Query(None, description="物种过滤"),
    db: AsyncSession = Depends(get_async_db)
):
    """获取品种列表（公开接口）"""

    breeds, total = await BreedService.get_breeds(
        db=db,
        page=page,
        size=size,
        search=search,
        species=species
    )

    # 转换为Pydantic模型
    breed_items = [BreedSchema.model_validate(breed) for breed in breeds]

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一分页响应格式
    return create_paginated_response(
        items=breed_items,
        total=total,
        page=page,
        size=size,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.get("/{breed_id}", response_model=SuccessResponse[BreedSchema])
async def get_breed(
    request: Request,
    breed_id: int,
    db: AsyncSession = Depends(get_async_db)
):
    """获取品种详情（公开接口）"""

    from sqlalchemy import select
    from ..models.pet import Breed

    result = await db.execute(select(Breed).where(Breed.id == breed_id))
    breed = result.scalar_one_or_none()

    if not breed:
        raise BusinessException(
            message="品种不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 转换为Pydantic模型
    breed_data = BreedSchema.model_validate(breed)

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=breed_data,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.post("/", response_model=SuccessResponse[BreedSchema], status_code=status.HTTP_201_CREATED)
async def create_breed(
    request: Request,
    breed_create: BreedCreate,
    current_user=Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """创建品种（需要管理员权限）"""

    try:
        breed = await BreedService.create_breed(
            db=db,
            breed_data=breed_create
        )
    except Exception as e:
        raise BusinessException(
            message="创建品种失败",
            error_code=ErrorCode.OPERATION_FAILED,
            details={"original_error": str(e)}
        )

    # 转换为Pydantic模型
    breed_data = BreedSchema.model_validate(breed)

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=breed_data,
        msg=SuccessMessage.CREATED,
        request_id=request_id
    )


@router.put("/{breed_id}", response_model=SuccessResponse[BreedSchema])
async def update_breed(
    request: Request,
    breed_id: int,
    breed_update: BreedUpdate,
    current_user=Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """更新品种信息（需要管理员权限）"""

    from sqlalchemy import select
    from ..models.pet import Breed

    # 获取品种
    result = await db.execute(select(Breed).where(Breed.id == breed_id))
    breed = result.scalar_one_or_none()

    if not breed:
        raise BusinessException(
            message="品种不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 检查名称唯一性（如果更新了名称）
    if breed_update.name and breed_update.name != breed.name:
        from sqlalchemy import and_
        existing_breed = await db.execute(
            select(Breed).where(
                and_(
                    Breed.name == breed_update.name,
                    Breed.species == (breed_update.species or breed.species),
                    Breed.id != breed_id
                )
            )
        )
        if existing_breed.scalar_one_or_none():
            raise BusinessException(
                message="该物种的品种名称已存在",
                error_code=ErrorCode.ALREADY_EXISTS,
                status_code=status.HTTP_400_BAD_REQUEST
            )

    # 更新字段
    update_data = breed_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(breed, field, value)

    try:
        await db.commit()
        await db.refresh(breed)
    except Exception as e:
        raise BusinessException(
            message="更新品种失败",
            error_code=ErrorCode.OPERATION_FAILED,
            details={"original_error": str(e)}
        )

    # 转换为Pydantic模型
    breed_data = BreedSchema.model_validate(breed)

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=breed_data,
        msg=SuccessMessage.UPDATED,
        request_id=request_id
    )


@router.delete("/{breed_id}", response_model=SuccessResponse[None])
async def delete_breed(
    request: Request,
    breed_id: int,
    current_user=Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """删除品种（需要管理员权限）"""

    from sqlalchemy import select
    from ..models.pet import Breed

    # 获取品种
    result = await db.execute(select(Breed).where(Breed.id == breed_id))
    breed = result.scalar_one_or_none()

    if not breed:
        raise BusinessException(
            message="品种不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 检查是否有宠物使用此品种
    from ..models.pet import Pet
    pets_using_breed = await db.execute(
        select(Pet).where(Pet.breed == breed.name).limit(1)
    )
    if pets_using_breed.scalar_one_or_none():
        raise BusinessException(
            message="无法删除，仍有宠物使用此品种",
            error_code=ErrorCode.CONFLICT,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    try:
        await db.delete(breed)
        await db.commit()
    except Exception as e:
        raise BusinessException(
            message="删除品种失败",
            error_code=ErrorCode.OPERATION_FAILED,
            details={"original_error": str(e)}
        )

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式（无数据）
    return create_success_response(
        data=None,
        msg=SuccessMessage.DELETED,
        request_id=request_id
    )


@router.get("/species/{species}/breeds", response_model=PaginatedResponse[BreedSchema])
async def get_breeds_by_species(
    request: Request,
    species: str,
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    db: AsyncSession = Depends(get_async_db)
):
    """根据物种获取品种列表（公开接口）"""

    breeds, total = await BreedService.get_breeds(
        db=db,
        page=page,
        size=size,
        search=search,
        species=species
    )

    # 转换为Pydantic模型
    breed_items = [BreedSchema.model_validate(breed) for breed in breeds]

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一分页响应格式
    return create_paginated_response(
        items=breed_items,
        total=total,
        page=page,
        size=size,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )
