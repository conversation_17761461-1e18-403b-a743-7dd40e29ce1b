"""
疫苗接种记录管理API端点 - 统一响应格式
"""
from fastapi import APIRouter, Depends, Query, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from shared.database import get_async_db
from shared.utils.auth import get_current_active_user
from shared.models.response import (
    SuccessResponse, PaginatedResponse, create_success_response,
    create_paginated_response, ErrorCode, SuccessMessage
)
from shared.middleware.request_id import get_request_id
from shared.utils.exception_handlers import BusinessException

from ..schemas.pet import (
    Vaccination as VaccinationSchema,
    VaccinationCreate,
    VaccinationUpdate
)
from ..services.pet_service import VaccinationService

router = APIRouter()


@router.get("/pets/{pet_id}/vaccinations", response_model=PaginatedResponse[VaccinationSchema])
async def get_vaccinations(
    request: Request,
    pet_id: int,
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    current_user=Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取宠物疫苗接种记录列表"""

    vaccinations, total = await VaccinationService.get_vaccinations(
        db=db,
        pet_id=pet_id,
        current_user_id=current_user.get('id'),
        is_superuser=current_user.get('is_superuser'),
        page=page,
        size=size
    )

    # 转换为Pydantic模型
    vaccination_items = [VaccinationSchema.model_validate(
        vaccination) for vaccination in vaccinations]

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一分页响应格式
    return create_paginated_response(
        items=vaccination_items,
        total=total,
        page=page,
        size=size,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.post("/pets/{pet_id}/vaccinations", response_model=SuccessResponse[VaccinationSchema], status_code=status.HTTP_201_CREATED)
async def create_vaccination(
    request: Request,
    pet_id: int,
    vaccination_create: VaccinationCreate,
    current_user=Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """创建疫苗接种记录"""

    # 确保pet_id一致
    if vaccination_create.pet_id != pet_id:
        raise BusinessException(
            message="路径中的宠物ID与请求体中的宠物ID不一致",
            error_code=ErrorCode.INVALID_REQUEST,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    try:
        vaccination = await VaccinationService.create_vaccination(
            db=db,
            vaccination_data=vaccination_create,
            current_user_id=current_user.get('id'),
            is_superuser=current_user.get('is_superuser', False)
        )
    except Exception as e:
        raise BusinessException(
            message="创建疫苗接种记录失败",
            error_code=ErrorCode.OPERATION_FAILED,
            details={"original_error": str(e)}
        )

    # 转换为Pydantic模型
    vaccination_data = VaccinationSchema.model_validate(vaccination)

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=vaccination_data,
        msg=SuccessMessage.CREATED,
        request_id=request_id
    )


@router.get("/vaccinations/{vaccination_id}", response_model=SuccessResponse[VaccinationSchema])
async def get_vaccination(
    request: Request,
    vaccination_id: int,
    current_user=Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取疫苗接种记录详情"""

    from sqlalchemy import select
    from sqlalchemy.orm import selectinload
    from ..models.pet import Vaccination

    # 获取疫苗接种记录
    query = select(Vaccination).options(selectinload(Vaccination.pet)).where(
        Vaccination.id == vaccination_id
    )
    result = await db.execute(query)
    vaccination = result.scalar_one_or_none()

    if not vaccination:
        raise BusinessException(
            message="疫苗接种记录不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 检查权限
    if not current_user.get('is_superuser', False) and vaccination.pet.owner_id != current_user.get('id'):
        raise BusinessException(
            message="无权限访问此疫苗接种记录",
            error_code=ErrorCode.FORBIDDEN,
            status_code=status.HTTP_403_FORBIDDEN
        )

    # 转换为Pydantic模型
    vaccination_data = VaccinationSchema.model_validate(vaccination)

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=vaccination_data,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.put("/vaccinations/{vaccination_id}", response_model=SuccessResponse[VaccinationSchema])
async def update_vaccination(
    request: Request,
    vaccination_id: int,
    vaccination_update: VaccinationUpdate,
    current_user=Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """更新疫苗接种记录"""

    from sqlalchemy import select
    from sqlalchemy.orm import selectinload
    from ..models.pet import Vaccination

    # 获取疫苗接种记录
    query = select(Vaccination).options(selectinload(Vaccination.pet)).where(
        Vaccination.id == vaccination_id
    )
    result = await db.execute(query)
    vaccination = result.scalar_one_or_none()

    if not vaccination:
        raise BusinessException(
            message="疫苗接种记录不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 检查权限
    if not current_user.get("is_superuser") and vaccination.pet.owner_id != current_user.get("id"):
        raise BusinessException(
            message="无权限修改此疫苗接种记录",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_403_FORBIDDEN
        )

    # 更新字段
    update_data = vaccination_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(vaccination, field, value)

    await db.commit()
    await db.refresh(vaccination)

    # 转换为Pydantic模型
    vaccination_data = VaccinationSchema.model_validate(vaccination)

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=vaccination_data,
        msg=SuccessMessage.UPDATED,
        request_id=request_id
    )


@router.delete("/vaccinations/{vaccination_id}", response_model=SuccessResponse[None])
async def delete_vaccination(
    request: Request,
    vaccination_id: int,
    current_user=Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """删除疫苗接种记录"""

    from sqlalchemy import select
    from sqlalchemy.orm import selectinload
    from ..models.pet import Vaccination

    # 获取疫苗接种记录
    query = select(Vaccination).options(selectinload(Vaccination.pet)).where(
        Vaccination.id == vaccination_id
    )
    result = await db.execute(query)
    vaccination = result.scalar_one_or_none()

    if not vaccination:
        raise BusinessException(
            message="疫苗接种记录不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 检查权限
    if not current_user.get("is_superuser") and vaccination.pet.owner_id != current_user.get("id"):
        raise BusinessException(
            message="无权限删除此疫苗接种记录",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_403_FORBIDDEN
        )

    try:
        await db.delete(vaccination)
        await db.commit()
    except Exception as e:
        raise BusinessException(
            message="删除疫苗接种记录失败",
            error_code=ErrorCode.OPERATION_FAILED,
            details={"original_error": str(e)}
        )

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式（无数据）
    return create_success_response(
        data=None,
        msg=SuccessMessage.DELETED,
        request_id=request_id
    )
