"""
宠物服务Pydantic模式
"""
from .pet import (
    Pet, PetC<PERSON>, PetUpdate, PetList,
    MedicalRecord, MedicalRecordCreate, MedicalRecordUpdate, MedicalRecordList,
    Vaccination, VaccinationCreate, VaccinationUpdate, VaccinationList,
    Breed, BreedCreate, Breed<PERSON>pdate, BreedList
)

__all__ = [
    # 宠物相关
    "Pet",
    "PetCreate",
    "PetUpdate",
    "PetList",
    # 医疗记录相关
    "MedicalRecord",
    "MedicalRecordCreate",
    "MedicalRecordUpdate",
    "MedicalRecordList",
    # 疫苗接种相关
    "Vaccination",
    "VaccinationCreate",
    "VaccinationUpdate",
    "VaccinationList",
    # 品种相关
    "Breed",
    "BreedCreate",
    "BreedUpdate",
    "BreedList"
]
