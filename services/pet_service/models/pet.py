"""
宠物相关数据模型
"""
from enum import Enum

from sqlalchemy import (
    Boolean, Column, DateTime, ForeignKey, Integer, String, Text,
    Date, Float, Enum as SQLEnum, Index, JSON
)
from sqlalchemy.orm import relationship

from shared.config.database_schema import create_table_args, get_foreign_key_with_schema
from shared.models.base import BaseAuditModel


class PetGender(str, Enum):
    """宠物性别枚举"""
    MALE = "male"
    FEMALE = "female"
    UNKNOWN = "unknown"


class PetStatus(str, Enum):
    """宠物状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DECEASED = "deceased"
    LOST = "lost"


class Pet(BaseAuditModel):
    """宠物模型"""

    __tablename__ = "pets"

    # 基本信息
    name = Column(String(100), nullable=False, comment="宠物姓名")
    species = Column(String(50), nullable=False, comment="物种")
    breed = Column(String(100), nullable=True, comment="品种")
    gender = Column(SQLEnum(PetGender),
                    default=PetGender.UNKNOWN, comment="性别")

    # 出生和年龄信息
    birth_date = Column(Date, nullable=True, comment="出生日期")
    age_years = Column(Integer, nullable=True, comment="年龄(年)")
    age_months = Column(Integer, nullable=True, comment="年龄(月)")

    # 外观特征
    color = Column(String(100), nullable=True, comment="毛色")
    weight = Column(Float, nullable=True, comment="体重(kg)")
    height = Column(Float, nullable=True, comment="身高(cm)")

    # 身份信息
    microchip_id = Column(String(50), unique=True,
                          nullable=True, comment="芯片号")
    registration_number = Column(String(100), nullable=True, comment="注册号")

    # 状态信息
    status = Column(SQLEnum(PetStatus), default=PetStatus.ACTIVE, comment="状态")
    is_neutered = Column(Boolean, default=False, comment="是否绝育")
    is_vaccinated = Column(Boolean, default=False, comment="是否接种疫苗")

    # 描述信息
    description = Column(Text, nullable=True, comment="描述")
    special_needs = Column(Text, nullable=True, comment="特殊需求")

    # 照片
    avatar_url = Column(String(500), nullable=True, comment="头像URL")

    # 补充信息
    other_info = Column(JSON, nullable=True, comment="其他补充信息(JSON格式)")

    # 关联信息 (外键字段保留用于手动关联)
    owner_id = Column(Integer, nullable=False, comment="主人ID", index=True)
    tenant_pet_id = Column(String(128), nullable=True, comment="厂商的宠物ID", index=True, unique=True)

    # 关系 (仅保留同一微服务内的关系)
    medical_records = relationship(
        "MedicalRecord", back_populates="pet", cascade="all, delete-orphan")
    vaccinations = relationship(
        "Vaccination", back_populates="pet", cascade="all, delete-orphan")

    # 数据库约束和索引
    __table_args__ = create_table_args(
        # 复合索引优化查询性能
        Index('ix_pet_owner_status', 'owner_id', 'status'),
        Index('ix_pet_species_breed', 'species', 'breed'),
        Index('ix_pet_microchip', 'microchip_id'),
    )

    # Note: 跨微服务关系在服务层处理：
    # - User relationship: 通过 owner_id 查询 user_service

    def __repr__(self):
        return f"<Pet(id={self.id}, name='{self.name}', species='{self.species}')>"

    @property
    def age_display(self) -> str:
        """获取年龄显示字符串"""
        if self.age_years and self.age_months:
            return f"{self.age_years}岁{self.age_months}个月"
        elif self.age_years:
            return f"{self.age_years}岁"
        elif self.age_months:
            return f"{self.age_months}个月"
        return "未知"


class MedicalRecord(BaseAuditModel):
    """医疗记录模型"""

    __tablename__ = "medical_records"

    # 基本信息
    visit_date = Column(DateTime(timezone=True),
                        nullable=False, comment="就诊日期")
    diagnosis = Column(Text, nullable=False, comment="诊断")
    symptoms = Column(Text, nullable=True, comment="症状")
    treatment = Column(Text, nullable=True, comment="治疗方案")

    # 医生信息
    veterinarian_name = Column(String(100), nullable=True, comment="兽医姓名")
    clinic_name = Column(String(200), nullable=True, comment="诊所名称")

    # 费用信息
    cost = Column(Float, nullable=True, comment="费用")

    # 备注
    notes = Column(Text, nullable=True, comment="备注")

    # 关联信息
    pet_id = Column(Integer, ForeignKey(get_foreign_key_with_schema("pets")),
                    nullable=False, comment="宠物ID")

    # 关系
    pet = relationship("Pet", back_populates="medical_records")

    # 数据库约束和索引
    __table_args__ = create_table_args(
        # 复合索引优化查询性能
        Index('ix_medical_record_pet_date', 'pet_id', 'visit_date'),
        Index('ix_medical_record_date', 'visit_date'),
    )

    def __repr__(self):
        return f"<MedicalRecord(id={self.id}, pet_id={self.pet_id}, visit_date='{self.visit_date}')>"


class Vaccination(BaseAuditModel):
    """疫苗接种记录模型"""

    __tablename__ = "vaccinations"

    # 疫苗信息
    vaccine_name = Column(String(100), nullable=False, comment="疫苗名称")
    vaccine_type = Column(String(50), nullable=True, comment="疫苗类型")
    batch_number = Column(String(100), nullable=True, comment="批次号")

    # 接种信息
    vaccination_date = Column(Date, nullable=False, comment="接种日期")
    next_due_date = Column(Date, nullable=True, comment="下次接种日期")

    # 医生信息
    veterinarian_name = Column(String(100), nullable=True, comment="兽医姓名")
    clinic_name = Column(String(200), nullable=True, comment="诊所名称")

    # 备注
    notes = Column(Text, nullable=True, comment="备注")

    # 关联信息
    pet_id = Column(Integer, ForeignKey(get_foreign_key_with_schema("pets")),
                    nullable=False, comment="宠物ID")

    # 关系
    pet = relationship("Pet", back_populates="vaccinations")

    # 数据库约束和索引
    __table_args__ = create_table_args(
        # 复合索引优化查询性能
        Index('ix_vaccination_pet_date', 'pet_id', 'vaccination_date'),
        Index('ix_vaccination_due_date', 'next_due_date'),
        Index('ix_vaccination_vaccine', 'vaccine_name'),
    )

    def __repr__(self):
        return f"<Vaccination(id={self.id}, pet_id={self.pet_id}, vaccine_name='{self.vaccine_name}')>"


class Breed(BaseAuditModel):
    """品种模型"""

    __tablename__ = "breeds"

    # 基本信息
    name = Column(String(100), nullable=False, unique=True, comment="品种名称")
    species = Column(String(50), nullable=False, comment="物种")

    # 描述信息
    description = Column(Text, nullable=True, comment="品种描述")
    origin_country = Column(String(100), nullable=True, comment="原产国")

    # 特征信息
    average_weight_min = Column(Float, nullable=True, comment="平均体重最小值(kg)")
    average_weight_max = Column(Float, nullable=True, comment="平均体重最大值(kg)")
    average_height_min = Column(Float, nullable=True, comment="平均身高最小值(cm)")
    average_height_max = Column(Float, nullable=True, comment="平均身高最大值(cm)")
    life_expectancy = Column(Integer, nullable=True, comment="预期寿命(年)")

    # 性格特征
    temperament = Column(Text, nullable=True, comment="性格特征")
    care_requirements = Column(Text, nullable=True, comment="护理要求")

    # 数据库约束和索引
    __table_args__ = create_table_args(
        # 复合索引优化查询性能
        Index('ix_breed_species_name', 'species', 'name'),
    )

    def __repr__(self):
        return f"<Breed(id={self.id}, name='{self.name}', species='{self.species}')>"
