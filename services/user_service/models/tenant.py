"""
租户模型
"""
from datetime import datetime, timezone
from typing import Optional

from sqlalchemy import Column, String, Text, Boolean, Integer, DateTime, JSON, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from shared.models.base import BaseAuditModel
from shared.config.database_schema import create_table_args, get_foreign_key_with_schema


class Tenant(BaseAuditModel):
    """租户模型 - 多租户架构的核心实体"""

    __tablename__ = "tenants"

    # 基本租户信息
    name = Column(String(100), nullable=False, index=True, comment="租户名称")
    slug = Column(String(50), unique=True, index=True,
                  nullable=False, comment="租户标识符(URL友好)")
    display_name = Column(String(200), nullable=False, comment="租户显示名称")
    description = Column(Text, nullable=True, comment="租户描述")

    # 联系信息
    contact_email = Column(String(255), nullable=True, comment="联系邮箱")
    contact_phone = Column(String(20), nullable=True, comment="联系电话")
    contact_person = Column(String(100), nullable=True, comment="联系人")

    # 租户状态
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活")
    is_trial = Column(Boolean, default=True, nullable=False, comment="是否试用版")
    is_suspended = Column(Boolean, default=False,
                          nullable=False, comment="是否暂停")

    # 时间管理
    trial_ends_at = Column(DateTime(timezone=True),
                           nullable=True, comment="试用结束时间")
    subscription_starts_at = Column(
        DateTime(timezone=True), nullable=True, comment="订阅开始时间")
    subscription_ends_at = Column(
        DateTime(timezone=True), nullable=True, comment="订阅结束时间")

    # 资源限制
    max_users = Column(Integer, default=10, nullable=False, comment="最大用户数")
    max_api_keys = Column(Integer, default=50,
                          nullable=False, comment="最大API密钥数")
    max_storage_mb = Column(Integer, default=1024,
                            nullable=False, comment="最大存储空间(MB)")

    # 功能配置
    features_enabled = Column(
        JSON, default=dict, nullable=False, comment="启用的功能列表")
    settings = Column(JSON, default=dict, nullable=False, comment="租户配置")

    # 计费信息
    billing_email = Column(String(255), nullable=True, comment="计费邮箱")
    billing_address = Column(Text, nullable=True, comment="计费地址")

    # 统计信息(冗余字段，用于快速查询)
    user_count = Column(Integer, default=0, nullable=False, comment="用户数量")
    api_key_count = Column(
        Integer, default=0, nullable=False, comment="API密钥数量")
    last_activity_at = Column(DateTime(timezone=True),
                              nullable=True, comment="最后活动时间")

    # 关系映射
    users = relationship(
        "User",
        back_populates="tenant",
        cascade="all, delete-orphan",
        lazy="dynamic"
    )

    # 数据库约束和索引
    __table_args__ = create_table_args()

    def __repr__(self):
        return f"<Tenant(id={self.id}, name='{self.name}', slug='{self.slug}')>"

    @property
    def is_expired(self) -> bool:
        """检查租户是否已过期"""
        if self.is_trial and self.trial_ends_at:
            return datetime.now(timezone.utc) > self.trial_ends_at
        elif self.subscription_ends_at:
            return datetime.now(timezone.utc) > self.subscription_ends_at
        return False

    @property
    def is_available(self) -> bool:
        """检查租户是否可用"""
        return (
            self.is_active and
            not self.is_suspended and
            not self.is_expired and
            not self.is_deleted
        )

    @property
    def days_until_expiry(self) -> Optional[int]:
        """获取距离过期的天数"""
        expiry_date = None
        if self.is_trial and self.trial_ends_at:
            expiry_date = self.trial_ends_at
        elif self.subscription_ends_at:
            expiry_date = self.subscription_ends_at

        if expiry_date:
            delta = expiry_date - datetime.now(timezone.utc)
            return max(0, delta.days)
        return None

    def can_add_user(self) -> bool:
        """检查是否可以添加新用户"""
        return self.is_available and self.user_count < self.max_users

    def can_add_api_key(self) -> bool:
        """检查是否可以添加新API密钥"""
        return self.is_available and self.api_key_count < self.max_api_keys

    def is_feature_enabled(self, feature_name: str) -> bool:
        """检查功能是否启用"""
        return self.features_enabled.get(feature_name, False)

    def get_setting(self, key: str, default=None):
        """获取租户配置"""
        return self.settings.get(key, default)

    def set_setting(self, key: str, value):
        """设置租户配置"""
        if self.settings is None:
            self.settings = {}
        self.settings[key] = value

    async def update_user_count(self, db_session=None):
        """更新用户数量统计"""
        from sqlalchemy import select, func
        from .user import User

        if db_session is None:
            # 如果没有提供会话，则无法执行异步查询
            # 这种情况下，我们需要在调用处提供会话
            raise ValueError("需要提供数据库会话来执行异步查询")

        # 使用异步查询计算用户数量
        query = select(func.count(User.id)).where(
            User.tenant_id == self.id,
            User.is_deleted == False
        )
        result = await db_session.execute(query)
        self.user_count = result.scalar() or 0

    def update_last_activity(self):
        """更新最后活动时间"""
        self.last_activity_at = datetime.now(timezone.utc)

    def suspend(self, reason: str = None):
        """暂停租户"""
        self.is_suspended = True
        if reason:
            self.set_setting('suspension_reason', reason)
        self.set_setting('suspended_at', datetime.now(
            timezone.utc).isoformat())

    def unsuspend(self):
        """恢复租户"""
        self.is_suspended = False
        self.set_setting('suspension_reason', None)
        self.set_setting('suspended_at', None)

    def extend_trial(self, days: int):
        """延长试用期"""
        if self.trial_ends_at:
            from datetime import timedelta
            self.trial_ends_at += timedelta(days=days)
        else:
            from datetime import timedelta
            self.trial_ends_at = datetime.now(
                timezone.utc) + timedelta(days=days)

    def upgrade_to_paid(self, subscription_days: int = 365):
        """升级为付费版"""
        from datetime import timedelta
        now = datetime.now(timezone.utc)

        self.is_trial = False
        self.subscription_starts_at = now
        self.subscription_ends_at = now + timedelta(days=subscription_days)
        self.trial_ends_at = None


class TenantRole(BaseAuditModel):
    """租户角色模型 - 定义租户内的用户角色"""

    __tablename__ = "tenant_roles"

    # 基本角色信息
    name = Column(String(50), nullable=False, comment="角色名称")
    display_name = Column(String(100), nullable=False, comment="角色显示名称")
    description = Column(Text, nullable=True, comment="角色描述")

    # 角色属性
    is_system = Column(Boolean, default=False,
                       nullable=False, comment="是否系统角色")
    is_default = Column(Boolean, default=False,
                        nullable=False, comment="是否默认角色")
    level = Column(Integer, default=0, nullable=False,
                   comment="角色级别(数字越大权限越高)")

    # 租户关联
    tenant_id = Column(Integer, ForeignKey(get_foreign_key_with_schema("tenants")),
                       nullable=False, comment="所属租户ID")
    tenant = relationship("Tenant", backref="roles")

    # 权限配置
    permissions = Column(JSON, default=list, nullable=False, comment="角色权限列表")

    # 数据库约束和索引
    __table_args__ = create_table_args()

    def __repr__(self):
        return f"<TenantRole(id={self.id}, name='{self.name}', tenant_id={self.tenant_id})>"

    def has_permission(self, permission: str) -> bool:
        """检查角色是否具有指定权限"""
        return permission in self.permissions

    def add_permission(self, permission: str):
        """添加权限"""
        if permission not in self.permissions:
            self.permissions.append(permission)

    def remove_permission(self, permission: str):
        """移除权限"""
        if permission in self.permissions:
            self.permissions.remove(permission)


# 默认租户功能配置
DEFAULT_TENANT_FEATURES = {
    "ai_diagnosis": True,          # AI问诊功能
    "ai_vision": True,             # AI视觉识别
    "ai_report_generation": True,  # AI报告生成
    "ai_report_analysis": True,    # AI报告解读
    "ocr_service": True,           # OCR服务
    "api_access": True,            # API访问
    "custom_agents": False,        # 自定义智能体(付费功能)
    "advanced_analytics": False,   # 高级分析(付费功能)
    "priority_support": False,     # 优先支持(付费功能)
    "white_label": False,          # 白标服务(企业功能)
}

# 默认租户设置
DEFAULT_TENANT_SETTINGS = {
    "timezone": "Asia/Shanghai",
    "language": "zh-CN",
    "date_format": "YYYY-MM-DD",
    "time_format": "24h",
    "currency": "CNY",
    "notification_email": True,
    "notification_sms": False,
    "data_retention_days": 365,
    "auto_backup": True,
    "backup_frequency": "daily",
}

# 系统默认租户角色
DEFAULT_TENANT_ROLES = [
    {
        "name": "tenant_admin",
        "display_name": "租户管理员",
        "description": "租户内的完全管理权限",
        "is_system": True,
        "is_default": False,
        "level": 100,
        "permissions": [
            "tenant.read", "tenant.update",
            "user.create", "user.read", "user.update", "user.delete",
            "api_key.create", "api_key.read", "api_key.update", "api_key.delete",
            "permission.read", "permission.assign",
            "agent.create", "agent.read", "agent.update", "agent.delete", "agent.execute",
            "pet.create", "pet.read", "pet.update", "pet.delete",
            "report.create", "report.read", "report.update", "report.delete",
            "analytics.read", "settings.read", "settings.update"
        ]
    },
    {
        "name": "user",
        "display_name": "普通用户",
        "description": "租户内的基本用户权限",
        "is_system": True,
        "is_default": True,
        "level": 10,
        "permissions": [
            "user.read_self", "user.update_self",
            "api_key.create_self", "api_key.read_self", "api_key.update_self", "api_key.delete_self",
            "agent.read", "agent.execute",
            "pet.create_own", "pet.read_own", "pet.update_own", "pet.delete_own",
            "report.create_own", "report.read_own"
        ]
    },
    {
        "name": "viewer",
        "display_name": "只读用户",
        "description": "租户内的只读权限",
        "is_system": True,
        "is_default": False,
        "level": 5,
        "permissions": [
            "user.read_self",
            "agent.read",
            "pet.read_own",
            "report.read_own"
        ]
    }
]
