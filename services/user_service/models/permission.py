"""
用户服务的权限模型
"""
from sqlalchemy import Column, String, Text, Boolean
from sqlalchemy.orm import relationship

from shared.models.base import BaseAuditModel
from shared.config.database_schema import create_table_args


class Permission(BaseAuditModel):
    """权限模型"""

    __tablename__ = "permissions"

    # 权限信息
    name = Column(String(100), unique=True, index=True, nullable=False)
    display_name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    category = Column(String(50), nullable=False, default="general")

    # 权限状态
    # 系统权限不能被删除
    is_system = Column(Boolean, default=False, nullable=False)

    # 用户关系：通过中间表user_permissions建立多对多关系
    # 注意：这里暂时注释掉，关系将在user.py中的configure_model_relationships()中配置
    # users = relationship(...)

    # 数据库约束和索引
    __table_args__ = create_table_args()

    def __repr__(self):
        return f"<Permission(id={self.id}, name='{self.name}', category='{self.category}')>"


# UserPermission表已在user.py中定义为关联表


# 预定义的系统权限
SYSTEM_PERMISSIONS = [
    # 用户管理
    {
        "name": "user.create",
        "display_name": "创建用户",
        "description": "创建新用户账户",
        "category": "user_management",
        "is_system": True
    },
    {
        "name": "user.read",
        "display_name": "查看用户",
        "description": "查看用户信息",
        "category": "user_management",
        "is_system": True
    },
    {
        "name": "user.update",
        "display_name": "更新用户",
        "description": "修改用户信息",
        "category": "user_management",
        "is_system": True
    },
    {
        "name": "user.delete",
        "display_name": "删除用户",
        "description": "删除用户账户",
        "category": "user_management",
        "is_system": True
    },

    # 权限管理
    {
        "name": "permission.create",
        "display_name": "创建权限",
        "description": "创建新权限",
        "category": "permission_management",
        "is_system": True
    },
    {
        "name": "permission.read",
        "display_name": "查看权限",
        "description": "查看权限",
        "category": "permission_management",
        "is_system": True
    },
    {
        "name": "permission.update",
        "display_name": "更新权限",
        "description": "修改权限",
        "category": "permission_management",
        "is_system": True
    },
    {
        "name": "permission.delete",
        "display_name": "删除权限",
        "description": "删除权限",
        "category": "permission_management",
        "is_system": True
    },

    # API密钥管理
    {
        "name": "api_key.create",
        "display_name": "创建API密钥",
        "description": "创建新的API密钥",
        "category": "api_management",
        "is_system": True
    },
    {
        "name": "api_key.read",
        "display_name": "查看API密钥",
        "description": "查看API密钥",
        "category": "api_management",
        "is_system": True
    },
    {
        "name": "api_key.update",
        "display_name": "更新API密钥",
        "description": "修改API密钥",
        "category": "api_management",
        "is_system": True
    },
    {
        "name": "api_key.delete",
        "display_name": "删除API密钥",
        "description": "删除API密钥",
        "category": "api_management",
        "is_system": True
    },

    # 宠物管理
    {
        "name": "pet.create",
        "display_name": "创建宠物",
        "description": "添加新的宠物记录",
        "category": "pet_management",
        "is_system": True
    },
    {
        "name": "pet.read",
        "display_name": "查看宠物",
        "description": "查看宠物信息",
        "category": "pet_management",
        "is_system": True
    },
    {
        "name": "pet.update",
        "display_name": "更新宠物",
        "description": "修改宠物信息",
        "category": "pet_management",
        "is_system": True
    },
    {
        "name": "pet.delete",
        "display_name": "删除宠物",
        "description": "删除宠物记录",
        "category": "pet_management",
        "is_system": True
    },

    # 医疗记录
    {
        "name": "medical_record.create",
        "display_name": "创建医疗记录",
        "description": "添加新的医疗记录",
        "category": "medical_management",
        "is_system": True
    },
    {
        "name": "medical_record.read",
        "display_name": "查看医疗记录",
        "description": "查看医疗记录",
        "category": "medical_management",
        "is_system": True
    },
    {
        "name": "medical_record.update",
        "display_name": "更新医疗记录",
        "description": "修改医疗记录",
        "category": "medical_management",
        "is_system": True
    },
    {
        "name": "medical_record.delete",
        "display_name": "删除医疗记录",
        "description": "删除医疗记录",
        "category": "medical_management",
        "is_system": True
    },

    # 应用管理
    {
        "name": "application.create",
        "display_name": "创建应用",
        "description": "创建新的AI应用",
        "category": "app_management",
        "is_system": True
    },
    {
        "name": "application.read",
        "display_name": "查看应用",
        "description": "查看AI应用",
        "category": "app_management",
        "is_system": True
    },
    {
        "name": "application.update",
        "display_name": "更新应用",
        "description": "修改AI应用",
        "category": "app_management",
        "is_system": True
    },
    {
        "name": "application.delete",
        "display_name": "删除应用",
        "description": "删除AI应用",
        "category": "app_management",
        "is_system": True
    },

    # 系统管理
    {
        "name": "system.admin",
        "display_name": "系统管理",
        "description": "完整的系统管理访问权限",
        "category": "system",
        "is_system": True
    },
    {
        "name": "system.logs",
        "display_name": "查看系统日志",
        "description": "访问系统日志和监控",
        "category": "system",
        "is_system": True
    }
]
