"""
用户服务的用户模型
"""
from datetime import datetime, timezone

from sqlalchemy import Boolean, Column, DateTime, ForeignKey, Integer, String, Text, Table, Index, UniqueConstraint
from sqlalchemy.orm import relationship

from shared.database import Base
from shared.config.database_schema import create_table_args, get_foreign_key_with_schema, get_database_schema
from shared.models.base import BaseAuditModel

# 定义用户权限关联表
user_permissions = Table(
    'user_permissions',
    Base.metadata,
    Column('user_id', Integer, ForeignKey(
        get_foreign_key_with_schema('users')), primary_key=True),
    Column('permission_id', Integer, ForeignKey(
        get_foreign_key_with_schema('permissions')), primary_key=True),
    schema=get_database_schema()  # 使用统一的schema配置
)


class User(BaseAuditModel):
    """用户模型"""

    __tablename__ = "users"

    # 多租户支持
    tenant_id = Column(Integer, <PERSON><PERSON>ey(get_foreign_key_with_schema("tenants")),
                       nullable=True, index=True, comment="所属租户ID")

    # 基本用户信息
    email = Column(String(255), nullable=False, comment="用户邮箱")
    username = Column(String(100), nullable=False, comment="用户名")
    hashed_password = Column(String(255), nullable=False, comment="哈希密码")

    # 用户状态
    is_active = Column(Boolean, default=True, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)

    # 个人资料信息
    first_name = Column(String(100), nullable=True)
    last_name = Column(String(100), nullable=True)
    phone = Column(String(20), nullable=True)
    avatar_url = Column(String(500), nullable=True)

    # 租户角色
    tenant_role_id = Column(Integer, ForeignKey(
        get_foreign_key_with_schema("tenant_roles")), nullable=True, comment="租户角色ID")

    # 时间戳字段
    last_login_at = Column(DateTime(timezone=True), nullable=True)
    email_verified_at = Column(DateTime(timezone=True), nullable=True)
    deleted_at = Column(DateTime(timezone=True), nullable=True)

    # 关系映射（仅保留同一微服务内的关系）
    tenant = relationship("Tenant", back_populates="users")
    tenant_role = relationship("TenantRole")
    api_keys = relationship(
        "APIKey", back_populates="user", cascade="all, delete-orphan")
    temporary_api_keys = relationship(
        "TemporaryAPIKey", back_populates="user", cascade="all, delete-orphan")

    # 权限关系：通过中间表user_permissions建立多对多关系
    permissions = relationship(
        "Permission",
        secondary=user_permissions,
        back_populates="users",
        lazy="selectin"  # 默认使用selectin加载策略
    )

    # 数据库约束和索引
    __table_args__ = create_table_args(
        # 租户内用户名唯一约束
        UniqueConstraint('tenant_id', 'username', name='uq_tenant_username'),
        # 租户内邮箱唯一约束
        UniqueConstraint('tenant_id', 'email', name='uq_tenant_email'),
        # 注意：全局用户的唯一性将通过应用层逻辑处理
        # 复合索引优化查询性能
        Index('ix_tenant_user_active', 'tenant_id', 'is_active'),
        Index('ix_tenant_user_email', 'tenant_id', 'email'),
        Index('ix_tenant_user_username', 'tenant_id', 'username'),
    )

    # 注意：跨微服务关系在服务层处理，不使用ORM关系：
    # - 宠物关系：通过 owner_id 字段在 pet_service 中维护
    # - 智能体关系：通过 owner_id 字段在 app_service 中维护

    def __repr__(self):
        return f"<User(id={self.id}, email='{self.email}', username='{self.username}')>"

    @property
    def full_name(self) -> str:
        """获取用户全名"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.username

    @property
    def is_tenant_admin(self) -> bool:
        """检查是否为租户管理员"""
        return self.tenant_role and self.tenant_role.name == "tenant_admin"

    @property
    def is_global_admin(self) -> bool:
        """检查是否为全局管理员(无租户的超级用户)"""
        return self.is_superuser and self.tenant_id is None

    def has_tenant_permission(self, permission: str) -> bool:
        """检查是否具有租户权限"""
        if self.is_global_admin:
            return True
        if self.tenant_role:
            return self.tenant_role.has_permission(permission)
        return False

    def can_access_tenant(self, tenant_id: int) -> bool:
        """检查是否可以访问指定租户"""
        if self.is_global_admin:
            return True
        return self.tenant_id == tenant_id

    def get_tenant_context(self) -> dict:
        """获取租户上下文信息"""
        return {
            "tenant_id": self.tenant_id,
            "tenant_role": self.tenant_role.name if self.tenant_role else None,
            "is_tenant_admin": self.is_tenant_admin,
            "is_global_admin": self.is_global_admin
        }

    # 注意：权限相关方法移至服务层实现
    # 使用 shared.utils.cross_service_queries.CrossServiceQueryHelper
    # 进行权限检查和管理


class APIKey(BaseAuditModel):
    """API密钥模型，用于外部访问"""

    __tablename__ = "api_keys"

    # 多租户支持
    tenant_id = Column(Integer, ForeignKey(get_foreign_key_with_schema("tenants")),
                       nullable=True, index=True, comment="所属租户ID")

    # API密钥信息
    name = Column(String(100), nullable=False, comment="API密钥名称")
    key = Column(String(100), unique=True, index=True,
                 nullable=False, comment="API密钥值")
    description = Column(Text, nullable=True, comment="API密钥描述")

    # 状态和使用情况
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活")
    last_used_at = Column(DateTime(timezone=True),
                          nullable=True, comment="最后使用时间")
    expires_at = Column(DateTime(timezone=True), nullable=True, comment="过期时间")

    # 速率限制配置
    rate_limit_per_minute = Column(
        Integer, default=60, nullable=False, comment="每分钟请求限制")
    rate_limit_per_day = Column(
        Integer, default=10000, nullable=False, comment="每日请求限制")

    # 关系映射
    user_id = Column(Integer, ForeignKey(get_foreign_key_with_schema("users")),
                     nullable=False, comment="用户ID")
    user = relationship("User", back_populates="api_keys")
    tenant = relationship("Tenant")
    temporary_keys = relationship(
        "TemporaryAPIKey", back_populates="real_api_key", cascade="all, delete-orphan")

    # 数据库约束和索引
    __table_args__ = create_table_args(
        # 租户内API密钥名称唯一约束
        UniqueConstraint('tenant_id', 'user_id', 'name',
                         name='uq_tenant_user_api_key_name'),
        # 复合索引优化查询性能
        Index('ix_tenant_api_key_active', 'tenant_id', 'is_active'),
        Index('ix_tenant_user_api_key', 'tenant_id', 'user_id'),
        Index('ix_api_key_expires', 'expires_at'),
    )

    def __repr__(self):
        return f"<APIKey(id={self.id}, name='{self.name}', user_id={self.user_id})>"

    def is_expired(self) -> bool:
        """检查API密钥是否已过期"""
        if self.expires_at is None:
            return False
        return datetime.now(timezone.utc) > self.expires_at

    def is_valid(self) -> bool:
        """检查API密钥是否有效（激活且未过期且未删除）"""
        return self.is_active and not self.is_expired() and not self.is_deleted

    def belongs_to_tenant(self, tenant_id: int) -> bool:
        """检查API密钥是否属于指定租户"""
        return self.tenant_id == tenant_id

    def can_be_used_by_user(self, user_id: int, tenant_id: int = None) -> bool:
        """检查API密钥是否可以被指定用户使用"""
        if not self.is_valid():
            return False

        # 检查用户所有权
        if self.user_id != user_id:
            return False

        # 检查租户权限
        if tenant_id is not None and self.tenant_id != tenant_id:
            return False

        return True

    def get_tenant_context(self) -> dict:
        """获取API密钥的租户上下文"""
        return {
            "tenant_id": self.tenant_id,
            "user_id": self.user_id,
            "key_name": self.name
        }


class TemporaryAPIKey(BaseAuditModel):
    """临时API密钥模型，用于临时访问授权"""

    __tablename__ = "temporary_api_keys"

    # 多租户支持
    tenant_id = Column(Integer, ForeignKey(get_foreign_key_with_schema("tenants")),
                       nullable=True, index=True, comment="所属租户ID")

    # 临时密钥信息
    name = Column(String(100), nullable=False, comment="临时API密钥名称")
    temp_key = Column(String(100), unique=True, index=True,
                      nullable=False, comment="临时API密钥值（tt_开头）")
    description = Column(Text, nullable=True, comment="临时API密钥描述")

    # 关联的真实API密钥
    real_api_key_id = Column(Integer, ForeignKey(get_foreign_key_with_schema("api_keys")),
                             nullable=False, comment="关联的真实API密钥ID")

    # 状态和使用情况
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活")
    last_used_at = Column(DateTime(timezone=True),
                          nullable=True, comment="最后使用时间")
    expires_at = Column(DateTime(timezone=True),
                        nullable=False, comment="过期时间")

    # 使用统计
    usage_count = Column(Integer, default=0, nullable=False, comment="使用次数")
    max_usage_count = Column(Integer, nullable=True, comment="最大使用次数限制")

    # 关系映射
    user_id = Column(Integer, ForeignKey(get_foreign_key_with_schema("users")),
                     nullable=False, comment="用户ID")
    user = relationship("User", back_populates="temporary_api_keys")
    real_api_key = relationship("APIKey", back_populates="temporary_keys")
    tenant = relationship("Tenant")

    # 数据库约束和索引
    __table_args__ = (
        # 租户内临时API密钥名称唯一约束
        UniqueConstraint('tenant_id', 'user_id', 'name',
                         name='uq_tenant_user_temp_api_key_name'),
        # 复合索引优化查询性能
        Index('ix_tenant_temp_api_key_active', 'tenant_id', 'is_active'),
        Index('ix_tenant_user_temp_api_key', 'tenant_id', 'user_id'),
        Index('ix_temp_api_key_expires', 'expires_at'),
        Index('ix_temp_api_key_real_key', 'real_api_key_id'),
    )

    def __repr__(self):
        return f"<TemporaryAPIKey(id={self.id}, name='{self.name}', user_id={self.user_id})>"

    def is_expired(self) -> bool:
        """检查临时API密钥是否已过期"""
        return datetime.now(timezone.utc) > self.expires_at

    def is_usage_exceeded(self) -> bool:
        """检查是否超过使用次数限制"""
        if self.max_usage_count is None:
            return False
        return self.usage_count >= self.max_usage_count

    def is_valid(self) -> bool:
        """检查临时API密钥是否有效（激活且未过期且未删除且未超过使用限制）"""
        return (self.is_active and
                not self.is_expired() and
                not self.is_deleted and
                not self.is_usage_exceeded())

    def belongs_to_tenant(self, tenant_id: int) -> bool:
        """检查临时API密钥是否属于指定租户"""
        return self.tenant_id == tenant_id

    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
        self.last_used_at = datetime.now(timezone.utc)


class UserSession(BaseAuditModel):
    """用户会话模型，用于跟踪活跃会话"""

    __tablename__ = "user_sessions"

    # 多租户支持
    tenant_id = Column(Integer, ForeignKey(get_foreign_key_with_schema("tenants")),
                       nullable=True, index=True, comment="所属租户ID")

    # 会话信息
    session_id = Column(String(255), unique=True, index=True,
                        nullable=False, comment="会话ID")
    refresh_token = Column(String(500), nullable=False, comment="刷新令牌")
    user_agent = Column(String(500), nullable=True, comment="用户代理")
    ip_address = Column(String(45), nullable=True, comment="IP地址")  # 兼容IPv6

    # 会话状态
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活")
    expires_at = Column(DateTime(timezone=True),
                        nullable=False, comment="过期时间")
    last_activity_at = Column(DateTime(timezone=True),
                              nullable=False, comment="最后活动时间")

    # 关系映射
    user_id = Column(Integer, ForeignKey(get_foreign_key_with_schema("users")),
                     nullable=False, comment="用户ID")
    user = relationship("User")
    tenant = relationship("Tenant")

    # 数据库约束和索引
    __table_args__ = create_table_args(
        # 复合索引优化查询性能
        Index('ix_tenant_user_session', 'tenant_id', 'user_id'),
        Index('ix_session_expires', 'expires_at'),
        Index('ix_tenant_session_active', 'tenant_id', 'is_active'),
    )

    def __repr__(self):
        return f"<UserSession(id={self.id}, user_id={self.user_id}, session_id='{self.session_id}')>"

    def is_expired(self) -> bool:
        """检查会话是否已过期"""
        return datetime.now(timezone.utc) > self.expires_at

    def is_valid(self) -> bool:
        """检查会话是否有效（激活且未过期且未删除）"""
        return self.is_active and not self.is_expired() and not self.is_deleted

    def belongs_to_tenant(self, tenant_id: int) -> bool:
        """检查会话是否属于指定租户"""
        return self.tenant_id == tenant_id

    def get_tenant_context(self) -> dict:
        """获取会话的租户上下文"""
        return {
            "tenant_id": self.tenant_id,
            "user_id": self.user_id,
            "session_id": self.session_id
        }


# 配置模型关系 - 在所有模型定义完成后
def configure_model_relationships():
    """配置模型之间的关系，避免循环导入问题"""
    from .permission import Permission

    # 确保user_permissions表在Permission模块的全局作用域中可用
    import sys
    permission_module = sys.modules[Permission.__module__]
    permission_module.user_permissions = user_permissions

    # 为Permission模型添加users关系
    Permission.users = relationship(
        "User",
        secondary=user_permissions,
        back_populates="permissions",
        lazy="selectin"
    )


# 调用关系配置
configure_model_relationships()
