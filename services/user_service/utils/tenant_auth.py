"""
租户级别的权限控制工具
"""
from typing import Optional, Dict, Any, List
from fastapi import status, Depends
from pydantic import BaseModel, Field
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from shared.database import get_async_db
from shared.utils.exception_handlers import BusinessException
from shared.models.response import ErrorCode
from shared.utils.auth import get_current_user_from_token
from ..models.user import User
from ..models.tenant import Tenant, TenantRole


class TenantContext(BaseModel):
    """租户上下文类 - 使用Pydantic模型以避免验证问题"""

    # 基本字段
    tenant_id: Optional[int] = None
    is_global_admin: bool = False

    # 非序列化字段（用于内部逻辑）
    tenant: Optional[Tenant] = Field(default=None, exclude=True)
    user: Optional[User] = Field(default=None, exclude=True)
    tenant_role: Optional[TenantRole] = Field(default=None, exclude=True)

    class Config:
        arbitrary_types_allowed = True  # 允许SQLAlchemy模型类型

    @property
    def is_tenant_admin(self) -> bool:
        """检查是否为租户管理员"""
        # 全局管理员在任何租户中都被视为租户管理员
        if self.is_global_admin:
            return True
        # 否则检查租户角色
        return self.tenant_role and self.tenant_role.name == "tenant_admin"

    @property
    def can_manage_tenant(self) -> bool:
        """检查是否可以管理租户"""
        return self.is_global_admin or self.is_tenant_admin

    def has_permission(self, permission: str) -> bool:
        """检查是否具有指定权限"""
        if self.is_global_admin:
            return True
        if self.tenant_role:
            return self.tenant_role.has_permission(permission)
        return False

    def can_access_tenant(self, target_tenant_id: int) -> bool:
        """检查是否可以访问指定租户"""
        if self.is_global_admin:
            return True
        return self.tenant_id == target_tenant_id

    def can_access_user(self, target_user_id: int, target_tenant_id: Optional[int] = None) -> bool:
        """检查是否可以访问指定用户"""
        if self.is_global_admin:
            return True

        # 用户只能访问自己
        if self.user and self.user.id == target_user_id:
            return True

        # 租户管理员可以访问租户内的所有用户
        if self.is_tenant_admin and target_tenant_id == self.tenant_id:
            return True

        return False

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "tenant_id": self.tenant_id,
            "user_id": self.user.id if self.user else None,
            "tenant_role": self.tenant_role.name if self.tenant_role else None,
            "is_global_admin": self.is_global_admin,
            "is_tenant_admin": self.is_tenant_admin,
            "can_manage_tenant": self.can_manage_tenant
        }


async def get_tenant_context(
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_async_db)
) -> TenantContext:
    """获取当前用户的租户上下文"""

    # 查询用户详细信息
    user_query = select(User).options(
        selectinload(User.tenant),
        selectinload(User.tenant_role)
    ).where(User.id == current_user.get("id"))

    result = await db.execute(user_query)
    user = result.scalar_one_or_none()

    if not user:
        raise BusinessException(
            message="用户不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 检查用户是否为全局管理员
    # 修复逻辑：superuser都是全局管理员，不管是否有tenant_id
    is_global_admin = user.is_superuser

    return TenantContext(
        tenant_id=user.tenant_id,
        is_global_admin=is_global_admin,
        tenant=user.tenant,
        user=user,
        tenant_role=user.tenant_role
    )


async def require_tenant_permission(
    permission: str,
    tenant_context: TenantContext = Depends(get_tenant_context)
) -> TenantContext:
    """要求指定的租户权限"""

    if not tenant_context.has_permission(permission):
        raise BusinessException(
            message=f"缺少权限: {permission}",
            error_code=ErrorCode.FORBIDDEN,
            status_code=status.HTTP_403_FORBIDDEN
        )

    return tenant_context


async def require_tenant_admin(
    tenant_context: TenantContext = Depends(get_tenant_context)
) -> TenantContext:
    """要求租户管理员权限"""

    if not tenant_context.can_manage_tenant:
        raise BusinessException(
            message="需要租户管理员或全局管理员权限",
            error_code=ErrorCode.FORBIDDEN,
            status_code=status.HTTP_403_FORBIDDEN
        )

    return tenant_context


async def require_global_admin(
    tenant_context: TenantContext = Depends(get_tenant_context)
) -> TenantContext:
    """要求全局管理员权限"""

    if not tenant_context.is_global_admin:
        raise BusinessException(
            message="需要全局管理员权限",
            error_code=ErrorCode.FORBIDDEN,
            status_code=status.HTTP_403_FORBIDDEN
        )

    return tenant_context


async def require_tenant_access(
    tenant_id: int,
    tenant_context: TenantContext = Depends(get_tenant_context)
) -> TenantContext:
    """要求对指定租户的访问权限"""

    if not tenant_context.can_access_tenant(tenant_id):
        raise BusinessException(
            message="无权访问此租户",
            error_code=ErrorCode.FORBIDDEN,
            status_code=status.HTTP_403_FORBIDDEN
        )

    return tenant_context


async def require_user_access(
    user_id: int,
    tenant_context: TenantContext = Depends(get_tenant_context),
    db: AsyncSession = Depends(get_async_db)
) -> TenantContext:
    """要求对指定用户的访问权限"""

    # 查询目标用户的租户信息
    target_user_query = select(User).where(User.id == user_id)
    result = await db.execute(target_user_query)
    target_user = result.scalar_one_or_none()

    if not target_user:
        raise BusinessException(
            message="用户不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    if not tenant_context.can_access_user(user_id, target_user.tenant_id):
        raise BusinessException(
            message="无权访问此用户",
            error_code=ErrorCode.FORBIDDEN,
            status_code=status.HTTP_403_FORBIDDEN
        )

    return tenant_context


class TenantPermissionChecker:
    """租户权限检查器"""

    def __init__(self, required_permissions: List[str]):
        self.required_permissions = required_permissions

    async def __call__(
        self,
        tenant_context: TenantContext = Depends(get_tenant_context)
    ) -> TenantContext:
        """检查是否具有所需的所有权限"""

        missing_permissions = []
        for permission in self.required_permissions:
            if not tenant_context.has_permission(permission):
                missing_permissions.append(permission)

        if missing_permissions:
            raise BusinessException(
                message=f"缺少权限: {', '.join(missing_permissions)}",
                error_code=ErrorCode.FORBIDDEN,
                status_code=status.HTTP_403_FORBIDDEN
            )

        return tenant_context


def require_permissions(*permissions: str):
    """创建权限检查依赖项"""
    return TenantPermissionChecker(list(permissions))


async def validate_tenant_availability(
    tenant_id: int,
    db: AsyncSession = Depends(get_async_db)
) -> Tenant:
    """验证租户是否可用"""

    query = select(Tenant).where(Tenant.id == tenant_id)
    result = await db.execute(query)
    tenant = result.scalar_one_or_none()

    if not tenant:
        raise BusinessException(
            message="租户不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    if not tenant.is_available:
        reasons = []
        if not tenant.is_active:
            reasons.append("租户未激活")
        if tenant.is_suspended:
            reasons.append("租户已暂停")
        if tenant.is_expired:
            reasons.append("租户已过期")
        if tenant.is_deleted:
            reasons.append("租户已删除")

        raise BusinessException(
            message=f"租户不可用: {', '.join(reasons)}",
            error_code=ErrorCode.FORBIDDEN,
            status_code=status.HTTP_403_FORBIDDEN
        )

    return tenant


async def check_tenant_resource_limits(
    tenant_id: int,
    resource_type: str,
    db: AsyncSession = Depends(get_async_db)
) -> bool:
    """检查租户资源限制"""

    tenant = await validate_tenant_availability(tenant_id, db)

    if resource_type == "user":
        return tenant.can_add_user()
    elif resource_type == "api_key":
        return tenant.can_add_api_key()
    else:
        return True


def create_tenant_filter(tenant_context: TenantContext):
    """创建租户过滤条件"""
    def filter_func(query, model_class):
        """为查询添加租户过滤条件"""
        if tenant_context.is_global_admin:
            # 全局管理员可以看到所有数据
            return query
        elif hasattr(model_class, 'tenant_id'):
            # 普通用户只能看到自己租户的数据
            return query.where(model_class.tenant_id == tenant_context.tenant_id)
        else:
            # 如果模型没有tenant_id字段，返回原查询
            return query

    return filter_func
