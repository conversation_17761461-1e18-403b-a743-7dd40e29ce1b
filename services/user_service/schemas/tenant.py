"""
租户相关的Pydantic模式
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator
import re


class TenantBase(BaseModel):
    """租户基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="租户名称")
    slug: str = Field(..., min_length=1, max_length=50, description="租户标识符")
    display_name: str = Field(..., min_length=1,
                              max_length=200, description="租户显示名称")
    description: Optional[str] = Field(None, description="租户描述")
    contact_email: Optional[str] = Field(None, description="联系邮箱")
    contact_phone: Optional[str] = Field(None, description="联系电话")
    contact_person: Optional[str] = Field(None, description="联系人")

    @validator('slug')
    def validate_slug(cls, v):
        """验证slug格式"""
        if not re.match(r'^[a-z0-9-]+$', v):
            raise ValueError('租户标识符只能包含小写字母、数字和连字符')
        if v.startswith('-') or v.endswith('-'):
            raise ValueError('租户标识符不能以连字符开头或结尾')
        return v

    @validator('contact_email')
    def validate_email(cls, v):
        """验证邮箱格式"""
        if v and not re.match(r'^[^@]+@[^@]+\.[^@]+$', v):
            raise ValueError('邮箱格式不正确')
        return v


class TenantCreate(TenantBase):
    """创建租户的模式"""
    is_trial: bool = True
    max_users: int = Field(10, ge=1, le=10000, description="最大用户数")
    max_api_keys: int = Field(50, ge=1, le=1000, description="最大API密钥数")
    max_storage_mb: int = Field(
        1024, ge=100, le=100000, description="最大存储空间(MB)")
    trial_days: int = Field(30, ge=1, le=365, description="试用天数")
    features_enabled: Optional[Dict[str, bool]
                               ] = Field(None, description="启用的功能")
    settings: Optional[Dict[str, Any]] = Field(None, description="租户配置")


class TenantUpdate(BaseModel):
    """更新租户的模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    display_name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    contact_email: Optional[str] = None
    contact_phone: Optional[str] = None
    contact_person: Optional[str] = None
    is_active: Optional[bool] = None
    is_suspended: Optional[bool] = None
    max_users: Optional[int] = Field(None, ge=1, le=10000)
    max_api_keys: Optional[int] = Field(None, ge=1, le=1000)
    max_storage_mb: Optional[int] = Field(None, ge=100, le=100000)
    features_enabled: Optional[Dict[str, bool]] = None
    settings: Optional[Dict[str, Any]] = None
    billing_email: Optional[str] = None
    billing_address: Optional[str] = None

    @validator('contact_email', 'billing_email')
    def validate_email(cls, v):
        """验证邮箱格式"""
        if v and not re.match(r'^[^@]+@[^@]+\.[^@]+$', v):
            raise ValueError('邮箱格式不正确')
        return v


class Tenant(TenantBase):
    """租户响应模式"""
    id: int
    is_active: bool
    is_trial: bool
    is_suspended: bool
    trial_ends_at: Optional[datetime]
    subscription_starts_at: Optional[datetime]
    subscription_ends_at: Optional[datetime]
    max_users: int
    max_api_keys: int
    max_storage_mb: int
    features_enabled: Dict[str, bool]
    settings: Dict[str, Any]
    billing_email: Optional[str]
    billing_address: Optional[str]
    user_count: int
    api_key_count: int
    last_activity_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    # 计算属性
    is_expired: bool = Field(..., description="是否已过期")
    is_available: bool = Field(..., description="是否可用")
    days_until_expiry: Optional[int] = Field(None, description="距离过期天数")

    class Config:
        from_attributes = True


class TenantSummary(BaseModel):
    """租户摘要模式（用于列表显示）"""
    id: int
    name: str
    slug: str
    display_name: str
    is_active: bool
    is_trial: bool
    is_suspended: bool
    user_count: int
    api_key_count: int
    max_users: int
    max_api_keys: int
    max_storage_mb: int
    trial_ends_at: Optional[datetime]
    subscription_starts_at: Optional[datetime]
    subscription_ends_at: Optional[datetime]
    last_activity_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    # 额外字段（为了兼容完整模型）
    features_enabled: Dict[str, bool]
    settings: Dict[str, Any]
    billing_email: Optional[str]
    billing_address: Optional[str]

    # 计算属性（可选，由API层计算）
    is_expired: Optional[bool] = None
    is_available: Optional[bool] = None

    class Config:
        from_attributes = True


class TenantList(BaseModel):
    """租户列表响应模式"""
    items: List[TenantSummary]
    total: int
    page: int
    size: int
    pages: int


class TenantRoleBase(BaseModel):
    """租户角色基础模式"""
    name: str = Field(..., min_length=1, max_length=50, description="角色名称")
    display_name: str = Field(..., min_length=1,
                              max_length=100, description="角色显示名称")
    description: Optional[str] = Field(None, description="角色描述")
    level: int = Field(0, ge=0, le=100, description="角色级别")
    permissions: List[str] = Field(default_factory=list, description="权限列表")


class TenantRoleCreate(TenantRoleBase):
    """创建租户角色的模式"""
    pass


class TenantRoleUpdate(BaseModel):
    """更新租户角色的模式"""
    display_name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    level: Optional[int] = Field(None, ge=0, le=100)
    permissions: Optional[List[str]] = None


class TenantRole(TenantRoleBase):
    """租户角色响应模式"""
    id: int
    tenant_id: int
    is_system: bool
    is_default: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class TenantStats(BaseModel):
    """租户统计信息"""
    total_tenants: int
    active_tenants: int
    trial_tenants: int
    suspended_tenants: int
    expired_tenants: int
    total_users: int
    total_api_keys: int
    avg_users_per_tenant: float
    avg_api_keys_per_tenant: float


class TenantSubscriptionUpdate(BaseModel):
    """租户订阅更新模式"""
    subscription_days: int = Field(..., ge=1, le=3650, description="订阅天数")
    max_users: Optional[int] = Field(None, ge=1, le=10000)
    max_api_keys: Optional[int] = Field(None, ge=1, le=1000)
    max_storage_mb: Optional[int] = Field(None, ge=100, le=100000)
    features_enabled: Optional[Dict[str, bool]] = None


class TenantTrialExtension(BaseModel):
    """租户试用期延长模式"""
    days: int = Field(..., ge=1, le=365, description="延长天数")
    reason: Optional[str] = Field(None, description="延长原因")


class TenantSuspension(BaseModel):
    """租户暂停模式"""
    reason: str = Field(..., min_length=1, description="暂停原因")


class TenantFeatureToggle(BaseModel):
    """租户功能开关模式"""
    feature_name: str = Field(..., description="功能名称")
    enabled: bool = Field(..., description="是否启用")


class TenantSettingUpdate(BaseModel):
    """租户设置更新模式"""
    key: str = Field(..., description="设置键")
    value: Any = Field(..., description="设置值")
