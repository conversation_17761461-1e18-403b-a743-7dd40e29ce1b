"""
用户相关的Pydantic模式
"""
from __future__ import annotations

from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, EmailStr, Field, validator


class UserBase(BaseModel):
    """用户基础模式"""
    email: EmailStr
    username: str = Field(..., min_length=3, max_length=100)
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    avatar_url: Optional[str] = Field(None, max_length=500)
    tenant_id: Optional[int] = Field(None, description="所属租户ID")


class UserCreate(UserBase):
    """创建用户的模式"""
    password: str = Field(..., min_length=8, max_length=100)
    is_active: bool = True
    is_superuser: bool = False
    is_verified: bool = False
    tenant_role_id: Optional[int] = Field(None, description="租户角色ID")

    @validator('password')
    def validate_password(cls, v):
        """验证密码强度"""
        if len(v) < 8:
            raise ValueError('密码长度至少为8个字符')
        if not any(c.isupper() for c in v):
            raise ValueError('密码必须包含至少一个大写字母')
        if not any(c.islower() for c in v):
            raise ValueError('密码必须包含至少一个小写字母')
        if not any(c.isdigit() for c in v):
            raise ValueError('密码必须包含至少一个数字')
        return v


class UserUpdate(BaseModel):
    """更新用户的模式"""
    email: Optional[EmailStr] = None
    username: Optional[str] = Field(None, min_length=3, max_length=100)
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    avatar_url: Optional[str] = Field(None, max_length=500)
    is_active: Optional[bool] = None
    is_superuser: Optional[bool] = None
    is_verified: Optional[bool] = None


class UserPasswordUpdate(BaseModel):
    """更新用户密码的模式"""
    current_password: str
    new_password: str = Field(..., min_length=8, max_length=100)

    @validator('new_password')
    def validate_password(cls, v):
        """验证密码强度"""
        if len(v) < 8:
            raise ValueError('密码长度至少为8个字符')
        if not any(c.isupper() for c in v):
            raise ValueError('密码必须包含至少一个大写字母')
        if not any(c.islower() for c in v):
            raise ValueError('密码必须包含至少一个小写字母')
        if not any(c.isdigit() for c in v):
            raise ValueError('密码必须包含至少一个数字')
        return v


class PermissionBase(BaseModel):
    """权限基础模式"""
    name: str = Field(..., max_length=100)
    display_name: str = Field(..., max_length=200)
    description: Optional[str] = None
    category: str = Field(..., max_length=50)


class Permission(PermissionBase):
    """权限模式"""
    id: int
    is_system: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class User(UserBase):
    """包含关系的用户模式"""
    id: int
    is_active: bool
    is_superuser: bool
    is_verified: bool
    tenant_role_id: Optional[int]
    last_login_at: Optional[datetime]
    email_verified_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    permissions: List[Permission] = []

    # 计算属性（可选，由业务逻辑计算）
    is_tenant_admin: Optional[bool] = Field(
        default=None, description="是否为租户管理员")
    is_global_admin: Optional[bool] = Field(
        default=None, description="是否为全局管理员")

    class Config:
        from_attributes = True

    @property
    def full_name(self) -> str:
        """获取用户全名"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.username


class UserInDB(User):
    """包含哈希密码的用户模式"""
    hashed_password: str


class APIKeyBase(BaseModel):
    """API密钥基础模式"""
    name: str = Field(..., max_length=100)
    description: Optional[str] = None
    expires_at: Optional[datetime] = None
    rate_limit_per_minute: int = Field(60, ge=1, le=1000)
    rate_limit_per_day: int = Field(10000, ge=1, le=1000000)


class APIKeyCreate(BaseModel):
    """创建API密钥的模式"""
    name: str = Field(..., max_length=100, description="API密钥名称")
    description: Optional[str] = Field(None, description="API密钥描述")
    expires_days: Optional[int] = Field(None, ge=1, le=365, description="过期天数")


class APIKeyUpdate(BaseModel):
    """更新API密钥的模式"""
    name: Optional[str] = Field(None, max_length=100, description="API密钥名称")
    description: Optional[str] = Field(None, description="API密钥描述")
    is_active: Optional[bool] = Field(None, description="是否激活")
    expires_days: Optional[int] = Field(None, ge=1, le=365, description="过期天数")


class APIKey(APIKeyBase):
    """API密钥响应模式（不包含密钥值）"""
    id: int
    is_active: bool
    last_used_at: Optional[datetime]
    user_id: int
    tenant_id: Optional[int]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class APIKeyWithKey(APIKey):
    """包含密钥值的API密钥模式（内部使用）"""
    key: str


class TemporaryAPIKeyBase(BaseModel):
    """临时API密钥基础模式"""
    name: str = Field(..., max_length=100)
    description: Optional[str] = None
    expires_at: datetime
    max_usage_count: Optional[int] = Field(None, ge=1, description="最大使用次数限制")


class TemporaryAPIKeyCreate(BaseModel):
    """创建临时API密钥的模式"""
    name: str = Field(..., max_length=100, description="临时API密钥名称")
    description: Optional[str] = Field(None, description="临时API密钥描述")
    expires_hours: int = Field(24, ge=1, le=8760, description="过期小时数（1小时到1年）")
    max_usage_count: Optional[int] = Field(None, ge=1, description="最大使用次数限制")


class TemporaryAPIKeyUpdate(BaseModel):
    """更新临时API密钥的模式"""
    name: Optional[str] = Field(None, max_length=100, description="临时API密钥名称")
    description: Optional[str] = Field(None, description="临时API密钥描述")
    is_active: Optional[bool] = Field(None, description="是否激活")
    expires_hours: Optional[int] = Field(None, ge=1, le=8760, description="过期小时数")
    max_usage_count: Optional[int] = Field(None, ge=1, description="最大使用次数限制")


class TemporaryAPIKey(TemporaryAPIKeyBase):
    """临时API密钥响应模式（不包含密钥值）"""
    id: int
    temp_key: str  # 在响应中显示临时密钥值
    real_api_key_id: int
    is_active: bool
    last_used_at: Optional[datetime]
    usage_count: int
    user_id: int
    tenant_id: Optional[int]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class TemporaryAPIKeyWithKey(TemporaryAPIKey):
    """包含密钥值的临时API密钥模式（创建时返回）"""
    temp_key: str


class UserSessionBase(BaseModel):
    """用户会话基础模式"""
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None


class UserSession(UserSessionBase):
    """用户会话模式"""
    id: int
    session_id: str
    user_id: int
    is_active: bool
    expires_at: datetime
    last_activity_at: datetime
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class UserList(BaseModel):
    """用户列表响应模式"""
    items: List[User]
    total: int
    page: int
    size: int
    pages: int


class PermissionList(BaseModel):
    """权限列表响应模式"""
    items: List[Permission]
    total: int
    page: int
    size: int
    pages: int


class APIKeyList(BaseModel):
    """API密钥列表响应模式"""
    items: List[APIKey]
    total: int
    page: int
    size: int
    pages: int
