"""
认证相关的Pydantic模式
"""
from typing import Optional

from pydantic import BaseModel, EmailStr, Field


class LoginRequest(BaseModel):
    """登录请求模式"""
    email: EmailStr
    password: str


class LoginResponse(BaseModel):
    """登录响应模式"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int  # 过期时间（秒）


class RefreshTokenRequest(BaseModel):
    """刷新令牌请求模式"""
    refresh_token: str


class RefreshTokenResponse(BaseModel):
    """刷新令牌响应模式"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int  # 过期时间（秒）


class RegisterRequest(BaseModel):
    """用户注册请求模式"""
    email: EmailStr
    username: str = Field(..., min_length=3, max_length=100)
    password: str = Field(..., min_length=8, max_length=100)
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)


class RegisterResponse(BaseModel):
    """用户注册响应模式"""
    id: int
    email: str
    username: str
    first_name: Optional[str]
    last_name: Optional[str]
    phone: Optional[str]
    is_active: bool
    is_superuser: bool
    is_verified: bool
    created_at: str
    updated_at: str


class PasswordResetRequest(BaseModel):
    """密码重置请求模式"""
    email: EmailStr


class PasswordResetResponse(BaseModel):
    """密码重置响应模式"""
    message: str


class PasswordResetConfirm(BaseModel):
    """密码重置确认模式"""
    token: str
    new_password: str = Field(..., min_length=8, max_length=100)


class EmailVerificationRequest(BaseModel):
    """邮箱验证请求模式"""
    email: EmailStr


class EmailVerificationResponse(BaseModel):
    """邮箱验证响应模式"""
    message: str


class EmailVerificationConfirm(BaseModel):
    """邮箱验证确认模式"""
    token: str


class LogoutRequest(BaseModel):
    """登出请求模式"""
    refresh_token: Optional[str] = None


class LogoutResponse(BaseModel):
    """登出响应模式"""
    message: str


class TokenPayload(BaseModel):
    """令牌载荷模式"""
    sub: str  # 用户ID
    exp: int  # 过期时间戳
    type: str  # 令牌类型（access/refresh）
    iat: Optional[int] = None  # 签发时间
    jti: Optional[str] = None  # JWT ID


class UserProfile(BaseModel):
    """用户资料模式"""
    id: int
    email: str
    username: str
    first_name: Optional[str]
    last_name: Optional[str]
    phone: Optional[str]
    avatar_url: Optional[str]
    is_active: bool
    is_superuser: bool
    is_verified: bool
    last_login_at: Optional[str]
    email_verified_at: Optional[str]
    created_at: Optional[str]

    class Config:
        from_attributes = True
