"""
用户认证API端点 - 统一响应格式
"""
from datetime import datetime, timedelta, timezone

from fastapi import APIRouter, Depends, Request, status
from fastapi.security import HTTPBearer
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from shared.database import get_async_db
from shared.utils.auth import get_current_user_from_token
from shared.models.response import (
    SuccessResponse, create_success_response, ErrorCode, SuccessMessage
)
from shared.middleware.request_id import get_request_id
from shared.utils.exception_handlers import BusinessException
from shared.utils.security import (
    verify_password,
    get_password_hash,
    create_access_token,
    create_refresh_token,
    verify_token
)
from ..config import settings
from ..models.user import User, UserSession
from ..schemas.auth import (
    LoginRequest,
    LoginResponse,
    RegisterRequest,
    RegisterResponse,
    RefreshTokenRequest,
    RefreshTokenResponse,
    UserProfile,
    LogoutRequest,
    LogoutResponse
)

router = APIRouter()
security = HTTPBearer()


@router.post("/login", response_model=SuccessResponse[LoginResponse])
async def login(
        request: Request,
        login_data: LoginRequest,
        db: AsyncSession = Depends(get_async_db)
):
    """用户登录"""
    # 根据邮箱查找用户
    result = await db.execute(
        select(User).where(User.email == login_data.email)
    )
    user = result.scalar_one_or_none()

    # 验证用户存在性和密码正确性
    if not user or not verify_password(login_data.password, user.hashed_password):
        raise BusinessException(
            message="邮箱或密码错误",
            error_code=ErrorCode.UNAUTHORIZED,
            status_code=status.HTTP_401_UNAUTHORIZED
        )

    # 检查用户是否激活
    if not user.is_active:
        raise BusinessException(
            message="用户未激活",
            error_code=ErrorCode.FORBIDDEN,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 创建访问令牌和刷新令牌
    access_token = create_access_token(data={"sub": str(user.id)})
    refresh_token = create_refresh_token(data={"sub": str(user.id)})

    # 创建用户会话记录，用于管理登录状态
    now = datetime.now(timezone.utc)
    session = UserSession(
        session_id=f"session_{user.id}_{now.timestamp()}",
        refresh_token=refresh_token,
        user_id=user.id,
        expires_at=now + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS),
        last_activity_at=now
    )
    db.add(session)

    # 更新用户最后登录时间
    user.last_login_at = now

    try:
        await db.commit()
    except Exception as e:
        raise BusinessException(
            message="登录失败",
            error_code=ErrorCode.OPERATION_FAILED,
            details={"original_error": str(e)}
        )

    # 创建登录响应数据
    login_response = LoginResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=login_response,
        msg=SuccessMessage.LOGIN_SUCCESS,
        request_id=request_id
    )


@router.post("/register", response_model=SuccessResponse[RegisterResponse], status_code=status.HTTP_201_CREATED)
async def register(
        request: Request,
        register_data: RegisterRequest,
        db: AsyncSession = Depends(get_async_db)
):
    """用户注册"""
    # 检查用户是否已存在（邮箱或用户名重复）
    result = await db.execute(
        select(User).where(
            (User.email == register_data.email) |
            (User.username == register_data.username)
        )
    )
    existing_user = result.scalar_one_or_none()

    # 如果用户已存在，返回相应错误信息
    if existing_user:
        if existing_user.email == register_data.email:
            raise BusinessException(
                message="邮箱已被注册",
                error_code=ErrorCode.INVALID_REQUEST,
                status_code=status.HTTP_400_BAD_REQUEST
            )
        else:
            raise BusinessException(
                message="用户名已被占用",
                error_code=ErrorCode.INVALID_REQUEST,
                status_code=status.HTTP_400_BAD_REQUEST
            )

    # 创建新用户，密码进行哈希加密
    hashed_password = get_password_hash(register_data.password)
    user = User(
        email=register_data.email,
        username=register_data.username,
        hashed_password=hashed_password,
        first_name=register_data.first_name,
        last_name=register_data.last_name,
        phone=register_data.phone,
        is_active=True,
        is_verified=False  # 新用户默认未验证邮箱
    )

    db.add(user)
    try:
        await db.commit()
        await db.refresh(user)
    except Exception as e:
        raise BusinessException(
            message="注册失败",
            error_code=ErrorCode.OPERATION_FAILED,
            details={"original_error": str(e)}
        )

    # 创建注册响应数据
    register_response = RegisterResponse(
        id=user.id,
        email=user.email,
        username=user.username,
        first_name=user.first_name,
        last_name=user.last_name,
        phone=user.phone,
        is_active=user.is_active,
        is_superuser=user.is_superuser,
        is_verified=user.is_verified,
        created_at=user.created_at.isoformat(),
        updated_at=user.updated_at.isoformat()
    )

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=register_response,
        msg=SuccessMessage.CREATED,
        request_id=request_id
    )


@router.post("/refresh", response_model=SuccessResponse[RefreshTokenResponse])
async def refresh_token(
        request: Request,
        refresh_data: RefreshTokenRequest,
        db: AsyncSession = Depends(get_async_db)
):
    """刷新访问令牌"""
    # 验证刷新令牌的有效性
    payload = verify_token(refresh_data.refresh_token, "refresh")
    if not payload:
        raise BusinessException(
            message="无效的刷新令牌",
            error_code=ErrorCode.INVALID_REQUEST,
            status_code=status.HTTP_401_UNAUTHORIZED
        )

    # 从令牌载荷中提取用户ID
    user_id = payload.get("sub")
    if not user_id:
        raise BusinessException(
            message="无效的令牌载荷",
            error_code=ErrorCode.INVALID_REQUEST,
            status_code=status.HTTP_401_UNAUTHORIZED
        )

    # 检查会话是否存在且有效
    result = await db.execute(
        select(UserSession).where(
            UserSession.refresh_token == refresh_data.refresh_token,
            UserSession.is_active == True
        )
    )
    session = result.scalar_one_or_none()

    # 验证会话状态和过期时间
    if not session or session.is_expired():
        raise BusinessException(
            message="无效或已过期的会话",
            error_code=ErrorCode.INVALID_REQUEST,
            status_code=status.HTTP_401_UNAUTHORIZED
        )

    # 获取用户信息并验证用户状态
    result = await db.execute(select(User).where(User.id == int(user_id)))
    user = result.scalar_one_or_none()

    if not user or not user.is_active:
        raise BusinessException(
            message="用户不存在或未激活",
            error_code=ErrorCode.INVALID_REQUEST,
            status_code=status.HTTP_401_UNAUTHORIZED
        )

    # 创建新的访问令牌
    access_token = create_access_token(data={"sub": str(user.id)})

    # 更新会话活动时间
    session.last_activity_at = datetime.now(timezone.utc)
    await db.commit()

    # 创建刷新令牌响应数据
    refresh_response = RefreshTokenResponse(
        access_token=access_token,
        expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60

    )

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=refresh_response,
        msg=SuccessMessage.OPERATION_SUCCESS,
        request_id=request_id
    )


@router.post("/logout", response_model=SuccessResponse[LogoutResponse])
async def logout(
        request: Request,
        logout_data: LogoutRequest,
        current_user: User = Depends(get_current_user_from_token),
        db: AsyncSession = Depends(get_async_db)
):
    """用户登出"""
    if logout_data.refresh_token:
        # 停用指定的会话
        result = await db.execute(
            select(UserSession).where(
                UserSession.refresh_token == logout_data.refresh_token
            )
        )
        session = result.scalar_one_or_none()

        if session:
            session.is_active = False
            await db.commit()

    # 创建登出响应数据
    logout_response = LogoutResponse(message="登出成功")

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=logout_response,
        msg=SuccessMessage.LOGOUT_SUCCESS,
        request_id=request_id
    )


@router.get("/me", response_model=SuccessResponse[UserProfile])
async def get_current_user_profile(
        request: Request,
        current_user: User = Depends(get_current_user_from_token)
):
    """获取当前用户资料"""
    # 兼容字典和对象格式的用户数据
    if isinstance(current_user, dict):
        # 创建用户资料响应数据
        user_profile = UserProfile(
            id=current_user["id"],
            email=current_user["email"],
            username=current_user["username"],
            first_name=current_user.get("first_name"),
            last_name=current_user.get("last_name"),
            phone=current_user.get("phone"),
            avatar_url=current_user.get("avatar_url"),
            is_active=current_user["is_active"],
            is_superuser=current_user["is_superuser"],
            is_verified=current_user["is_verified"],
            last_login_at=current_user.get("last_login_at"),
            email_verified_at=current_user.get("email_verified_at"),
            created_at=current_user.get("created_at")
        )
    else:
        # 创建用户资料响应数据
        user_profile = UserProfile(
            id=current_user.id,
            email=current_user.email,
            username=current_user.username,
            first_name=current_user.first_name,
            last_name=current_user.last_name,
            phone=current_user.phone,
            avatar_url=current_user.avatar_url,
            is_active=current_user.is_active,
            is_superuser=current_user.is_superuser,
            is_verified=current_user.is_verified,
            last_login_at=current_user.last_login_at.isoformat(
            ) if current_user.last_login_at else None,
            email_verified_at=current_user.email_verified_at.isoformat(
            ) if current_user.email_verified_at else None,
            created_at=current_user.created_at.isoformat() if current_user.created_at else None,
        )

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=user_profile,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )
