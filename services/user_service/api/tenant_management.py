"""
租户管理扩展API端点 - 统计、功能管理等
"""
from datetime import datetime, timedelta, timezone
from typing import Optional

from fastapi import APIRouter, Depends, Request, status
from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession

from shared.database import get_async_db
from shared.models.response import (
    SuccessResponse, PaginatedResponse, create_success_response,
    create_paginated_response, ErrorCode, SuccessMessage
)
from shared.middleware.request_id import get_request_id
from shared.utils.exception_handlers import BusinessException
from shared.utils.auth import get_current_user_from_token
from ..models.tenant import Tenant, TenantRole
from ..models.user import User, APIKey
from ..schemas.tenant import (
    TenantStats, TenantTrialExtension, TenantSubscriptionUpdate,
    TenantFeatureToggle, TenantSettingUpdate
)

router = APIRouter()


async def get_current_superuser(
    request: Request,
    current_user: dict = Depends(get_current_user_from_token)
) -> dict:
    """获取当前超级用户"""
    if not current_user.get("is_superuser", False):
        raise BusinessException(
            message="需要超级用户权限",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_403_FORBIDDEN
        )
    return current_user


@router.get("/stats", response_model=TenantStats)
async def get_tenant_stats(
    request: Request,
    current_user: dict = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """获取租户统计信息（仅超级用户）"""

    # 租户统计
    total_tenants_query = select(func.count(Tenant.id)).where(
        Tenant.is_deleted == False)
    active_tenants_query = select(func.count(Tenant.id)).where(
        and_(Tenant.is_deleted == False, Tenant.is_active == True)
    )
    trial_tenants_query = select(func.count(Tenant.id)).where(
        and_(Tenant.is_deleted == False, Tenant.is_trial == True)
    )
    suspended_tenants_query = select(func.count(Tenant.id)).where(
        and_(Tenant.is_deleted == False, Tenant.is_suspended == True)
    )

    # 过期租户统计
    now = datetime.now(timezone.utc)
    expired_tenants_query = select(func.count(Tenant.id)).where(
        and_(
            Tenant.is_deleted == False,
            or_(
                and_(Tenant.is_trial == True, Tenant.trial_ends_at < now),
                and_(Tenant.is_trial == False, Tenant.subscription_ends_at < now)
            )
        )
    )

    # 用户和API密钥统计
    total_users_query = select(func.count(User.id)).where(
        User.is_deleted == False)
    total_api_keys_query = select(func.count(
        APIKey.id)).where(APIKey.is_deleted == False)

    # 执行查询
    total_tenants = (await db.execute(total_tenants_query)).scalar()
    active_tenants = (await db.execute(active_tenants_query)).scalar()
    trial_tenants = (await db.execute(trial_tenants_query)).scalar()
    suspended_tenants = (await db.execute(suspended_tenants_query)).scalar()
    expired_tenants = (await db.execute(expired_tenants_query)).scalar()
    total_users = (await db.execute(total_users_query)).scalar()
    total_api_keys = (await db.execute(total_api_keys_query)).scalar()

    # 计算平均值
    avg_users_per_tenant = total_users / total_tenants if total_tenants > 0 else 0
    avg_api_keys_per_tenant = total_api_keys / \
        total_tenants if total_tenants > 0 else 0

    return TenantStats(
        total_tenants=total_tenants,
        active_tenants=active_tenants,
        trial_tenants=trial_tenants,
        suspended_tenants=suspended_tenants,
        expired_tenants=expired_tenants,
        total_users=total_users,
        total_api_keys=total_api_keys,
        avg_users_per_tenant=round(avg_users_per_tenant, 2),
        avg_api_keys_per_tenant=round(avg_api_keys_per_tenant, 2)
    )


@router.post("/{tenant_id}/extend-trial")
async def extend_trial(
    request: Request,
    tenant_id: int,
    extension_data: TenantTrialExtension,
    current_user: dict = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """延长租户试用期（仅超级用户）"""

    # 获取租户
    query = select(Tenant).where(
        and_(
            Tenant.id == tenant_id,
            Tenant.is_deleted == False
        )
    )
    result = await db.execute(query)
    tenant = result.scalar_one_or_none()

    if not tenant:
        raise BusinessException(
            message="租户不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    if not tenant.is_trial:
        raise BusinessException(
            message="租户不是试用版",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 延长试用期
    old_trial_end = tenant.trial_ends_at
    tenant.extend_trial(extension_data.days)
    tenant.updated_by_id = current_user.get("id")

    # 记录延长原因
    if extension_data.reason:
        tenant.set_setting('trial_extension_reason', extension_data.reason)
        tenant.set_setting('trial_extension_date',
                           datetime.now(timezone.utc).isoformat())

    await db.commit()

    return {
        "message": f"试用期已延长 {extension_data.days} 天",
        "old_trial_end": old_trial_end.isoformat() if old_trial_end else None,
        "new_trial_end": tenant.trial_ends_at.isoformat() if tenant.trial_ends_at else None
    }


@router.post("/{tenant_id}/upgrade")
async def upgrade_tenant(
    request: Request,
    tenant_id: int,
    subscription_data: TenantSubscriptionUpdate,
    current_user: dict = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """升级租户为付费版（仅超级用户）"""

    # 获取租户
    query = select(Tenant).where(
        and_(
            Tenant.id == tenant_id,
            Tenant.is_deleted == False
        )
    )
    result = await db.execute(query)
    tenant = result.scalar_one_or_none()

    if not tenant:
        raise BusinessException(
            message="租户不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 升级为付费版
    tenant.upgrade_to_paid(subscription_data.subscription_days)

    # 更新资源限制
    if subscription_data.max_users:
        tenant.max_users = subscription_data.max_users
    if subscription_data.max_api_keys:
        tenant.max_api_keys = subscription_data.max_api_keys
    if subscription_data.max_storage_mb:
        tenant.max_storage_mb = subscription_data.max_storage_mb

    # 更新功能配置
    if subscription_data.features_enabled:
        tenant.features_enabled.update(subscription_data.features_enabled)

    tenant.updated_by_id = current_user.get("id")

    await db.commit()

    return {
        "message": "租户已升级为付费版",
        "subscription_ends_at": tenant.subscription_ends_at.isoformat() if tenant.subscription_ends_at else None
    }


@router.post("/{tenant_id}/toggle-feature")
async def toggle_feature(
    request: Request,
    tenant_id: int,
    feature_data: TenantFeatureToggle,
    current_user: dict = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """切换租户功能开关（仅超级用户）"""

    # 获取租户
    query = select(Tenant).where(
        and_(
            Tenant.id == tenant_id,
            Tenant.is_deleted == False
        )
    )
    result = await db.execute(query)
    tenant = result.scalar_one_or_none()

    if not tenant:
        raise BusinessException(
            message="租户不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 更新功能开关
    if tenant.features_enabled is None:
        tenant.features_enabled = {}

    tenant.features_enabled[feature_data.feature_name] = feature_data.enabled
    tenant.updated_by_id = current_user.get("id")

    await db.commit()

    return {
        "message": f"功能 {feature_data.feature_name} 已{'启用' if feature_data.enabled else '禁用'}",
        "feature_name": feature_data.feature_name,
        "enabled": feature_data.enabled
    }


@router.post("/{tenant_id}/update-setting")
async def update_setting(
    request: Request,
    tenant_id: int,
    setting_data: TenantSettingUpdate,
    current_user: dict = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """更新租户设置（仅超级用户）"""

    # 获取租户
    query = select(Tenant).where(
        and_(
            Tenant.id == tenant_id,
            Tenant.is_deleted == False
        )
    )
    result = await db.execute(query)
    tenant = result.scalar_one_or_none()

    if not tenant:
        raise BusinessException(
            message="租户不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 更新设置
    tenant.set_setting(setting_data.key, setting_data.value)
    tenant.updated_by_id = current_user.get("id")

    await db.commit()

    return {
        "message": f"设置 {setting_data.key} 已更新",
        "key": setting_data.key,
        "value": setting_data.value
    }


@router.get("/{tenant_id}/users-count")
async def get_tenant_users_count(
    request: Request,
    tenant_id: int,
    current_user: dict = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """获取租户用户数量（仅超级用户）"""

    # 检查租户是否存在
    tenant_query = select(Tenant).where(
        and_(
            Tenant.id == tenant_id,
            Tenant.is_deleted == False
        )
    )
    tenant_result = await db.execute(tenant_query)
    tenant = tenant_result.scalar_one_or_none()

    if not tenant:
        raise BusinessException(
            message="租户不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 统计用户数量
    total_users_query = select(func.count(User.id)).where(
        and_(
            User.tenant_id == tenant_id,
            User.is_deleted == False
        )
    )
    active_users_query = select(func.count(User.id)).where(
        and_(
            User.tenant_id == tenant_id,
            User.is_deleted == False,
            User.is_active == True
        )
    )

    total_users = (await db.execute(total_users_query)).scalar()
    active_users = (await db.execute(active_users_query)).scalar()

    return {
        "tenant_id": tenant_id,
        "total_users": total_users,
        "active_users": active_users,
        "max_users": tenant.max_users,
        "can_add_users": tenant.can_add_user()
    }
