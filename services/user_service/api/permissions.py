"""
权限管理API端点
"""
from typing import List, Optional

from fastapi import APIRouter, Depends, Query, Request, status
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from shared.database import get_async_db
from shared.models.response import (
    SuccessResponse, PaginatedResponse, create_success_response,
    create_paginated_response, ErrorCode, SuccessMessage
)
from shared.middleware.request_id import get_request_id
from shared.utils.exception_handlers import BusinessException
from shared.utils.auth import get_current_superuser, get_current_active_user
from ..models.permission import Permission
from ..models.user import User
from ..schemas.user import (
    Permission as PermissionSchema,
    PermissionList
)

router = APIRouter()


@router.get("/", response_model=PaginatedResponse[PermissionSchema])
async def get_permissions(
    request: Request,
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(50, ge=1, le=200, description="每页数量"),
    category: Optional[str] = Query(None, description="权限分类"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取权限列表"""

    # 构建查询
    query = select(Permission)

    # 添加分类过滤
    if category:
        query = query.where(Permission.category == category)

    # 添加搜索条件
    if search:
        search_pattern = f"%{search}%"
        query = query.where(
            (Permission.name.ilike(search_pattern)) |
            (Permission.display_name.ilike(search_pattern)) |
            (Permission.description.ilike(search_pattern))
        )

    # 获取总数
    count_query = select(func.count(Permission.id))
    if category:
        count_query = count_query.where(Permission.category == category)
    if search:
        search_pattern = f"%{search}%"
        count_query = count_query.where(
            (Permission.name.ilike(search_pattern)) |
            (Permission.display_name.ilike(search_pattern)) |
            (Permission.description.ilike(search_pattern))
        )

    total_result = await db.execute(count_query)
    total = total_result.scalar()

    # 分页查询
    offset = (page - 1) * size
    query = query.offset(offset).limit(size).order_by(
        Permission.category, Permission.name)

    result = await db.execute(query)
    permissions = result.scalars().all()

    # 转换为Pydantic模型
    permission_items = [PermissionSchema.model_validate(
        perm) for perm in permissions]

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一分页响应格式
    return create_paginated_response(
        items=permission_items,
        total=total,
        page=page,
        size=size,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.get("/{permission_id}", response_model=SuccessResponse[PermissionSchema])
async def get_permission(
    request: Request,
    permission_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取权限详情"""

    query = select(Permission).where(Permission.id == permission_id)
    result = await db.execute(query)
    permission = result.scalar_one_or_none()

    if not permission:
        raise BusinessException(
            message="权限不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 转换为Pydantic模型
    permission_data = PermissionSchema.model_validate(permission)
    
    # 获取请求ID
    request_id = get_request_id(request)
    
    # 返回统一成功响应格式
    return create_success_response(
        data=permission_data,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.get("/categories")
async def get_permission_categories(
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取权限分类列表"""

    query = select(Permission.category).distinct().order_by(
        Permission.category)
    result = await db.execute(query)
    categories = result.scalars().all()

    return {"categories": categories}


@router.post("/", response_model=SuccessResponse[PermissionSchema])
async def create_permission(
    request: Request,
    permission_data: dict,
    current_user: User = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """创建权限（需要管理员权限）"""

    # 检查权限名称唯一性
    name_query = select(Permission).where(
        Permission.name == permission_data["name"])
    name_result = await db.execute(name_query)
    if name_result.scalar_one_or_none():
        raise BusinessException(
            message="权限名称已存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 创建权限
    permission = Permission(**permission_data)
    db.add(permission)
    await db.commit()
    await db.refresh(permission)

    # 转换为Pydantic模型
    permission_data = PermissionSchema.model_validate(permission)
    
    # 获取请求ID
    request_id = get_request_id(request)
    
    # 返回统一成功响应格式
    return create_success_response(
        data=permission_data,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.put("/{permission_id}", response_model=SuccessResponse[PermissionSchema])
async def update_permission(
    request: Request,
    permission_id: int,
    permission_data: dict,
    current_user: User = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """更新权限（需要管理员权限）"""

    # 查询权限
    query = select(Permission).where(Permission.id == permission_id)
    result = await db.execute(query)
    permission = result.scalar_one_or_none()

    if not permission:
        raise BusinessException(
            message="权限不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 系统权限不能修改
    if permission.is_system:
        raise BusinessException(
            message="系统权限不能修改",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 检查权限名称唯一性
    if "name" in permission_data and permission_data["name"] != permission.name:
        name_query = select(Permission).where(
            Permission.name == permission_data["name"],
            Permission.id != permission_id
        )
        name_result = await db.execute(name_query)
        if name_result.scalar_one_or_none():
            raise BusinessException(
                message="权限名称已存在",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_400_BAD_REQUEST
            )

    # 更新权限
    for field, value in permission_data.items():
        if hasattr(permission, field):
            setattr(permission, field, value)

    await db.commit()
    await db.refresh(permission)

    # 转换为Pydantic模型
    permission_data = PermissionSchema.model_validate(permission)
    
    # 获取请求ID
    request_id = get_request_id(request)
    
    # 返回统一成功响应格式
    return create_success_response(
        data=permission_data,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.delete("/{permission_id}")
async def delete_permission(
    request: Request,
    permission_id: int,
    current_user: User = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """删除权限（需要管理员权限）"""

    # 查询权限
    query = select(Permission).where(Permission.id == permission_id)
    result = await db.execute(query)
    permission = result.scalar_one_or_none()

    if not permission:
        raise BusinessException(
            message="权限不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 系统权限不能删除
    if permission.is_system:
        raise BusinessException(
            message="系统权限不能删除",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 删除权限
    await db.delete(permission)
    await db.commit()

    # 获取请求ID
    request_id = get_request_id(request)
    
    # 返回统一成功响应格式
    return create_success_response(
        data=None,
        msg=SuccessMessage.DELETED,
        request_id=request_id
    )


@router.get("/{user_id}/permissions", response_model=List[PermissionSchema])
async def get_user_permissions(
    request: Request,
    user_id: int,
    current_user=Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取用户权限列表"""

    # 检查权限：只能查看自己的权限，或者管理员可以查看所有用户权限
    if current_user.get('id') != user_id and not current_user.get('is_superuser', False):
        raise BusinessException(
            message="没有权限查看此用户权限",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_403_FORBIDDEN
        )

    # 查询用户及其权限
    query = select(User).options(selectinload(
        User.permissions)).where(User.id == user_id)
    result = await db.execute(query)
    user = result.scalar_one_or_none()

    if not user:
        raise BusinessException(
            message="用户不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 转换为Pydantic模型列表
    permission_list = [PermissionSchema.model_validate(perm) for perm in user.permissions]
    
    # 获取请求ID
    request_id = get_request_id(request)
    
    # 返回统一成功响应格式
    return create_success_response(
        data=permission_list,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.post("/users/{user_id}/permissions")
async def assign_user_permissions(
    request: Request,
    user_id: int,
    request_data: dict,
    current_user: User = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """分配用户权限（需要管理员权限）"""

    # 查询用户
    user_query = select(User).options(selectinload(
        User.permissions)).where(User.id == user_id)
    user_result = await db.execute(user_query)
    user = user_result.scalar_one_or_none()

    if not user:
        raise BusinessException(
            message="用户不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 获取权限ID列表
    permission_ids = request_data.get("permission_ids", [])

    # 查询权限
    perm_query = select(Permission).where(Permission.id.in_(permission_ids))
    perm_result = await db.execute(perm_query)
    permissions = perm_result.scalars().all()

    if len(permissions) != len(permission_ids):
        raise BusinessException(
            message="部分权限不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 分配权限
    user.permissions = permissions
    await db.commit()

    return {
        "message": "权限分配成功",
        "user_id": user_id,
        "permission_count": len(permissions)
    }


@router.delete("/{user_id}/permissions/{permission_id}")
async def remove_user_permission(
    request: Request,
    user_id: int,
    permission_id: int,
    current_user: User = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """移除用户权限（需要管理员权限）"""

    # 查询用户
    user_query = select(User).options(selectinload(
        User.permissions)).where(User.id == user_id)
    user_result = await db.execute(user_query)
    user = user_result.scalar_one_or_none()

    if not user:
        raise BusinessException(
            message="用户不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 查询权限
    perm_query = select(Permission).where(Permission.id == permission_id)
    perm_result = await db.execute(perm_query)
    permission = perm_result.scalar_one_or_none()

    if not permission:
        raise BusinessException(
            message="权限不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 移除权限
    if permission in user.permissions:
        user.permissions.remove(permission)
        await db.commit()
        # 获取请求ID
        request_id = get_request_id(request)
        
        # 返回统一成功响应格式
        return create_success_response(
            data=None,
            msg=SuccessMessage.OPERATION_SUCCESS,
            request_id=request_id
        )
    else:
        raise BusinessException(
            message="用户没有此权限",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_400_BAD_REQUEST
        )
