"""
临时API密钥管理接口
"""
from datetime import datetime, timezone, timedelta
from typing import List

from fastapi import APIRouter, Depends, Request, status
from sqlalchemy import and_, select, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from shared.database import get_async_db
from shared.models.response import ErrorCode
from shared.utils.exception_handlers import BusinessException
from shared.utils.security import generate_temporary_api_key
from shared.utils.auth import get_current_active_user_from_api_key

from ..models.user import APIKey, TemporaryAPIKey
from ..schemas.user import (
    TemporaryAPIKeyCreate,
    TemporaryAPIKeyUpdate,
    TemporaryAPIKey as TemporaryAPIKeySchema
)

router = APIRouter(prefix="/temporary-api-keys", tags=["临时API密钥管理"])


@router.get("/", response_model=List[TemporaryAPIKeySchema])
async def list_temporary_api_keys(
    request: Request,
    skip: int = 0,
    limit: int = 100,
    current_user: dict = Depends(get_current_active_user_from_api_key),
    db: AsyncSession = Depends(get_async_db)
):
    """获取指定真实API密钥下的所有临时API密钥列表"""

    # 从当前用户信息中获取真实API密钥
    real_api_key_value = current_user.get('current_api_key')
    if not real_api_key_value:
        raise BusinessException(
            message="无法获取当前API密钥",
            error_code=ErrorCode.BUSINESS_ERROR,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 首先查找真实API密钥记录
    user_id = current_user.get('id')
    tenant_id = current_user.get('tenant_id')

    real_api_key_query = select(APIKey).where(
        and_(
            APIKey.key == real_api_key_value,
            APIKey.user_id == user_id,
            APIKey.is_active == True,
            APIKey.is_deleted == False
        )
    )

    # 处理多租户情况
    if tenant_id:
        real_api_key_query = real_api_key_query.where(
            APIKey.tenant_id == tenant_id
        )
    else:
        real_api_key_query = real_api_key_query.where(
            APIKey.tenant_id.is_(None)
        )

    result = await db.execute(real_api_key_query)
    real_api_key = result.scalar_one_or_none()

    if not real_api_key:
        raise BusinessException(
            message="无法找到对应的API密钥记录",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 构建查询条件 - 只查询该真实API密钥下的临时密钥
    query = select(TemporaryAPIKey).where(
        and_(
            TemporaryAPIKey.real_api_key_id == real_api_key.id,
            TemporaryAPIKey.is_deleted == False
        )
    )

    query = query.options(selectinload(TemporaryAPIKey.real_api_key))
    query = query.offset(skip).limit(limit)
    query = query.order_by(TemporaryAPIKey.created_at.desc())

    result = await db.execute(query)
    temporary_keys = result.scalars().all()

    return temporary_keys


@router.post("/", response_model=dict)
async def create_temporary_api_key(
    request: Request,
    temp_key_data: TemporaryAPIKeyCreate,
    current_user: dict = Depends(get_current_active_user_from_api_key),
    db: AsyncSession = Depends(get_async_db)
):
    """创建临时API密钥（使用请求头中的API密钥作为真实密钥）"""

    # 从当前用户信息中获取真实API密钥
    real_api_key_value = current_user.get('current_api_key')
    if not real_api_key_value:
        raise BusinessException(
            message="无法获取当前API密钥",
            error_code=ErrorCode.BUSINESS_ERROR,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 通过密钥字符串查找真实API密钥记录
    real_api_key_query = select(APIKey).where(
        and_(
            APIKey.key == real_api_key_value,
            APIKey.user_id == current_user.get('id'),
            APIKey.is_active == True,
            APIKey.is_deleted == False
        )
    )

    # 处理多租户情况
    tenant_id = current_user.get('tenant_id')
    if tenant_id:
        real_api_key_query = real_api_key_query.where(
            APIKey.tenant_id == tenant_id
        )
    else:
        real_api_key_query = real_api_key_query.where(
            APIKey.tenant_id.is_(None)
        )

    result = await db.execute(real_api_key_query)
    real_api_key = result.scalar_one_or_none()

    if not real_api_key:
        raise BusinessException(
            message="无法找到对应的API密钥记录",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 检查真实API密钥是否过期
    if real_api_key.is_expired():
        raise BusinessException(
            message="关联的真实API密钥已过期",
            error_code=ErrorCode.BUSINESS_ERROR,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 检查租户内临时API密钥名称唯一性
    user_id = current_user.get('id')
    if tenant_id:
        name_query = select(TemporaryAPIKey).where(
            and_(
                TemporaryAPIKey.tenant_id == tenant_id,
                TemporaryAPIKey.user_id == user_id,
                TemporaryAPIKey.name == temp_key_data.name,
                TemporaryAPIKey.is_deleted == False
            )
        )
    else:
        # 全局用户的临时API密钥名称唯一性检查
        name_query = select(TemporaryAPIKey).where(
            and_(
                TemporaryAPIKey.tenant_id.is_(None),
                TemporaryAPIKey.user_id == user_id,
                TemporaryAPIKey.name == temp_key_data.name,
                TemporaryAPIKey.is_deleted == False
            )
        )

    existing_result = await db.execute(name_query)
    if existing_result.scalar_one_or_none():
        raise BusinessException(
            message="临时API密钥名称已存在",
            error_code=ErrorCode.BUSINESS_ERROR,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 检查临时API密钥数量限制
    count_query = select(func.count(TemporaryAPIKey.id)).where(
        and_(
            TemporaryAPIKey.user_id == user_id,
            TemporaryAPIKey.is_active == True,
            TemporaryAPIKey.is_deleted == False
        )
    )

    if tenant_id:
        count_query = count_query.where(
            TemporaryAPIKey.tenant_id == tenant_id
        )
    else:
        count_query = count_query.where(
            TemporaryAPIKey.tenant_id.is_(None)
        )

    count_result = await db.execute(count_query)
    active_count = count_result.scalar()

    # 临时API密钥数量限制（可配置）
    max_temp_keys = 50  # 每个用户最多50个临时密钥

    if active_count >= max_temp_keys:
        raise BusinessException(
            message=f"临时API密钥数量已达上限（{max_temp_keys}个）",
            error_code=ErrorCode.INTERNAL_ERROR,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

    # 生成临时API密钥
    temp_key_value = generate_temporary_api_key()

    # 设置过期时间
    expires_at = datetime.now(timezone.utc) + timedelta(hours=temp_key_data.expires_hours)

    # 创建临时API密钥记录
    temporary_api_key = TemporaryAPIKey(
        name=temp_key_data.name,
        temp_key=temp_key_value,
        real_api_key_id=real_api_key.id,  # 使用找到的真实API密钥的ID
        user_id=user_id,
        tenant_id=tenant_id,
        expires_at=expires_at,
        description=temp_key_data.description,
        max_usage_count=temp_key_data.max_usage_count,
        is_active=True
    )

    db.add(temporary_api_key)
    await db.commit()
    await db.refresh(temporary_api_key)

    # 返回完整的临时API密钥（只在创建时返回）
    return {
        "message": "临时API密钥创建成功",
        "temporary_api_key": temp_key_value,
        "temp_key_id": temporary_api_key.id,
        "name": temporary_api_key.name,
        "expires_at": temporary_api_key.expires_at.isoformat(),
        "max_usage_count": temporary_api_key.max_usage_count,
        "real_api_key_info": {
            "id": real_api_key.id,
            "name": real_api_key.name,
            "key_preview": f"{real_api_key.key[:10]}...{real_api_key.key[-4:]}"  # 只显示部分密钥
        },
        "warning": "请妥善保存此临时API密钥，系统不会再次显示完整密钥"
    }


@router.get("/{temp_key_id}", response_model=TemporaryAPIKeySchema)
async def get_temporary_api_key(
    request: Request,
    temp_key_id: int,
    current_user: dict = Depends(get_current_active_user_from_api_key),
    db: AsyncSession = Depends(get_async_db)
):
    """获取指定的临时API密钥详情（必须属于当前真实API密钥）"""

    # 从当前用户信息中获取真实API密钥
    real_api_key_value = current_user.get('current_api_key')
    if not real_api_key_value:
        raise BusinessException(
            message="无法获取当前API密钥",
            error_code=ErrorCode.BUSINESS_ERROR,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 首先查找真实API密钥记录
    user_id = current_user.get('id')
    tenant_id = current_user.get('tenant_id')

    real_api_key_query = select(APIKey).where(
        and_(
            APIKey.key == real_api_key_value,
            APIKey.user_id == user_id,
            APIKey.is_active == True,
            APIKey.is_deleted == False
        )
    )

    if tenant_id:
        real_api_key_query = real_api_key_query.where(
            APIKey.tenant_id == tenant_id
        )
    else:
        real_api_key_query = real_api_key_query.where(
            APIKey.tenant_id.is_(None)
        )

    result = await db.execute(real_api_key_query)
    real_api_key = result.scalar_one_or_none()

    if not real_api_key:
        raise BusinessException(
            message="无法找到对应的API密钥记录",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 构建查询条件 - 只查询该真实API密钥下的指定临时密钥
    query = select(TemporaryAPIKey).where(
        and_(
            TemporaryAPIKey.id == temp_key_id,
            TemporaryAPIKey.real_api_key_id == real_api_key.id,
            TemporaryAPIKey.is_deleted == False
        )
    )

    query = query.options(selectinload(TemporaryAPIKey.real_api_key))

    result = await db.execute(query)
    temporary_key = result.scalar_one_or_none()

    if not temporary_key:
        raise BusinessException(
            message="临时API密钥不存在或不属于当前API密钥",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    return temporary_key


@router.put("/{temp_key_id}", response_model=TemporaryAPIKeySchema)
async def update_temporary_api_key(
    request: Request,
    temp_key_id: int,
    temp_key_data: TemporaryAPIKeyUpdate,
    current_user: dict = Depends(get_current_active_user_from_api_key),
    db: AsyncSession = Depends(get_async_db)
):
    """更新临时API密钥（必须属于当前真实API密钥）"""

    # 从当前用户信息中获取真实API密钥
    real_api_key_value = current_user.get('current_api_key')
    if not real_api_key_value:
        raise BusinessException(
            message="无法获取当前API密钥",
            error_code=ErrorCode.BUSINESS_ERROR,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 首先查找真实API密钥记录
    user_id = current_user.get('id')
    tenant_id = current_user.get('tenant_id')

    real_api_key_query = select(APIKey).where(
        and_(
            APIKey.key == real_api_key_value,
            APIKey.user_id == user_id,
            APIKey.is_active == True,
            APIKey.is_deleted == False
        )
    )

    if tenant_id:
        real_api_key_query = real_api_key_query.where(
            APIKey.tenant_id == tenant_id
        )
    else:
        real_api_key_query = real_api_key_query.where(
            APIKey.tenant_id.is_(None)
        )

    result = await db.execute(real_api_key_query)
    real_api_key = result.scalar_one_or_none()

    if not real_api_key:
        raise BusinessException(
            message="无法找到对应的API密钥记录",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 查找临时API密钥 - 只查询该真实API密钥下的临时密钥
    query = select(TemporaryAPIKey).where(
        and_(
            TemporaryAPIKey.id == temp_key_id,
            TemporaryAPIKey.real_api_key_id == real_api_key.id,
            TemporaryAPIKey.is_deleted == False
        )
    )

    result = await db.execute(query)
    temporary_key = result.scalar_one_or_none()

    if not temporary_key:
        raise BusinessException(
            message="临时API密钥不存在或不属于当前API密钥",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 检查名称唯一性（如果要更新名称）
    if temp_key_data.name and temp_key_data.name != temporary_key.name:
        if tenant_id:
            name_query = select(TemporaryAPIKey).where(
                and_(
                    TemporaryAPIKey.tenant_id == tenant_id,
                    TemporaryAPIKey.user_id == user_id,
                    TemporaryAPIKey.name == temp_key_data.name,
                    TemporaryAPIKey.id != temp_key_id,
                    TemporaryAPIKey.is_deleted == False
                )
            )
        else:
            name_query = select(TemporaryAPIKey).where(
                and_(
                    TemporaryAPIKey.tenant_id.is_(None),
                    TemporaryAPIKey.user_id == user_id,
                    TemporaryAPIKey.name == temp_key_data.name,
                    TemporaryAPIKey.id != temp_key_id,
                    TemporaryAPIKey.is_deleted == False
                )
            )

        existing_result = await db.execute(name_query)
        if existing_result.scalar_one_or_none():
            raise BusinessException(
                message="临时API密钥名称已存在",
                error_code=ErrorCode.BUSINESS_ERROR,
                status_code=status.HTTP_400_BAD_REQUEST
            )

    # 更新字段
    update_data = temp_key_data.dict(exclude_unset=True)

    # 处理过期时间更新
    if temp_key_data.expires_hours:
        update_data['expires_at'] = datetime.now(timezone.utc) + timedelta(hours=temp_key_data.expires_hours)
        del update_data['expires_hours']

    for field, value in update_data.items():
        setattr(temporary_key, field, value)

    await db.commit()
    await db.refresh(temporary_key)

    return temporary_key


@router.delete("/{temp_key_id}")
async def delete_temporary_api_key(
    request: Request,
    temp_key_id: int,
    current_user: dict = Depends(get_current_active_user_from_api_key),
    db: AsyncSession = Depends(get_async_db)
):
    """删除临时API密钥（软删除，必须属于当前真实API密钥）"""

    # 从当前用户信息中获取真实API密钥
    real_api_key_value = current_user.get('current_api_key')
    if not real_api_key_value:
        raise BusinessException(
            message="无法获取当前API密钥",
            error_code=ErrorCode.BUSINESS_ERROR,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 首先查找真实API密钥记录
    user_id = current_user.get('id')
    tenant_id = current_user.get('tenant_id')

    real_api_key_query = select(APIKey).where(
        and_(
            APIKey.key == real_api_key_value,
            APIKey.user_id == user_id,
            APIKey.is_active == True,
            APIKey.is_deleted == False
        )
    )

    if tenant_id:
        real_api_key_query = real_api_key_query.where(
            APIKey.tenant_id == tenant_id
        )
    else:
        real_api_key_query = real_api_key_query.where(
            APIKey.tenant_id.is_(None)
        )

    result = await db.execute(real_api_key_query)
    real_api_key = result.scalar_one_or_none()

    if not real_api_key:
        raise BusinessException(
            message="无法找到对应的API密钥记录",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 查找临时API密钥 - 只查询该真实API密钥下的临时密钥
    query = select(TemporaryAPIKey).where(
        and_(
            TemporaryAPIKey.id == temp_key_id,
            TemporaryAPIKey.real_api_key_id == real_api_key.id,
            TemporaryAPIKey.is_deleted == False
        )
    )

    result = await db.execute(query)
    temporary_key = result.scalar_one_or_none()

    if not temporary_key:
        raise BusinessException(
            message="临时API密钥不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 软删除
    temporary_key.soft_delete()
    await db.commit()

    return {"message": "临时API密钥删除成功"}


@router.post("/{temp_key_id}/deactivate")
async def deactivate_temporary_api_key(
    request: Request,
    temp_key_id: int,
    current_user: dict = Depends(get_current_active_user_from_api_key),
    db: AsyncSession = Depends(get_async_db)
):
    """停用临时API密钥（必须属于当前真实API密钥）"""

    # 从当前用户信息中获取真实API密钥
    real_api_key_value = current_user.get('current_api_key')
    if not real_api_key_value:
        raise BusinessException(
            message="无法获取当前API密钥",
            error_code=ErrorCode.BUSINESS_ERROR,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 首先查找真实API密钥记录
    user_id = current_user.get('id')
    tenant_id = current_user.get('tenant_id')

    real_api_key_query = select(APIKey).where(
        and_(
            APIKey.key == real_api_key_value,
            APIKey.user_id == user_id,
            APIKey.is_active == True,
            APIKey.is_deleted == False
        )
    )

    if tenant_id:
        real_api_key_query = real_api_key_query.where(
            APIKey.tenant_id == tenant_id
        )
    else:
        real_api_key_query = real_api_key_query.where(
            APIKey.tenant_id.is_(None)
        )

    result = await db.execute(real_api_key_query)
    real_api_key = result.scalar_one_or_none()

    if not real_api_key:
        raise BusinessException(
            message="无法找到对应的API密钥记录",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 查找临时API密钥 - 只查询该真实API密钥下的临时密钥
    query = select(TemporaryAPIKey).where(
        and_(
            TemporaryAPIKey.id == temp_key_id,
            TemporaryAPIKey.real_api_key_id == real_api_key.id,
            TemporaryAPIKey.is_deleted == False
        )
    )

    result = await db.execute(query)
    temporary_key = result.scalar_one_or_none()

    if not temporary_key:
        raise BusinessException(
            message="临时API密钥不存在或不属于当前API密钥",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 停用
    temporary_key.is_active = False
    await db.commit()

    return {"message": "临时API密钥已停用"}
