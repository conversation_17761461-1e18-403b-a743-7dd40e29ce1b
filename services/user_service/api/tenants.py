"""
租户管理API端点
"""
from datetime import datetime, timedelta, timezone
from typing import Optional

from fastapi import APIRouter, Depends, Query, Request, status
from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from shared.database import get_async_db
from shared.models.response import (
    SuccessResponse, PaginatedResponse, create_success_response,
    create_paginated_response, ErrorCode, SuccessMessage
)
from shared.middleware.request_id import get_request_id
from shared.utils.exception_handlers import BusinessException
from shared.utils.auth import get_current_user_from_token
from ..models.tenant import Tenant, TenantRole, DEFAULT_TENANT_FEATURES, DEFAULT_TENANT_SETTINGS, DEFAULT_TENANT_ROLES
from ..models.user import User
from ..schemas.tenant import (
    TenantCreate, TenantUpdate, Tenant as TenantSchema, <PERSON>ant<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,
    TenantStats, TenantSubscriptionUpdate, TenantTrialExtension, TenantSuspension,
    TenantFeatureToggle, TenantSettingUpdate, TenantRoleCreate, TenantRole as TenantRoleSchema
)

router = APIRouter()


async def get_current_superuser(
    request: Request,
    current_user: dict = Depends(get_current_user_from_token)
) -> dict:
    """获取当前超级用户"""
    if not current_user.get("is_superuser", False):
        raise BusinessException(
            message="需要超级用户权限",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_403_FORBIDDEN
        )
    return current_user


async def get_current_tenant_admin(
    request: Request,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_async_db)
) -> dict:
    """获取当前租户管理员或超级用户"""
    if current_user.get("is_superuser", False):
        return current_user

    # 检查是否为租户管理员
    user_query = select(User).options(selectinload(User.tenant_role)).where(
        User.id == current_user.get("id")
    )
    result = await db.execute(user_query)
    user = result.scalar_one_or_none()

    if not user or not user.is_tenant_admin:
        raise BusinessException(
            message="需要租户管理员或超级用户权限",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_403_FORBIDDEN
        )

    return current_user


@router.get("/", response_model=PaginatedResponse[TenantSchema])
async def get_tenants(
    request: Request,
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(50, ge=1, le=200, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    is_trial: Optional[bool] = Query(None, description="是否试用版"),
    is_suspended: Optional[bool] = Query(None, description="是否暂停"),
    current_user: dict = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """获取租户列表（仅超级用户）"""

    # 构建查询
    query = select(Tenant).where(Tenant.is_deleted == False)

    # 添加过滤条件
    if search:
        search_pattern = f"%{search}%"
        query = query.where(
            or_(
                Tenant.name.ilike(search_pattern),
                Tenant.display_name.ilike(search_pattern),
                Tenant.slug.ilike(search_pattern),
                Tenant.contact_email.ilike(search_pattern)
            )
        )

    if is_active is not None:
        query = query.where(Tenant.is_active == is_active)

    if is_trial is not None:
        query = query.where(Tenant.is_trial == is_trial)

    if is_suspended is not None:
        query = query.where(Tenant.is_suspended == is_suspended)

    # 获取总数
    count_query = select(func.count(Tenant.id)).select_from(query.subquery())
    count_result = await db.execute(count_query)
    total = count_result.scalar()

    # 分页查询
    offset = (page - 1) * size
    query = query.order_by(Tenant.created_at.desc()).offset(offset).limit(size)

    result = await db.execute(query)
    tenants = result.scalars().all()

    # 转换为Pydantic模型，手动计算属性
    tenant_items = []
    for tenant in tenants:
        tenant_dict = {
            'id': tenant.id,
            'name': tenant.name,
            'slug': tenant.slug,
            'display_name': tenant.display_name,
            'is_active': tenant.is_active,
            'is_trial': tenant.is_trial,
            'is_suspended': tenant.is_suspended,
            'user_count': tenant.user_count,
            'api_key_count': tenant.api_key_count,
            'max_users': tenant.max_users,
            'max_api_keys': tenant.max_api_keys,
            'max_storage_mb': tenant.max_storage_mb,
            'trial_ends_at': tenant.trial_ends_at,
            'subscription_starts_at': tenant.subscription_starts_at,
            'subscription_ends_at': tenant.subscription_ends_at,
            'last_activity_at': tenant.last_activity_at,
            'created_at': tenant.created_at,
            'updated_at': tenant.updated_at,
            'features_enabled': tenant.features_enabled or {},
            'settings': tenant.settings or {},
            'billing_email': tenant.billing_email,
            'billing_address': tenant.billing_address,
            'is_expired': tenant.is_expired,
            'is_available': tenant.is_available
        }
        tenant_items.append(TenantSummary(**tenant_dict))

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一分页响应格式
    return create_paginated_response(
        items=tenant_items,
        total=total,
        page=page,
        size=size,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.post("/", response_model=SuccessResponse[TenantSchema])
async def create_tenant(
    request: Request,
    tenant_data: TenantCreate,
    current_user: dict = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """创建租户（仅超级用户）"""

    # 检查slug唯一性
    slug_query = select(Tenant).where(Tenant.slug == tenant_data.slug)
    slug_result = await db.execute(slug_query)
    if slug_result.scalar_one_or_none():
        raise BusinessException(
            message="租户标识符已存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 设置默认功能和配置
    features_enabled = tenant_data.features_enabled or DEFAULT_TENANT_FEATURES.copy()
    settings = tenant_data.settings or DEFAULT_TENANT_SETTINGS.copy()

    # 计算试用结束时间
    trial_ends_at = None
    if tenant_data.is_trial:
        trial_ends_at = datetime.now(
            timezone.utc) + timedelta(days=tenant_data.trial_days)

    # 创建租户
    tenant = Tenant(
        name=tenant_data.name,
        slug=tenant_data.slug,
        display_name=tenant_data.display_name,
        description=tenant_data.description,
        contact_email=tenant_data.contact_email,
        contact_phone=tenant_data.contact_phone,
        contact_person=tenant_data.contact_person,
        is_trial=tenant_data.is_trial,
        trial_ends_at=trial_ends_at,
        max_users=tenant_data.max_users,
        max_api_keys=tenant_data.max_api_keys,
        max_storage_mb=tenant_data.max_storage_mb,
        features_enabled=features_enabled,
        settings=settings,
        created_by_id=current_user.get("id")
    )

    db.add(tenant)
    await db.commit()
    await db.refresh(tenant)

    # 创建默认租户角色
    for role_data in DEFAULT_TENANT_ROLES:
        role = TenantRole(
            tenant_id=tenant.id,
            **role_data
        )
        db.add(role)

    await db.commit()

    # 转换为Pydantic模型
    tenant_data = TenantSchema.model_validate(tenant)

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=tenant_data,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.get("/{tenant_id}", response_model=SuccessResponse[TenantSchema])
async def get_tenant(
    request: Request,
    tenant_id: int,
    current_user: dict = Depends(get_current_tenant_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """获取租户详情"""

    # 构建查询
    query = select(Tenant).where(
        and_(
            Tenant.id == tenant_id,
            Tenant.is_deleted == False
        )
    )

    # 非超级用户只能查看自己的租户
    if not current_user.get("is_superuser", False):
        user_query = select(User).where(User.id == current_user.get("id"))
        user_result = await db.execute(user_query)
        user = user_result.scalar_one_or_none()

        if not user or user.tenant_id != tenant_id:
            raise BusinessException(
                message="无权访问此租户",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_403_FORBIDDEN
            )

    result = await db.execute(query)
    tenant = result.scalar_one_or_none()

    if not tenant:
        raise BusinessException(
            message="租户不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 转换为Pydantic模型
    tenant_data = TenantSchema.model_validate(tenant)

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=tenant_data,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.put("/{tenant_id}", response_model=SuccessResponse[TenantSchema])
async def update_tenant(
    request: Request,
    tenant_id: int,
    tenant_data: TenantUpdate,
    current_user: dict = Depends(get_current_tenant_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """更新租户信息"""

    # 获取租户
    query = select(Tenant).where(
        and_(
            Tenant.id == tenant_id,
            Tenant.is_deleted == False
        )
    )

    # 非超级用户只能更新自己的租户
    if not current_user.get("is_superuser", False):
        user_query = select(User).where(User.id == current_user.get("id"))
        user_result = await db.execute(user_query)
        user = user_result.scalar_one_or_none()

        if not user or user.tenant_id != tenant_id:
            raise BusinessException(
                message="无权修改此租户",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_403_FORBIDDEN
            )

    result = await db.execute(query)
    tenant = result.scalar_one_or_none()

    if not tenant:
        raise BusinessException(
            message="租户不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 更新字段
    update_data = tenant_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        if hasattr(tenant, field):
            setattr(tenant, field, value)

    tenant.updated_by_id = current_user.get("id")

    await db.commit()
    await db.refresh(tenant)

    # 转换为Pydantic模型
    tenant_data = TenantSchema.model_validate(tenant)

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=tenant_data,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.delete("/{tenant_id}")
async def delete_tenant(
    request: Request,
    tenant_id: int,
    current_user: dict = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """删除租户（软删除，仅超级用户）"""

    # 获取租户
    query = select(Tenant).where(
        and_(
            Tenant.id == tenant_id,
            Tenant.is_deleted == False
        )
    )
    result = await db.execute(query)
    tenant = result.scalar_one_or_none()

    if not tenant:
        raise BusinessException(
            message="租户不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 检查是否有活跃用户
    user_count_query = select(func.count(User.id)).where(
        and_(
            User.tenant_id == tenant_id,
            User.is_deleted == False
        )
    )
    user_count_result = await db.execute(user_count_query)
    user_count = user_count_result.scalar()

    if user_count > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"无法删除租户，仍有 {user_count} 个活跃用户"
        )

    # 软删除
    tenant.soft_delete()
    tenant.updated_by_id = current_user.get("id")

    await db.commit()

    return {"message": "租户已删除"}


@router.post("/{tenant_id}/suspend")
async def suspend_tenant(
    request: Request,
    tenant_id: int,
    suspension_data: TenantSuspension,
    current_user: dict = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """暂停租户（仅超级用户）"""

    # 获取租户
    query = select(Tenant).where(
        and_(
            Tenant.id == tenant_id,
            Tenant.is_deleted == False
        )
    )
    result = await db.execute(query)
    tenant = result.scalar_one_or_none()

    if not tenant:
        raise BusinessException(
            message="租户不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    if tenant.is_suspended:
        raise BusinessException(
            message="租户已被暂停",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 暂停租户
    tenant.suspend(suspension_data.reason)
    tenant.updated_by_id = current_user.get("id")

    await db.commit()

    return {"message": "租户已暂停", "reason": suspension_data.reason}


@router.post("/{tenant_id}/unsuspend")
async def unsuspend_tenant(
    request: Request,
    tenant_id: int,
    current_user: dict = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """恢复租户（仅超级用户）"""

    # 获取租户
    query = select(Tenant).where(
        and_(
            Tenant.id == tenant_id,
            Tenant.is_deleted == False
        )
    )
    result = await db.execute(query)
    tenant = result.scalar_one_or_none()

    if not tenant:
        raise BusinessException(
            message="租户不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    if not tenant.is_suspended:
        raise BusinessException(
            message="租户未被暂停",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 恢复租户
    tenant.unsuspend()
    tenant.updated_by_id = current_user.get("id")

    await db.commit()

    return {"message": "租户已恢复"}
