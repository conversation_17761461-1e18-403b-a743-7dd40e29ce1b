"""
内部API端点 - 用于服务间调用，不需要认证
"""
from datetime import datetime, timezone

from fastapi import APIRouter, Depends, Request, status, HTTPException
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from shared.database import get_async_db
from shared.models.response import (
    ErrorCode
)
from shared.utils.exception_handlers import BusinessException
from shared.utils.security import verify_token
from ..models.permission import Permission
from ..models.user import User, APIKey, user_permissions

router = APIRouter()


@router.get("/users/{user_id}")
async def get_user_internal(
        request: Request,
        user_id: int,
        db: AsyncSession = Depends(get_async_db)
):
    """
    内部API：根据用户ID获取用户信息

    此端点专门用于服务间调用，不需要认证
    """
    # 查询用户
    query = select(User).where(User.id == user_id)
    result = await db.execute(query)
    user = result.scalar_one_or_none()

    if not user:
        raise BusinessException(
            message="用户不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 返回用户基本信息
    return {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "first_name": user.first_name,
        "last_name": user.last_name,
        "is_active": user.is_active,
        "is_superuser": user.is_superuser,
        "is_verified": user.is_verified,
        "created_at": user.created_at.isoformat() if user.created_at else None,
        "last_login_at": user.last_login_at.isoformat() if user.last_login_at else None
    }


@router.get("/users/by-email/{email}")
async def get_user_by_email_internal(
        request: Request,
        email: str,
        db: AsyncSession = Depends(get_async_db)
):
    """
    内部API：根据邮箱获取用户信息

    此端点专门用于服务间调用，不需要认证
    """
    # 查询用户
    query = select(User).where(User.email == email)
    result = await db.execute(query)
    user = result.scalar_one_or_none()

    if not user:
        raise BusinessException(
            message="用户不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 返回用户基本信息
    return {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "first_name": user.first_name,
        "last_name": user.last_name,
        "is_active": user.is_active,
        "is_superuser": user.is_superuser,
        "is_verified": user.is_verified,
        "created_at": user.created_at.isoformat() if user.created_at else None,
        "last_login_at": user.last_login_at.isoformat() if user.last_login_at else None
    }


@router.post("/verify-jwt-token")
async def verify_jwt_token_internal(
        http_request: Request,
        request: dict,
        db: AsyncSession = Depends(get_async_db)
):
    """
    内部API：验证JWT令牌并返回用户信息

    请求格式：
    {
        "token": "jwt-token-string",
        "token_type": "access"  # 可选，默认为access
    }
    """
    token = request.get("token")
    token_type = request.get("token_type", "access")

    if not token:
        raise BusinessException(
            message="令牌不能为空",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 验证JWT令牌
    payload = verify_token(token, token_type)
    if not payload:
        raise BusinessException(
            message="无效的令牌",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_401_UNAUTHORIZED
        )

    # 从令牌中提取用户ID
    user_id = payload.get("sub")
    if not user_id:
        raise BusinessException(
            message="无效的令牌载荷",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_401_UNAUTHORIZED
        )

    # 查询用户信息
    try:
        user_id_int = int(user_id)
        result = await db.execute(select(User).where(User.id == user_id_int))
        user = result.scalar_one_or_none()

        if not user:
            raise BusinessException(
                message="用户不存在",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        if not user.is_active:
            raise BusinessException(
                message="用户未激活",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        return {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "is_active": user.is_active,
            "is_superuser": user.is_superuser,
            "is_verified": user.is_verified,
            "auth_type": "jwt",
            "token_type": token_type
        }

    except ValueError:
        raise BusinessException(
            message="无效的用户ID格式",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_400_BAD_REQUEST
        )


@router.post("/verify-api-key")
async def verify_api_key_internal(
        http_request: Request,
        request: dict,
        db: AsyncSession = Depends(get_async_db)
):
    """
    内部API：验证API密钥并返回用户信息

    请求格式：
    {
        "api_key": "sk-proj-xxxxx"
    }
    """
    api_key = request.get("api_key")

    if not api_key:
        raise BusinessException(
            message="API密钥不能为空",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 查询API密钥
    result = await db.execute(
        select(APIKey).where(
            APIKey.key == api_key,
            APIKey.is_active == True
        )
    )
    api_key_obj = result.scalar_one_or_none()

    if not api_key_obj:
        raise BusinessException(
            message="无效的API密钥",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_401_UNAUTHORIZED
        )

    # 检查API密钥是否过期
    if api_key_obj.expires_at and api_key_obj.expires_at < datetime.now(timezone.utc):
        raise BusinessException(
            message="API密钥已过期",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_401_UNAUTHORIZED
        )

    # 获取用户信息
    result = await db.execute(select(User).where(User.id == api_key_obj.user_id))
    user = result.scalar_one_or_none()

    if not user or not user.is_active:
        raise BusinessException(
            message="用户不存在或未激活",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_401_UNAUTHORIZED
        )

    # 更新API密钥最后使用时间
    api_key_obj.last_used_at = datetime.now(timezone.utc)
    await db.commit()

    return {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "first_name": user.first_name,
        "last_name": user.last_name,
        "is_active": user.is_active,
        "is_superuser": user.is_superuser,
        "is_verified": user.is_verified,
        "auth_type": "api_key",
        "api_key_id": api_key_obj.id,
        "api_key_name": api_key_obj.name,
        "tenant_id": api_key_obj.tenant_id,
        "rate_limit_per_minute": api_key_obj.rate_limit_per_minute,
        "rate_limit_per_day": api_key_obj.rate_limit_per_day,
        "last_used_at": api_key_obj.last_used_at.isoformat() if api_key_obj.last_used_at else None,
        "expires_at": api_key_obj.expires_at.isoformat() if api_key_obj.expires_at else None
    }


@router.post("/verify-api-key-detailed")
async def verify_api_key_detailed_internal(
        http_request: Request,
        request: dict,
        db: AsyncSession = Depends(get_async_db)
):
    """
    内部API：详细验证API密钥并返回完整的验证结果

    请求格式：
    {
        "api_key": "sk-proj-xxxxx"
    }

    返回格式：
    {
        "valid": true/false,
        "id": user_id,
        "email": "<EMAIL>",
        ...其他用户信息
        "error_type": "错误类型（仅在验证失败时）",
        "error_message": "错误消息（仅在验证失败时）",
        "error_detail": "详细错误信息（仅在验证失败时）"
    }
    """
    api_key = request.get("api_key")

    if not api_key:
        return {
            "valid": False,
            "error_type": "invalid_request",
            "error_message": "API密钥不能为空",
            "error_detail": "请求中必须包含有效的API密钥"
        }

    try:
        # 查询API密钥（不限制is_active状态，以便提供详细的错误信息）
        result = await db.execute(
            select(APIKey).where(APIKey.key == api_key)
        )
        api_key_obj = result.scalar_one_or_none()

        if not api_key_obj:
            return {
                "valid": False,
                "error_type": "not_found",
                "error_message": "API密钥不存在",
                "error_detail": "提供的API密钥在系统中不存在，请检查密钥是否正确或联系管理员获取有效的API密钥"
            }

        # 检查API密钥是否被禁用
        if not api_key_obj.is_active:
            return {
                "valid": False,
                "error_type": "disabled",
                "error_message": "API密钥已被禁用",
                "error_detail": "此API密钥已被管理员禁用，请联系管理员重新激活或获取新的API密钥"
            }

        # 检查API密钥是否过期
        current_time = datetime.now(timezone.utc)
        if api_key_obj.expires_at and api_key_obj.expires_at < current_time:
            return {
                "valid": False,
                "error_type": "expired",
                "error_message": "API密钥已过期",
                "error_detail": f"此API密钥已于 {api_key_obj.expires_at.strftime('%Y-%m-%d %H:%M:%S UTC')} 过期，请联系管理员续期或获取新的API密钥",
                "expired_at": api_key_obj.expires_at.isoformat()
            }

        # 获取用户信息
        result = await db.execute(select(User).where(User.id == api_key_obj.user_id))
        user = result.scalar_one_or_none()

        if not user:
            return {
                "valid": False,
                "error_type": "user_not_found",
                "error_message": "关联用户不存在",
                "error_detail": "此API密钥关联的用户账户不存在，请联系管理员"
            }

        # 检查用户是否被删除
        if user.is_deleted:
            return {
                "valid": False,
                "error_type": "user_deleted",
                "error_message": "关联用户已被删除",
                "error_detail": "此API密钥关联的用户账户已被删除，该密钥无法继续使用"
            }

        # 检查用户是否被禁用
        if not user.is_active:
            return {
                "valid": False,
                "error_type": "user_disabled",
                "error_message": "关联用户已被禁用",
                "error_detail": "此API密钥关联的用户账户已被禁用，请联系管理员重新激活账户"
            }

        # 更新API密钥最后使用时间
        api_key_obj.last_used_at = current_time
        await db.commit()

        # 构建成功响应
        return {
            "valid": True,
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "is_active": user.is_active,
            "is_superuser": user.is_superuser,
            "is_verified": user.is_verified,
            "tier": "premium" if user.is_superuser else "standard",
            "tenant_id": user.tenant_id,
            "api_key_id": api_key_obj.id,
            "api_key_name": api_key_obj.name,
            "rate_limit_per_minute": api_key_obj.rate_limit_per_minute,
            "rate_limit_per_day": api_key_obj.rate_limit_per_day,
            "last_used_at": current_time.isoformat(),
            "expires_at": api_key_obj.expires_at.isoformat() if api_key_obj.expires_at else None
        }

    except Exception as e:
        logger.error(f"详细验证API密钥时发生错误: {e}")
        return {
            "valid": False,
            "error_type": "database_error",
            "error_message": "系统内部错误",
            "error_detail": "验证API密钥时发生数据库错误，请稍后重试或联系技术支持"
        }


@router.post("/check-permission")
async def check_permission_internal(
        http_request: Request,
        request: dict,
        db: AsyncSession = Depends(get_async_db)
):
    """
    内部API：检查用户权限

    请求格式：
    {
        "user_id": 123,
        "permission": "read_pets"
    }
    """
    user_id = request.get("user_id")
    permission_name = request.get("permission")

    if not user_id or not permission_name:
        raise BusinessException(
            message="用户ID和权限名称不能为空",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    try:
        # 检查用户是否存在
        result = await db.execute(select(User).where(User.id == user_id))
        user = result.scalar_one_or_none()

        if not user:
            raise BusinessException(
                message="用户不存在",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        # 如果是超级用户，拥有所有权限
        if user.is_superuser:
            return {"has_permission": True, "reason": "superuser"}

        # 查询权限
        result = await db.execute(
            select(Permission).where(Permission.name == permission_name)
        )
        permission = result.scalar_one_or_none()

        if not permission:
            # 权限不存在，返回false
            return {"has_permission": False, "reason": "permission_not_found"}

        # 检查用户是否有该权限
        result = await db.execute(
            select(user_permissions).where(
                user_permissions.c.user_id == user_id,
                user_permissions.c.permission_id == permission.id
            )
        )
        user_permission = result.first()

        has_permission = user_permission is not None

        return {
            "has_permission": has_permission,
            "reason": "granted" if has_permission else "not_granted"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"权限检查失败: {str(e)}"
        )


@router.post("/verify-temporary-api-key")
async def verify_temporary_api_key_internal(
        http_request: Request,
        request: dict,
        db: AsyncSession = Depends(get_async_db)
):
    """
    内部API：验证临时API密钥并返回对应的真实API密钥信息

    请求格式：
    {
        "temp_api_key": "tt-xxxxx"
    }

    返回格式：
    {
        "real_api_key": "sk-proj-xxxxx",
        "user_info": {...},
        "temp_key_info": {...}
    }
    """
    from ..models.user import TemporaryAPIKey
    from sqlalchemy.orm import selectinload

    temp_api_key = request.get("temp_api_key")

    if not temp_api_key:
        raise BusinessException(
            message="临时API密钥不能为空",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 查询临时API密钥
    result = await db.execute(
        select(TemporaryAPIKey)
        .options(
            selectinload(TemporaryAPIKey.real_api_key),
            selectinload(TemporaryAPIKey.user)
        )
        .where(
            TemporaryAPIKey.temp_key == temp_api_key,
            TemporaryAPIKey.is_active == True,
            TemporaryAPIKey.is_deleted == False
        )
    )
    temp_key_obj = result.scalar_one_or_none()

    if not temp_key_obj:
        raise BusinessException(
            message="无效的临时API密钥",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_401_UNAUTHORIZED
        )

    # 检查临时密钥是否过期
    if temp_key_obj.is_expired():
        raise BusinessException(
            message="临时API密钥已过期",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_401_UNAUTHORIZED
        )

    # 检查使用次数限制
    if temp_key_obj.is_usage_exceeded():
        raise BusinessException(
            message="临时API密钥使用次数已达上限",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_401_UNAUTHORIZED
        )

    # 检查关联的真实API密钥是否有效
    real_api_key = temp_key_obj.real_api_key
    if not real_api_key or not real_api_key.is_valid():
        raise BusinessException(
            message="关联的真实API密钥无效或已过期",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_401_UNAUTHORIZED
        )

    # 获取用户信息
    user = temp_key_obj.user
    if not user or not user.is_active:
        raise BusinessException(
            message="用户不存在或未激活",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_401_UNAUTHORIZED
        )

    # 更新临时密钥使用统计
    temp_key_obj.increment_usage()
    await db.commit()

    # 返回真实API密钥和用户信息
    return {
        "real_api_key": real_api_key.key,
        "user_info": {
            "id": user.id,
            "email": user.email,
            "username": user.username,
            "is_active": user.is_active,
            "is_superuser": user.is_superuser,
            "tenant_id": user.tenant_id,
            "first_name": user.first_name,
            "last_name": user.last_name
        },
        "temp_key_info": {
            "id": temp_key_obj.id,
            "name": temp_key_obj.name,
            "usage_count": temp_key_obj.usage_count,
            "max_usage_count": temp_key_obj.max_usage_count,
            "expires_at": temp_key_obj.expires_at.isoformat(),
            "last_used_at": temp_key_obj.last_used_at.isoformat() if temp_key_obj.last_used_at else None
        },
        "api_key_info": {
            "id": real_api_key.id,
            "name": real_api_key.name,
            "rate_limit_per_minute": real_api_key.rate_limit_per_minute,
            "rate_limit_per_day": real_api_key.rate_limit_per_day
        }
    }


@router.get("/health")
async def health_check():
    """内部健康检查端点"""
    return {"status": "healthy", "service": "user_service_internal"}
