"""
用户管理API端点 - 统一响应格式
"""
from typing import Optional

from fastapi import APIRouter, Depends, Query, Request, status
from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from shared.database import get_async_db
from shared.utils.auth import get_current_active_user, get_current_superuser
from shared.models.response import (
    SuccessResponse, PaginatedResponse, create_success_response,
    create_paginated_response, ErrorCode, SuccessMessage
)
from shared.middleware.request_id import get_request_id
from shared.utils.exception_handlers import BusinessException
from ..models.user import User
from ..models.tenant import Tenant, TenantRole
from ..schemas.user import (
    User as UserSchema,
    UserUpdate,
    UserList,
    UserCreate
)
from ..utils.tenant_auth import (
    TenantContext,
    get_tenant_context,
    require_tenant_admin,
    require_global_admin,
    require_user_access,
    create_tenant_filter,
    check_tenant_resource_limits
)
from ..decorators.tenant_decorators import (
    check_tenant_resource_limit,
    log_tenant_action
)

router = APIRouter()


@router.get("/", response_model=PaginatedResponse[UserSchema])
async def get_users(
    request: Request,
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    tenant_context: TenantContext = Depends(require_tenant_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """获取用户列表（需要租户管理员或全局管理员权限）"""

    # 构建查询
    query = select(User).options(
        selectinload(User.permissions),
        selectinload(User.tenant),
        selectinload(User.tenant_role)
    )

    # 应用租户过滤
    tenant_filter = create_tenant_filter(tenant_context)
    query = tenant_filter(query, User)

    # 添加搜索条件
    if search:
        search_pattern = f"%{search}%"
        query = query.where(
            or_(
                User.username.ilike(search_pattern),
                User.email.ilike(search_pattern),
                User.first_name.ilike(search_pattern),
                User.last_name.ilike(search_pattern)
            )
        )

    # 添加状态过滤
    if is_active is not None:
        query = query.where(User.is_active == is_active)

    # 排除已删除的用户
    query = query.where(User.is_deleted == False)

    # 获取总数
    count_query = select(func.count(User.id)).where(User.is_deleted == False)

    # 应用租户过滤到计数查询
    count_query = tenant_filter(count_query, User)

    if search:
        search_pattern = f"%{search}%"
        count_query = count_query.where(
            or_(
                User.username.ilike(search_pattern),
                User.email.ilike(search_pattern),
                User.first_name.ilike(search_pattern),
                User.last_name.ilike(search_pattern)
            )
        )
    if is_active is not None:
        count_query = count_query.where(User.is_active == is_active)

    total_result = await db.execute(count_query)
    total = total_result.scalar()

    # 分页查询
    offset = (page - 1) * size
    query = query.offset(offset).limit(size).order_by(User.created_at.desc())

    result = await db.execute(query)
    users = result.scalars().all()

    # 转换为Pydantic模型
    user_items = [UserSchema.model_validate(user) for user in users]

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一分页响应格式
    return create_paginated_response(
        items=user_items,
        total=total,
        page=page,
        size=size,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.get("/{user_id}", response_model=SuccessResponse[UserSchema])
async def get_user(
    request: Request,
    user_id: int,
    tenant_context: TenantContext = Depends(require_user_access),
    db: AsyncSession = Depends(get_async_db)
):
    """获取用户详情"""

    # 查询用户
    query = select(User).options(
        selectinload(User.permissions),
        selectinload(User.tenant),
        selectinload(User.tenant_role)
    ).where(
        and_(
            User.id == user_id,
            User.is_deleted == False
        )
    )

    result = await db.execute(query)
    user = result.scalar_one_or_none()

    if not user:
        raise BusinessException(
            message="用户不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 转换为Pydantic模型
    user_data = UserSchema.model_validate(user)

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=user_data,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.put("/{user_id}", response_model=SuccessResponse[UserSchema])
@log_tenant_action("update_user")
async def update_user(
    request: Request,
    user_id: int,
    user_update: UserUpdate,
    tenant_context: TenantContext = Depends(require_user_access),
    db: AsyncSession = Depends(get_async_db)
):
    """更新用户信息"""

    # 查询用户
    query = select(User).options(
        selectinload(User.permissions),
        selectinload(User.tenant),
        selectinload(User.tenant_role)
    ).where(
        and_(
            User.id == user_id,
            User.is_deleted == False
        )
    )
    result = await db.execute(query)
    user = result.scalar_one_or_none()

    if not user:
        raise BusinessException(
            message="用户不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 检查邮箱和用户名唯一性（在租户范围内）
    update_data = user_update.model_dump(exclude_unset=True)

    if "email" in update_data:
        email_query = select(User).where(
            and_(
                User.email == update_data["email"],
                User.id != user_id,
                User.tenant_id == user.tenant_id,
                User.is_deleted == False
            )
        )
        email_result = await db.execute(email_query)
        if email_result.scalar_one_or_none():
            raise BusinessException(
                message="邮箱已被使用",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_400_BAD_REQUEST
            )

    if "username" in update_data:
        username_query = select(User).where(
            and_(
                User.username == update_data["username"],
                User.id != user_id,
                User.tenant_id == user.tenant_id,
                User.is_deleted == False
            )
        )
        username_result = await db.execute(username_query)
        if username_result.scalar_one_or_none():
            raise BusinessException(
                message="用户名已被使用",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_400_BAD_REQUEST
            )

    # 权限控制：非全局管理员不能修改管理员相关字段
    if not tenant_context.is_global_admin:
        update_data.pop("is_superuser", None)
        update_data.pop("is_active", None)
        update_data.pop("is_verified", None)
        update_data.pop("tenant_id", None)  # 不能修改租户归属
        update_data.pop("tenant_role_id", None)  # 租户管理员才能修改角色

    # 租户管理员可以修改租户内用户的角色，但不能修改管理员字段
    if tenant_context.is_tenant_admin and not tenant_context.is_global_admin:
        update_data.pop("is_superuser", None)

    # 更新用户信息
    for field, value in update_data.items():
        setattr(user, field, value)

    await db.commit()
    await db.refresh(user)

    # 重新查询用户以确保所有关系被正确加载
    query = select(User).options(
        selectinload(User.permissions),
        selectinload(User.tenant),
        selectinload(User.tenant_role)
    ).where(User.id == user.id)
    result = await db.execute(query)
    user_with_relations = result.scalar_one()

    return UserSchema.model_validate(user_with_relations)


@router.delete("/{user_id}")
@log_tenant_action("delete_user")
async def delete_user(
    request: Request,
    user_id: int,
    tenant_context: TenantContext = Depends(require_tenant_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """删除用户（需要租户管理员或全局管理员权限）"""

    # 不能删除自己
    if tenant_context.user and tenant_context.user.id == user_id:
        raise BusinessException(
            message="不能删除自己的账户",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 查询用户
    query = select(User).where(
        and_(
            User.id == user_id,
            User.is_deleted == False
        )
    )

    # 非全局管理员只能删除自己租户内的用户
    if not tenant_context.is_global_admin:
        query = query.where(User.tenant_id == tenant_context.tenant_id)

    result = await db.execute(query)
    user = result.scalar_one_or_none()

    if not user:
        raise BusinessException(
            message="用户不存在或无权删除",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 软删除用户
    user.soft_delete()

    # 更新租户用户计数
    if user.tenant_id:
        tenant_query = select(Tenant).where(Tenant.id == user.tenant_id)
        tenant_result = await db.execute(tenant_query)
        tenant = tenant_result.scalar_one_or_none()
        if tenant:
            await tenant.update_user_count(db)

    await db.commit()

    return {"message": "用户已删除", "user_id": user_id}


@router.post("/", response_model=SuccessResponse[UserSchema], status_code=status.HTTP_201_CREATED)
@check_tenant_resource_limit("user")
@log_tenant_action("create_user")
async def create_user(
    request: Request,
    user_create: UserCreate,
    tenant_context: TenantContext = Depends(require_tenant_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """创建用户（需要租户管理员或全局管理员权限）"""

    # 确定目标租户ID
    target_tenant_id = user_create.tenant_id
    if not tenant_context.is_global_admin:
        # 非全局管理员只能在自己的租户内创建用户
        target_tenant_id = tenant_context.tenant_id
    elif target_tenant_id is None:
        # 全局管理员如果没有指定租户，则在自己的租户内创建
        target_tenant_id = tenant_context.tenant_id

    # 检查邮箱和用户名唯一性（在租户范围内）
    if target_tenant_id:
        email_query = select(User).where(
            and_(
                User.email == user_create.email,
                User.tenant_id == target_tenant_id,
                User.is_deleted == False
            )
        )
    else:
        # 全局用户（无租户）的唯一性检查
        email_query = select(User).where(
            and_(
                User.email == user_create.email,
                User.tenant_id.is_(None),
                User.is_deleted == False
            )
        )

    email_result = await db.execute(email_query)
    if email_result.scalar_one_or_none():
        raise BusinessException(
            message="邮箱已被使用",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 检查用户名唯一性
    if target_tenant_id:
        username_query = select(User).where(
            and_(
                User.username == user_create.username,
                User.tenant_id == target_tenant_id,
                User.is_deleted == False
            )
        )
    else:
        username_query = select(User).where(
            and_(
                User.username == user_create.username,
                User.tenant_id.is_(None),
                User.is_deleted == False
            )
        )

    username_result = await db.execute(username_query)
    if username_result.scalar_one_or_none():
        raise BusinessException(
            message="用户名已被使用",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 获取默认租户角色
    default_role = None
    if target_tenant_id and not user_create.tenant_role_id:
        role_query = select(TenantRole).where(
            and_(
                TenantRole.tenant_id == target_tenant_id,
                TenantRole.is_default == True
            )
        )
        role_result = await db.execute(role_query)
        default_role = role_result.scalar_one_or_none()

    # 创建用户 - 使用事务确保数据一致性
    try:
        from shared.utils.security import get_password_hash

        user_data = user_create.model_dump(exclude={"password"})
        user_data["hashed_password"] = get_password_hash(user_create.password)
        user_data["tenant_id"] = target_tenant_id

        if default_role and not user_data.get("tenant_role_id"):
            user_data["tenant_role_id"] = default_role.id

        user = User(**user_data)
        db.add(user)
        await db.flush()  # 获取用户ID但不提交事务

        # 更新租户用户计数
        if target_tenant_id:
            tenant_query = select(Tenant).where(Tenant.id == target_tenant_id)
            tenant_result = await db.execute(tenant_query)
            tenant = tenant_result.scalar_one_or_none()
            if tenant:
                await tenant.update_user_count(db)

        # 提交整个事务
        await db.commit()
        await db.refresh(user)

        # 重新查询用户以确保所有关系被正确加载
        query = select(User).options(
            selectinload(User.permissions),
            selectinload(User.tenant),
            selectinload(User.tenant_role)
        ).where(User.id == user.id)
        result = await db.execute(query)
        user_with_relations = result.scalar_one()

        return UserSchema.model_validate(user_with_relations)

    except Exception as e:
        # 回滚事务
        await db.rollback()

        # 检查是否是数据库约束违反（邮箱重复）
        if "uq_tenant_email" in str(e) or "duplicate key" in str(e).lower():
            raise BusinessException(
                message="邮箱已被使用",
                error_code=ErrorCode.CONFLICT,
                status_code=status.HTTP_409_CONFLICT
            )
        elif "uq_tenant_username" in str(e):
            raise BusinessException(
                message="用户名已被使用",
                error_code=ErrorCode.CONFLICT,
                status_code=status.HTTP_409_CONFLICT
            )
        else:
            # 重新抛出原始异常
            raise
