"""
API密钥管理API端点
"""
from datetime import datetime, timedelta, timezone
from typing import Optional

from fastapi import APIRouter, Depends, Query, Request, status
from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from shared.database import get_async_db
from shared.models.response import (
    SuccessResponse, PaginatedResponse, create_success_response,
    create_paginated_response, ErrorCode, SuccessMessage
)
from shared.middleware.request_id import get_request_id
from shared.utils.exception_handlers import BusinessException
from shared.utils.auth import get_current_active_user, get_current_superuser
from shared.utils.security import generate_api_key
from ..models.user import User, APIKey
from ..models.tenant import Tenant
from ..schemas.user import (
    APIKey as APIKeySchema,
    APIKeyList,
    APIKeyCreate,
    APIKeyUpdate
)
from ..utils.tenant_auth import (
    TenantContext,
    get_tenant_context,
    require_tenant_admin,
    require_global_admin,
    create_tenant_filter,
    check_tenant_resource_limits
)
from ..decorators.tenant_decorators import (
    check_tenant_resource_limit,
    log_tenant_action
)

router = APIRouter()


@router.get("/", response_model=PaginatedResponse[APIKeySchema])
async def get_api_keys(
    request: Request,
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    user_id: Optional[int] = Query(None, description="用户ID（租户管理员可用）"),
    tenant_context: TenantContext = Depends(get_tenant_context),
    db: AsyncSession = Depends(get_async_db)
):
    """获取API密钥列表"""

    # 构建查询
    query = select(APIKey).options(
        selectinload(APIKey.user),
        selectinload(APIKey.tenant)
    )

    # 应用租户过滤
    tenant_filter = create_tenant_filter(tenant_context)
    query = tenant_filter(query, APIKey)

    # 用户过滤逻辑
    if user_id:
        # 只有租户管理员或全局管理员可以查看其他用户的API密钥
        if not tenant_context.can_manage_tenant:
            raise BusinessException(
                message="无权查看其他用户的API密钥",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_403_FORBIDDEN
            )

        # 验证目标用户是否在同一租户内
        if not tenant_context.is_global_admin:
            user_query = select(User).where(User.id == user_id)
            user_result = await db.execute(user_query)
            target_user = user_result.scalar_one_or_none()

            if not target_user or target_user.tenant_id != tenant_context.tenant_id:
                raise BusinessException(
                    message="用户不存在或不在同一租户内",
                    error_code=ErrorCode.NOT_FOUND,
                    status_code=status.HTTP_404_NOT_FOUND
                )

        query = query.where(APIKey.user_id == user_id)
    else:
        # 普通用户只能查看自己的API密钥
        if not tenant_context.can_manage_tenant:
            query = query.where(APIKey.user_id == tenant_context.user.id)

    # 添加状态过滤
    if is_active is not None:
        query = query.where(APIKey.is_active == is_active)

    # 排除已删除的API密钥
    query = query.where(APIKey.is_deleted == False)

    # 获取总数
    count_query = select(func.count(APIKey.id)).where(
        APIKey.is_deleted == False)

    # 应用租户过滤到计数查询
    count_query = tenant_filter(count_query, APIKey)

    # 应用用户过滤到计数查询
    if user_id:
        count_query = count_query.where(APIKey.user_id == user_id)
    elif not tenant_context.can_manage_tenant:
        count_query = count_query.where(
            APIKey.user_id == tenant_context.user.id)

    if is_active is not None:
        count_query = count_query.where(APIKey.is_active == is_active)

    total_result = await db.execute(count_query)
    total = total_result.scalar()

    # 分页查询
    offset = (page - 1) * size
    query = query.offset(offset).limit(size).order_by(APIKey.created_at.desc())

    result = await db.execute(query)
    api_keys = result.scalars().all()

    # 转换为Pydantic模型
    api_key_items = [APIKeySchema.model_validate(key) for key in api_keys]

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一分页响应格式
    return create_paginated_response(
        items=api_key_items,
        total=total,
        page=page,
        size=size,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.post("/", response_model=dict)
@check_tenant_resource_limit("api_key")
@log_tenant_action("create_api_key")
async def create_api_key(
    request: Request,
    api_key_data: APIKeyCreate,
    tenant_context: TenantContext = Depends(get_tenant_context),
    db: AsyncSession = Depends(get_async_db)
):
    """创建API密钥"""

    # 检查租户内API密钥名称唯一性
    if tenant_context.tenant_id:
        name_query = select(APIKey).where(
            and_(
                APIKey.tenant_id == tenant_context.tenant_id,
                APIKey.user_id == tenant_context.user.id,
                APIKey.name == api_key_data.name,
                APIKey.is_deleted == False
            )
        )
    else:
        # 全局用户的API密钥名称唯一性检查
        name_query = select(APIKey).where(
            and_(
                APIKey.tenant_id.is_(None),
                APIKey.user_id == tenant_context.user.id,
                APIKey.name == api_key_data.name,
                APIKey.is_deleted == False
            )
        )

    name_result = await db.execute(name_query)
    if name_result.scalar_one_or_none():
        raise BusinessException(
            message="API密钥名称已存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 检查用户是否已有太多API密钥
    count_query = select(func.count(APIKey.id)).where(
        and_(
            APIKey.user_id == tenant_context.user.id,
            APIKey.is_active == True,
            APIKey.is_deleted == False
        )
    )
    count_result = await db.execute(count_query)
    active_count = count_result.scalar()

    # 获取租户的API密钥限制
    max_api_keys = 10  # 默认限制
    if tenant_context.tenant_id:
        tenant_query = select(Tenant).where(
            Tenant.id == tenant_context.tenant_id)
        tenant_result = await db.execute(tenant_query)
        tenant = tenant_result.scalar_one_or_none()
        if tenant:
            max_api_keys = tenant.max_api_keys

    if active_count >= max_api_keys:
        raise BusinessException(
            message=f"API密钥数量已达上限（{max_api_keys}个）",
            error_code=ErrorCode.INTERNAL_ERROR,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

    # 生成API密钥
    key_value = generate_api_key()

    # 设置过期时间
    expires_at = None
    if api_key_data.expires_days:
        expires_at = datetime.now(timezone.utc) + \
            timedelta(days=api_key_data.expires_days)

    # 创建API密钥记录
    api_key = APIKey(
        name=api_key_data.name,
        key=key_value,  # 在实际应用中应该存储哈希值
        user_id=tenant_context.user.id,
        tenant_id=tenant_context.tenant_id,
        expires_at=expires_at,
        description=api_key_data.description,
        is_active=True
    )

    db.add(api_key)
    await db.commit()
    await db.refresh(api_key)

    # 更新租户API密钥计数
    if tenant_context.tenant_id:
        tenant_query = select(Tenant).where(
            Tenant.id == tenant_context.tenant_id)
        tenant_result = await db.execute(tenant_query)
        tenant = tenant_result.scalar_one_or_none()
        if tenant:
            tenant.api_key_count = tenant.api_key_count + 1
            await db.commit()

    # 返回完整的API密钥（只在创建时返回）
    return {
        "message": "API密钥创建成功",
        "api_key": key_value,
        "api_key_id": api_key.id,
        "name": api_key.name,
        "expires_at": api_key.expires_at.isoformat() if api_key.expires_at else None,
        "warning": "请妥善保存此API密钥，系统不会再次显示完整密钥"
    }


@router.get("/{api_key_id}", response_model=SuccessResponse[APIKeySchema])
async def get_api_key(
    request: Request,
    api_key_id: int,
    tenant_context: TenantContext = Depends(get_tenant_context),
    db: AsyncSession = Depends(get_async_db)
):
    """获取API密钥详情"""

    # 查询API密钥
    query = select(APIKey).options(
        selectinload(APIKey.user),
        selectinload(APIKey.tenant)
    ).where(
        and_(
            APIKey.id == api_key_id,
            APIKey.is_deleted == False
        )
    )

    # 应用租户过滤
    tenant_filter = create_tenant_filter(tenant_context)
    query = tenant_filter(query, APIKey)

    # 非租户管理员只能查看自己的API密钥
    if not tenant_context.can_manage_tenant:
        query = query.where(APIKey.user_id == tenant_context.user.id)

    result = await db.execute(query)
    api_key = result.scalar_one_or_none()

    if not api_key:
        raise BusinessException(
            message="API密钥不存在或无权访问",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 转换为Pydantic模型
    api_key_data = APIKeySchema.model_validate(api_key)

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=api_key_data,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.put("/{api_key_id}", response_model=SuccessResponse[APIKeySchema])
@log_tenant_action("update_api_key")
async def update_api_key(
    request: Request,
    api_key_id: int,
    api_key_update: APIKeyUpdate,
    tenant_context: TenantContext = Depends(get_tenant_context),
    db: AsyncSession = Depends(get_async_db)
):
    """更新API密钥"""

    # 查询API密钥
    query = select(APIKey).where(
        and_(
            APIKey.id == api_key_id,
            APIKey.is_deleted == False
        )
    )

    # 应用租户过滤
    tenant_filter = create_tenant_filter(tenant_context)
    query = tenant_filter(query, APIKey)

    # 非租户管理员只能更新自己的API密钥
    if not tenant_context.can_manage_tenant:
        query = query.where(APIKey.user_id == tenant_context.user.id)

    result = await db.execute(query)
    api_key = result.scalar_one_or_none()

    if not api_key:
        raise BusinessException(
            message="API密钥不存在或无权访问",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 检查名称唯一性（如果要更新名称）
    update_data = api_key_update.model_dump(exclude_unset=True)
    if "name" in update_data and update_data["name"] != api_key.name:
        if tenant_context.tenant_id:
            name_query = select(APIKey).where(
                and_(
                    APIKey.tenant_id == tenant_context.tenant_id,
                    APIKey.user_id == api_key.user_id,
                    APIKey.name == update_data["name"],
                    APIKey.id != api_key_id,
                    APIKey.is_deleted == False
                )
            )
        else:
            name_query = select(APIKey).where(
                and_(
                    APIKey.tenant_id.is_(None),
                    APIKey.user_id == api_key.user_id,
                    APIKey.name == update_data["name"],
                    APIKey.id != api_key_id,
                    APIKey.is_deleted == False
                )
            )

        name_result = await db.execute(name_query)
        if name_result.scalar_one_or_none():
            raise BusinessException(
                message="API密钥名称已存在",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_400_BAD_REQUEST
            )

    # 更新API密钥信息
    update_data = api_key_update.model_dump(exclude_unset=True)

    for field, value in update_data.items():
        if field == "expires_days" and value:
            # 更新过期时间
            api_key.expires_at = datetime.now(
                timezone.utc) + timedelta(days=value)
        elif hasattr(api_key, field):
            setattr(api_key, field, value)

    await db.commit()
    await db.refresh(api_key)

    # 转换为Pydantic模型
    api_key_data = APIKeySchema.model_validate(api_key)

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=api_key_data,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.delete("/{api_key_id}")
async def delete_api_key(
    request: Request,
    api_key_id: int,
    current_user=Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """删除API密钥"""

    # 查询API密钥
    query = select(APIKey).where(APIKey.id == api_key_id)

    # 非管理员只能删除自己的API密钥
    if not current_user.get("is_superuser"):
        query = query.where(APIKey.user_id == current_user.get("id"))

    result = await db.execute(query)
    api_key = result.scalar_one_or_none()

    if not api_key:
        raise BusinessException(
            message="API密钥不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 软删除API密钥
    api_key.is_active = False
    api_key.deleted_at = datetime.now(timezone.utc)

    await db.commit()

    return {"message": "API密钥已删除", "api_key_id": api_key_id}


@router.post("/{api_key_id}/regenerate", response_model=dict)
async def regenerate_api_key(
    request: Request,
    api_key_id: int,
    current_user=Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """重新生成API密钥"""

    # 查询API密钥
    query = select(APIKey).where(APIKey.id == api_key_id)

    # 非管理员只能重新生成自己的API密钥
    if not current_user.get("is_superuser"):
        query = query.where(APIKey.user_id == current_user.get("id"))

    result = await db.execute(query)
    api_key = result.scalar_one_or_none()

    if not api_key:
        raise BusinessException(
            message="API密钥不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    if not api_key.is_active:
        raise BusinessException(
            message="无法重新生成已禁用的API密钥",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # 生成新的API密钥
    new_key_value = generate_api_key()
    api_key.key = new_key_value  # 在实际应用中应该存储哈希值
    api_key.last_used_at = None  # 重置使用时间

    await db.commit()

    return {
        "message": "API密钥重新生成成功",
        "api_key": new_key_value,
        "api_key_id": api_key.id,
        "warning": "请妥善保存新的API密钥，旧密钥已失效"
    }


@router.get("/verify/{api_key}")
async def verify_api_key(
    request: Request,
    api_key: str,
    db: AsyncSession = Depends(get_async_db)
):
    """验证API密钥并返回用户信息（用于网关认证）"""

    # 查找API密钥
    result = await db.execute(
        select(APIKey).where(
            APIKey.key == api_key,
            APIKey.is_active == True
        )
    )
    api_key_obj = result.scalar_one_or_none()

    if not api_key_obj:
        raise BusinessException(
            message="API密钥不存在或已失效",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 检查是否过期
    if api_key_obj.expires_at and api_key_obj.expires_at < datetime.now(timezone.utc):
        raise BusinessException(
            message="API密钥已过期",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 获取用户信息
    result = await db.execute(
        select(User).where(User.id == api_key_obj.user_id)
    )
    user = result.scalar_one_or_none()

    if not user:
        raise BusinessException(
            message="用户不存在",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 更新最后使用时间
    api_key_obj.last_used_at = datetime.now(timezone.utc)
    await db.commit()

    # 返回用户信息
    return {
        "id": user.id,
        "email": user.email,
        "is_active": user.is_active,
        "tier": "premium" if user.is_superuser else "free",
        "api_key_id": api_key_obj.id,
        "api_key_name": api_key_obj.name
    }
