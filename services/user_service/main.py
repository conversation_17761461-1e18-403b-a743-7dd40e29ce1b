"""
用户服务主应用程序
"""
import asyncio
import builtins
import sys
from contextlib import asynccontextmanager
from pathlib import Path

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi_offline import FastAPIOffline

from shared.database import init_db, close_db
from shared.logging_config import setup_user_service_logger
from shared.middleware.request_id import add_request_id_middleware
from shared.redis_simple import redis_client
from shared.utils.exception_handlers import register_all_exception_handlers

# 添加项目根目录到Python路径，支持直接运行
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 修复asyncio兼容性问题 - 必须在最开始导入
try:
    # 尝试相对导入
    from .config import settings
    from .api.api_keys import router as api_keys_router
    from .api.temporary_api_keys import router as temporary_api_keys_router
    from .api.permissions import router as permissions_router
    from .api.users import router as users_router
    from .api.auth import router as auth_router
    from .api.tenants import router as tenants_router
    from .api.tenant_management import router as tenant_management_router
    from .api import internal as internal_router
except ImportError:
    # 如果相对导入失败，使用绝对导入
    from services.user_service.config import settings
    from services.user_service.api.api_keys import router as api_keys_router
    from services.user_service.api.temporary_api_keys import router as temporary_api_keys_router
    from services.user_service.api.permissions import router as permissions_router
    from services.user_service.api.users import router as users_router
    from services.user_service.api.auth import router as auth_router
    from services.user_service.api.tenants import router as tenants_router
    from services.user_service.api.tenant_management import router as tenant_management_router
    from services.user_service.api import internal as internal_router
if not hasattr(asyncio, 'TimeoutError'):
    asyncio.TimeoutError = builtins.TimeoutError

# 初始化用户服务日志系统
logger = setup_user_service_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用程序生命周期事件"""
    # 启动阶段
    logger.info("正在启动用户服务...")
    try:
        # 导入所有模型以确保它们在Base.metadata中注册
        # 注意：先导入Permission，再导入Tenant，最后导入User，避免关系引用问题
        try:
            from .models.permission import Permission
            from .models.tenant import Tenant, TenantRole
            from .models.user import User, APIKey, TemporaryAPIKey, UserSession
        except ImportError:
            from services.user_service.models.permission import Permission
            from services.user_service.models.tenant import Tenant, TenantRole
            from services.user_service.models.user import User, APIKey, TemporaryAPIKey, UserSession
        logger.info("模型导入成功")

        # 初始化数据库连接
        await init_db()
        logger.info("数据库初始化完成")

        # 测试Redis连接
        await redis_client.set("health_check", "ok", expire=10)
        health_status = await redis_client.get("health_check")
        if health_status == "ok":
            logger.info("Redis连接成功")
        else:
            logger.warning("Redis连接失败")

        logger.info("用户服务启动成功")
    except Exception as e:
        logger.error(f"用户服务启动失败: {e}")
        raise

    yield

    # 关闭阶段
    logger.info("正在关闭用户服务...")
    try:
        await close_db()
        await redis_client.close()
        logger.info("用户服务关闭完成")
    except Exception as e:
        logger.error(f"关闭过程中出错: {e}")


# 创建FastAPI应用程序
app = FastAPIOffline(
    title=settings.PROJECT_NAME,
    version=settings.PROJECT_VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=f"{settings.API_V1_STR}/docs",
    redoc_url=f"{settings.API_V1_STR}/redoc",
    lifespan=lifespan
)

# 添加请求ID中间件（在其他中间件之前）
add_request_id_middleware(app)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册统一异常处理器
register_all_exception_handlers(app)


# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查端点 - 使用统一响应格式"""
    from shared.models.response import create_success_response, create_error_response

    try:
        # 测试Redis连接
        await redis_client.set("health_check", "ok", expire=5)
        redis_status = await redis_client.get("health_check")

        health_data = {
            "service": "user_service",
            "version": settings.PROJECT_VERSION,
            "status": "healthy",
            "components": {
                "redis": "connected" if redis_status == "ok" else "disconnected"
            }
        }

        return create_success_response(
            data=health_data,
            msg="健康检查完成"
        )
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return create_error_response(
            msg="健康检查失败",
            error_code="HEALTH_CHECK_FAILED",
            error_details={"error": str(e)}
        )


# 包含路由器
app.include_router(
    auth_router,
    prefix=f"{settings.API_V1_STR}/auth",
    tags=["认证"]
)

app.include_router(
    users_router,
    prefix=f"{settings.API_V1_STR}/users",
    tags=["用户管理"]
)

app.include_router(
    permissions_router,
    prefix=f"{settings.API_V1_STR}/permissions",
    tags=["权限管理"]
)

app.include_router(
    api_keys_router,
    prefix=f"{settings.API_V1_STR}/api-keys",
    tags=["API密钥管理"]
)

app.include_router(
    temporary_api_keys_router,
    prefix=f"{settings.API_V1_STR}",
    tags=["临时API密钥管理"]
)

app.include_router(
    tenants_router,
    prefix=f"{settings.API_V1_STR}/tenants",
    tags=["租户管理"]
)

app.include_router(
    tenant_management_router,
    prefix=f"{settings.API_V1_STR}/tenant-management",
    tags=["租户管理扩展"]
)

# 内部API路由（用于服务间调用）
app.include_router(
    internal_router.router,
    prefix="/internal",
    tags=["内部API"]
)

if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
