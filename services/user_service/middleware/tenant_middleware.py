"""
租户级别的中间件
"""
import time
from typing import Callable, Optional
from fastapi import Request, Response, HTTPException, status
from fastapi.responses import JSONResponse
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from shared.database import AsyncSessionLocal
from shared.logging_config import get_logger
from ..models.tenant import Tenant
from ..models.user import User

logger = get_logger(__name__)


class TenantMiddleware:
    """租户中间件 - 处理租户上下文和访问控制"""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        """中间件主要逻辑"""
        
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        request = Request(scope, receive)
        
        # 跳过不需要租户验证的路径
        if self._should_skip_tenant_check(request.url.path):
            await self.app(scope, receive, send)
            return
        
        # 处理租户上下文
        try:
            await self._process_tenant_context(request)
        except HTTPException as e:
            response = JSONResponse(
                status_code=e.status_code,
                content={"detail": e.detail}
            )
            await response(scope, receive, send)
            return
        except Exception as e:
            logger.error(f"租户中间件处理错误: {e}")
            response = JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"detail": "内部服务器错误"}
            )
            await response(scope, receive, send)
            return
        
        await self.app(scope, receive, send)
    
    def _should_skip_tenant_check(self, path: str) -> bool:
        """检查是否应该跳过租户验证"""
        skip_paths = [
            "/health",
            "/docs",
            "/redoc",
            "/openapi.json",
            "/api/v1/auth/login",
            "/api/v1/auth/register",
            "/api/v1/auth/refresh",
            "/internal/",  # 内部API
        ]
        
        for skip_path in skip_paths:
            if path.startswith(skip_path):
                return True
        
        return False
    
    async def _process_tenant_context(self, request: Request):
        """处理租户上下文"""
        
        # 从请求头或查询参数获取租户信息
        tenant_id = self._extract_tenant_id(request)
        
        if tenant_id:
            # 验证租户是否存在且可用
            async with AsyncSessionLocal() as db:
                await self._validate_tenant(tenant_id, db)
            
            # 将租户ID添加到请求状态
            request.state.tenant_id = tenant_id
        
        # 记录租户访问日志
        if hasattr(request.state, 'tenant_id'):
            logger.info(f"租户 {request.state.tenant_id} 访问 {request.url.path}")
    
    def _extract_tenant_id(self, request: Request) -> Optional[int]:
        """从请求中提取租户ID"""
        
        # 1. 从请求头获取
        tenant_header = request.headers.get("X-Tenant-ID")
        if tenant_header:
            try:
                return int(tenant_header)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的租户ID格式"
                )
        
        # 2. 从查询参数获取
        tenant_param = request.query_params.get("tenant_id")
        if tenant_param:
            try:
                return int(tenant_param)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的租户ID格式"
                )
        
        # 3. 从路径参数获取（如果路径包含tenant_id）
        if hasattr(request, 'path_params') and 'tenant_id' in request.path_params:
            try:
                return int(request.path_params['tenant_id'])
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的租户ID格式"
                )
        
        return None
    
    async def _validate_tenant(self, tenant_id: int, db: AsyncSession):
        """验证租户是否存在且可用"""
        
        query = select(Tenant).where(Tenant.id == tenant_id)
        result = await db.execute(query)
        tenant = result.scalar_one_or_none()
        
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="租户不存在"
            )
        
        if not tenant.is_available:
            reasons = []
            if not tenant.is_active:
                reasons.append("租户未激活")
            if tenant.is_suspended:
                reasons.append("租户已暂停")
            if tenant.is_expired:
                reasons.append("租户已过期")
            
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"租户不可用: {', '.join(reasons)}"
            )


class TenantActivityMiddleware:
    """租户活动跟踪中间件"""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        """中间件主要逻辑"""
        
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        request = Request(scope, receive)
        start_time = time.time()
        
        # 处理请求
        async def send_wrapper(message):
            if message["type"] == "http.response.start":
                # 记录租户活动
                await self._record_tenant_activity(request, start_time)
            await send(message)
        
        await self.app(scope, receive, send_wrapper)
    
    async def _record_tenant_activity(self, request: Request, start_time: float):
        """记录租户活动"""
        
        # 跳过不需要记录的路径
        if self._should_skip_activity_log(request.url.path):
            return
        
        try:
            tenant_id = getattr(request.state, 'tenant_id', None)
            if tenant_id:
                async with AsyncSessionLocal() as db:
                    # 更新租户最后活动时间
                    query = select(Tenant).where(Tenant.id == tenant_id)
                    result = await db.execute(query)
                    tenant = result.scalar_one_or_none()
                    
                    if tenant:
                        tenant.update_last_activity()
                        await db.commit()
                
                # 记录活动日志
                duration = time.time() - start_time
                logger.info(
                    f"租户活动 - 租户ID: {tenant_id}, "
                    f"路径: {request.url.path}, "
                    f"方法: {request.method}, "
                    f"耗时: {duration:.3f}s"
                )
        
        except Exception as e:
            logger.error(f"记录租户活动失败: {e}")
    
    def _should_skip_activity_log(self, path: str) -> bool:
        """检查是否应该跳过活动记录"""
        skip_paths = [
            "/health",
            "/docs",
            "/redoc",
            "/openapi.json",
        ]
        
        for skip_path in skip_paths:
            if path.startswith(skip_path):
                return True
        
        return False


class TenantRateLimitMiddleware:
    """租户级别的速率限制中间件"""
    
    def __init__(self, app):
        self.app = app
        self.rate_limit_cache = {}  # 简单的内存缓存，生产环境应使用Redis
    
    async def __call__(self, scope, receive, send):
        """中间件主要逻辑"""
        
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        request = Request(scope, receive)
        
        # 检查速率限制
        try:
            await self._check_rate_limit(request)
        except HTTPException as e:
            response = JSONResponse(
                status_code=e.status_code,
                content={"detail": e.detail}
            )
            await response(scope, receive, send)
            return
        
        await self.app(scope, receive, send)
    
    async def _check_rate_limit(self, request: Request):
        """检查租户速率限制"""
        
        tenant_id = getattr(request.state, 'tenant_id', None)
        if not tenant_id:
            return
        
        # 跳过不需要限制的路径
        if self._should_skip_rate_limit(request.url.path):
            return
        
        current_time = time.time()
        cache_key = f"tenant_{tenant_id}_rate_limit"
        
        # 获取当前计数
        if cache_key not in self.rate_limit_cache:
            self.rate_limit_cache[cache_key] = {
                'count': 0,
                'window_start': current_time
            }
        
        cache_data = self.rate_limit_cache[cache_key]
        
        # 检查时间窗口（1分钟）
        if current_time - cache_data['window_start'] > 60:
            # 重置计数器
            cache_data['count'] = 0
            cache_data['window_start'] = current_time
        
        # 增加计数
        cache_data['count'] += 1
        
        # 检查是否超过限制（每分钟1000次请求）
        if cache_data['count'] > 1000:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="租户请求频率过高，请稍后再试"
            )
    
    def _should_skip_rate_limit(self, path: str) -> bool:
        """检查是否应该跳过速率限制"""
        skip_paths = [
            "/health",
            "/docs",
            "/redoc",
            "/openapi.json",
        ]
        
        for skip_path in skip_paths:
            if path.startswith(skip_path):
                return True
        
        return False
