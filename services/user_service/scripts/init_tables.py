#!/usr/bin/env python3
"""
用户服务数据库表初始化脚本
"""
import asyncio
import sys
from pathlib import Path

from shared.database import get_async_engine, Base
from shared.logging_config import get_logger

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent.parent
sys.path.insert(0, str(project_root))

logger = get_logger(__name__)


async def init_user_service_tables(drop_existing: bool = False):
    """初始化用户服务相关的数据库表"""
    try:
        logger.info("开始初始化用户服务数据库表...")

        # 导入用户服务的所有模型
        from ..models.permission import Permission
        from ..models.user import User, APIKey, TemporaryAPIKey, UserSession
        from ..models.tenant import Tenant, TenantRole

        logger.info("用户服务模型导入成功")

        # 获取数据库引擎
        engine = get_async_engine()

        # 如果需要，先删除现有表
        if drop_existing:
            logger.warning("删除现有用户服务表...")
            async with engine.begin() as conn:
                # 只删除用户服务相关的表（注意删除顺序，先删除有外键依赖的表）
                await conn.run_sync(lambda sync_conn: Base.metadata.drop_all(
                    sync_conn,
                    tables=[
                        APIKey.__table__,
                        TemporaryAPIKey.__table__,
                        UserSession.__table__,
                        User.__table__,
                        TenantRole.__table__,
                        Tenant.__table__,
                        Permission.__table__
                    ]
                ))
            logger.info("现有用户服务表已删除")

        # 创建表
        logger.info("创建用户服务表...")
        async with engine.begin() as conn:
            # 只创建用户服务相关的表（注意创建顺序，先创建被引用的表）
            await conn.run_sync(lambda sync_conn: Base.metadata.create_all(
                sync_conn,
                tables=[
                    Permission.__table__,
                    Tenant.__table__,
                    TenantRole.__table__,
                    User.__table__,
                    APIKey.__table__,
                    TemporaryAPIKey.__table__,
                    UserSession.__table__
                ]
            ))

        logger.info("✅ 用户服务数据库表初始化完成")
        return True

    except Exception as e:
        logger.error(f"❌ 用户服务数据库表初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def init_default_permissions():
    """初始化默认权限"""
    try:
        from shared.database import AsyncSessionLocal
        from ..models.permission import Permission
        from sqlalchemy import select

        logger.info("开始初始化默认权限...")

        # 默认权限列表
        default_permissions = [
            {
                "name": "user:read",
                "display_name": "查看用户",
                "description": "查看用户信息的权限",
                "category": "user",
                "is_system": True
            },
            {
                "name": "user:write",
                "display_name": "管理用户",
                "description": "创建、更新、删除用户的权限",
                "category": "user",
                "is_system": True
            },
            {
                "name": "pet:read",
                "display_name": "查看宠物",
                "description": "查看宠物信息的权限",
                "category": "pet",
                "is_system": True
            },
            {
                "name": "pet:write",
                "display_name": "管理宠物",
                "description": "创建、更新、删除宠物的权限",
                "category": "pet",
                "is_system": True
            },
            {
                "name": "agent:read",
                "display_name": "查看智能体",
                "description": "查看智能体信息的权限",
                "category": "agent",
                "is_system": True
            },
            {
                "name": "agent:write",
                "display_name": "管理智能体",
                "description": "创建、更新、删除智能体的权限",
                "category": "agent",
                "is_system": True
            },
            {
                "name": "agent:execute",
                "display_name": "执行智能体",
                "description": "执行智能体的权限",
                "category": "agent",
                "is_system": True
            }
        ]

        async with AsyncSessionLocal() as db:
            created_count = 0
            for perm_data in default_permissions:
                # 检查权限是否已存在
                result = await db.execute(
                    select(Permission).where(
                        Permission.name == perm_data["name"])
                )
                existing_perm = result.scalar_one_or_none()

                if not existing_perm:
                    permission = Permission(**perm_data)
                    db.add(permission)
                    created_count += 1
                    logger.info(f"创建权限: {perm_data['name']}")

            if created_count > 0:
                await db.commit()
                logger.info(f"✅ 成功创建 {created_count} 个默认权限")
            else:
                logger.info("✅ 所有默认权限已存在，无需创建")

        return True

    except Exception as e:
        logger.error(f"❌ 初始化默认权限失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def create_default_tenant():
    """创建默认租户"""
    try:
        from shared.database import AsyncSessionLocal
        from ..models.tenant import Tenant, TenantRole, DEFAULT_TENANT_ROLES
        from sqlalchemy import select

        logger.info("开始创建默认租户...")

        async with AsyncSessionLocal() as db:
            # 检查默认租户是否已存在
            result = await db.execute(
                select(Tenant).where(Tenant.slug == "default")
            )
            existing_tenant = result.scalar_one_or_none()

            if not existing_tenant:
                # 创建默认租户
                default_tenant = Tenant(
                    name="默认租户",
                    slug="default",
                    display_name="默认租户",
                    description="系统默认租户，用于未指定租户的用户",
                    contact_email="<EMAIL>",
                    is_trial=False,
                    max_users=1000,
                    max_api_keys=100,
                    max_storage_mb=10240,  # 10GB
                    features_enabled=["basic", "advanced"],
                    settings={}
                )

                db.add(default_tenant)
                await db.commit()
                await db.refresh(default_tenant)

                # 创建默认租户角色
                for role_data in DEFAULT_TENANT_ROLES:
                    role = TenantRole(
                        tenant_id=default_tenant.id,
                        **role_data
                    )
                    db.add(role)

                await db.commit()

                logger.info(f"✅ 默认租户创建成功 (ID: {default_tenant.id})")
                return default_tenant.id
            else:
                logger.info("✅ 默认租户已存在，无需创建")
                return existing_tenant.id

    except Exception as e:
        logger.error(f"❌ 创建默认租户失败: {e}")
        import traceback
        traceback.print_exc()
        return None


async def create_admin_user():
    """创建管理员用户"""
    try:
        from shared.database import AsyncSessionLocal
        from ..models.user import User
        from shared.utils.security import get_password_hash
        from sqlalchemy import select

        logger.info("开始创建管理员用户...")

        async with AsyncSessionLocal() as db:
            # 检查管理员是否已存在
            result = await db.execute(
                select(User).where(User.username == "admin")
            )
            existing_admin = result.scalar_one_or_none()

            if not existing_admin:
                admin_user = User(
                    username="admin",
                    email="<EMAIL>",
                    first_name="系统",
                    last_name="管理员",
                    hashed_password=get_password_hash("admin123"),
                    is_active=True,
                    is_superuser=True,
                    is_verified=True
                )

                db.add(admin_user)
                await db.commit()
                await db.refresh(admin_user)

                logger.info(f"✅ 管理员用户创建成功 (ID: {admin_user.id})")
                logger.info("默认管理员账号: admin / admin123")
            else:
                logger.info("✅ 管理员用户已存在，无需创建")

        return True

    except Exception as e:
        logger.error(f"❌ 创建管理员用户失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="用户服务数据库初始化")
    parser.add_argument("--drop", action="store_true", help="删除现有表后重新创建")
    parser.add_argument(
        "--tables-only", action="store_true", help="只创建表，不初始化数据")

    args = parser.parse_args()

    logger.info("🎯 用户服务数据库初始化工具")
    logger.info("=" * 50)

    # 初始化表
    success = await init_user_service_tables(drop_existing=args.drop)
    if not success:
        sys.exit(1)

    if not args.tables_only:
        # 初始化默认数据
        success = await init_default_permissions()
        if not success:
            sys.exit(1)

        success = await create_admin_user()
        if not success:
            sys.exit(1)

    logger.info("🎉 用户服务数据库初始化完成！")


if __name__ == "__main__":
    asyncio.run(main())
