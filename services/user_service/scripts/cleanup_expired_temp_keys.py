"""
定时清理过期临时API密钥的脚本
"""
import asyncio
import sys
from datetime import datetime, timezone
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import select, and_

from shared.database import AsyncSessionLocal
from shared.logging_config import get_logger

logger = get_logger(__name__)


async def cleanup_expired_temporary_keys():
    """清理过期的临时API密钥"""
    try:
        logger.info("开始清理过期的临时API密钥...")

        # 导入模型
        try:
            from ..models.user import TemporaryAPIKey
        except ImportError:
            from services.user_service.models.user import TemporaryAPIKey

        # 获取数据库会话
        async with AsyncSessionLocal() as db:
            # 查找所有过期的临时API密钥
            current_time = datetime.now(timezone.utc)

            # 查找过期但未删除的临时密钥
            expired_query = select(TemporaryAPIKey).where(
                and_(
                    TemporaryAPIKey.expires_at < current_time,
                    TemporaryAPIKey.is_deleted == False
                )
            )

            result = await db.execute(expired_query)
            expired_keys = result.scalars().all()

            if not expired_keys:
                logger.info("没有找到过期的临时API密钥")
                return 0

            # 软删除过期的临时密钥
            deleted_count = 0
            for temp_key in expired_keys:
                temp_key.soft_delete()
                temp_key.is_active = False
                deleted_count += 1
                logger.info(f"删除过期临时密钥: {temp_key.name} (ID: {temp_key.id})")

            # 提交更改
            await db.commit()

            logger.info(f"✅ 成功清理了 {deleted_count} 个过期的临时API密钥")
            return deleted_count

    except Exception as e:
        logger.error(f"❌ 清理过期临时API密钥失败: {e}")
        import traceback
        traceback.print_exc()
        return -1


async def cleanup_usage_exceeded_temporary_keys():
    """清理使用次数超限的临时API密钥"""
    try:
        logger.info("开始清理使用次数超限的临时API密钥...")

        # 导入模型
        try:
            from ..models.user import TemporaryAPIKey
        except ImportError:
            from services.user_service.models.user import TemporaryAPIKey

        # 获取数据库会话
        async with AsyncSessionLocal() as db:
            # 查找使用次数超限的临时API密钥
            exceeded_query = select(TemporaryAPIKey).where(
                and_(
                    TemporaryAPIKey.max_usage_count.isnot(None),
                    TemporaryAPIKey.usage_count >= TemporaryAPIKey.max_usage_count,
                    TemporaryAPIKey.is_deleted == False
                )
            )

            result = await db.execute(exceeded_query)
            exceeded_keys = result.scalars().all()

            if not exceeded_keys:
                logger.info("没有找到使用次数超限的临时API密钥")
                return 0

            # 软删除使用次数超限的临时密钥
            deleted_count = 0
            for temp_key in exceeded_keys:
                temp_key.soft_delete()
                temp_key.is_active = False
                deleted_count += 1
                logger.info(
                    f"删除使用次数超限临时密钥: {temp_key.name} (ID: {temp_key.id}, 使用次数: {temp_key.usage_count}/{temp_key.max_usage_count})")

            # 提交更改
            await db.commit()

            logger.info(f"✅ 成功清理了 {deleted_count} 个使用次数超限的临时API密钥")
            return deleted_count

    except Exception as e:
        logger.error(f"❌ 清理使用次数超限临时API密钥失败: {e}")
        import traceback
        traceback.print_exc()
        return -1


async def cleanup_old_deleted_temporary_keys(days_old: int = 30):
    """清理已软删除超过指定天数的临时API密钥（硬删除）"""
    try:
        logger.info(f"开始清理已删除超过 {days_old} 天的临时API密钥...")

        # 导入模型
        try:
            from ..models.user import TemporaryAPIKey
        except ImportError:
            from services.user_service.models.user import TemporaryAPIKey

        # 获取数据库会话
        async with AsyncSessionLocal() as db:
            # 计算截止时间
            from datetime import timedelta
            cutoff_time = datetime.now(timezone.utc) - timedelta(days=days_old)

            # 查找已软删除且超过指定天数的临时API密钥
            old_deleted_query = select(TemporaryAPIKey).where(
                and_(
                    TemporaryAPIKey.is_deleted == True,
                    TemporaryAPIKey.deleted_at < cutoff_time
                )
            )

            result = await db.execute(old_deleted_query)
            old_deleted_keys = result.scalars().all()

            if not old_deleted_keys:
                logger.info(f"没有找到已删除超过 {days_old} 天的临时API密钥")
                return 0

            # 硬删除这些记录
            deleted_count = 0
            for temp_key in old_deleted_keys:
                await db.delete(temp_key)
                deleted_count += 1
                logger.info(f"硬删除临时密钥: {temp_key.name} (ID: {temp_key.id})")

            # 提交更改
            await db.commit()

            logger.info(f"✅ 成功硬删除了 {deleted_count} 个旧的临时API密钥记录")
            return deleted_count

    except Exception as e:
        logger.error(f"❌ 清理旧的已删除临时API密钥失败: {e}")
        import traceback
        traceback.print_exc()
        return -1


async def main():
    """主函数"""
    logger.info("开始临时API密钥清理任务...")

    try:
        # 清理过期的临时密钥
        expired_count = await cleanup_expired_temporary_keys()

        # 清理使用次数超限的临时密钥
        exceeded_count = await cleanup_usage_exceeded_temporary_keys()

        # 清理已删除超过30天的临时密钥（硬删除）
        old_deleted_count = await cleanup_old_deleted_temporary_keys(30)

        total_cleaned = expired_count + exceeded_count + old_deleted_count
        logger.info(f"✅ 临时API密钥清理任务完成，共清理 {total_cleaned} 个密钥")

        return total_cleaned

    except Exception as e:
        logger.error(f"❌ 临时API密钥清理任务失败: {e}")
        return -1


if __name__ == "__main__":
    # 运行清理任务
    result = asyncio.run(main())

    if result >= 0:
        print(f"清理任务完成，共清理 {result} 个临时API密钥")
        sys.exit(0)
    else:
        print("清理任务失败")
        sys.exit(1)
