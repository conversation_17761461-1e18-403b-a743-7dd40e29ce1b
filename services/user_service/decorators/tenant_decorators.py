"""
租户权限装饰器
"""
from functools import wraps
from typing import List, Optional, Callable, Any
from fastapi import HTTPException, status, Depends
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from shared.database import get_async_db
from ..models.user import User
from ..models.tenant import Tenant
from ..utils.tenant_auth import TenantContext, get_tenant_context


def require_tenant_permission(permission: str):
    """要求特定租户权限的装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取tenant_context
            tenant_context = kwargs.get('tenant_context')
            if not tenant_context or not isinstance(tenant_context, TenantContext):
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="缺少租户上下文"
                )
            
            if not tenant_context.has_permission(permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"缺少权限: {permission}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_tenant_permissions(*permissions: str):
    """要求多个租户权限的装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取tenant_context
            tenant_context = kwargs.get('tenant_context')
            if not tenant_context or not isinstance(tenant_context, TenantContext):
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="缺少租户上下文"
                )
            
            missing_permissions = []
            for permission in permissions:
                if not tenant_context.has_permission(permission):
                    missing_permissions.append(permission)
            
            if missing_permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"缺少权限: {', '.join(missing_permissions)}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_tenant_admin(func: Callable) -> Callable:
    """要求租户管理员权限的装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # 从kwargs中获取tenant_context
        tenant_context = kwargs.get('tenant_context')
        if not tenant_context or not isinstance(tenant_context, TenantContext):
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="缺少租户上下文"
            )
        
        if not tenant_context.can_manage_tenant:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要租户管理员或全局管理员权限"
            )
        
        return await func(*args, **kwargs)
    return wrapper


def require_global_admin(func: Callable) -> Callable:
    """要求全局管理员权限的装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # 从kwargs中获取tenant_context
        tenant_context = kwargs.get('tenant_context')
        if not tenant_context or not isinstance(tenant_context, TenantContext):
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="缺少租户上下文"
            )
        
        if not tenant_context.is_global_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要全局管理员权限"
            )
        
        return await func(*args, **kwargs)
    return wrapper


def validate_tenant_access(tenant_id_param: str = "tenant_id"):
    """验证租户访问权限的装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取tenant_context和tenant_id
            tenant_context = kwargs.get('tenant_context')
            tenant_id = kwargs.get(tenant_id_param)
            
            if not tenant_context or not isinstance(tenant_context, TenantContext):
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="缺少租户上下文"
                )
            
            if tenant_id is None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="缺少租户ID参数"
                )
            
            if not tenant_context.can_access_tenant(tenant_id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权访问此租户"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def validate_user_access(user_id_param: str = "user_id"):
    """验证用户访问权限的装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取tenant_context、user_id和db
            tenant_context = kwargs.get('tenant_context')
            user_id = kwargs.get(user_id_param)
            db = kwargs.get('db')
            
            if not tenant_context or not isinstance(tenant_context, TenantContext):
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="缺少租户上下文"
                )
            
            if user_id is None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="缺少用户ID参数"
                )
            
            if not db:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="缺少数据库会话"
                )
            
            # 查询目标用户的租户信息
            target_user_query = select(User).where(User.id == user_id)
            result = await db.execute(target_user_query)
            target_user = result.scalar_one_or_none()
            
            if not target_user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="用户不存在"
                )
            
            if not tenant_context.can_access_user(user_id, target_user.tenant_id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权访问此用户"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def check_tenant_resource_limit(resource_type: str):
    """检查租户资源限制的装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取tenant_context和db
            tenant_context = kwargs.get('tenant_context')
            db = kwargs.get('db')
            
            if not tenant_context or not isinstance(tenant_context, TenantContext):
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="缺少租户上下文"
                )
            
            if not db:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="缺少数据库会话"
                )
            
            # 全局管理员跳过资源限制检查
            if tenant_context.is_global_admin:
                return await func(*args, **kwargs)
            
            if not tenant_context.tenant_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="缺少租户信息"
                )
            
            # 查询租户信息
            tenant_query = select(Tenant).where(Tenant.id == tenant_context.tenant_id)
            result = await db.execute(tenant_query)
            tenant = result.scalar_one_or_none()
            
            if not tenant:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="租户不存在"
                )
            
            # 检查资源限制
            if resource_type == "user" and not tenant.can_add_user():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"已达到用户数量上限 ({tenant.max_users})"
                )
            elif resource_type == "api_key" and not tenant.can_add_api_key():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"已达到API密钥数量上限 ({tenant.max_api_keys})"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def log_tenant_action(action: str):
    """记录租户操作的装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            from shared.logging_config import get_logger
            logger = get_logger(__name__)
            
            # 从kwargs中获取tenant_context
            tenant_context = kwargs.get('tenant_context')
            
            if tenant_context and isinstance(tenant_context, TenantContext):
                logger.info(
                    f"租户操作 - 动作: {action}, "
                    f"租户ID: {tenant_context.tenant_id}, "
                    f"用户ID: {tenant_context.user.id if tenant_context.user else None}, "
                    f"角色: {tenant_context.tenant_role.name if tenant_context.tenant_role else None}"
                )
            
            try:
                result = await func(*args, **kwargs)
                
                if tenant_context and isinstance(tenant_context, TenantContext):
                    logger.info(
                        f"租户操作成功 - 动作: {action}, "
                        f"租户ID: {tenant_context.tenant_id}"
                    )
                
                return result
            
            except Exception as e:
                if tenant_context and isinstance(tenant_context, TenantContext):
                    logger.error(
                        f"租户操作失败 - 动作: {action}, "
                        f"租户ID: {tenant_context.tenant_id}, "
                        f"错误: {str(e)}"
                    )
                raise
        
        return wrapper
    return decorator
