#!/usr/bin/env python3
"""
应用服务数据库表初始化脚本
"""
from shared.logging_config import get_logger
from shared.database import get_async_engine, Base
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent.parent
sys.path.insert(0, str(project_root))


logger = get_logger(__name__)


async def init_app_service_tables(drop_existing: bool = False):
    """初始化应用服务相关的数据库表"""
    try:
        logger.info("开始初始化应用服务数据库表...")

        # 导入应用服务的所有模型
        from ..models.agent import Agent, AgentPermission, AgentExecution
        from ..models.conversation import Conversation, Message, ConversationContext

        logger.info("应用服务模型导入成功")

        # 获取数据库引擎
        engine = get_async_engine()

        # 如果需要，先删除现有表
        if drop_existing:
            logger.warning("删除现有应用服务表...")
            async with engine.begin() as conn:
                # 只删除应用服务相关的表
                await conn.run_sync(lambda sync_conn: Base.metadata.drop_all(
                    sync_conn,
                    tables=[
                        ConversationContext.__table__,
                        Message.__table__,
                        Conversation.__table__,
                        AgentExecution.__table__,
                        AgentPermission.__table__,
                        Agent.__table__
                    ]
                ))
            logger.info("现有应用服务表已删除")

        # 创建表
        logger.info("创建应用服务表...")
        async with engine.begin() as conn:
            # 只创建应用服务相关的表
            await conn.run_sync(lambda sync_conn: Base.metadata.create_all(
                sync_conn,
                tables=[
                    Agent.__table__,
                    AgentPermission.__table__,
                    AgentExecution.__table__
                ]
            ))

        logger.info("✅ 应用服务数据库表初始化完成")
        return True

    except Exception as e:
        logger.error(f"❌ 应用服务数据库表初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def init_system_agents():
    """初始化系统级智能体"""
    try:
        from shared.database import AsyncSessionLocal
        from ..models.agent import Agent, AgentType, AgentStatus, SystemAgentCategory
        from sqlalchemy import select

        logger.info("开始初始化系统级智能体...")

        # 系统智能体配置
        system_agents = [
            {
                "agent_name": "ai_diagnosis",
                "display_name": "AI问诊智能体",
                "description": "基于宠物症状和体征，提供专业的诊断建议和治疗方案",
                "agent_type": AgentType.SYSTEM,
                "system_category": SystemAgentCategory.DIAGNOSIS,
                "status": AgentStatus.ACTIVE,
                "is_public": True,
                "prompt_template": "你是一位专业的宠物医生...",
                "config": {
                    "model": "ds-vet-answer-32B",
                    "temperature": 0.7,
                    "max_tokens": 2000
                }
            },
            {
                "agent_name": "ai_vision",
                "display_name": "AI视觉识别智能体",
                "description": "通过图像分析识别宠物健康状况、疾病症状和异常情况",
                "agent_type": AgentType.SYSTEM,
                "system_category": SystemAgentCategory.VISION,
                "status": AgentStatus.ACTIVE,
                "is_public": True,
                "prompt_template": "你是一位专业的宠物医生，擅长通过图像分析...",
                "config": {
                    "model": "ds-vet-answer-32B",
                    "temperature": 0.5,
                    "max_tokens": 1500
                }
            },
            {
                "agent_name": "ai_report_generation",
                "display_name": "AI报告生成智能体",
                "description": "根据宠物健康数据生成专业的医疗报告和健康评估",
                "agent_type": AgentType.SYSTEM,
                "system_category": SystemAgentCategory.REPORT_GENERATION,
                "status": AgentStatus.ACTIVE,
                "is_public": True,
                "prompt_template": "你是一位专业的宠物医生，负责生成医疗报告...",
                "config": {
                    "model": "ds-vet-answer-32B",
                    "temperature": 0.3,
                    "max_tokens": 3000
                }
            },
            {
                "agent_name": "ai_report_analysis",
                "display_name": "AI报告解读智能体",
                "description": "解读医疗报告，提供通俗易懂的解释和建议",
                "agent_type": AgentType.SYSTEM,
                "system_category": SystemAgentCategory.REPORT_ANALYSIS,
                "status": AgentStatus.ACTIVE,
                "is_public": True,
                "prompt_template": "你是一位专业的宠物医生，擅长解读医疗报告...",
                "config": {
                    "model": "ds-vet-answer-32B",
                    "temperature": 0.4,
                    "max_tokens": 2500
                }
            }
        ]

        async with AsyncSessionLocal() as db:
            created_count = 0
            for agent_data in system_agents:
                # 检查智能体是否已存在
                result = await db.execute(
                    select(Agent).where(Agent.agent_name ==
                                        agent_data["agent_name"])
                )
                existing_agent = result.scalar_one_or_none()

                if not existing_agent:
                    agent = Agent(**agent_data)
                    db.add(agent)
                    created_count += 1
                    logger.info(f"创建系统智能体: {agent_data['display_name']}")
                else:
                    logger.info(f"系统智能体已存在: {agent_data['display_name']}")

            if created_count > 0:
                await db.commit()
                logger.info(f"✅ 成功创建 {created_count} 个系统智能体")
            else:
                logger.info("✅ 所有系统智能体已存在，无需创建")

        return True

    except Exception as e:
        logger.error(f"❌ 初始化系统智能体失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="应用服务数据库初始化")
    parser.add_argument("--drop", action="store_true", help="删除现有表后重新创建")
    parser.add_argument(
        "--tables-only", action="store_true", help="只创建表，不初始化数据")

    args = parser.parse_args()

    logger.info("🎯 应用服务数据库初始化工具")
    logger.info("=" * 50)

    # 初始化表
    success = await init_app_service_tables(drop_existing=args.drop)
    if not success:
        sys.exit(1)

    if not args.tables_only:
        # 初始化默认数据
        success = await init_system_agents()
        if not success:
            sys.exit(1)

    logger.info("🎉 应用服务数据库初始化完成！")


if __name__ == "__main__":
    asyncio.run(main())
