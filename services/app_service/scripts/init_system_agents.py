#!/usr/bin/env python3
"""
初始化系统级智能体脚本
"""
from shared.logging_config import get_app_service_logger
from services.app_service.schemas.agent import AgentCreate
from services.app_service.services.agent_service import AgentService
from services.app_service.models.agent import Agent, AgentType, AgentStatus, SystemAgentCategory
from shared.database import AsyncSessionLocal
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))


logger = get_app_service_logger()


async def create_system_agent(
    name: str,
    display_name: str,
    description: str,
    category: SystemAgentCategory,
    prompt_template: str,
    config: dict = None
) -> bool:
    """创建系统级智能体"""
    try:
        async with AsyncSessionLocal() as db:
            agent_service = AgentService(db)

            # 检查是否已存在
            from sqlalchemy import select, and_
            query = select(Agent).where(
                and_(
                    Agent.name == name,
                    Agent.agent_type == AgentType.SYSTEM,
                    Agent.is_deleted == False
                )
            )
            result = await db.execute(query)
            existing_agent = result.scalar_one_or_none()

            if existing_agent:
                logger.info(f"系统智能体已存在: {name}")
                return True

            # 创建智能体数据
            agent_data = AgentCreate(
                name=name,
                display_name=display_name,
                description=description,
                agent_type=AgentType.SYSTEM,
                system_category=category,
                is_public=True,
                config=config or {},
                prompt_template=prompt_template
            )

            # 创建智能体
            agent = await agent_service.create_agent(agent_data, owner_id=None)

            # 激活智能体
            agent.status = AgentStatus.ACTIVE
            await db.commit()

            logger.info(f"创建系统智能体成功: {name} (ID: {agent.id})")
            return True

    except Exception as e:
        logger.error(f"创建系统智能体失败 {name}: {e}")
        return False


async def init_diagnosis_agent():
    """初始化AI问诊智能体"""
    prompt_template = """你是一位专业的宠物医生AI助手，专门为宠物主人提供初步的健康咨询和诊断建议。

请根据以下宠物信息和症状描述，提供专业的分析和建议：

宠物信息：
- 品种：{pet_breed}
- 年龄：{pet_age}
- 性别：{pet_gender}
- 体重：{pet_weight}
- 既往病史：{medical_history}

当前症状：
{symptoms}

补充信息：
{additional_info}

请按照以下格式提供分析：

## 症状分析
[对症状的专业分析]

## 可能的诊断
[列出可能的疾病或健康问题，按可能性排序]

## 建议措施
[提供具体的护理建议和注意事项]

## 就医建议
[是否需要立即就医，以及建议的检查项目]

## 预防措施
[未来的预防建议]

注意：此分析仅供参考，不能替代专业兽医的诊断。如症状严重或持续，请及时就医。"""

    config = {
        "model": "ds-vet-answer-32B",
        "temperature": 0.7,
        "max_tokens": 2048
    }

    return await create_system_agent(
        name="ai_diagnosis",
        display_name="AI问诊智能体",
        description="专业的宠物健康问诊AI助手，能够分析宠物症状并提供初步诊断建议",
        category=SystemAgentCategory.DIAGNOSIS,
        prompt_template=prompt_template,
        config=config
    )


async def init_vision_agent():
    """初始化AI视觉识别智能体"""
    prompt_template = """你是一位专业的宠物医疗AI视觉分析专家，能够分析宠物相关的图像并提供专业的医疗建议。

分析类型：{analysis_type}

请仔细观察图像中的以下方面：
1. 宠物的整体状态和精神状况
2. 皮肤、毛发的健康状况
3. 眼部、鼻部、口腔的异常情况
4. 体态、姿势是否正常
5. 任何可见的外伤、肿胀或异常
6. 环境因素对宠物健康的影响

额外上下文信息：
{additional_context}

请按照以下格式提供分析：

## 图像概述
[描述图像中看到的宠物和环境情况]

## 健康状况评估
[基于视觉观察的健康状况分析]

## 发现的异常
[列出任何观察到的异常情况]

## 专业建议
[基于观察结果提供的建议]

## 进一步检查建议
[建议进行的进一步检查或就医建议]

注意：此分析基于图像观察，不能替代现场专业兽医检查。如发现异常，请及时就医。"""

    config = {
        "model": "ds-vet-answer-32B",
        "vision_model": "gpt-4-vision-preview",
        "temperature": 0.7,
        "max_tokens": 2048
    }

    return await create_system_agent(
        name="ai_vision",
        display_name="AI视觉识别智能体",
        description="专业的宠物图像分析AI助手，能够识别和分析宠物健康状况",
        category=SystemAgentCategory.VISION,
        prompt_template=prompt_template,
        config=config
    )


async def init_report_generation_agent():
    """初始化AI报告生成智能体"""
    prompt_template = """你是一位专业的宠物医疗报告生成专家，能够根据宠物的健康数据生成专业、详细的医疗报告。

报告类型：{report_type}
生成日期：{generation_date}

宠物基本信息：
{pet_basic_info}

健康数据：
{health_data}

请根据以上信息生成一份专业的{report_type}报告，报告应包含以下部分：

## 宠物基本信息
[整理和展示宠物的基本信息]

## 数据概览
[对提供的健康数据进行概览性总结]

## 详细分析
[根据报告类型进行深入的数据分析]

## 健康评估
[基于数据给出健康状况评估]

## 风险提示
[识别潜在的健康风险]

## 建议措施
[提供具体的护理和预防建议]

## 后续计划
[制定后续的健康管理计划]

请确保报告内容专业、准确、易懂，适合宠物主人阅读理解。"""

    config = {
        "model": "ds-vet-answer-32B",
        "temperature": 0.6,
        "max_tokens": 3000
    }

    return await create_system_agent(
        name="ai_report_generation",
        display_name="AI报告生成智能体",
        description="专业的宠物健康报告生成AI助手，能够根据健康数据生成各类医疗报告",
        category=SystemAgentCategory.REPORT_GENERATION,
        prompt_template=prompt_template,
        config=config
    )


async def init_report_analysis_agent():
    """初始化AI报告解读智能体"""
    prompt_template = """你是一位专业的宠物医疗报告解读专家，能够分析和解读各种宠物医疗报告，为宠物主人提供通俗易懂的解释。

文件类型：{file_type}
分析重点：{analysis_focus}

报告内容：
{report_content}

请按照以下格式提供详细的报告解读：

## 报告概述
[总结报告的基本信息和检查项目]

## 关键指标解读
[解释重要的检查指标和数值含义]

## 异常发现
[指出任何异常或需要关注的发现]

## 健康状况评估
[基于报告内容评估宠物的整体健康状况]

## 治疗建议
[根据报告结果提供治疗或护理建议]

## 后续跟进
[建议的后续检查或监测计划]

## 注意事项
[宠物主人需要特别注意的事项]

请用通俗易懂的语言解释专业术语，帮助宠物主人更好地理解报告内容。"""

    config = {
        "model": "ds-vet-answer-32B",
        "temperature": 0.7,
        "max_tokens": 2500
    }

    return await create_system_agent(
        name="ai_report_analysis",
        display_name="AI报告解读智能体",
        description="专业的宠物医疗报告解读AI助手，能够解读各种医疗报告并提供通俗易懂的解释",
        category=SystemAgentCategory.REPORT_ANALYSIS,
        prompt_template=prompt_template,
        config=config
    )


async def main():
    """主函数"""
    logger.info("开始初始化系统级智能体...")

    success_count = 0
    total_count = 4

    # 初始化各个系统智能体
    if await init_diagnosis_agent():
        success_count += 1

    if await init_vision_agent():
        success_count += 1

    if await init_report_generation_agent():
        success_count += 1

    if await init_report_analysis_agent():
        success_count += 1

    logger.info(f"系统级智能体初始化完成: {success_count}/{total_count} 成功")

    if success_count == total_count:
        logger.info("所有系统级智能体初始化成功！")
        return True
    else:
        logger.error(f"部分系统级智能体初始化失败: {total_count - success_count} 个失败")
        return False


if __name__ == "__main__":
    asyncio.run(main())
