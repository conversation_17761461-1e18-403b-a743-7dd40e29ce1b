# 宠物医疗平台 - 应用服务（AI智能体）
FROM python:3.11

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 配置pip使用清华镜像源
COPY pip.conf /etc/pip.conf

# 安装Python依赖包
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

# 复制共享模块和服务代码
COPY shared/ ./shared/
COPY services/app_service/ ./services/app_service/

# 创建日志目录
RUN mkdir -p /app/logs

# 创建非root用户
RUN adduser --disabled-password --gecos '' appuser \
    && chown -R appuser:appuser /app
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8003/health || exit 1

# 暴露端口
EXPOSE 8003

# 启动命令
CMD ["uvicorn", "services.app_service.main:app", "--host", "0.0.0.0", "--port", "8003"]
