from enum import Enum
from typing import Optional, List

from pydantic import BaseModel, Field

class PetGender(str, Enum):
    """宠物性别枚举"""
    MALE = "male"
    FEMALE = "female"
    UNKNOWN = "unknown"

class BetaCatPetInfo(BaseModel):
    # 基础信息
    pet_id: str = Field(..., description="宠物ID")
    name: Optional[str] = Field(None, description="宠物名称")
    category_name: Optional[str] = Field(None, description="物种")
    kind_name: Optional[str] = Field(None, description="宠物品种")
    birthday: Optional[str] = Field(None, description="出生日期")
    gender: Optional[PetGender] = Field(None, description="宠物性别")
    weight: Optional[str] = Field(None, description="体重")


class BetaCatPetOtherInfo(BaseModel):
    pet_symptoms: Optional[str] = Field(None, description="宠物症状信息")
    context: Optional[str] = Field(None, description="宠物病史信息")
    pet_signs: Optional[str] = Field(None, description="宠物的体征信息")
    pet_spirit: Optional[str] = Field(None, description="宠物精神状态")
    pet_visual: Optional[str] = Field(None, description="可视粘膜颜色")
    pet_oral: Optional[str] = Field(None, description="口腔情况")
    pet_skin: Optional[str] = Field(None, description="皮肤情况")
    pet_heart: Optional[str] = Field(None, description="心音听诊断")
    pet_abdominal: Optional[str] = Field(None, description="腹部触诊")
    pet_lymph: Optional[str] = Field(None, description="淋巴")


class BetaCatCdssAgentInput(BaseModel):
    request_id: str = Field(..., description="请求ID")
    pet_info: Optional[BetaCatPetInfo] = Field(None, description="宠物基本数据")
    pet_other_info: Optional[BetaCatPetOtherInfo] = Field(None, description="宠物补充数据")
    stream: Optional[bool] = Field(False, description="是否流式返回结果")


class BetaCatCdssAgentOutput(BaseModel):
    request_id: str = Field(..., description="请求ID")
    content: str = Field(..., description="回复内容")
    pushTime: int = Field(..., description="推送时间，毫秒")
    msgType: int = Field(..., description="消息类型:1-lab，2-cdss")


class BetaCatPetExamItem(BaseModel):
    name: Optional[str] = Field(None, description="检测的项目名称，如:WBC")
    unit: Optional[str] = Field(None, description="单位，如:%，10*9/L")
    value: Optional[str] = Field(None, description="检测结果，如:23")
    range: Optional[str] = Field(None, description="参考范围，如:3-17")


class BetaCatLabAgentInput(BaseModel):
    request_id: str = Field(..., description="请求ID")
    pet_info: Optional[BetaCatPetInfo] = Field(None, description="宠物基本数据")
    lab_project: Optional[str] = Field(None, description="实验室检查项目:血常规")
    instrument_info: Optional[str] = Field(None, description="设备名称")
    report_id: Optional[str] = Field(None, description="报告ID")
    item_values: Optional[List[BetaCatPetExamItem]] = Field(None, description="检查的值")
    stream: Optional[bool] = Field(False, description="是否流式返回结果")
