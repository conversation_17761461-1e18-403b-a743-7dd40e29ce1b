"""
会话管理相关的Pydantic模式
"""
from datetime import datetime
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field, field_validator


# 移除枚举导入，现在使用整数常量


# ============ 会话相关模式 ============

class ConversationBase(BaseModel):
    """会话基础模式"""
    title: str = Field(..., min_length=1, max_length=200, description="会话标题")
    conversation_id: Optional[str] = Field(None,max_length=64, description="会话ID")
    conversation_type: Union[int, str] = Field(...,
                                               description="会话类型: 1=diagnosis, 2=general 或 'diagnosis', 'general'")
    config: Optional[Dict[str, Any]] = Field(None, description="会话配置参数")
    context_window_size: int = Field(10, ge=1, le=50, description="上下文窗口大小")
    max_messages: int = Field(100, ge=10, le=1000, description="最大消息数量")
    expires_in_hours: Optional[int] = Field(
        None, ge=1, le=8760, description="过期时间(小时)")

    @field_validator('conversation_type')
    @classmethod
    def validate_conversation_type(cls, v):
        """验证并转换会话类型"""
        if isinstance(v, str):
            # 字符串到整数的映射
            string_to_int = {
                'diagnosis': 1,
                'general': 2
            }
            if v.lower() in string_to_int:
                return string_to_int[v.lower()]
            else:
                raise ValueError(
                    f"无效的会话类型字符串: {v}，支持的值: {list(string_to_int.keys())}")
        elif isinstance(v, int):
            # 验证整数值是否有效
            if v in [1, 2]:
                return v
            else:
                raise ValueError(f"无效的会话类型整数: {v}，支持的值: 1, 2")
        else:
            raise ValueError(f"会话类型必须是字符串或整数，收到: {type(v)}")


class ConversationCreate(ConversationBase):
    """创建会话请求"""
    primary_agent_id: Optional[int] = Field(None, description="主要智能体ID")
    initial_context: Optional[Dict[str, Any]] = Field(None, description="初始上下文")
    enable_ocr: bool = Field(True, description="启用OCR功能")
    auto_context: bool = Field(True, description="自动管理上下文")
    extend: Optional[Dict[str, Any]] = Field(None, description="扩展字段")


class ConversationUpdate(BaseModel):
    """更新会话请求"""
    title: Optional[str] = Field(
        None, min_length=1, max_length=200, description="会话标题")
    status: Optional[int] = Field(
        None, description="会话状态: 1=active, 2=paused, 3=completed, 4=archived, 5=expired")
    config: Optional[Dict[str, Any]] = Field(None, description="会话配置参数")
    context_window_size: Optional[int] = Field(
        None, ge=1, le=50, description="上下文窗口大小")
    max_messages: Optional[int] = Field(
        None, ge=10, le=1000, description="最大消息数量")


class ConversationResponse(ConversationBase):
    """会话响应"""
    id: int
    status: int
    user_id: int
    tenant_id: Optional[int]
    primary_agent_id: Optional[int]
    last_activity_at: datetime
    expires_at: Optional[datetime]
    message_count: int
    total_tokens: int
    created_at: datetime
    updated_at: datetime

    # 关联数据
    primary_agent: Optional[Dict[str, Any]] = None
    recent_messages: List[Dict[str, Any]] = []
    context_summary: Optional[str] = None

    class Config:
        from_attributes = True


class ConversationListResponse(BaseModel):
    """会话列表响应"""
    items: List[ConversationResponse]
    total: int
    page: int
    size: int
    pages: int


# ============ 消息相关模式 ============

class MessageBase(BaseModel):
    """消息基础模式"""
    content: str = Field(..., min_length=1, description="消息内容")
    message_type: Union[int, str] = Field(
        1, description="消息类型: 1=text, 2=image, 3=file 或 'text', 'image', 'file'")
    metadata: Optional[Dict[str, Any]] = Field(None, description="消息元数据")

    @field_validator('message_type')
    @classmethod
    def validate_message_type(cls, v):
        """验证并转换消息类型"""
        if isinstance(v, str):
            # 字符串到整数的映射
            string_to_int = {
                'text': 1,
                'image': 2,
                'file': 3
            }
            if v.lower() in string_to_int:
                return string_to_int[v.lower()]
            else:
                raise ValueError(
                    f"无效的消息类型字符串: {v}，支持的值: {list(string_to_int.keys())}")
        elif isinstance(v, int):
            # 验证整数值是否有效
            if v in [1, 2, 3]:
                return v
            else:
                raise ValueError(f"无效的消息类型整数: {v}，支持的值: 1, 2, 3")
        else:
            raise ValueError(f"消息类型必须是字符串或整数，收到: {type(v)}")


class MessageCreate(MessageBase):
    """发送消息请求"""
    # 附件支持（主要是图片）
    attachments: Optional[List[Dict[str, Any]]
    ] = Field(None, description="附件信息")

    # OCR相关
    ocr_results: Optional[List[Dict[str, Any]]] = Field(
        None, description="前端传入的OCR识别结果")

    # 处理配置
    stream: bool = Field(True, description="是否流式响应（默认开启）")

    # 上下文控制
    include_context: bool = Field(True, description="是否包含上下文")
    context_window: Optional[int] = Field(None, description="上下文窗口大小")


class MessageUpdate(BaseModel):
    """更新消息请求"""
    content: Optional[str] = Field(None, min_length=1, description="消息内容")
    metadata: Optional[Dict[str, Any]] = Field(None, description="消息元数据")
    status: Optional[int] = Field(
        None, description="消息状态: 1=pending, 2=processing, 3=completed, 4=failed, 5=cancelled")


class MessageResponse(BaseModel):
    """消息响应"""
    id: int
    conversation_id: int
    agent_id: Optional[int]
    content: str
    role: int
    message_type: int
    metadata: Optional[Dict[str, Any]] = Field(None, alias="message_metadata")
    attachments: Optional[List[Dict[str, Any]]]
    ocr_result: Optional[Dict[str, Any]]
    has_ocr_content: bool
    status: int
    processing_time_ms: Optional[int]
    token_usage: Optional[Dict[str, Any]]
    sequence_number: int
    parent_message_id: Optional[int]
    error_message: Optional[str]
    retry_count: int
    created_at: datetime
    updated_at: datetime

    # 关联数据
    agent: Optional[Dict[str, Any]] = None

    model_config = {
        "from_attributes": True,
        "json_encoders": {
            datetime: lambda v: v.isoformat() if v else None
        }
    }


class MessageListResponse(BaseModel):
    """消息列表响应"""
    items: List[MessageResponse]
    total: int
    page: int
    size: int
    pages: int


# ============ 上下文相关模式 ============

class ConversationContextBase(BaseModel):
    """上下文基础模式"""
    context_type: int = Field(
        ..., description="上下文类型: 1=system_prompt, 2=user_profile, 3=pet_info, 4=medical_history, 5=summary")
    context_key: str = Field(..., min_length=1,
                             max_length=100, description="上下文键")
    context_value: Dict[str, Any] = Field(..., description="上下文值")
    priority: int = Field(0, description="优先级")
    is_persistent: bool = Field(False, description="是否持久化")


class ConversationContextCreate(ConversationContextBase):
    """创建上下文请求"""
    expires_in_hours: Optional[int] = Field(
        None, ge=1, le=8760, description="过期时间(小时)")


class ConversationContextUpdate(BaseModel):
    """更新上下文请求"""
    context_value: Optional[Dict[str, Any]] = Field(None, description="上下文值")
    priority: Optional[int] = Field(None, description="优先级")
    is_persistent: Optional[bool] = Field(None, description="是否持久化")
    expires_in_hours: Optional[int] = Field(
        None, ge=1, le=8760, description="过期时间(小时)")


class ConversationContextResponse(ConversationContextBase):
    """上下文响应"""
    id: int
    conversation_id: int
    expires_at: Optional[datetime]
    version: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ConversationContextListResponse(BaseModel):
    """上下文列表响应"""
    items: List[ConversationContextResponse]
    total: int


# ============ 查询参数模式 ============

class ConversationQueryParams(BaseModel):
    """会话查询参数"""
    conversation_type: Optional[int] = Field(
        None, description="会话类型: 1=diagnosis, 2=general")
    status: Optional[int] = Field(
        None, description="会话状态: 1=active, 2=paused, 3=completed, 4=archived, 5=expired")
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(10, ge=1, le=100, description="每页大小")
    order_by: str = Field("created_at", description="排序字段")
    order_desc: bool = Field(True, description="是否降序")


class MessageQueryParams(BaseModel):
    """消息查询参数"""
    role: Optional[int] = Field(
        None, description="消息角色: 1=user, 2=assistant, 3=system")
    message_type: Optional[int] = Field(
        None, description="消息类型: 1=text, 2=image, 3=file")
    status: Optional[int] = Field(
        None, description="消息状态: 1=pending, 2=processing, 3=completed, 4=failed, 5=cancelled")
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页大小")
    order_by: str = Field("sequence_number", description="排序字段")
    order_desc: bool = Field(False, description="是否降序")


# ============ 统计相关模式 ============

class ConversationStatsResponse(BaseModel):
    """会话统计响应"""
    total_conversations: int = Field(..., description="总会话数")
    active_conversations: int = Field(..., description="活跃会话数")
    total_messages: int = Field(..., description="总消息数")
    total_tokens: int = Field(..., description="总Token使用量")
    avg_messages_per_conversation: float = Field(..., description="平均每会话消息数")
    conversations_by_type: Dict[str, int] = Field(..., description="按类型分组的会话数")
    conversations_by_status: Dict[str,
    int] = Field(..., description="按状态分组的会话数")


# ============ 流式响应模式 ============

class StreamMessageChunk(BaseModel):
    """流式消息块"""
    type: str = Field(..., description="块类型: start, content, end, error")
    message_id: Optional[int] = Field(None, description="消息ID")
    content: Optional[str] = Field(None, description="内容块")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")
    error: Optional[str] = Field(None, description="错误信息")


class StreamResponse(BaseModel):
    """流式响应包装"""
    conversation_id: int = Field(..., description="会话ID")
    message_id: int = Field(..., description="消息ID")
    stream_url: str = Field(..., description="流式响应URL")
