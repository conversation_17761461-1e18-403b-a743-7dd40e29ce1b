from typing import Optional

from pydantic import BaseModel, Field

class ReportPetInfo(BaseModel):
    # 基础信息
    name: Optional[str] = Field(None, description="宠物名称")
    breed: Optional[str] = Field(None, description="宠物品种")
    age: Optional[str] = Field(None, description="宠物年龄")
    gender: Optional[str] = Field(None, description="宠物性别")
    is_neutered: Optional[str] = Field(None, description="宠物绝育情况")
    species: Optional[str] = Field(None, description="物种")
    weight: Optional[float] = Field(None, description="体重（kg）")
    height: Optional[float] = Field(None, description="身高（cm）")
    birth_date: Optional[str] = Field(None, description="出生日期")
    color: Optional[str] = Field(None, description="毛色")
    is_vaccinated: Optional[str] = Field(None, description="疫苗接种情况")
    description: Optional[str] = Field(None, description="宠物描述")
    special_needs: Optional[str] = Field(None, description="特殊需求")
    # 补充信息
    other: Optional[str] = Field(None, description="其他信息")

class ReportBaseResponse(BaseModel):
    # 基础信息
    report_name: str = Field(..., description="报告名称")
    name: Optional[str] = Field(None, description="宠物名称")
    breed: Optional[str] = Field(None, description="宠物品种")
    age: Optional[str] = Field(None, description="宠物年龄")
    gender: Optional[str] = Field(None, description="宠物性别")
    is_neutered: Optional[str] = Field(None, description="宠物绝育情况")
    # 备注
    remark: str = Field(..., description="备注")


class HealthManagementReportResponse(ReportBaseResponse):
    # 报告数据
    health_assessment: str = Field(..., description="健康评估")
    disease_risk: str = Field(..., description="疾病风险预测")
    nutritional_recommendations: str = Field(..., description="营养饮食建议")
    grooming_and_exercise: str = Field(..., description="洗护与运动建议")
    daily_care_tips: str = Field(..., description="生活建议")
    health_monitoring_plan: str = Field(..., description="体检与监测")

class DiseaseRiskResponse(ReportBaseResponse):
    # 报告数据
    disease_risk: str = Field(..., description="疾病风险预测")
    health_assessment: str = Field(..., description="健康评估")

class VaccinationPlanResponse(ReportBaseResponse):
    # 报告数据
    core_vaccination_protocol: str = Field(..., description="基础免疫（核心程序）内容")
    non_core_vaccination_recommendations: str = Field(..., description="进阶（非核心）免疫推荐内容")

class DewormingPlanResponse(ReportBaseResponse):
    # 报告数据
    parasite_risk_assessment: str = Field(..., description="驱虫目标内容")
    deworming_protocol: str = Field(..., description="驱虫方案内容")
    environmental_management: str = Field(..., description="环境管理内容")
    follow_up_schedule: str = Field(..., description="复诊提醒内容")
    health_risk_warning: str = Field(..., description="风险预警内容")
    clinical_references: str = Field(..., description="依据出处内容")

class NutritionalDietResponse(ReportBaseResponse):
    # 报告数据
    nutritional_goals: str = Field(..., description="饮食目标内容")
    dietary_composition: str = Field(..., description="饮食结构内容")
    hydration_management: str = Field(..., description="饮水管理内容")
    monitoring_parameters: str = Field(..., description="监测指标内容")
    follow_up_schedule: str = Field(..., description="复诊提醒内容")
    health_risk_warning: str = Field(..., description="风险预警内容")
    clinical_references: str = Field(..., description="依据出处内容")

class ChronicDiseaseResponse(ReportBaseResponse):
    # 报告数据
    core_management_objectives: str = Field(..., description="核心管理目标内容")
    basic_management: str = Field(..., description="基础管理内容")
    treatment_recommendations: str = Field(..., description="治疗建议内容")
    monitoring_management: str = Field(..., description="监测管理内容")
    complication_prevention: str = Field(..., description="并发症预防与管理内容")
    follow_up_recommendations: str = Field(..., description="复诊建议内容")
    clinical_references: str = Field(..., description="依据出处内容")