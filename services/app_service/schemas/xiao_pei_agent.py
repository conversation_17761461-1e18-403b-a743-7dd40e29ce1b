from enum import Enum
from typing import Optional, List

from pydantic import BaseModel, Field

class XiaoPeiPetInfo(BaseModel):
    # 基础信息
    name: Optional[str] = Field(None, description="宠物名称")
    species: Optional[str] = Field(None, description="物种")
    breed: Optional[str] = Field(None, description="宠物品种")
    age_years: Optional[int] = Field(None, description="年龄(年)")
    age_months: Optional[int] = Field(None, description="年龄(月)")
    gender: Optional[str] = Field(None, description="宠物性别")
    is_neutered: Optional[bool] = Field(None, description="是否绝育")
    weight: Optional[float] = Field(None, description="体重")

class XiaoPeiChatAgentInput(BaseModel):
    conversation_id: Optional[str] = Field(None, description="会话ID")
    pet_id: Optional[int] = Field(None, description="宠物ID")
    message:str = Field(..., description="用户输入消息")
    stream: Optional[bool] = Field(False, description="是否流式返回结果")

# class BetaCatCdssAgentOutput(BaseModel):
#     request_id: str = Field(..., description="请求ID")
#     content: str = Field(..., description="回复内容")
#     pushTime: int = Field(..., description="推送时间，毫秒")
#     msgType: int = Field(..., description="消息类型:1-lab，2-cdss")

# class BetaCatLabAgentInput(BaseModel):
#     request_id: str = Field(..., description="请求ID")
#     pet_info: Optional[BetaCatPetInfo] = Field(None, description="宠物基本数据")
#     lab_project: Optional[str] = Field(None, description="实验室检查项目:血常规")
#     instrument_info: Optional[str] = Field(None, description="设备名称")
#     report_id: Optional[str] = Field(None, description="报告ID")
#     item_values: Optional[List[BetaCatPetExamItem]] = Field(None, description="检查的值")
#     stream: Optional[bool] = Field(False, description="是否流式返回结果")
