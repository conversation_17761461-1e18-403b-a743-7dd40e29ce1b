"""
应用服务Pydantic模式
"""
from .agent import (
    <PERSON><PERSON><PERSON>,
    Agent<PERSON><PERSON>,
    AgentUpdate,
    AgentResponse,
    AgentListResponse,
    AgentPermissionBase,
    AgentPermissionCreate,
    AgentPermissionResponse,
    AgentExecutionRequest,
    AgentExecutionResponse,
    DiagnosisAgentInput,
    VisionAgentInput,
    ReportGenerationAgentInput,
    ReportAnalysisAgentInput,
    AgentQueryParams,
    AgentStatsResponse,
    # 灵活输入模式
    ChatMessage,
    FlexibleAgentInput
)
from .conversation import (
    ConversationCreate,
    ConversationUpdate,
    ConversationResponse,
    ConversationListResponse,
    MessageCreate,
    MessageUpdate,
    MessageResponse,
    MessageListResponse,
    ConversationContextCreate,
    ConversationContextUpdate,
    ConversationContextResponse,
    ConversationContextListResponse,
    ConversationQueryParams,
    MessageQueryParams,
    ConversationStatsResponse,
    StreamMessageChunk,
    StreamResponse
)

__all__ = [
    # Agent相关
    "AgentBase",
    "AgentCreate",
    "AgentUpdate",
    "AgentResponse",
    "AgentListResponse",
    "AgentPermissionBase",
    "AgentPermissionCreate",
    "AgentPermissionResponse",
    "AgentExecutionRequest",
    "AgentExecutionResponse",
    "DiagnosisAgentInput",
    "VisionAgentInput",
    "ReportGenerationAgentInput",
    "ReportAnalysisAgentInput",
    "AgentQueryParams",
    "AgentStatsResponse",
    # Conversation相关
    "ConversationCreate",
    "ConversationUpdate",
    "ConversationResponse",
    "ConversationListResponse",
    "MessageCreate",
    "MessageUpdate",
    "MessageResponse",
    "MessageListResponse",
    "ConversationContextCreate",
    "ConversationContextUpdate",
    "ConversationContextResponse",
    "ConversationContextListResponse",
    "ConversationQueryParams",
    "MessageQueryParams",
    "ConversationStatsResponse",
    "StreamMessageChunk",
    "StreamResponse",
    # 灵活输入模式
    "ChatMessage",
    "FlexibleAgentInput"
]
