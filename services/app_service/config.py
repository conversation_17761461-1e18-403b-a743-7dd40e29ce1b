"""
应用服务配置模块
"""
import os
from typing import List


class Settings:
    """应用服务配置类"""

    # 项目基本信息
    PROJECT_NAME: str = os.getenv("PROJECT_NAME", "宠物医疗AI开放平台")
    PROJECT_VERSION: str = os.getenv("PROJECT_VERSION", "1.0.0")

    # API配置
    API_V1_STR: str = os.getenv("API_V1_STR", "/api/v1")

    # 服务端口
    SERVICE_PORT: int = int(os.getenv("APP_SERVICE_PORT", "8003"))

    # 调试模式
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"

    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")

    # 数据库配置
    DATABASE_URL: str = os.getenv("DATABASE_URL", "")

    # Redis配置
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")

    # CORS配置
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:8080",
        "http://localhost:8000",
    ]

    # 安全配置
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key")

    # JWT配置
    JWT_SECRET_KEY: str = os.getenv("JWT_SECRET_KEY", "your-jwt-secret")
    ALGORITHM: str = os.getenv("ALGORITHM", "HS256")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(
        os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))


# 创建配置实例
settings = Settings()
