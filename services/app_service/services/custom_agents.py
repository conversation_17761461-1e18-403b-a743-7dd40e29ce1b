"""
用户自定义智能体处理器
"""
from typing import Dict, Any, Optional
from openai import AsyncOpenAI

from shared.logging_config import get_app_service_logger
from ..models.agent import Agent

logger = get_app_service_logger()


class CustomAgentProcessor:
    """用户自定义智能体处理器"""

    def __init__(self):
        self.default_model = "ds-vet-answer-32B"

    def _get_openai_client(self, model_name: str = None) -> AsyncOpenAI:
        """获取OpenAI客户端"""
        if not model_name:
            model_name = self.default_model

        # 使用环境变量或默认配置
        import os
        api_key = os.getenv(
            "OPENAI_API_KEY", "y03ZvBwVvHAqAo0nosjdlVGCfCNYGlgm8U")
        base_url = os.getenv("OPENAI_BASE_URL", "http://10.201.112.7:8888/v1")

        # 创建OpenAI客户端
        return AsyncOpenAI(
            api_key=api_key,
            base_url=base_url
        )

    def _merge_config(self, agent: Agent, config_override: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """合并配置参数"""
        # 从智能体获取基础配置
        base_config = agent.config or {}

        # 应用覆盖配置
        if config_override:
            base_config.update(config_override)

        # 设置默认值
        default_config = {
            "model": self.default_model,
            "temperature": 0.7,
            "max_tokens": 2048,
            "top_p": 0.9
        }

        for key, value in default_config.items():
            if key not in base_config:
                base_config[key] = value

        return base_config

    def _build_prompt(self, agent: Agent, input_data: Dict[str, Any]) -> str:
        """构建提示词"""
        # 获取智能体的提示词模板
        prompt_template = agent.prompt_template or self._get_default_prompt()

        # 尝试使用输入数据填充模板变量
        try:
            # 如果模板包含变量占位符，尝试填充
            if "{" in prompt_template and "}" in prompt_template:
                return prompt_template.format(**input_data)
            else:
                # 如果没有变量占位符，直接返回模板
                return prompt_template
        except KeyError as e:
            logger.warning(f"提示词模板变量缺失: {e}")
            return prompt_template

    def _get_default_prompt(self) -> str:
        """获取默认提示词"""
        return """你是一个智能助手，请根据用户的输入提供有帮助的回答。

用户输入：{user_input}

请提供准确、有用的回答。"""

    async def process(
        self,
        agent: Agent,
        input_data: Dict[str, Any],
        config_override: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """处理用户自定义智能体请求"""
        try:
            # 验证输入数据
            if not input_data:
                return self._format_error_response("输入数据不能为空")

            # 构建提示词
            system_prompt = self._build_prompt(agent, input_data)

            # 构建用户消息
            user_message = self._build_user_message(input_data)

            # 准备消息
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ]

            # 合并配置
            config = self._merge_config(agent, config_override)

            # 调用LLM
            llm_response = await self._call_llm(messages, config)

            # 构建响应
            result = self._format_success_response(
                content=llm_response["content"],
                metadata={
                    "agent_name": agent.agent_name,
                    "agent_version": agent.version,
                    "model_used": llm_response["model"],
                    "token_usage": llm_response["usage"],
                    "input_keys": list(input_data.keys())
                }
            )

            logger.info(f"用户自定义智能体处理完成: {agent.agent_name}")
            return result

        except Exception as e:
            logger.error(f"用户自定义智能体处理失败: {e}")
            return self._format_error_response(f"处理失败: {str(e)}")

    def _build_user_message(self, input_data: Dict[str, Any]) -> str:
        """构建用户消息"""
        # 如果输入数据包含特定的用户消息字段
        if "user_message" in input_data:
            return str(input_data["user_message"])

        if "message" in input_data:
            return str(input_data["message"])

        if "query" in input_data:
            return str(input_data["query"])

        if "question" in input_data:
            return str(input_data["question"])

        # 如果没有明确的消息字段，将所有输入数据格式化为消息
        message_parts = []
        for key, value in input_data.items():
            if isinstance(value, (str, int, float)):
                message_parts.append(f"{key}: {value}")
            elif isinstance(value, (list, dict)):
                message_parts.append(f"{key}: {str(value)}")

        return "\n".join(message_parts) if message_parts else "请提供帮助"

    async def _call_llm(
        self,
        messages: list,
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """调用LLM服务"""
        try:
            client = self._get_openai_client(config.get("model"))

            response = await client.chat.completions.create(
                model=config.get("model", self.default_model),
                messages=messages,
                temperature=config.get("temperature", 0.7),
                max_tokens=config.get("max_tokens", 2048),
                top_p=config.get("top_p", 0.9),
                stream=False
            )

            # 提取响应内容
            content = response.choices[0].message.content

            # 构建返回结果
            result = {
                "content": content,
                "model": response.model,
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                "finish_reason": response.choices[0].finish_reason
            }

            return result

        except Exception as e:
            logger.error(f"调用LLM服务失败: {e}")
            raise

    def _format_error_response(self, error_message: str) -> Dict[str, Any]:
        """格式化错误响应"""
        return {
            "success": False,
            "error": error_message,
            "content": None
        }

    def _format_success_response(self, content: str, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """格式化成功响应"""
        result = {
            "success": True,
            "content": content,
            "error": None
        }

        if metadata:
            result.update(metadata)

        return result
