"""
会话管理缓存服务
"""
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from shared.redis_simple import redis_client
from shared.logging_config import get_app_service_logger
from ..models.conversation import Conversation, Message, ConversationContext

logger = get_app_service_logger()


class CacheConfig:
    """缓存配置"""

    # 会话缓存配置
    CONVERSATION_TTL = 3600 * 24  # 24小时
    CONVERSATION_LIST_TTL = 300   # 5分钟
    CONVERSATION_STATUS_TTL = 60  # 1分钟

    # 消息缓存配置
    MESSAGE_TTL = 3600 * 6        # 6小时
    MESSAGE_LIST_TTL = 300        # 5分钟
    MESSAGE_RECENT_TTL = 60       # 1分钟

    # 上下文缓存配置
    CONTEXT_TTL = 3600 * 2        # 2小时
    CONTEXT_WINDOW_TTL = 300      # 5分钟
    CONTEXT_SUMMARY_TTL = 3600    # 1小时

    # 性能缓存配置
    HOT_CONVERSATION_TTL = 3600   # 1小时
    STATS_TTL = 300               # 5分钟

    # 缓存大小限制
    MAX_MESSAGE_CACHE_SIZE = 100  # 每个会话最多缓存100条消息
    MAX_CONTEXT_CACHE_SIZE = 50   # 每个会话最多缓存50个上下文项


class ConversationCacheService:
    """会话缓存服务"""

    def __init__(self, tenant_id: Optional[int] = None):
        self.redis = redis_client
        self.config = CacheConfig()
        self.tenant_id = tenant_id or 0

    def _get_tenant_key(self, key: str) -> str:
        """生成租户隔离的缓存键"""
        return f"tenant:{self.tenant_id}:{key}"

    # ============ 会话缓存方法 ============

    async def cache_conversation(self, conversation: Conversation):
        """缓存会话信息"""
        try:
            key = self._get_tenant_key(f"conversation:{conversation.id}")
            data = {
                "id": conversation.id,
                "title": conversation.title,
                "conversation_type": conversation.conversation_type,
                "status": conversation.status,
                "user_id": conversation.user_id,
                "tenant_id": conversation.tenant_id,
                "primary_agent_id": conversation.primary_agent_id,
                "config": conversation.config,
                "context_window_size": conversation.context_window_size,
                "max_messages": conversation.max_messages,
                "last_activity_at": conversation.last_activity_at.isoformat() if conversation.last_activity_at else None,
                "expires_at": conversation.expires_at.isoformat() if conversation.expires_at else None,
                "message_count": conversation.message_count,
                "total_tokens": conversation.total_tokens,
                "created_at": conversation.created_at.isoformat() if conversation.created_at else None,
                "updated_at": conversation.updated_at.isoformat() if conversation.updated_at else None
            }

            # 使用try-catch包装Redis操作，避免异步上下文错误
            try:
                await self.redis.set(key, json.dumps(data), expire=self.config.CONVERSATION_TTL)
                logger.debug(f"缓存会话信息: {conversation.id}")
            except Exception as redis_error:
                # 如果Redis操作失败，静默跳过（缓存是可选的）
                error_msg = str(redis_error)
                if "greenlet_spawn" in error_msg:
                    # 这是已知的异步上下文问题，不影响功能，静默跳过
                    pass
                else:
                    logger.warning(f"Redis缓存操作失败，但不影响主流程: {redis_error}")

        except Exception as e:
            logger.error(f"缓存会话信息失败: {e}")

    async def get_conversation_from_cache(self, conversation_id: int) -> Optional[Dict]:
        """从缓存获取会话信息"""
        try:
            key = self._get_tenant_key(f"conversation:{conversation_id}")
            data = await self.redis.get(key)
            if data:
                logger.debug(f"从缓存获取会话信息: {conversation_id}")
                # 检查数据类型，如果已经是dict则直接返回，否则解析JSON
                if isinstance(data, dict):
                    return data
                elif isinstance(data, (str, bytes, bytearray)):
                    return json.loads(data)
                else:
                    logger.warning(f"未知的缓存数据类型: {type(data)}")
                    return None
            return None
        except Exception as e:
            logger.error(f"从缓存获取会话信息失败: {e}")
            return None

    async def cache_user_conversations(self, user_id: int, conversations: List[Dict]):
        """缓存用户会话列表"""
        try:
            key = self._get_tenant_key(f"conversation:list:user:{user_id}")
            data = json.dumps(conversations)
            await self.redis.set(key, data, expire=self.config.CONVERSATION_LIST_TTL)
            logger.debug(
                f"缓存用户会话列表: user_id={user_id}, count={len(conversations)}")
        except Exception as e:
            logger.error(f"缓存用户会话列表失败: {e}")

    async def get_user_conversations_from_cache(self, user_id: int) -> Optional[List[Dict]]:
        """从缓存获取用户会话列表"""
        try:
            key = self._get_tenant_key(f"conversation:list:user:{user_id}")
            data = await self.redis.get(key)
            if data:
                logger.debug(f"从缓存获取用户会话列表: user_id={user_id}")
                return json.loads(data)
            return None
        except Exception as e:
            logger.error(f"从缓存获取用户会话列表失败: {e}")
            return None

    # ============ 消息缓存方法 ============

    async def cache_message(self, message: Message):
        """缓存消息信息"""
        try:
            key = self._get_tenant_key(f"message:{message.id}")
            data = {
                "id": message.id,
                "conversation_id": message.conversation_id,
                "agent_id": message.agent_id,
                "role": message.role,
                "message_type": message.message_type,
                "content": message.content,
                "message_metadata": message.message_metadata,
                "attachments": message.attachments,
                "ocr_result": message.ocr_result,
                "has_ocr_content": message.has_ocr_content,
                "status": message.status,
                "processing_time_ms": message.processing_time_ms,
                "token_usage": message.token_usage,
                "sequence_number": message.sequence_number,
                "parent_message_id": message.parent_message_id,
                "error_message": message.error_message,
                "retry_count": message.retry_count,
                "created_at": message.created_at.isoformat() if message.created_at else None,
                "updated_at": message.updated_at.isoformat() if message.updated_at else None
            }
            # 使用try-catch包装Redis操作，避免异步上下文错误
            try:
                await self.redis.set(key, json.dumps(data), expire=self.config.MESSAGE_TTL)
                logger.debug(f"缓存消息信息: {message.id}")
            except Exception as redis_error:
                # 如果Redis操作失败，静默跳过（缓存是可选的）
                error_msg = str(redis_error)
                if "greenlet_spawn" in error_msg:
                    # 这是已知的异步上下文问题，不影响功能，静默跳过
                    pass
                else:
                    logger.warning(f"Redis消息缓存操作失败，但不影响主流程: {redis_error}")

        except Exception as e:
            logger.error(f"缓存消息信息失败: {e}")

    async def cache_recent_messages(self, conversation_id: int, messages: List[Message]):
        """缓存最近消息"""
        try:
            key = self._get_tenant_key(
                f"message:recent:conversation:{conversation_id}")
            data = [
                {
                    "id": msg.id,
                    "role": msg.role,
                    "content": msg.content,
                    "sequence_number": msg.sequence_number,
                    "created_at": msg.created_at.isoformat() if msg.created_at else None
                }
                for msg in messages[-self.config.MAX_MESSAGE_CACHE_SIZE:]
            ]
            # 使用try-catch包装Redis操作，避免异步上下文错误
            try:
                await self.redis.set(key, json.dumps(data), expire=self.config.MESSAGE_RECENT_TTL)
                logger.debug(
                    f"缓存最近消息: conversation_id={conversation_id}, count={len(data)}")
            except Exception as redis_error:
                # 如果Redis操作失败，静默跳过（缓存是可选的）
                error_msg = str(redis_error)
                if "greenlet_spawn" in error_msg:
                    # 这是已知的异步上下文问题，不影响功能，静默跳过
                    pass
                else:
                    logger.warning(f"Redis最近消息缓存操作失败，但不影响主流程: {redis_error}")

        except Exception as e:
            logger.error(f"缓存最近消息失败: {e}")

    async def get_recent_messages_from_cache(self, conversation_id: int) -> Optional[List[Dict]]:
        """从缓存获取最近消息"""
        try:
            key = self._get_tenant_key(
                f"message:recent:conversation:{conversation_id}")
            data = await self.redis.get(key)
            if data:
                logger.debug(f"从缓存获取最近消息: conversation_id={conversation_id}")
                return json.loads(data)
            return None
        except Exception as e:
            logger.error(f"从缓存获取最近消息失败: {e}")
            return None

    # ============ 上下文缓存方法 ============

    async def cache_conversation_context(self, conversation_id: int, context: Dict[str, Any]):
        """缓存会话上下文"""
        try:
            key = self._get_tenant_key(f"context:window:{conversation_id}")
            await self.redis.set(key, json.dumps(context), expire=self.config.CONTEXT_WINDOW_TTL)
            logger.debug(f"缓存会话上下文: conversation_id={conversation_id}")
        except Exception as e:
            logger.error(f"缓存会话上下文失败: {e}")

    async def get_conversation_context_from_cache(self, conversation_id: int) -> Optional[Dict[str, Any]]:
        """从缓存获取会话上下文"""
        try:
            key = self._get_tenant_key(f"context:window:{conversation_id}")
            data = await self.redis.get(key)
            if data:
                logger.debug(f"从缓存获取会话上下文: conversation_id={conversation_id}")
                return json.loads(data)
            return None
        except Exception as e:
            logger.error(f"从缓存获取会话上下文失败: {e}")
            return None

    # ============ 缓存清理方法 ============

    async def invalidate_conversation_cache(self, conversation_id: int):
        """清除会话相关缓存"""
        try:
            patterns = [
                f"conversation:{conversation_id}",
                f"message:*:conversation:{conversation_id}",
                f"context:*:{conversation_id}",
                f"hot:conversation:{conversation_id}"
            ]

            for pattern in patterns:
                tenant_pattern = self._get_tenant_key(pattern)
                keys = await self.redis.keys(tenant_pattern)
                if keys:
                    await self.redis.delete(*keys)

            logger.debug(f"清除会话缓存: conversation_id={conversation_id}")
        except Exception as e:
            logger.error(f"清除会话缓存失败: {e}")

    async def invalidate_user_cache(self, user_id: int):
        """清除用户相关缓存"""
        try:
            patterns = [
                f"conversation:list:user:{user_id}",
                f"stats:user:{user_id}"
            ]

            for pattern in patterns:
                tenant_pattern = self._get_tenant_key(pattern)
                keys = await self.redis.keys(tenant_pattern)
                if keys:
                    await self.redis.delete(*keys)

            logger.debug(f"清除用户缓存: user_id={user_id}")
        except Exception as e:
            logger.error(f"清除用户缓存失败: {e}")

    # ============ 统计缓存方法 ============

    async def cache_conversation_stats(self, stats: Dict[str, Any]):
        """缓存会话统计信息"""
        try:
            key = self._get_tenant_key("stats:conversations")
            await self.redis.set(key, json.dumps(stats), expire=self.config.STATS_TTL)
            logger.debug("缓存会话统计信息")
        except Exception as e:
            logger.error(f"缓存会话统计信息失败: {e}")

    async def get_conversation_stats_from_cache(self) -> Optional[Dict[str, Any]]:
        """从缓存获取会话统计信息"""
        try:
            key = self._get_tenant_key("stats:conversations")
            data = await self.redis.get(key)
            if data:
                logger.debug("从缓存获取会话统计信息")
                return json.loads(data)
            return None
        except Exception as e:
            logger.error(f"从缓存获取会话统计信息失败: {e}")
            return None
