"""
智能体服务层
"""
import uuid
from datetime import datetime, timezone
from typing import Optional, List, Tuple

from sqlalchemy import select, and_, or_, func, desc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from shared.logging_config import get_app_service_logger
from ..models.agent import (
    Agent, AgentExecution,
    AgentType, AgentStatus, SystemAgentCategory, ExecutionStatus
)
from ..schemas.agent import (
    AgentCreate, AgentUpdate, AgentQueryParams,
    AgentExecutionRequest, AgentExecutionResponse
)

logger = get_app_service_logger()


class AgentService:
    """智能体服务类"""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_agent(self, agent_data: AgentCreate, owner_id: Optional[int] = None) -> Agent:
        """创建智能体"""
        try:
            # 创建智能体实例
            agent = Agent(
                agent_name=agent_data.name,
                display_name=agent_data.display_name,
                description=agent_data.description,
                avatar_url=agent_data.avatar_url,
                agent_type=agent_data.agent_type,
                system_category=agent_data.system_category,
                is_public=agent_data.is_public,
                config=agent_data.config,
                prompt_template=agent_data.prompt_template,
                owner_id=owner_id,
                status=AgentStatus.DRAFT
            )

            self.db.add(agent)
            await self.db.commit()
            await self.db.refresh(agent)

            logger.info(f"创建智能体成功: {agent.agent_name} (ID: {agent.id})")
            return agent

        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建智能体失败: {e}")
            raise

    async def get_agent(self, agent_id: int, user_id: Optional[int] = None) -> Optional[Agent]:
        """获取智能体详情"""
        try:
            query = select(Agent).where(
                and_(
                    Agent.id == agent_id,
                    Agent.is_deleted == False
                )
            ).options(
                selectinload(Agent.permissions)
            )

            result = await self.db.execute(query)
            agent = result.scalar_one_or_none()

            if not agent:
                return None

            # 检查访问权限
            if user_id and not agent.can_be_used_by(user_id):
                return None

            return agent

        except Exception as e:
            logger.error(f"获取智能体失败: {e}")
            raise

    async def list_agents(
        self,
        params: AgentQueryParams,
        user_id: Optional[int] = None
    ) -> Tuple[List[Agent], int]:
        """获取智能体列表"""
        try:
            # 构建查询条件
            conditions = [Agent.is_deleted == False]

            if params.agent_type:
                conditions.append(Agent.agent_type == params.agent_type)

            if params.system_category:
                conditions.append(Agent.system_category ==
                                  params.system_category)

            if params.status:
                conditions.append(Agent.status == params.status)

            if params.is_public is not None:
                conditions.append(Agent.is_public == params.is_public)

            if params.owner_id:
                conditions.append(Agent.owner_id == params.owner_id)

            # 权限过滤
            if user_id:
                # 用户可以看到：1. 系统智能体 2. 自己的智能体 3. 公开的智能体
                permission_conditions = or_(
                    Agent.agent_type == AgentType.SYSTEM,
                    Agent.owner_id == user_id,
                    Agent.is_public == True
                )
                conditions.append(permission_conditions)

            # 搜索条件
            if params.search:
                search_condition = or_(
                    Agent.agent_name.ilike(f"%{params.search}%"),
                    Agent.display_name.ilike(f"%{params.search}%"),
                    Agent.description.ilike(f"%{params.search}%")
                )
                conditions.append(search_condition)

            # 计算总数
            count_query = select(func.count(Agent.id)).where(and_(*conditions))
            count_result = await self.db.execute(count_query)
            total = count_result.scalar()

            # 分页查询
            offset = (params.page - 1) * params.page_size
            query = select(Agent).where(and_(*conditions)).order_by(
                desc(Agent.usage_count), desc(Agent.updated_at)
            ).offset(offset).limit(params.page_size)

            result = await self.db.execute(query)
            agents = result.scalars().all()

            return list(agents), total

        except Exception as e:
            logger.error(f"获取智能体列表失败: {e}")
            raise

    async def update_agent(
        self,
        agent_id: int,
        agent_data: AgentUpdate,
        user_id: Optional[int] = None
    ) -> Optional[Agent]:
        """更新智能体"""
        try:
            agent = await self.get_agent(agent_id, user_id)
            if not agent:
                return None

            # 检查编辑权限
            if user_id and agent.agent_type == AgentType.CUSTOM and agent.owner_id != user_id:
                return None

            # 更新字段
            update_data = agent_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(agent, field, value)

            await self.db.commit()
            await self.db.refresh(agent)

            logger.info(f"更新智能体成功: {agent.agent_name} (ID: {agent.id})")
            return agent

        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新智能体失败: {e}")
            raise

    async def delete_agent(self, agent_id: int, user_id: Optional[int] = None) -> bool:
        """删除智能体（软删除）"""
        try:
            agent = await self.get_agent(agent_id, user_id)
            if not agent:
                return False

            # 检查删除权限
            if user_id and agent.agent_type == AgentType.CUSTOM and agent.owner_id != user_id:
                return False

            # 系统智能体不能删除
            if agent.agent_type == AgentType.SYSTEM:
                return False

            agent.soft_delete()
            await self.db.commit()

            logger.info(f"删除智能体成功: {agent.agent_name} (ID: {agent.id})")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"删除智能体失败: {e}")
            raise

    async def execute_agent(
        self,
        agent_id: int,
        request_data: AgentExecutionRequest,
        user_id: int
    ) -> AgentExecutionResponse:
        """执行智能体"""
        try:
            agent = await self.get_agent(agent_id, user_id)
            if not agent:
                raise ValueError("智能体不存在或无权限访问")

            if not agent.is_active():
                raise ValueError("智能体未激活")

            # 创建执行记录
            execution_id = str(uuid.uuid4())
            execution = AgentExecution(
                agent_id=agent_id,
                user_id=user_id,
                execution_id=execution_id,
                input_data=request_data.input_data,
                status="running"
            )

            self.db.add(execution)
            await self.db.commit()

            # 增加使用次数
            agent.increment_usage()
            await self.db.commit()

            # 根据智能体类型执行相应逻辑
            result = await self._execute_agent_logic(agent, request_data, execution)

            return result

        except Exception as e:
            logger.error(f"执行智能体失败: {e}")
            raise

    async def execute_agent_stream(
        self,
        agent_id: int,
        request_data: AgentExecutionRequest,
        user_id: int
    ):
        """
        流式执行智能体
        """
        import json
        import asyncio
        from datetime import datetime

        try:
            # 获取智能体
            agent = await self.get_agent(agent_id)
            if not agent:
                yield f"data: {json.dumps({'error': '智能体不存在'})}\n\n"
                return

            # 检查权限
            if agent.owner_id != user_id and not agent.is_public:
                yield f"data: {json.dumps({'error': '无权限访问此智能体'})}\n\n"
                return

            # 创建执行记录
            execution_id = str(uuid.uuid4())
            execution = AgentExecution(
                execution_id=execution_id,
                agent_id=agent_id,
                user_id=user_id,
                input_data=request_data.input_data,
                status=ExecutionStatus.RUNNING,
                started_at=datetime.now(timezone.utc)
            )

            self.db.add(execution)
            await self.db.commit()

            # 发送开始事件 - 使用统一响应格式
            start_response = {
                "status": "success",
                "msg": "智能体执行开始",
                "data": {
                    "type": "start",
                    "execution_id": execution_id,
                    "agent_id": agent_id
                },
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "request_id": execution_id  # 使用execution_id作为request_id
            }
            yield f"data: {json.dumps(start_response, ensure_ascii=False)}\n\n"

            # 增加使用次数
            agent.increment_usage()
            await self.db.commit()

            # 真正的流式执行智能体逻辑
            try:
                # 根据智能体类型执行相应逻辑
                if agent.agent_type == AgentType.SYSTEM:
                    async for chunk in self._execute_system_agent_stream(agent, request_data, execution):
                        yield chunk
                else:
                    # 用户自定义智能体的流式处理逻辑
                    async for chunk in self._execute_custom_agent_stream(agent, request_data, execution):
                        yield chunk

                # 更新执行状态为完成
                execution.status = ExecutionStatus.COMPLETED
                execution.completed_at = datetime.now(timezone.utc)
                await self.db.commit()

                # 发送完成事件 - 使用统一响应格式
                end_response = {
                    "status": "success",
                    "msg": "智能体执行完成",
                    "data": {
                        "type": "end",
                        "execution_id": execution_id,
                        "agent_id": agent_id
                    },
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "request_id": execution_id
                }
                yield f"data: {json.dumps(end_response, ensure_ascii=False)}\n\n"

            except Exception as e:
                # 更新执行状态为失败
                execution.status = ExecutionStatus.FAILED
                execution.error_message = str(e)
                execution.completed_at = datetime.now(timezone.utc)
                await self.db.commit()

                logger.error(f"智能体流式执行失败: {e}")
                # 发送错误事件 - 使用统一响应格式
                error_response = {
                    "status": "error",
                    "msg": "智能体执行失败",
                    "data": None,
                    "error_code": "EXECUTION_FAILED",
                    "error_details": {
                        "type": "error",
                        "error": str(e),
                        "execution_id": execution_id,
                        "agent_id": agent_id
                    },
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "request_id": execution_id
                }
                yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"

        except Exception as e:
            logger.error(f"流式执行智能体失败: {e}")
            # 发送初始化错误事件 - 使用统一响应格式
            error_response = {
                "status": "error",
                "msg": f"执行失败: {str(e)}",
                "data": None,
                "error_code": "INITIALIZATION_FAILED",
                "error_details": {"error": str(e)},
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "request_id": str(uuid.uuid4())
            }
            yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"

    async def _get_system_agent(self, category: SystemAgentCategory) -> Optional[Agent]:
        """获取系统智能体"""
        try:
            logger.info(f"🔍 正在查询系统智能体: category={category}")

            # 先检查数据库连接和表是否存在
            from sqlalchemy import text
            try:
                # 检查表是否存在
                result = await self.db.execute(text(
                    "SELECT table_name FROM information_schema.tables WHERE table_schema = 'vet' AND table_name = 'agents'"
                ))
                table_exists = result.scalar() is not None
                logger.info(f"📋 vet.agents表存在检查: {table_exists}")

                if table_exists:
                    # 检查表中的记录数
                    result = await self.db.execute(text("SELECT COUNT(*) FROM vet.agents"))
                    count = result.scalar()
                    logger.info(f"📊 vet.agents表总记录数: {count}")

                    # 检查系统智能体数量
                    result = await self.db.execute(text(
                        "SELECT COUNT(*) FROM vet.agents WHERE agent_type = 'SYSTEM'"
                    ))
                    system_count = result.scalar()
                    logger.info(f"📊 系统智能体记录数: {system_count}")

            except Exception as check_e:
                logger.error(f"❌ 表存在性检查失败: {check_e}")

            # 构建查询
            query = select(Agent).where(
                and_(
                    Agent.agent_type == AgentType.SYSTEM,
                    Agent.system_category == category,
                    Agent.status == AgentStatus.ACTIVE,
                    Agent.is_deleted == False
                )
            )

            logger.info(f"🔍 执行查询: {query}")
            result = await self.db.execute(query)
            agent = result.scalar_one_or_none()

            if agent:
                logger.info(f"✅ 找到系统智能体: {agent.agent_name} (ID: {agent.id})")
            else:
                logger.warning(f"⚠️ 未找到系统智能体: category={category}")

            return agent

        except Exception as e:
            logger.error(f"❌ 获取系统智能体失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return None

    def _should_enable_short_term_memory(
        self,
        agent: Agent,
        explicit_setting: Optional[bool]
    ) -> bool:
        """
        确定是否应该启用短期记忆

        Args:
            agent: 智能体实例
            explicit_setting: 显式设置的短期记忆参数

        Returns:
            bool: 是否启用短期记忆
        """
        # 如果显式设置了参数，使用显式设置
        if explicit_setting is not None:
            return explicit_setting

        # 检查智能体配置中的默认设置
        if agent.config and 'enable_short_term_memory' in agent.config:
            return agent.config['enable_short_term_memory']

        # 根据智能体类型和分类确定默认值
        if agent.agent_type == AgentType.SYSTEM:
            # 系统智能体的默认设置
            if agent.system_category in [SystemAgentCategory.DIAGNOSIS]:
                return True  # 普通诊断智能体默认启用短期记忆
            else:
                return True  # 其他系统智能体默认启用短期记忆
        else:
            # 用户自定义智能体默认启用短期记忆
            return True

    async def _execute_agent_logic(
        self,
        agent: Agent,
        request_data: AgentExecutionRequest,
        execution: AgentExecution
    ) -> AgentExecutionResponse:
        """执行智能体逻辑（抽象方法，由具体智能体实现）"""
        # 这里是基础实现，具体的智能体逻辑在各自的处理器中实现
        start_time = datetime.now(timezone.utc)

        try:
            # 根据系统智能体分类调用相应的处理器
            if agent.agent_type == AgentType.SYSTEM:
                from .system_agents import get_system_agent_processor
                processor = get_system_agent_processor(agent.system_category)

                # 为诊断智能体设置数据库会话（用于短期记忆功能）
                if hasattr(processor, 'db_session'):
                    processor.db_session = self.db

                # 处理短期记忆控制参数
                input_data = request_data.input_data.copy()

                # 确定是否启用短期记忆
                enable_memory = self._should_enable_short_term_memory(
                    agent, request_data.enable_short_term_memory
                )

                # 如果禁用短期记忆，移除会话上下文
                if not enable_memory and '_conversation_context' in input_data:
                    logger.info("短期记忆已禁用，移除会话上下文")
                    input_data.pop('_conversation_context', None)

                result = await processor.process(agent, input_data, request_data.config_override)
            else:
                # 用户自定义智能体的处理逻辑
                from .custom_agents import CustomAgentProcessor
                processor = CustomAgentProcessor()
                result = await processor.process(agent, request_data.input_data, request_data.config_override)

            # 更新执行记录
            end_time = datetime.now(timezone.utc)
            duration_ms = int((end_time - start_time).total_seconds() * 1000)

            execution.status = "completed"
            # 确保输出数据可序列化
            try:
                import copy
                import json
                serializable_result = copy.deepcopy(result)
                # 测试是否可以序列化
                json.dumps(serializable_result, ensure_ascii=False)
                execution.output_data = serializable_result
            except (TypeError, ValueError) as e:
                logger.warning(f"结果数据不可序列化，使用简化版本: {e}")
                execution.output_data = {
                    "success": result.get("success", False),
                    "content": str(result.get("content", "")),
                    "error": str(result.get("error", "")),
                    "serialization_error": str(e)
                }
            execution.completed_at = end_time
            execution.duration_ms = duration_ms

            await self.db.commit()

            return AgentExecutionResponse(
                execution_id=execution.execution_id,
                status=execution.status,
                output_data=execution.output_data,
                started_at=execution.started_at,
                completed_at=execution.completed_at,
                duration_ms=execution.duration_ms,
                token_usage=execution.token_usage
            )

        except Exception as e:
            # 更新执行记录为失败状态
            execution.status = "failed"
            execution.error_message = str(e)
            execution.completed_at = datetime.now(timezone.utc)
            await self.db.commit()

            raise

    async def _execute_system_agent_stream(self, agent, request_data, execution):
        """流式执行系统智能体"""
        import json
        import asyncio

        try:
            # 根据系统智能体分类调用相应的处理器
            from .system_agents import get_system_agent_processor
            processor = get_system_agent_processor(agent.system_category)

            # 为诊断智能体设置数据库会话（用于短期记忆功能）
            if hasattr(processor, 'db_session'):
                processor.db_session = self.db

            # 处理短期记忆控制参数
            input_data = request_data.input_data.copy()

            # 确定是否启用短期记忆
            enable_memory = self._should_enable_short_term_memory(
                agent, getattr(request_data, 'enable_short_term_memory', None)
            )

            # 如果禁用短期记忆，移除会话上下文
            if not enable_memory and '_conversation_context' in input_data:
                logger.info("流式执行：短期记忆已禁用，移除会话上下文")
                input_data.pop('_conversation_context', None)

            # 获取配置覆盖参数
            config_override = getattr(request_data, 'config_override', None)

            # 检查处理器是否支持流式执行
            if hasattr(processor, 'process_stream'):
                # 使用处理器的流式方法
                async for chunk in processor.process_stream(agent, input_data, config_override):
                    # 包装为统一响应格式
                    stream_response = {
                        "status": "success",
                        "msg": "内容流式输出",
                        "data": chunk,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "request_id": execution.execution_id
                    }
                    yield f"data: {json.dumps(stream_response, ensure_ascii=False)}\n\n"
            else:
                # 如果处理器不支持流式，则执行普通处理并分块输出
                result = await processor.process(agent, input_data, config_override)

                # 提取内容并分块流式输出
                content = result.get("content", "") if result.get(
                    "success") else result.get("error", "处理失败")

                if content:
                    # 按句子或段落分块
                    import re
                    chunks = re.split(r'([。\n])', content)
                    current_chunk = ""

                    for i, part in enumerate(chunks):
                        current_chunk += part

                        # 每积累一定长度或遇到分隔符就输出一个chunk
                        if len(current_chunk) >= 20 or part in ['。', '\n'] or i == len(chunks) - 1:
                            if current_chunk.strip():
                                # 包装为统一响应格式
                                chunk_data = {
                                    "type": "content",
                                    "content": current_chunk,
                                    "execution_id": execution.execution_id
                                }
                                stream_response = {
                                    "status": "success",
                                    "msg": "内容流式输出",
                                    "data": chunk_data,
                                    "timestamp": datetime.now(timezone.utc).isoformat(),
                                    "request_id": execution.execution_id
                                }
                                yield f"data: {json.dumps(stream_response, ensure_ascii=False)}\n\n"
                                current_chunk = ""

                                # 添加小延迟以模拟流式效果
                                await asyncio.sleep(0.05)

                # 输出完整结果的元数据
                if result.get("success") and result.get("metadata"):
                    metadata_chunk = {
                        "type": "metadata",
                        "data": result["metadata"],
                        "execution_id": execution.execution_id
                    }
                    # 包装为统一响应格式
                    stream_response = {
                        "status": "success",
                        "msg": "元数据输出",
                        "data": metadata_chunk,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "request_id": execution.execution_id
                    }
                    yield f"data: {json.dumps(stream_response, ensure_ascii=False)}\n\n"

                # 更新执行记录 - 确保数据可序列化
                try:
                    # 深拷贝并清理不可序列化的数据
                    import copy
                    import json
                    serializable_result = copy.deepcopy(result)
                    # 测试是否可以序列化
                    json.dumps(serializable_result, ensure_ascii=False)
                    execution.output_data = serializable_result
                except (TypeError, ValueError) as e:
                    logger.warning(f"结果数据不可序列化，使用简化版本: {e}")
                    # 如果不能序列化，保存简化版本
                    execution.output_data = {
                        "success": result.get("success", False),
                        "content": str(result.get("content", "")),
                        "error": str(result.get("error", "")),
                        "serialization_error": str(e)
                    }

        except Exception as e:
            logger.error(f"系统智能体流式执行失败: {e}")
            error_chunk = {
                "type": "error",
                "error": str(e),
                "execution_id": execution.execution_id
            }
            yield f"data: {json.dumps(error_chunk, ensure_ascii=False)}\n\n"

    async def _execute_custom_agent_stream(self, agent, request_data, execution):
        """流式执行用户自定义智能体"""
        import json
        import asyncio

        try:
            # 用户自定义智能体的流式处理逻辑
            from .custom_agents import CustomAgentProcessor
            processor = CustomAgentProcessor()

            # 处理短期记忆控制参数
            input_data = request_data.input_data.copy()

            # 确定是否启用短期记忆
            enable_memory = self._should_enable_short_term_memory(
                agent, getattr(request_data, 'enable_short_term_memory', None)
            )

            # 如果禁用短期记忆，移除会话上下文
            if not enable_memory and '_conversation_context' in input_data:
                logger.info("自定义智能体流式执行：短期记忆已禁用，移除会话上下文")
                input_data.pop('_conversation_context', None)

            # 获取配置覆盖参数
            config_override = getattr(request_data, 'config_override', None)

            # 检查是否支持流式执行
            if hasattr(processor, 'process_stream'):
                async for chunk in processor.process_stream(agent, input_data, config_override):
                    yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
            else:
                # 执行普通处理并模拟流式输出
                result = await processor.process(agent, input_data, config_override)

                # 简单的流式输出模拟
                content = result.get("content", "自定义智能体处理完成")
                for i, char in enumerate(content):
                    if i % 5 == 0:  # 每5个字符输出一次
                        chunk_data = {
                            "type": "content",
                            "content": content[i:i+5],
                            "execution_id": execution.execution_id
                        }
                        yield f"data: {json.dumps(chunk_data, ensure_ascii=False)}\n\n"
                        await asyncio.sleep(0.1)

                # 更新执行记录
                execution.output_data = result

        except Exception as e:
            logger.error(f"自定义智能体流式执行失败: {e}")
            error_chunk = {
                "type": "error",
                "error": str(e),
                "execution_id": execution.execution_id
            }
            yield f"data: {json.dumps(error_chunk, ensure_ascii=False)}\n\n"
