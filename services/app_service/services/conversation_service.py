"""
会话管理服务
"""
from datetime import datetime, timezone, timedelta
from typing import List, Optional, Dict, Any, Tu<PERSON>
from math import ceil

from sqlalchemy import select, func, and_, or_, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession

from shared.logging_config import get_app_service_logger
from ..models.conversation import (
    Conversation, ConversationType, ConversationStatus
)
from ..schemas.conversation import (
    ConversationCreate, ConversationUpdate, ConversationQueryParams,
    ConversationStatsResponse
)
from .conversation_cache_service import ConversationCacheService

logger = get_app_service_logger()


class ConversationService:
    """会话管理服务"""

    def __init__(self, db: AsyncSession, tenant_id: Optional[int] = None):
        self.db = db
        self.tenant_id = tenant_id
        self.cache_service = ConversationCacheService(tenant_id)

    def _apply_tenant_filter(self, query):
        """应用租户过滤条件"""
        if self.tenant_id:
            return query.where(Conversation.tenant_id == self.tenant_id)
        else:
            return query.where(Conversation.tenant_id.is_(None))

    async def get_by_conversation_id(
        self,
        conversation_id: str,
        user_id: Optional[int] = None
    ) -> Optional[Conversation]:
        """根据conversation_id获取会话"""
        try:
            query = select(Conversation).where(
                Conversation.conversation_id == conversation_id,
                Conversation.is_deleted == False
            )

            # 如果提供了user_id，则添加用户过滤条件
            if user_id is not None:
                query = query.where(Conversation.user_id == user_id)

            # 应用租户过滤
            query = self._apply_tenant_filter(query)

            result = await self.db.execute(query)
            conversation = result.scalar_one_or_none()

            if conversation:
                # 缓存会话信息
                await self.cache_service.cache_conversation(conversation)

            return conversation

        except Exception as e:
            logger.error(
                f"根据conversation_id获取会话失败: conversation_id={conversation_id}, user_id={user_id}, error={e}")
            raise

    async def create_conversation(
        self,
        conversation_data: ConversationCreate,
        user_id: int
    ) -> Conversation:
        """创建会话"""
        try:
            # 计算过期时间
            expires_at = None
            if conversation_data.expires_in_hours:
                expires_at = datetime.now(timezone.utc) + timedelta(
                    hours=conversation_data.expires_in_hours
                )

            # 创建会话对象
            conversation = Conversation(
                title=conversation_data.title,
                conversation_type=conversation_data.conversation_type,
                user_id=user_id,
                tenant_id=self.tenant_id,
                primary_agent_id=conversation_data.primary_agent_id,
                config=conversation_data.config or {},
                context_window_size=conversation_data.context_window_size,
                max_messages=conversation_data.max_messages,
                conversation_id=conversation_data.conversation_id,
                expires_at=expires_at
            )

            self.db.add(conversation)
            await self.db.commit()
            await self.db.refresh(conversation)

            # 缓存会话信息
            await self.cache_service.cache_conversation(conversation)

            # 清除用户会话列表缓存
            await self.cache_service.invalidate_user_cache(user_id)

            logger.info(f"创建会话成功: id={conversation.id}, user_id={user_id}")
            return conversation

        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建会话失败: {e}")
            raise

    async def get_conversation(
        self,
        conversation_id: int,
        user_id: int
    ) -> Optional[Conversation]:
        """获取会话详情"""
        try:
            # 先尝试从缓存获取
            cached_data = await self.cache_service.get_conversation_from_cache(conversation_id)
            if cached_data and cached_data.get("user_id") == user_id:
                # 验证租户权限
                if self.tenant_id and cached_data.get("tenant_id") != self.tenant_id:
                    return None
                if not self.tenant_id and cached_data.get("tenant_id") is not None:
                    return None

                # 从数据库获取完整对象（缓存只存储基础信息）
                query = select(Conversation).where(
                    Conversation.id == conversation_id,
                    Conversation.is_deleted == False
                )

                query = self._apply_tenant_filter(query)
                result = await self.db.execute(query)
                return result.scalar_one_or_none()

            # 从数据库查询
            query = select(Conversation).where(
                Conversation.id == conversation_id,
                Conversation.user_id == user_id,
                Conversation.is_deleted == False
            )

            query = self._apply_tenant_filter(query)
            result = await self.db.execute(query)
            conversation = result.scalar_one_or_none()

            if conversation:
                # 缓存会话信息
                await self.cache_service.cache_conversation(conversation)

            return conversation

        except Exception as e:
            logger.error(
                f"获取会话失败: conversation_id={conversation_id}, error={e}")
            raise

    async def get_conversations(
        self,
        user_id: int,
        params: ConversationQueryParams
    ) -> Tuple[List[Conversation], int]:
        """获取用户会话列表"""
        try:
            # 构建查询
            query = select(Conversation).where(
                Conversation.user_id == user_id,
                Conversation.is_deleted == False
            )

            # 应用租户过滤
            query = self._apply_tenant_filter(query)

            # 应用过滤条件
            if params.conversation_type:
                query = query.where(
                    Conversation.conversation_type == params.conversation_type)
            if params.status:
                query = query.where(Conversation.status == params.status)

            # 计算总数
            count_query = select(func.count(Conversation.id)).where(
                Conversation.user_id == user_id,
                Conversation.is_deleted == False
            )
            count_query = self._apply_tenant_filter(count_query)
            if params.conversation_type:
                count_query = count_query.where(
                    Conversation.conversation_type == params.conversation_type)
            if params.status:
                count_query = count_query.where(
                    Conversation.status == params.status)

            total_result = await self.db.execute(count_query)
            total = total_result.scalar()

            # 应用排序
            if params.order_by == "created_at":
                order_field = Conversation.created_at
            elif params.order_by == "updated_at":
                order_field = Conversation.updated_at
            elif params.order_by == "last_activity_at":
                order_field = Conversation.last_activity_at
            else:
                order_field = Conversation.created_at

            if params.order_desc:
                query = query.order_by(desc(order_field))
            else:
                query = query.order_by(asc(order_field))

            # 应用分页
            offset = (params.page - 1) * params.size
            query = query.offset(offset).limit(params.size)

            result = await self.db.execute(query)
            conversations = result.scalars().all()

            logger.debug(
                f"获取用户会话列表: user_id={user_id}, count={len(conversations)}, total={total}")
            return conversations, total

        except Exception as e:
            logger.error(f"获取用户会话列表失败: user_id={user_id}, error={e}")
            raise

    async def update_conversation(
        self,
        conversation_id: int,
        conversation_data: ConversationUpdate,
        user_id: int
    ) -> Optional[Conversation]:
        """更新会话"""
        try:
            # 获取会话
            conversation = await self.get_conversation(conversation_id, user_id)
            if not conversation:
                return None

            # 更新字段
            update_data = conversation_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(conversation, field, value)

            await self.db.commit()
            await self.db.refresh(conversation)

            # 更新缓存
            await self.cache_service.cache_conversation(conversation)
            await self.cache_service.invalidate_user_cache(user_id)

            logger.info(f"更新会话成功: id={conversation_id}, user_id={user_id}")
            return conversation

        except Exception as e:
            await self.db.rollback()
            logger.error(
                f"更新会话失败: conversation_id={conversation_id}, error={e}")
            raise

    async def delete_conversation(
        self,
        conversation_id: int,
        user_id: int
    ) -> bool:
        """删除会话（软删除）"""
        try:
            # 获取会话
            conversation = await self.get_conversation(conversation_id, user_id)
            if not conversation:
                return False

            # 软删除
            conversation.is_deleted = True
            conversation.status = ConversationStatus.ARCHIVED

            await self.db.commit()

            # 清除缓存
            await self.cache_service.invalidate_conversation_cache(conversation_id)
            await self.cache_service.invalidate_user_cache(user_id)

            logger.info(f"删除会话成功: id={conversation_id}, user_id={user_id}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(
                f"删除会话失败: conversation_id={conversation_id}, error={e}")
            raise

    async def update_conversation_activity(self, conversation_id: int):
        """更新会话活动时间"""
        try:
            query = select(Conversation).where(
                Conversation.id == conversation_id,
                Conversation.is_deleted == False
            )
            result = await self.db.execute(query)
            conversation = result.scalar_one_or_none()

            if conversation:
                conversation.last_activity_at = datetime.now(timezone.utc)
                await self.db.commit()

                # 更新缓存
                await self.cache_service.cache_conversation(conversation)

        except Exception as e:
            logger.error(
                f"更新会话活动时间失败: conversation_id={conversation_id}, error={e}")

    async def increment_message_count(self, conversation_id: int, token_count: int = 0):
        """增加消息计数和Token使用量"""
        try:
            query = select(Conversation).where(
                Conversation.id == conversation_id,
                Conversation.is_deleted == False
            )
            result = await self.db.execute(query)
            conversation = result.scalar_one_or_none()

            if conversation:
                conversation.increment_message_count()
                if token_count > 0:
                    conversation.add_tokens(token_count)

                await self.db.commit()

                # 更新缓存
                await self.cache_service.cache_conversation(conversation)

        except Exception as e:
            logger.error(
                f"更新会话统计失败: conversation_id={conversation_id}, error={e}")

    async def get_conversation_stats(self, user_id: Optional[int] = None) -> ConversationStatsResponse:
        """获取会话统计信息"""
        try:
            # 先尝试从缓存获取
            if not user_id:  # 全局统计
                cached_stats = await self.cache_service.get_conversation_stats_from_cache()
                if cached_stats:
                    return ConversationStatsResponse(**cached_stats)

            # 构建基础查询
            base_query = select(Conversation).where(
                Conversation.is_deleted == False)
            base_query = self._apply_tenant_filter(base_query)

            if user_id:
                base_query = base_query.where(Conversation.user_id == user_id)

            # 总会话数
            total_conversations_result = await self.db.execute(
                select(func.count(Conversation.id)).select_from(
                    base_query.subquery())
            )
            total_conversations = total_conversations_result.scalar()

            # 活跃会话数
            active_conversations_result = await self.db.execute(
                select(func.count(Conversation.id)).select_from(
                    base_query.where(Conversation.status ==
                                     ConversationStatus.ACTIVE).subquery()
                )
            )
            active_conversations = active_conversations_result.scalar()

            # 总消息数和Token数
            message_stats_result = await self.db.execute(
                select(
                    func.sum(Conversation.message_count),
                    func.sum(Conversation.total_tokens)
                ).select_from(base_query.subquery())
            )
            total_messages, total_tokens = message_stats_result.first()
            total_messages = total_messages or 0
            total_tokens = total_tokens or 0

            # 平均每会话消息数
            avg_messages_per_conversation = (
                total_messages / total_conversations if total_conversations > 0 else 0
            )

            # 按类型分组统计
            type_stats_result = await self.db.execute(
                select(
                    Conversation.conversation_type,
                    func.count(Conversation.id)
                ).select_from(base_query.subquery()).group_by(Conversation.conversation_type)
            )
            conversations_by_type = {
                ConversationType.get_name(row[0]): row[1] for row in type_stats_result.fetchall()
            }

            # 按状态分组统计
            status_stats_result = await self.db.execute(
                select(
                    Conversation.status,
                    func.count(Conversation.id)
                ).select_from(base_query.subquery()).group_by(Conversation.status)
            )
            conversations_by_status = {
                ConversationStatus.get_name(row[0]): row[1] for row in status_stats_result.fetchall()
            }

            stats = ConversationStatsResponse(
                total_conversations=total_conversations,
                active_conversations=active_conversations,
                total_messages=total_messages,
                total_tokens=total_tokens,
                avg_messages_per_conversation=round(
                    avg_messages_per_conversation, 2),
                conversations_by_type=conversations_by_type,
                conversations_by_status=conversations_by_status
            )

            # 缓存全局统计
            if not user_id:
                await self.cache_service.cache_conversation_stats(stats.dict())

            return stats

        except Exception as e:
            logger.error(f"获取会话统计失败: error={e}")
            raise
