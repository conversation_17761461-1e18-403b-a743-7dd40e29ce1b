class ReportGenerationPrompts:
    def __init__(self):
        self._output_requirements = """
        # 输出要求
        1. 请严格按输出样例进行JSON格式输出，仅输出合法的JSON，禁止任何额外解释、注释或Markdown符号。
        2. 所有字段值必须使用字符串类型，若某项无有效数据则填写空字符串""，禁止使用null或其他类型。。
        3. 生成时必须替换所有参考值，禁止保留任何占位符文字（如"xxxx内容"、"xxxx建议"等示例文本）。
        4. 若涉及专业医学内容，请确保信息符合最新临床指南，并标注参考文献来源。
        5. 数值型数据需附带单位（如"30ml/kg"），禁止出现模糊表述。
        6. 请将所有字段内容以字符串格式输出，不要嵌套JSON对象或数组。最终输出结果必须是一个完整的、平铺的JSON格式，所有值都必须是字符串类型。
        7. 字段输出样例仅作内容结构的参考，实际内容需根据业务数据生成，禁止直接复制样例数据。
        """

    def health_management_prompt(self):
        return rf"""
        作为专业宠物健康顾问，请根据用户输入的宠物信息生成结构化综合健康管理报告：
        
        {self._output_requirements}
        
        # 字段要求
        1. 健康评估：结合品种特性与当前年龄段对宠物进行概括，健康体重范围、性格特征、日常习惯等，300字左右；要求是一句话概括品种核心特点，有亮点。必须有文献出处支持。文献出处为固定字段；输出内容包括简介、性格特征（列出四条）、日常习惯（列出四条）、文献出处。
        2. 疾病风险：宠物高发疾病风险，根据新瑞鹏病例库对于高发疾病进行举例，【比如根据新瑞鹏XX病例得出结论】体现报告的权威性，包含疾病患病风险、需要对疾病做出简单解释，筛查建议、预防措施、紧急情况预警。列明前三种疾病，案例数量占比；输出内容包括疾病名称/疾病解释/判断依据/检查建议/预防措施/紧急情况预警,不要输出相关检查的价格或价钱。
        3. 营养饮食建议：营养饮食建议要包含饮食结构优化、营养补充方案、水分管理。
            饮食结构优化包含主粮类型比如鸡肉/鱼肉，湿粮干粮比例，零食摄入建议等等，不要拆解过细，用户大部分喂养成品宠物食品，建议中使用用成品举例但不要出现商品名及品牌名；饮食结构优化的输出内容包括饮食结构建议/文献来源。
            营养补充方案要包含建议宠物补充的营养元素并写清作用，补剂所预防的疾病，不要太书面，给出切实的建议，不要出现具体的品牌推荐；营养补充方案的输出内容要包括营养补充方案建议/文献来源。
            水分管理要包含目标饮水量、增加饮水的方法；水分管理的输出内容要包括水分管理建议/文献来源。
        4. 洗护与运动建议：包含体重管理（活动时间、活动强度）、疫苗绝育建议、口腔管理（清洁频次）、清洁护理（洗澡频次）、身心健康等；输出内容包括标准体重范围/体重维持建议/口腔护理建议/环境建议/毛发管理建议。
        5. 生活建议：提供日常护理、环境管理等方面的综合建议，提供日常护理（如毛发梳理、牙齿清洁）、环境管理（如温湿度控制、安全防护）等方面的综合建议，并针对特殊需求（如老年犬防滑垫、猫咪攀爬架）给出优化方案。
        6. 体检与监测：营养饮食建议要包含定期检查、定期护理。
            定期检查包含频次、项目。结合年龄，突出检查的必要性，最好有检查的市场价格以供用户参考。输出内容要包括检项目名/频次/原因/依据/参考市场价。
            定期护理包含非医疗类的护理，洗护、剪指甲、驱虫等等。输出内容要包括项目名/频次/原因/参考市场价。
            
        # 字段输出样例
        健康评估:coco是一只处于青壮年的东方短毛猫，这个阶段的coco可能性格活泼好动，性格稳定性逐渐增强，但依然保留较强探索欲。健康体重范围在3.5-5.5kg。建议通过定期互动和环境丰富化（如猫爬架、隐藏玩具）满足其身心需求，同时注意观察行为变化以及时发现健康问题。\n性格特征：\n1.活泼好动：精力充沛，喜欢跳跃和追逐游戏。\n2.社交黏人：对主人依赖度高，常通过蹭人、跟随等方式表达亲昵。\n3.聪明机敏；学习能力强，对新鲜事物充满好奇心。\n4.温顺友善：通常与家庭成员及其他宠物相处融洽。\n日常习惯：\n1.作息规律：白天活动频繁，夜间睡眠时间较长，但可能因狩猎本能短暂清醒。\n2.清洁习惯：有规律舔毛行为，短毛特性使其掉毛量相对较少，但仍需定期梳理保持皮\n毛健康。\n3.饮食偏好：对高蛋白食物兴趣较高，可能因活泼性格而消耗能量快，需注意控制食量避免肥胖。\n4.互动需求：需要每日互动游戏，独处时可能因无聊而出现破坏行为。\n文献来源：《XXXXX》
        疾病风险：①　肥厚型心肌病（HCM）[东方短毛猫高发]，是一种猫咪先天心脏疾病，严重时危及生命\n判断依据：根据新瑞鹏病例库，接诊3000只东方短毛猫中，有1200只有心肌问题或倾向。\n建议检查：心脏超声（心脏超声）/NT-proBNP检测\n预防措施：维持健康体重，定期体检，保护心脏，可考虑摄入辅酶Q10\n紧急情况预警（出现以下症状需立即就诊）：\n●呼吸急促（>40次/分钟）\n②　公猫尿道阻塞风险（虽已绝育仍需关注），是因为猫咪水摄入不足导致的泌尿系统疾病，表现为频繁出入厕所，尿少无尿\n    判断依据：根据新瑞鹏病例库，公猫产生泌尿问题高达40%，文献xxxx,\n预防措施：饮水量监测（目标>60ml/kg/day），\n建议检查：尿液分析（尿常规）\n紧急情况预警（出现以下症状需立即就诊）：\n●排尿困难/血尿
        营养饮食建议：1.饮食结构建议\n①　主粮优先选择鸡肉/鱼肉（占比40-50%），避免单一鱼类（如金枪鱼）过量（预防维生素A中毒），主粮选择粗蛋白大于30%的猫粮。\n②　零食摄入量不要超过整日食物摄入占比的30%\n文献来源：XXXXX\n\n2.营养补充方案：\n①　必需脂肪酸：添加鱼油，建议每日补充（剂量：200mg EPA+DHA/5kg体重）\n②　抗氧化剂：维生素E（50IU/天）+硒（0.1mg/天）\n③　益生菌：布拉氏酵母菌（1×10^9 CFU/天）\n文献来源：XXXXX\n\n3.水分管理：\n①　目标饮水量：60ml/kg/day（当前体重3.5kg需210ml/天）\n②　实现方式：湿粮占比>70%\n③　使用流动饮水器\n④　添加风味电解质输出字段：水分管理建议/文献来源\n文献来源：XXXXX
        洗护与运动建议：①　体重管理\n品种标准体重范围：3.5-5.5kg\n建议：每月体重监测，活动时间建议，选择低嘌呤处方粮（预防尿酸结石）\n②　口腔护理\n品种易发牙周病\n建议：每日刷牙（使用宠物专用牙膏），每6个月口腔检查\n③　环境管理\n    互动时间每天半小时以上，有2种以上玩具丰容\n④　毛发管理\n毛发管理：每周2次梳毛（预防脱毛）,无皮肤问题不要频繁洗澡，建议3个月以上洗一次
        生活建议：3-5条生活建议。
        体检与监测：1.定期检查：频次、项目。结合年龄，突出检查的必要性，最好有检查的市场价格以供用户参考\n①　心脏监测：心超+血压测量\n频次：每6个月检查，若有咳嗽等现象应尽快检查\n原因：心肌炎检查，若有问题尽早干预\n依据：xxxxx\n市场参考价：600元/次\n②　家庭监测呼吸频率（正常<30次/分钟）\n频次：日常家庭观测，1月左右进行一次\n原因：xxxxxx\n依据：xxxxx\n③　泌尿系统：尿比重检测（目标1.035-1.045）\n频次：有病史需每月检查，无病史半年体检时检查\n原因：xxxxx\n依据：xxxxx\n市场参考价：600元/次\n2.定期护理：\n①　洗护：洗澡除废毛\n频次：每3个月洗，日常可用宠物湿巾清洁\n依据：xxxxx\n①　驱虫：体内外一体驱虫\n频次：每3个月驱虫\n依据：xxxxx
        
        # 输出格式
        {{
           "health_assessment": "健康评估内容",
           "disease_risk": "疾病风险内容",
           "nutritional_recommendations": "营养饮食建议内容",
           "grooming_and_exercise": "洗护与运动建议",
           "daily_care_tips": "生活建议",
           "health_monitoring_plan": "体检与监测",
        }}
        """

    def disease_risk_prompt(self):
        return f"""
        作为专业宠物健康顾问，请根据用户输入的宠物信息生成结构化疾病风险预警报告：
        
        # 字段要求
        1. 疾病风险预警：预测宠物潜在的疾病风险并提供预警信息,预测潜在高发疾病，如品种遗传病、年龄相关疾病、环境诱发疾病，并提供具体预警指标和预防措施。
        2. 综合健康建议：提供的针对性健康管理方案,整合营养、运动、护理、环境等维度的针对性方案。
        
        {self._output_requirements}
        
        输出样例：
        json
        {{
           "disease_risk": "疾病风险预警内容",
           "health_assessment": "综合健康建议内容",
        }}
        """

    def vaccination_plan_prompt(self):
        return f"""
        作为专业宠物健康顾问，请根据用户输入的宠物信息生成结构化疫苗接种计划报告：

        # 字段要求
        1. 基础免疫（核心程序）：基础免疫（核心程序）是保障宠物健康的关键防护，为宠物抵御常见高风险传染病提供基础保障。
        2. 进阶（非核心）免疫推荐：进阶（非核心）免疫推荐是根据宠物生活环境、活动范围等具体情况给出的额外防护建议，可进一步提升宠物的健康保障水平。
        
        {self._output_requirements}

        输出样例：
        json
        {{
            "core_vaccination_protocol": "基础免疫（核心程序）内容",
            "non_core_vaccination_recommendations": "进阶（非核心）免疫推荐内容"
        }}
        """

    def deworming_plan_prompt(self, deworming_plan_target: str = "根据用户提供的宠物信息智能推荐,明确本次驱虫计划的核心目标如预防心丝虫跳蚤绦虫等需具体到寄生虫种类"):
        return f"""
        作为专业宠物健康顾问，请根据用户输入的宠物信息生成结构化驱虫计划报告：

        # 字段要求
        1. 驱虫目标：{deworming_plan_target}。
        2. 驱虫方案：严格基于驱虫目标（重点）制定驱虫方案,基于目标制定具体方案包含药物名称使用频率剂量及注意事项。
        3. 环境管理：根据用户提供的宠物信息智能推荐,针对宠物生活环境给出消杀频率重点区域及用品推荐。
        4. 复诊提醒：根据驱虫方案、宠物情况智能推荐,根据用药周期和宠物状况标注下次检测时间节点。
        5. 风险预警：根据驱虫方案、宠物情况智能预警可能存在的风险,列出药物不良反应监测指标及应急处理方案。
        6. 依据出处：标注参考的权威指南名称和版本。
        
        {self._output_requirements}

        输出样例：
        json
        {{
            "parasite_risk_assessment": "驱虫目标内容",
            "deworming_protocol": "驱虫方案内容",
            "environmental_management": "环境管理内容",
            "follow_up_schedule": "复诊提醒内容",
            "health_risk_warning": "风险预警内容",
            "clinical_references":  "依据出处内容"
        }}
        """

    def nutritional_diet_prompt(self, nutritional_diet_target: str):
        return f"""
        作为专业宠物健康顾问，请根据用户输入的宠物信息生成结构化营养饮食方案：

        # 字段要求
        1. 饮食目标：{f"{nutritional_diet_target}" if nutritional_diet_target else "根据用户提供的宠物信息智能推荐,明确需要解决的核心营养问题"}。
        2. 饮食结构：严格基于饮食目标（重点）及宠物信息智能制定方案,制定具体方案包含主粮类型每日热量分配加餐建议及禁忌食物。
        3. 饮水管理：严格基于饮食目标（重点）及宠物信息智能制定方案,根据宠物体质给出每日饮水量要求水质建议及补水方式。
        4. 监测指标：基于饮食方案、宠物情况智能推荐,列出需重点关注的体征如体重变化毛发状态排便频率等。
        5. 风险预警：根据饮食方案、宠物情况智能预警可能存在的风险,标注可能出现的饮食不良反应及应对措施。
        6. 复诊提醒：根据饮食方案、宠物情况智能预警可能存在的风险,根据饮食调整周期标注下次营养评估时间节点。
        7. 依据出处：标注参考的权威指南名称和版本。
        
        {self._output_requirements}

        输出样例：
        json
        {{
            "nutritional_goals": "饮食目标内容",
            "dietary_composition": "饮食结构内容",
            "hydration_management": "饮水管理内容",
            "monitoring_parameters": "监测指标内容",
            "follow_up_schedule": "复诊提醒内容",
            "health_risk_warning": "风险预警内容",
            "clinical_references": "依据出处内容"
        }}
        """

    def chronic_disease_prompt(self):
        return f"""
        作为专业宠物健康顾问，请根据用户输入的宠物信息生成结构化慢病管理方案：

        # 字段要求
        1. 核心管理目标：基于宠物信息智能制定方案,基于宠物个体特征明确首要解决的健康问题如血糖控制关节养护或皮肤病管理。
        2. 基础管理：关于饮食、体重、生活部分的基础管理内容，智能制定方案,制定饮食方案热量标准体重控制目标及日常活动安排。
        3. 治疗建议：基于宠物信息，智能生成治疗建议,提供药物名称剂量使用频率及物理治疗等具体方案。
        4. 监测管理：基于宠物情况智能生成,列出需定期检测的生理指标和观察要点。
        5. 并发症预防与管理：基于宠物情况智能生成,预测可能出现的继发问题及预防处置措施。
        6. 复诊建议：基于宠物情况智能生成,根据治疗方案标注关键复诊时间节点和必检项目。
        7. 依据出处：标注参考的权威指南名称和版本。

        {self._output_requirements}

        输出样例：
        json
        {{
            "core_management_objectives": "核心管理目标内容",
            "basic_management": "基础管理内容",
            "treatment_recommendations": "治疗建议内容",
            "monitoring_management": "监测管理内容",
            "complication_prevention": "并发症预防与管理内容",
            "follow_up_recommendations": "复诊建议内容"
            "clinical_references": "依据出处内容"
        }}
        """
