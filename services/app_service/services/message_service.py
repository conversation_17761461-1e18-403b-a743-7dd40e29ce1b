"""
消息管理服务
"""
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any, Tuple

from sqlalchemy import select, func, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from shared.logging_config import get_app_service_logger
from ..models.conversation import (
    Message, MessageRole, MessageType, MessageStatus
)
from ..schemas.conversation import (
    MessageCreate, MessageUpdate, MessageQueryParams
)
from .conversation_service import ConversationService
from .conversation_cache_service import ConversationCacheService

logger = get_app_service_logger()


class MessageService:
    """消息管理服务"""

    def __init__(self, db: AsyncSession, tenant_id: Optional[int] = None):
        self.db = db
        self.tenant_id = tenant_id
        self.conversation_service = ConversationService(db, tenant_id)
        self.cache_service = ConversationCacheService(tenant_id)

    async def create_message(
        self,
        conversation_id: int,
        message_data: MessageCreate,
        user_id: int,
        role: MessageRole = MessageRole.USER,
        agent_id: Optional[int] = None
    ) -> Message:
        """创建消息"""
        try:
            # 验证会话权限
            conversation = await self.conversation_service.get_conversation(conversation_id, user_id)
            if not conversation:
                raise ValueError("会话不存在或无权限访问")

            # 检查会话是否活跃
            if not conversation.is_active():
                raise ValueError("会话已过期或不可用")

            # 检查消息数量限制
            if conversation.message_count >= conversation.max_messages:
                raise ValueError("会话消息数量已达上限")

            # 获取下一个序号
            sequence_number = await self._get_next_sequence_number(conversation_id)

            # 处理OCR结果
            ocr_result = None
            has_ocr_content = False
            if message_data.ocr_results:
                ocr_result = {
                    "results": message_data.ocr_results,
                    "processed_at": datetime.now(timezone.utc).isoformat()
                }
                has_ocr_content = True

            # 创建消息对象
            message = Message(
                conversation_id=conversation_id,
                agent_id=agent_id,
                role=role,
                message_type=message_data.message_type,
                content=message_data.content,
                message_metadata=message_data.metadata,
                attachments=message_data.attachments,
                ocr_result=ocr_result,
                has_ocr_content=has_ocr_content,
                sequence_number=sequence_number,
                status=MessageStatus.COMPLETED
            )

            self.db.add(message)
            await self.db.commit()
            await self.db.refresh(message)

            # 更新会话统计
            await self.conversation_service.increment_message_count(conversation_id)

            # 缓存消息
            await self.cache_service.cache_message(message)

            # 更新最近消息缓存
            await self._update_recent_messages_cache(conversation_id)

            logger.info(
                f"创建消息成功: id={message.id}, conversation_id={conversation_id}")
            return message

        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建消息失败: {e}")
            raise

    async def create_assistant_message(
        self,
        conversation_id: int,
        content: str,
        user_id: int,
        agent_id: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None,
        token_usage: Optional[Dict[str, Any]] = None,
        processing_time_ms: Optional[int] = None
    ) -> Message:
        """创建助手消息"""
        try:
            # 验证会话权限
            conversation = await self.conversation_service.get_conversation(conversation_id, user_id)
            if not conversation:
                raise ValueError("会话不存在或无权限访问")

            # 获取下一个序号
            sequence_number = await self._get_next_sequence_number(conversation_id)

            # 创建助手消息
            message = Message(
                conversation_id=conversation_id,
                agent_id=agent_id,
                role=MessageRole.ASSISTANT,
                message_type=MessageType.TEXT,
                content=content,
                message_metadata=metadata,
                token_usage=token_usage,
                processing_time_ms=processing_time_ms,
                sequence_number=sequence_number,
                status=MessageStatus.COMPLETED
            )

            self.db.add(message)
            await self.db.commit()
            await self.db.refresh(message)

            # 更新会话统计
            token_count = token_usage.get(
                "total_tokens", 0) if token_usage else 0
            await self.conversation_service.increment_message_count(conversation_id, token_count)

            # 缓存消息
            await self.cache_service.cache_message(message)

            # 更新最近消息缓存
            await self._update_recent_messages_cache(conversation_id)

            logger.info(
                f"创建助手消息成功: id={message.id}, conversation_id={conversation_id}")
            return message

        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建助手消息失败: {e}")
            raise

    async def get_message(
        self,
        message_id: int,
        user_id: int
    ) -> Optional[Message]:
        """获取消息详情"""
        try:
            query = select(Message).where(
                Message.id == message_id,
                Message.is_deleted == False
            ).options(
                selectinload(Message.conversation),
                selectinload(Message.agent)
            )

            result = await self.db.execute(query)
            message = result.scalar_one_or_none()

            if not message:
                return None

            # 验证用户权限
            if message.conversation.user_id != user_id:
                return None

            # 验证租户权限
            if self.tenant_id and message.conversation.tenant_id != self.tenant_id:
                return None
            if not self.tenant_id and message.conversation.tenant_id is not None:
                return None

            return message

        except Exception as e:
            logger.error(f"获取消息失败: message_id={message_id}, error={e}")
            raise

    async def get_messages(
        self,
        conversation_id: int,
        user_id: int,
        params: MessageQueryParams
    ) -> Tuple[List[Message], int]:
        """获取会话消息列表"""
        try:
            # 验证会话权限
            conversation = await self.conversation_service.get_conversation(conversation_id, user_id)
            if not conversation:
                raise ValueError("会话不存在或无权限访问")

            # 构建查询
            query = select(Message).where(
                Message.conversation_id == conversation_id,
                Message.is_deleted == False
            )

            # 应用过滤条件
            if params.role:
                query = query.where(Message.role == params.role)
            if params.message_type:
                query = query.where(Message.message_type ==
                                    params.message_type)
            if params.status:
                query = query.where(Message.status == params.status)

            # 计算总数
            count_query = select(func.count(Message.id)).where(
                Message.conversation_id == conversation_id,
                Message.is_deleted == False
            )
            if params.role:
                count_query = count_query.where(Message.role == params.role)
            if params.message_type:
                count_query = count_query.where(
                    Message.message_type == params.message_type)
            if params.status:
                count_query = count_query.where(
                    Message.status == params.status)

            total_result = await self.db.execute(count_query)
            total = total_result.scalar()

            # 应用排序
            if params.order_by == "sequence_number":
                order_field = Message.sequence_number
            elif params.order_by == "created_at":
                order_field = Message.created_at
            else:
                order_field = Message.sequence_number

            if params.order_desc:
                query = query.order_by(desc(order_field))
            else:
                query = query.order_by(asc(order_field))

            # 应用分页
            offset = (params.page - 1) * params.size
            query = query.offset(offset).limit(params.size)

            result = await self.db.execute(query)
            messages = result.scalars().all()

            logger.debug(
                f"获取会话消息列表: conversation_id={conversation_id}, count={len(messages)}, total={total}")
            return messages, total

        except Exception as e:
            logger.error(
                f"获取会话消息列表失败: conversation_id={conversation_id}, error={e}")
            raise

    async def update_message(
        self,
        message_id: int,
        message_data: MessageUpdate,
        user_id: int
    ) -> Optional[Message]:
        """更新消息"""
        try:
            # 获取消息
            message = await self.get_message(message_id, user_id)
            if not message:
                return None

            # 更新字段
            update_data = message_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(message, field, value)

            await self.db.commit()
            await self.db.refresh(message)

            # 更新缓存
            await self.cache_service.cache_message(message)
            await self._update_recent_messages_cache(message.conversation_id)

            logger.info(f"更新消息成功: id={message_id}")
            return message

        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新消息失败: message_id={message_id}, error={e}")
            raise

    async def delete_message(
        self,
        message_id: int,
        user_id: int
    ) -> bool:
        """删除消息（软删除）"""
        try:
            # 获取消息
            message = await self.get_message(message_id, user_id)
            if not message:
                return False

            # 软删除
            message.is_deleted = True

            await self.db.commit()

            # 更新缓存
            await self._update_recent_messages_cache(message.conversation_id)

            logger.info(f"删除消息成功: id={message_id}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"删除消息失败: message_id={message_id}, error={e}")
            raise

    async def get_conversation_context_messages(
        self,
        conversation_id: int,
        user_id: int,
        window_size: Optional[int] = None
    ) -> List[Message]:
        """获取会话上下文消息"""
        try:
            # 验证会话权限
            conversation = await self.conversation_service.get_conversation(conversation_id, user_id)
            if not conversation:
                raise ValueError("会话不存在或无权限访问")

            # 使用会话配置的窗口大小
            if window_size is None:
                window_size = conversation.context_window_size

            # 获取最近的消息
            query = select(Message).where(
                Message.conversation_id == conversation_id,
                Message.is_deleted == False
            ).order_by(desc(Message.sequence_number)).limit(window_size)

            result = await self.db.execute(query)
            messages = result.scalars().all()

            # 按序号正序返回
            return list(reversed(messages))

        except Exception as e:
            logger.error(
                f"获取会话上下文消息失败: conversation_id={conversation_id}, error={e}")
            raise

    async def get_recent_user_messages(
        self,
        conversation_id: int,
        user_id: int,
        limit: int = 10
    ) -> List[Message]:
        """获取会话中最近的用户消息历史（用于短期记忆）"""
        try:
            # 验证会话权限
            conversation = await self.conversation_service.get_conversation(conversation_id, user_id)
            if not conversation:
                raise ValueError("会话不存在或无权限访问")

            # 获取最近的用户消息，按时间倒序
            query = select(Message).where(
                Message.conversation_id == conversation_id,
                Message.role == MessageRole.USER,  # 只获取用户消息
                Message.is_deleted == False
            ).order_by(desc(Message.created_at)).limit(limit)

            result = await self.db.execute(query)
            messages = result.scalars().all()

            # 按时间正序返回（最早的在前面）
            return list(reversed(messages))

        except Exception as e:
            logger.error(
                f"获取用户消息历史失败: conversation_id={conversation_id}, error={e}")
            raise

    async def _get_next_sequence_number(self, conversation_id: int) -> int:
        """获取下一个消息序号"""
        try:
            result = await self.db.execute(
                select(func.max(Message.sequence_number)).where(
                    Message.conversation_id == conversation_id
                )
            )
            max_seq = result.scalar()
            return (max_seq or 0) + 1
        except Exception as e:
            logger.error(
                f"获取消息序号失败: conversation_id={conversation_id}, error={e}")
            return 1

    async def _update_recent_messages_cache(self, conversation_id: int):
        """更新最近消息缓存"""
        try:
            # 获取最近的消息
            query = select(Message).where(
                Message.conversation_id == conversation_id,
                Message.is_deleted == False
            ).order_by(desc(Message.sequence_number)).limit(20)

            result = await self.db.execute(query)
            messages = result.scalars().all()

            # 更新缓存
            await self.cache_service.cache_recent_messages(conversation_id, list(reversed(messages)))

        except Exception as e:
            logger.error(
                f"更新最近消息缓存失败: conversation_id={conversation_id}, error={e}")
