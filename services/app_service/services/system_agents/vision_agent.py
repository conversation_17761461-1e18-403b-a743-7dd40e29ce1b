"""
AI视觉识别智能体处理器
"""
from typing import Dict, Any, Optional, List
import base64
import httpx
from .base import BaseSystemAgentProcessor
from ...models.agent import Agent
from shared.logging_config import get_app_service_logger

logger = get_app_service_logger()


class VisionAgentProcessor(BaseSystemAgentProcessor):
    """AI视觉识别智能体处理器"""
    
    def get_default_prompt(self) -> str:
        """获取默认提示词模板"""
        return """你是一位专业的宠物医疗AI视觉分析专家，能够分析宠物相关的图像并提供专业的医疗建议。

分析类型：{analysis_type}

请仔细观察图像中的以下方面：
1. 宠物的整体状态和精神状况
2. 皮肤、毛发的健康状况
3. 眼部、鼻部、口腔的异常情况
4. 体态、姿势是否正常
5. 任何可见的外伤、肿胀或异常
6. 环境因素对宠物健康的影响

额外上下文信息：
{additional_context}

请按照以下格式提供分析：

## 图像概述
[描述图像中看到的宠物和环境情况]

## 健康状况评估
[基于视觉观察的健康状况分析]

## 发现的异常
[列出任何观察到的异常情况]

## 专业建议
[基于观察结果提供的建议]

## 进一步检查建议
[建议进行的进一步检查或就医建议]

注意：此分析基于图像观察，不能替代现场专业兽医检查。如发现异常，请及时就医。"""
    
    async def process(
        self, 
        agent: Agent, 
        input_data: Dict[str, Any], 
        config_override: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """处理AI视觉识别请求"""
        try:
            # 验证输入数据
            self._validate_input(input_data, ["image_urls", "analysis_type"])
            
            # 提取数据
            image_urls = input_data["image_urls"]
            analysis_type = input_data["analysis_type"]
            additional_context = input_data.get("additional_context", "无")
            
            # 验证图片URL列表
            if not isinstance(image_urls, list) or not image_urls:
                return self._format_error_response("图片URL列表不能为空")
            
            if len(image_urls) > 5:  # 限制最多5张图片
                return self._format_error_response("最多支持5张图片同时分析")
            
            # 验证分析类型
            valid_analysis_types = [
                "health_check",      # 健康检查
                "skin_condition",    # 皮肤状况
                "injury_assessment", # 外伤评估
                "behavior_analysis", # 行为分析
                "general_analysis"   # 综合分析
            ]
            
            if analysis_type not in valid_analysis_types:
                return self._format_error_response(f"不支持的分析类型: {analysis_type}")
            
            # 下载和处理图片
            processed_images = await self._process_images(image_urls)
            if not processed_images:
                return self._format_error_response("无法处理任何图片")
            
            # 构建上下文
            context = {
                "analysis_type": self._get_analysis_type_description(analysis_type),
                "additional_context": additional_context
            }
            
            # 构建系统提示词
            system_prompt = self._build_system_prompt(agent, context)
            
            # 构建用户消息（包含图片）
            user_message = self._build_user_message_with_images(
                analysis_type, additional_context, processed_images
            )
            
            # 准备消息
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ]
            
            # 合并配置
            config = self._merge_config(agent, config_override)
            
            # 调用LLM（注意：这里需要支持视觉的模型）
            llm_response = await self._call_vision_llm(messages, config, processed_images)
            
            # 解析和结构化响应
            structured_response = self._parse_vision_response(llm_response["content"])
            
            # 构建最终响应
            result = self._format_success_response(
                content=llm_response["content"],
                metadata={
                    "structured_analysis": structured_response,
                    "analysis_type": analysis_type,
                    "image_count": len(processed_images),
                    "model_used": llm_response["model"],
                    "token_usage": llm_response["usage"],
                    "confidence_level": self._assess_confidence(llm_response["content"])
                }
            )
            
            logger.info(f"AI视觉识别处理完成，分析类型: {analysis_type}, 图片数量: {len(processed_images)}")
            return result
            
        except Exception as e:
            logger.error(f"AI视觉识别处理失败: {e}")
            return self._format_error_response(f"处理失败: {str(e)}")
    
    async def _process_images(self, image_urls: List[str]) -> List[Dict[str, Any]]:
        """处理图片URL，下载并转换为base64"""
        processed_images = []
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            for i, url in enumerate(image_urls):
                try:
                    # 下载图片
                    response = await client.get(url)
                    response.raise_for_status()
                    
                    # 检查文件大小（限制10MB）
                    if len(response.content) > 10 * 1024 * 1024:
                        logger.warning(f"图片 {i+1} 过大，跳过处理")
                        continue
                    
                    # 检查内容类型
                    content_type = response.headers.get("content-type", "")
                    if not content_type.startswith("image/"):
                        logger.warning(f"图片 {i+1} 格式不支持: {content_type}")
                        continue
                    
                    # 转换为base64
                    image_base64 = base64.b64encode(response.content).decode('utf-8')
                    
                    processed_images.append({
                        "index": i + 1,
                        "url": url,
                        "content_type": content_type,
                        "size": len(response.content),
                        "base64": image_base64
                    })
                    
                except Exception as e:
                    logger.warning(f"处理图片 {i+1} 失败: {e}")
                    continue
        
        return processed_images
    
    def _get_analysis_type_description(self, analysis_type: str) -> str:
        """获取分析类型描述"""
        descriptions = {
            "health_check": "全面健康检查 - 评估宠物的整体健康状况",
            "skin_condition": "皮肤状况分析 - 重点关注皮肤、毛发健康",
            "injury_assessment": "外伤评估 - 分析可见的外伤和损伤",
            "behavior_analysis": "行为分析 - 通过姿态和表情分析行为状态",
            "general_analysis": "综合分析 - 全方位的图像分析"
        }
        return descriptions.get(analysis_type, "综合分析")
    
    def _build_user_message_with_images(
        self, 
        analysis_type: str, 
        additional_context: str, 
        processed_images: List[Dict[str, Any]]
    ) -> str:
        """构建包含图片的用户消息"""
        message_parts = [
            f"请对以下{len(processed_images)}张宠物图片进行{self._get_analysis_type_description(analysis_type)}：",
            ""
        ]
        
        for img in processed_images:
            message_parts.append(f"图片 {img['index']}: {img['url']}")
        
        if additional_context and additional_context != "无":
            message_parts.extend([
                "",
                f"额外上下文信息：{additional_context}"
            ])
        
        message_parts.extend([
            "",
            "请提供详细的专业分析和建议。"
        ])
        
        return "\n".join(message_parts)
    
    async def _call_vision_llm(
        self, 
        messages: list, 
        config: Dict[str, Any],
        processed_images: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """调用支持视觉的LLM服务"""
        try:
            # 注意：这里需要使用支持视觉的模型
            # 如果当前模型不支持视觉，可能需要切换到支持的模型
            vision_model = config.get("vision_model", "gpt-4-vision-preview")
            
            client = self._get_openai_client(vision_model)
            
            # 构建包含图片的消息
            vision_messages = []
            for msg in messages:
                if msg["role"] == "user":
                    # 为用户消息添加图片
                    content = [{"type": "text", "text": msg["content"]}]
                    
                    for img in processed_images:
                        content.append({
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:{img['content_type']};base64,{img['base64']}"
                            }
                        })
                    
                    vision_messages.append({
                        "role": "user",
                        "content": content
                    })
                else:
                    vision_messages.append(msg)
            
            response = await client.chat.completions.create(
                model=vision_model,
                messages=vision_messages,
                temperature=config.get("temperature", 0.7),
                max_tokens=config.get("max_tokens", 2048),
                top_p=config.get("top_p", 0.9),
                stream=False
            )
            
            # 提取响应内容
            content = response.choices[0].message.content
            
            # 构建返回结果
            result = {
                "content": content,
                "model": response.model,
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                "finish_reason": response.choices[0].finish_reason
            }
            
            return result
            
        except Exception as e:
            logger.error(f"调用视觉LLM服务失败: {e}")
            # 如果视觉模型调用失败，回退到文本模型
            return await self._call_llm(messages, config)
    
    def _parse_vision_response(self, response_content: str) -> Dict[str, Any]:
        """解析视觉分析响应"""
        try:
            sections = {
                "image_overview": "",
                "health_assessment": "",
                "abnormalities": [],
                "recommendations": [],
                "further_examination": ""
            }
            
            # 按标题分割内容
            lines = response_content.split('\n')
            current_section = None
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # 识别章节标题
                if "图像概述" in line:
                    current_section = "image_overview"
                elif "健康状况评估" in line:
                    current_section = "health_assessment"
                elif "发现的异常" in line:
                    current_section = "abnormalities"
                elif "专业建议" in line:
                    current_section = "recommendations"
                elif "进一步检查建议" in line:
                    current_section = "further_examination"
                elif current_section:
                    # 添加内容到当前章节
                    if current_section in ["image_overview", "health_assessment", "further_examination"]:
                        sections[current_section] += line + "\n"
                    elif line.startswith(("-", "•", "*")) or line[0].isdigit():
                        # 列表项
                        clean_line = line.lstrip("-•***********. ")
                        if current_section in ["abnormalities", "recommendations"]:
                            sections[current_section].append(clean_line)
            
            return sections
            
        except Exception as e:
            logger.warning(f"解析视觉分析响应失败: {e}")
            return {"raw_content": response_content}
    
    def _assess_confidence(self, response_content: str) -> str:
        """评估分析置信度"""
        # 置信度关键词
        high_confidence_keywords = ["明显", "清楚", "确定", "显著"]
        low_confidence_keywords = ["可能", "疑似", "建议进一步", "需要确认"]
        
        response_text = response_content.lower()
        
        high_count = sum(1 for keyword in high_confidence_keywords if keyword in response_text)
        low_count = sum(1 for keyword in low_confidence_keywords if keyword in response_text)
        
        if high_count > low_count:
            return "high"
        elif low_count > high_count:
            return "low"
        else:
            return "medium"
