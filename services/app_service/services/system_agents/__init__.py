"""
系统级智能体处理器
"""
from typing import Dict, Any
from ...models.agent import SystemAgentCategory
from .base import BaseSystemAgentProcessor
from .diagnosis_agent import DiagnosisAgentProcessor
from .vision_agent import VisionAgentProcessor
from .report_generation_agent import ReportGenerationAgentProcessor
from .report_analysis_agent import ReportAnalysisAgentProcessor


# 系统智能体处理器映射
SYSTEM_AGENT_PROCESSORS: Dict[SystemAgentCategory, BaseSystemAgentProcessor] = {
    SystemAgentCategory.DIAGNOSIS: DiagnosisAgentProcessor(),
    SystemAgentCategory.VISION: VisionAgentProcessor(),
    SystemAgentCategory.REPORT_GENERATION: ReportGenerationAgentProcessor(),
    SystemAgentCategory.REPORT_ANALYSIS: ReportAnalysisAgentProcessor(),
}


def get_system_agent_processor(category: SystemAgentCategory) -> BaseSystemAgentProcessor:
    """获取系统智能体处理器"""
    processor = SYSTEM_AGENT_PROCESSORS.get(category)
    if not processor:
        raise ValueError(f"不支持的系统智能体分类: {category}")
    return processor


__all__ = [
    "BaseSystemAgentProcessor",
    "DiagnosisAgentProcessor",
    "VisionAgentProcessor", 
    "ReportGenerationAgentProcessor",
    "ReportAnalysisAgentProcessor",
    "get_system_agent_processor"
]
