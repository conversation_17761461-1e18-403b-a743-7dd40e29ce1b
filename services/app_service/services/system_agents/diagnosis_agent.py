"""
AI问诊智能体处理器
"""
from typing import Dict, Any, Optional, List, AsyncGenerator
from .base import BaseSystemAgentProcessor
from ...models.agent import Agent
from ...models.conversation import MessageRole
from shared.logging_config import get_app_service_logger

logger = get_app_service_logger()


class DiagnosisAgentProcessor(BaseSystemAgentProcessor):
    """AI问诊智能体处理器"""

    def __init__(self):
        super().__init__()
        self.message_service = None  # 将在需要时初始化
        self.db_session = None  # 存储数据库会话

    def get_default_prompt(self) -> str:
        """获取默认提示词模板"""
        return """你是一位专业的宠物医生AI助手，专门为宠物主人提供初步的健康咨询和诊断建议。

请根据以下宠物信息和症状描述，提供专业的分析和建议：

宠物信息：
- 品种：{pet_breed}
- 年龄：{pet_age}
- 性别：{pet_gender}
- 体重：{pet_weight}
- 既往病史：{medical_history}

当前症状：
{symptoms}

补充信息：
{additional_info}

请按照以下格式提供分析：

## 症状分析
[对症状的专业分析]

## 可能的诊断
[列出可能的疾病或健康问题，按可能性排序]

## 建议措施
[提供具体的护理建议和注意事项]

## 就医建议
[是否需要立即就医，以及建议的检查项目]

## 预防措施
[未来的预防建议]

注意：此分析仅供参考，不能替代专业兽医的诊断。如症状严重或持续，请及时就医。"""

    async def process(
        self,
        agent: Agent,
        input_data: Dict[str, Any],
        config_override: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """处理AI问诊请求 - 支持多种输入格式和短期记忆"""
        try:
            # 添加调试日志
            logger.info(f"AI问诊输入数据键: {list(input_data.keys())}")
            logger.info(f"输入数据内容: {input_data}")

            # 提取会话上下文信息
            conversation_context = input_data.get('_conversation_context', {})

            # 初始化 MessageService（如果有会话上下文和数据库会话）
            if conversation_context and self.db_session:
                from ...services.message_service import MessageService
                self.message_service = MessageService(
                    self.db_session,
                    conversation_context.get('tenant_id')
                )

            # 检测输入格式并处理
            if "messages" in input_data:
                # OpenAI风格的messages输入
                logger.info("使用messages输入格式")
                return await self._process_messages_input(agent, input_data, config_override)
            elif "prompt" in input_data:
                # 直接文本输入
                logger.info("使用prompt输入格式")
                return await self._process_prompt_input(agent, input_data, config_override)
            elif "structured_input" in input_data:
                # 结构化输入（包装的传统格式）
                logger.info("使用structured_input输入格式")
                return await self._process_structured_input(agent, input_data, config_override)
            elif "pet_info" in input_data and "symptoms" in input_data:
                # 传统格式（向后兼容）
                logger.info("使用传统输入格式")
                return await self._process_traditional_input(agent, input_data, config_override)
            else:
                logger.warning(f"未识别的输入格式，输入键: {list(input_data.keys())}")
                return self._format_error_response("未识别的输入格式，请提供messages、prompt、structured_input或传统的pet_info+symptoms格式")

        except Exception as e:
            logger.error(f"AI问诊处理失败: {e}")
            return self._format_error_response(f"处理失败: {str(e)}")

    async def process_stream(
        self,
        agent: Agent,
        input_data: Dict[str, Any],
        config_override: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """处理AI问诊流式请求 - 支持多种输入格式和短期记忆"""
        try:
            # 添加调试日志
            logger.info(f"AI问诊流式输入数据键: {list(input_data.keys())}")
            logger.info(f"流式输入数据内容: {input_data}")

            # 提取会话上下文信息
            conversation_context = input_data.get('_conversation_context', {})

            # 初始化 MessageService（如果有会话上下文和数据库会话）
            if conversation_context and self.db_session:
                from ...services.message_service import MessageService
                self.message_service = MessageService(
                    self.db_session,
                    conversation_context.get('tenant_id')
                )

            # 检测输入格式并处理
            if "messages" in input_data:
                # OpenAI风格的messages输入
                logger.info("使用messages流式输入格式")
                async for chunk in self._process_messages_input_stream(agent, input_data, config_override):
                    yield chunk
            elif "prompt" in input_data:
                # 直接文本输入
                logger.info("使用prompt流式输入格式")
                async for chunk in self._process_prompt_input_stream(agent, input_data, config_override):
                    yield chunk
            elif "structured_input" in input_data:
                # 结构化输入（包装的传统格式）
                logger.info("使用structured_input流式输入格式")
                async for chunk in self._process_structured_input_stream(agent, input_data, config_override):
                    yield chunk
            elif "pet_info" in input_data and "symptoms" in input_data:
                # 传统格式（向后兼容）
                logger.info("使用传统流式输入格式")
                async for chunk in self._process_traditional_input_stream(agent, input_data, config_override):
                    yield chunk
            else:
                logger.warning(f"未识别的流式输入格式，输入键: {list(input_data.keys())}")
                yield {
                    "type": "error",
                    "error": "未识别的输入格式，请提供messages、prompt、structured_input或传统的pet_info+symptoms格式"
                }

        except Exception as e:
            logger.error(f"AI问诊流式处理失败: {e}")
            yield {
                "type": "error",
                "error": f"流式处理失败: {str(e)}"
            }

    async def _process_messages_input(self, agent: Agent, input_data: Dict[str, Any], config_override: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """处理OpenAI风格的messages输入"""
        try:
            messages = input_data.get("messages", [])
            if not messages:
                return self._format_error_response("messages不能为空")

            # 验证messages格式
            for msg in messages:
                if not isinstance(msg, dict) or "role" not in msg or "content" not in msg:
                    return self._format_error_response("messages格式不正确，每个消息必须包含role和content字段")

            # 提取系统消息和用户消息
            system_messages = [
                msg for msg in messages if msg.get("role") == "system"]
            user_messages = [
                msg for msg in messages if msg.get("role") == "user"]

            # 构建系统提示词
            if system_messages:
                system_prompt = "\n".join(
                    [msg["content"] for msg in system_messages])
            else:
                # 使用默认的AI问诊系统提示词
                system_prompt = self.get_default_prompt()

            # 获取会话历史上下文（短期记忆）
            conversation_context = input_data.get('_conversation_context', {})
            if conversation_context and self.message_service:
                history_context = await self._get_conversation_history_context(
                    conversation_context['conversation_id'],
                    conversation_context['user_id'],
                    conversation_context.get('tenant_id'),
                    limit=10
                )
                if history_context:
                    system_prompt = f"{system_prompt}\n\n{history_context}"
                    logger.info("已添加会话历史上下文到系统提示词")

            # 合并用户消息
            user_content = "\n".join([msg["content"] for msg in user_messages])

            # 准备最终的messages
            final_messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_content}
            ]

            # 合并配置
            config = self._merge_config(agent, config_override)

            # 调用LLM
            llm_response = await self._call_llm(final_messages, config)

            # 构建响应
            result = self._format_success_response(
                content=llm_response["content"],
                metadata={
                    "model_used": llm_response["model"],
                    "token_usage": llm_response["usage"],
                    "input_format": "messages",
                    "message_count": len(messages)
                }
            )

            logger.info(f"Messages格式AI问诊处理完成，消息数量: {len(messages)}")
            return result

        except Exception as e:
            logger.error(f"Messages格式AI问诊处理失败: {e}")
            return self._format_error_response(f"处理失败: {str(e)}")

    async def _process_prompt_input(self, agent: Agent, input_data: Dict[str, Any], config_override: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """处理直接文本输入"""
        try:
            prompt = input_data.get("prompt", "")
            if not prompt.strip():
                return self._format_error_response("prompt不能为空")

            # 构建系统提示词
            system_prompt = self.get_default_prompt()

            # 获取会话历史上下文（短期记忆）
            conversation_context = input_data.get('_conversation_context', {})
            if conversation_context and self.message_service:
                history_context = await self._get_conversation_history_context(
                    conversation_context['conversation_id'],
                    conversation_context['user_id'],
                    conversation_context.get('tenant_id'),
                    limit=10
                )
                if history_context:
                    system_prompt = f"{system_prompt}\n\n{history_context}"
                    logger.info("已添加会话历史上下文到系统提示词")

            # 准备messages
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ]

            # 合并配置
            config = self._merge_config(agent, config_override)

            # 调用LLM
            llm_response = await self._call_llm(messages, config)

            # 构建响应
            result = self._format_success_response(
                content=llm_response["content"],
                metadata={
                    "model_used": llm_response["model"],
                    "token_usage": llm_response["usage"],
                    "input_format": "prompt",
                    "prompt_length": len(prompt)
                }
            )

            logger.info(f"Prompt格式AI问诊处理完成，提示长度: {len(prompt)}")
            return result

        except Exception as e:
            logger.error(f"Prompt格式AI问诊处理失败: {e}")
            return self._format_error_response(f"处理失败: {str(e)}")

    async def _process_messages_input_stream(self, agent: Agent, input_data: Dict[str, Any], config_override: Optional[Dict[str, Any]] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """处理messages格式的流式输入"""
        try:
            messages = input_data.get("messages", [])
            if not messages:
                yield {"type": "error", "error": "messages不能为空"}
                return

            # 分离系统消息和用户消息
            system_messages = [
                msg for msg in messages if msg.get("role") == "system"]
            user_messages = [
                msg for msg in messages if msg.get("role") == "user"]

            if not user_messages:
                yield {"type": "error", "error": "至少需要一条用户消息"}
                return

            # 构建系统提示词
            if system_messages:
                system_prompt = "\n".join(
                    [msg["content"] for msg in system_messages])
            else:
                # 使用默认的AI问诊系统提示词
                system_prompt = self.get_default_prompt()

            # 获取会话历史上下文（短期记忆）
            conversation_context = input_data.get('_conversation_context', {})
            if conversation_context and self.message_service:
                history_context = await self._get_conversation_history_context(
                    conversation_context['conversation_id'],
                    conversation_context['user_id'],
                    conversation_context.get('tenant_id'),
                    limit=10
                )
                if history_context:
                    system_prompt = f"{system_prompt}\n\n{history_context}"
                    logger.info("已添加会话历史上下文到流式系统提示词")

            # 合并用户消息
            user_content = "\n".join([msg["content"] for msg in user_messages])

            # 准备最终的messages
            final_messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_content}
            ]

            # 合并配置
            config = self._merge_config(agent, config_override)

            # 调用LLM流式服务
            collected_content = []
            async for chunk in self._call_llm_stream(final_messages, config):
                if chunk.get("type") == "content":
                    collected_content.append(chunk["content"])
                    yield {
                        "type": "content",
                        "content": chunk["content"],
                        "model": chunk.get("model"),
                        "input_format": "messages"
                    }
                elif chunk.get("type") == "finish":
                    # 发送完成事件
                    yield {
                        "type": "finish",
                        "finish_reason": chunk.get("finish_reason"),
                        "model": chunk.get("model"),
                        "total_content": "".join(collected_content),
                        "input_format": "messages",
                        "message_count": len(messages)
                    }

            logger.info(f"Messages格式AI问诊流式处理完成，消息数量: {len(messages)}")

        except Exception as e:
            logger.error(f"Messages格式AI问诊流式处理失败: {e}")
            yield {"type": "error", "error": f"流式处理失败: {str(e)}"}

    async def _process_prompt_input_stream(self, agent: Agent, input_data: Dict[str, Any], config_override: Optional[Dict[str, Any]] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """处理prompt格式的流式输入"""
        try:
            prompt = input_data.get("prompt", "")
            if not prompt.strip():
                yield {"type": "error", "error": "prompt不能为空"}
                return

            # 构建系统提示词
            system_prompt = self.get_default_prompt()

            # 获取会话历史上下文（短期记忆）
            conversation_context = input_data.get('_conversation_context', {})
            if conversation_context and self.message_service:
                history_context = await self._get_conversation_history_context(
                    conversation_context['conversation_id'],
                    conversation_context['user_id'],
                    conversation_context.get('tenant_id'),
                    limit=10
                )
                if history_context:
                    system_prompt = f"{system_prompt}\n\n{history_context}"
                    logger.info("已添加会话历史上下文到流式系统提示词")

            # 准备messages
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ]

            # 合并配置
            config = self._merge_config(agent, config_override)

            # 调用LLM流式服务
            collected_content = []
            async for chunk in self._call_llm_stream(messages, config):
                if chunk.get("type") == "content":
                    collected_content.append(chunk["content"])
                    yield {
                        "type": "content",
                        "content": chunk["content"],
                        "model": chunk.get("model"),
                        "input_format": "prompt"
                    }
                elif chunk.get("type") == "finish":
                    # 发送完成事件
                    yield {
                        "type": "finish",
                        "finish_reason": chunk.get("finish_reason"),
                        "model": chunk.get("model"),
                        "total_content": "".join(collected_content),
                        "input_format": "prompt",
                        "prompt_length": len(prompt)
                    }

            logger.info(f"Prompt格式AI问诊流式处理完成，提示长度: {len(prompt)}")

        except Exception as e:
            logger.error(f"Prompt格式AI问诊流式处理失败: {e}")
            yield {"type": "error", "error": f"流式处理失败: {str(e)}"}

    async def _process_structured_input_stream(self, agent: Agent, input_data: Dict[str, Any], config_override: Optional[Dict[str, Any]] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """处理结构化输入的流式处理"""
        try:
            structured_data = input_data.get("structured_input", {})
            if not structured_data:
                yield {"type": "error", "error": "structured_input不能为空"}
                return

            # 递归调用流式处理结构化数据
            async for chunk in self.process_stream(agent, structured_data, config_override):
                yield chunk

        except Exception as e:
            logger.error(f"结构化输入AI问诊流式处理失败: {e}")
            yield {"type": "error", "error": f"流式处理失败: {str(e)}"}

    async def _process_traditional_input_stream(self, agent: Agent, input_data: Dict[str, Any], config_override: Optional[Dict[str, Any]] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """处理传统格式的流式输入"""
        try:
            pet_info = input_data.get("pet_info", {})
            symptoms = input_data.get("symptoms", [])
            additional_info = input_data.get("additional_info", "")

            if not pet_info or not symptoms:
                yield {"type": "error", "error": "pet_info和symptoms不能为空"}
                return

            # 构建提示词上下文
            context = {
                "pet_breed": pet_info.get("breed", "未知"),
                "pet_age": pet_info.get("age", "未知"),
                "pet_gender": pet_info.get("gender", "未知"),
                "pet_weight": pet_info.get("weight", "未知"),
                "medical_history": pet_info.get("medical_history", "无"),
                "symptoms": "\n".join([f"- {symptom}" for symptom in symptoms]),
                "additional_info": additional_info or "无"
            }

            # 构建系统提示词
            system_prompt = self._build_system_prompt(agent, context)

            # 获取会话历史上下文（短期记忆）
            conversation_context = input_data.get('_conversation_context', {})
            if conversation_context and self.message_service:
                history_context = await self._get_conversation_history_context(
                    conversation_context['conversation_id'],
                    conversation_context['user_id'],
                    conversation_context.get('tenant_id'),
                    limit=10
                )
                if history_context:
                    system_prompt = f"{system_prompt}\n\n{history_context}"
                    logger.info("已添加会话历史上下文到流式系统提示词")

            # 构建用户输入
            user_prompt = f"""
宠物信息：
- 品种：{context['pet_breed']}
- 年龄：{context['pet_age']}
- 性别：{context['pet_gender']}
- 体重：{context['pet_weight']}
- 既往病史：{context['medical_history']}

当前症状：
{context['symptoms']}

补充信息：
{context['additional_info']}

请提供专业的分析和建议。
"""

            # 准备messages
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            # 合并配置
            config = self._merge_config(agent, config_override)

            # 调用LLM流式服务
            collected_content = []
            async for chunk in self._call_llm_stream(messages, config):
                if chunk.get("type") == "content":
                    collected_content.append(chunk["content"])
                    yield {
                        "type": "content",
                        "content": chunk["content"],
                        "model": chunk.get("model"),
                        "input_format": "traditional"
                    }
                elif chunk.get("type") == "finish":
                    # 发送完成事件
                    yield {
                        "type": "finish",
                        "finish_reason": chunk.get("finish_reason"),
                        "model": chunk.get("model"),
                        "total_content": "".join(collected_content),
                        "input_format": "traditional",
                        "pet_info": pet_info,
                        "symptoms": symptoms
                    }

            logger.info(
                f"传统格式AI问诊流式处理完成，宠物品种: {pet_info.get('breed', '未知')}, 症状数量: {len(symptoms)}")

        except Exception as e:
            logger.error(f"传统格式AI问诊流式处理失败: {e}")
            yield {"type": "error", "error": f"流式处理失败: {str(e)}"}

    async def _process_structured_input(self, agent: Agent, input_data: Dict[str, Any], config_override: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """处理结构化输入（包装的传统格式）"""
        try:
            structured_data = input_data.get("structured_input", {})
            if not structured_data:
                return self._format_error_response("structured_input不能为空")

            # 递归调用处理结构化数据
            return await self.process(agent, structured_data, config_override)

        except Exception as e:
            logger.error(f"结构化输入AI问诊处理失败: {e}")
            return self._format_error_response(f"处理失败: {str(e)}")

    async def _process_traditional_input(self, agent: Agent, input_data: Dict[str, Any], config_override: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """处理传统格式输入（向后兼容）"""
        try:
            # 提取数据
            pet_info = input_data["pet_info"]
            symptoms = input_data["symptoms"]
            additional_info = input_data.get("additional_info", "无")

            # 验证宠物信息（放宽要求，不强制所有字段）
            if not isinstance(pet_info, dict):
                return self._format_error_response("pet_info必须是字典格式")

            # 验证症状列表
            if not isinstance(symptoms, list) or not symptoms:
                return self._format_error_response("symptoms必须是非空列表")

            # 构建上下文（使用默认值处理缺失字段）
            context = {
                "pet_breed": pet_info.get("breed", "未知"),
                "pet_age": self._format_pet_age(pet_info),
                "pet_gender": pet_info.get("gender", "未知"),
                "pet_weight": pet_info.get("weight", "未知") + "kg" if pet_info.get("weight") else "未知",
                "medical_history": pet_info.get("medical_history", "无"),
                "symptoms": self._format_symptoms(symptoms),
                "additional_info": additional_info
            }

            # 构建系统提示词
            system_prompt = self._build_system_prompt(agent, context)

            # 获取会话历史上下文（短期记忆）
            conversation_context = input_data.get('_conversation_context', {})
            if conversation_context and self.message_service:
                history_context = await self._get_conversation_history_context(
                    conversation_context['conversation_id'],
                    conversation_context['user_id'],
                    conversation_context.get('tenant_id'),
                    limit=10
                )
                if history_context:
                    system_prompt = f"{system_prompt}\n\n{history_context}"
                    logger.info("已添加会话历史上下文到系统提示词")

            # 构建用户消息
            user_message = self._build_user_message(
                pet_info, symptoms, additional_info)

            # 准备消息
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ]

            # 合并配置
            config = self._merge_config(agent, config_override)

            # 调用LLM
            llm_response = await self._call_llm(messages, config)

            # 解析和结构化响应
            structured_response = self._parse_diagnosis_response(
                llm_response["content"])

            # 构建最终响应
            result = self._format_success_response(
                content=llm_response["content"],
                metadata={
                    "structured_analysis": structured_response,
                    "pet_info": pet_info,
                    "symptoms": symptoms,
                    "model_used": llm_response["model"],
                    "token_usage": llm_response["usage"],
                    "urgency_level": self._assess_urgency(symptoms, llm_response["content"]),
                    "input_format": "traditional"
                }
            )

            logger.info(
                f"传统格式AI问诊处理完成，宠物品种: {pet_info.get('breed', '未知')}, 症状数量: {len(symptoms)}")
            return result

        except Exception as e:
            logger.error(f"传统格式AI问诊处理失败: {e}")
            return self._format_error_response(f"处理失败: {str(e)}")

    def _format_pet_age(self, pet_info: Dict[str, Any]) -> str:
        """格式化宠物年龄"""
        age_years = pet_info.get("age_years", 0)
        age_months = pet_info.get("age_months", 0)

        if age_years > 0:
            if age_months > 0:
                return f"{age_years}岁{age_months}个月"
            else:
                return f"{age_years}岁"
        elif age_months > 0:
            return f"{age_months}个月"
        else:
            return "年龄未知"

    def _format_symptoms(self, symptoms: List[str]) -> str:
        """格式化症状列表"""
        if not symptoms:
            return "无明显症状"

        formatted_symptoms = []
        for i, symptom in enumerate(symptoms, 1):
            formatted_symptoms.append(f"{i}. {symptom}")

        return "\n".join(formatted_symptoms)

    def _build_user_message(
        self,
        pet_info: Dict[str, Any],
        symptoms: List[str],
        additional_info: str
    ) -> str:
        """构建用户消息"""
        message_parts = [
            "我的宠物出现了一些症状，希望得到专业的分析和建议。",
            "",
            f"宠物品种：{pet_info.get('breed', '未知')}",
            f"年龄：{self._format_pet_age(pet_info)}",
            f"性别：{pet_info.get('gender', '未知')}",
        ]

        if pet_info.get("weight"):
            message_parts.append(f"体重：{pet_info['weight']}kg")

        if pet_info.get("medical_history"):
            message_parts.append(f"既往病史：{pet_info['medical_history']}")

        message_parts.extend([
            "",
            "当前症状：",
            self._format_symptoms(symptoms)
        ])

        if additional_info and additional_info != "无":
            message_parts.extend([
                "",
                f"补充信息：{additional_info}"
            ])

        message_parts.append("\n请为我提供专业的分析和建议。")

        return "\n".join(message_parts)

    async def _get_conversation_history_context(
        self,
        conversation_id: int,
        user_id: int,
        tenant_id: Optional[str] = None,
        limit: int = 10
    ) -> str:
        """获取会话历史上下文用于短期记忆"""
        try:
            # 检查 MessageService 是否已初始化
            if self.message_service is None:
                logger.warning("MessageService 未初始化，无法获取会话历史")
                return ""

            # 获取最近的用户消息历史
            recent_messages = await self.message_service.get_recent_user_messages(
                conversation_id, user_id, limit
            )

            if not recent_messages:
                logger.info(f"会话 {conversation_id} 没有历史用户消息")
                return ""

            # 格式化历史消息
            history_parts = ["## 对话历史上下文", "以下是用户在本次会话中的历史提问和症状描述：", ""]

            for i, message in enumerate(recent_messages, 1):
                # 格式化时间
                time_str = message.created_at.strftime(
                    "%Y-%m-%d %H:%M") if message.created_at else "未知时间"
                # 截断过长的消息内容
                content = message.content[:200] + "..." if len(
                    message.content) > 200 else message.content
                history_parts.append(f"{i}. [{time_str}] {content}")

            history_parts.append("")  # 添加空行分隔
            logger.info(f"成功获取会话历史上下文，包含 {len(recent_messages)} 条历史消息")
            return "\n".join(history_parts)

        except Exception as e:
            logger.warning(f"获取会话历史上下文失败: {e}")
            return ""

    def _parse_diagnosis_response(self, response_content: str) -> Dict[str, Any]:
        """解析诊断响应，提取结构化信息"""
        try:
            # 简单的文本解析，提取各个部分
            sections = {
                "symptom_analysis": "",
                "possible_diagnoses": [],
                "recommendations": [],
                "medical_advice": "",
                "prevention_measures": []
            }

            # 按标题分割内容
            lines = response_content.split('\n')
            current_section = None

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 识别章节标题
                if "症状分析" in line:
                    current_section = "symptom_analysis"
                elif "可能的诊断" in line:
                    current_section = "possible_diagnoses"
                elif "建议措施" in line:
                    current_section = "recommendations"
                elif "就医建议" in line:
                    current_section = "medical_advice"
                elif "预防措施" in line:
                    current_section = "prevention_measures"
                elif current_section:
                    # 添加内容到当前章节
                    if current_section in ["symptom_analysis", "medical_advice"]:
                        sections[current_section] += line + "\n"
                    elif line.startswith(("-", "•", "*")) or line[0].isdigit():
                        # 列表项
                        clean_line = line.lstrip("-•***********. ")
                        if current_section in ["possible_diagnoses", "recommendations", "prevention_measures"]:
                            sections[current_section].append(clean_line)

            return sections

        except Exception as e:
            logger.warning(f"解析诊断响应失败: {e}")
            return {"raw_content": response_content}

    def _assess_urgency(self, symptoms: List[str], response_content: str) -> str:
        """评估紧急程度"""
        # 紧急症状关键词
        urgent_keywords = [
            "呼吸困难", "呼吸急促", "昏迷", "抽搐", "大量出血", "休克",
            "中毒", "误食", "严重外伤", "高烧", "脱水严重"
        ]

        # 中等紧急症状关键词
        moderate_keywords = [
            "发烧", "呕吐", "腹泻", "食欲不振", "精神萎靡", "咳嗽",
            "流鼻涕", "眼部分泌物", "皮肤红肿"
        ]

        # 检查症状
        symptoms_text = " ".join(symptoms).lower()
        response_text = response_content.lower()

        # 检查是否包含紧急关键词
        for keyword in urgent_keywords:
            if keyword in symptoms_text or keyword in response_text:
                return "urgent"  # 紧急

        # 检查是否包含中等紧急关键词
        for keyword in moderate_keywords:
            if keyword in symptoms_text:
                return "moderate"  # 中等

        return "low"  # 低
