"""
AI报告解读智能体处理器
"""
from typing import Dict, Any, Optional, List
import base64
import httpx
import io
from .base import BaseSystemAgentProcessor

# 可选依赖导入
try:
    import PyPDF2
    HAS_PDF_SUPPORT = True
except ImportError:
    PyPDF2 = None
    HAS_PDF_SUPPORT = False

try:
    from PIL import Image
    import pytesseract
    HAS_OCR_SUPPORT = True
except ImportError:
    Image = None
    pytesseract = None
    HAS_OCR_SUPPORT = False
from ...models.agent import Agent
from shared.logging_config import get_app_service_logger

logger = get_app_service_logger()


class ReportAnalysisAgentProcessor(BaseSystemAgentProcessor):
    """AI报告解读智能体处理器"""

    def get_default_prompt(self) -> str:
        """获取默认提示词模板"""
        return """你是一位专业的宠物医疗报告解读专家，能够分析和解读各种宠物医疗报告，为宠物主人提供通俗易懂的解释。

文件类型：{file_type}
分析重点：{analysis_focus}

报告内容：
{report_content}

请按照以下格式提供详细的报告解读：

## 报告概述
[总结报告的基本信息和检查项目]

## 关键指标解读
[解释重要的检查指标和数值含义]

## 异常发现
[指出任何异常或需要关注的发现]

## 健康状况评估
[基于报告内容评估宠物的整体健康状况]

## 治疗建议
[根据报告结果提供治疗或护理建议]

## 后续跟进
[建议的后续检查或监测计划]

## 注意事项
[宠物主人需要特别注意的事项]

请用通俗易懂的语言解释专业术语，帮助宠物主人更好地理解报告内容。"""

    async def process(
        self,
        agent: Agent,
        input_data: Dict[str, Any],
        config_override: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """处理AI报告解读请求"""
        try:
            # 验证输入数据
            self._validate_input(input_data, ["report_files", "file_type"])

            # 提取数据
            report_files = input_data["report_files"]
            file_type = input_data["file_type"]
            analysis_focus = input_data.get("analysis_focus", [])

            # 验证文件列表
            if not isinstance(report_files, list) or not report_files:
                return self._format_error_response("报告文件列表不能为空")

            if len(report_files) > 10:  # 限制最多10个文件
                return self._format_error_response("最多支持10个文件同时分析")

            # 验证文件类型
            valid_file_types = ["pdf", "image"]
            if file_type not in valid_file_types:
                return self._format_error_response(f"不支持的文件类型: {file_type}")

            # 处理文件并提取内容
            extracted_content = await self._extract_content_from_files(report_files, file_type)
            if not extracted_content:
                return self._format_error_response("无法从文件中提取内容")

            # 构建上下文
            context = {
                "file_type": "PDF文档" if file_type == "pdf" else "图片文件",
                "analysis_focus": self._format_analysis_focus(analysis_focus),
                "report_content": self._format_extracted_content(extracted_content)
            }

            # 构建系统提示词
            system_prompt = self._build_system_prompt(agent, context)

            # 构建用户消息
            user_message = self._build_user_message(
                file_type, analysis_focus, len(report_files))

            # 准备消息
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ]

            # 合并配置
            config = self._merge_config(agent, config_override)

            # 调用LLM
            llm_response = await self._call_llm(messages, config)

            # 解析和结构化响应
            structured_response = self._parse_analysis_response(
                llm_response["content"])

            # 构建最终响应
            result = self._format_success_response(
                content=llm_response["content"],
                metadata={
                    "structured_analysis": structured_response,
                    "file_type": file_type,
                    "file_count": len(report_files),
                    "analysis_focus": analysis_focus,
                    "extracted_content_length": len(context["report_content"]),
                    "model_used": llm_response["model"],
                    "token_usage": llm_response["usage"],
                    "complexity_level": self._assess_complexity(extracted_content)
                }
            )

            logger.info(
                f"AI报告解读处理完成，文件类型: {file_type}, 文件数量: {len(report_files)}")
            return result

        except Exception as e:
            logger.error(f"AI报告解读处理失败: {e}")
            return self._format_error_response(f"处理失败: {str(e)}")

    async def _extract_content_from_files(
        self,
        file_urls: List[str],
        file_type: str
    ) -> List[Dict[str, Any]]:
        """从文件中提取内容"""
        extracted_content = []

        async with httpx.AsyncClient(timeout=60.0) as client:
            for i, url in enumerate(file_urls):
                try:
                    # 下载文件
                    response = await client.get(url)
                    response.raise_for_status()

                    # 检查文件大小（限制50MB）
                    if len(response.content) > 50 * 1024 * 1024:
                        logger.warning(f"文件 {i+1} 过大，跳过处理")
                        continue

                    # 根据文件类型提取内容
                    if file_type == "pdf":
                        content = await self._extract_pdf_content(response.content)
                    else:  # image
                        content = await self._extract_image_content(response.content)

                    if content:
                        extracted_content.append({
                            "file_index": i + 1,
                            "file_url": url,
                            "content": content,
                            "content_length": len(content)
                        })

                except Exception as e:
                    logger.warning(f"处理文件 {i+1} 失败: {e}")
                    continue

        return extracted_content

    async def _extract_pdf_content(self, pdf_data: bytes) -> str:
        """从PDF中提取文本内容"""
        if not HAS_PDF_SUPPORT:
            logger.warning("PDF支持不可用，请安装PyPDF2: pip install PyPDF2")
            return "[PDF内容提取不可用 - 缺少PyPDF2依赖]"

        try:
            pdf_file = io.BytesIO(pdf_data)
            pdf_reader = PyPDF2.PdfReader(pdf_file)

            text_content = []
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text = page.extract_text()
                if text.strip():
                    text_content.append(f"第{page_num + 1}页：\n{text}")

            return "\n\n".join(text_content)

        except Exception as e:
            logger.error(f"PDF内容提取失败: {e}")
            return ""

    async def _extract_image_content(self, image_data: bytes) -> str:
        """从图片中提取文本内容（OCR）"""
        if not HAS_OCR_SUPPORT:
            logger.warning("OCR支持不可用，请安装依赖: pip install pillow pytesseract")
            return "[图片OCR功能不可用 - 缺少PIL/pytesseract依赖]"

        try:
            # 使用PIL打开图片
            image = Image.open(io.BytesIO(image_data))

            # 转换为RGB模式（如果需要）
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # 使用OCR提取文本
            # 注意：需要安装tesseract-ocr
            text = pytesseract.image_to_string(image, lang='chi_sim+eng')

            return text.strip()

        except Exception as e:
            logger.error(f"图片OCR提取失败: {e}")
            # 如果OCR失败，返回提示信息
            return "[图片内容无法自动识别，请手动描述图片中的关键信息]"

    def _format_analysis_focus(self, analysis_focus: List[str]) -> str:
        """格式化分析重点"""
        if not analysis_focus:
            return "全面分析"

        focus_descriptions = {
            "blood_test": "血液检查结果",
            "urine_test": "尿液检查结果",
            "imaging": "影像学检查",
            "biochemistry": "生化指标",
            "pathology": "病理检查",
            "microbiology": "微生物检查",
            "immunology": "免疫学检查",
            "genetics": "基因检测"
        }

        formatted_focus = []
        for focus in analysis_focus:
            description = focus_descriptions.get(focus, focus)
            formatted_focus.append(description)

        return "、".join(formatted_focus)

    def _format_extracted_content(self, extracted_content: List[Dict[str, Any]]) -> str:
        """格式化提取的内容"""
        if not extracted_content:
            return "无法提取文件内容"

        formatted_parts = []
        for item in extracted_content:
            formatted_parts.append(
                f"文件 {item['file_index']}：\n{item['content']}")

        return "\n\n" + "="*50 + "\n\n".join(formatted_parts)

    def _build_user_message(
        self,
        file_type: str,
        analysis_focus: List[str],
        file_count: int
    ) -> str:
        """构建用户消息"""
        file_type_desc = "PDF报告" if file_type == "pdf" else "图片报告"

        message_parts = [
            f"请帮我解读这{file_count}份{file_type_desc}。",
            ""
        ]

        if analysis_focus:
            focus_desc = self._format_analysis_focus(analysis_focus)
            message_parts.append(f"请重点关注：{focus_desc}")
            message_parts.append("")

        message_parts.extend([
            "请提供：",
            "1. 通俗易懂的解释，避免过多专业术语",
            "2. 明确指出异常指标和需要关注的问题",
            "3. 具体的护理建议和注意事项",
            "4. 是否需要进一步检查或治疗",
            "",
            "谢谢！"
        ])

        return "\n".join(message_parts)

    def _parse_analysis_response(self, response_content: str) -> Dict[str, Any]:
        """解析报告分析响应"""
        try:
            sections = {
                "report_overview": "",
                "key_indicators": [],
                "abnormal_findings": [],
                "health_assessment": "",
                "treatment_recommendations": [],
                "follow_up_plan": "",
                "precautions": []
            }

            # 按标题分割内容
            lines = response_content.split('\n')
            current_section = None

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 识别章节标题
                if "报告概述" in line:
                    current_section = "report_overview"
                elif "关键指标解读" in line:
                    current_section = "key_indicators"
                elif "异常发现" in line:
                    current_section = "abnormal_findings"
                elif "健康状况评估" in line:
                    current_section = "health_assessment"
                elif "治疗建议" in line:
                    current_section = "treatment_recommendations"
                elif "后续跟进" in line:
                    current_section = "follow_up_plan"
                elif "注意事项" in line:
                    current_section = "precautions"
                elif current_section:
                    # 添加内容到当前章节
                    if current_section in ["report_overview", "health_assessment", "follow_up_plan"]:
                        sections[current_section] += line + "\n"
                    elif line.startswith(("-", "•", "*")) or line[0].isdigit():
                        # 列表项
                        clean_line = line.lstrip("-•***********. ")
                        if current_section in ["key_indicators", "abnormal_findings", "treatment_recommendations", "precautions"]:
                            sections[current_section].append(clean_line)

            return sections

        except Exception as e:
            logger.warning(f"解析报告分析响应失败: {e}")
            return {"raw_content": response_content}

    def _assess_complexity(self, extracted_content: List[Dict[str, Any]]) -> str:
        """评估报告复杂度"""
        if not extracted_content:
            return "unknown"

        total_length = sum(len(item["content"]) for item in extracted_content)

        # 复杂度关键词
        complex_keywords = [
            "病理", "肿瘤", "癌", "恶性", "转移", "手术", "化疗",
            "血液学", "生化", "免疫", "基因", "染色体"
        ]

        simple_keywords = [
            "正常", "阴性", "无异常", "健康", "良好"
        ]

        content_text = " ".join(item["content"] for item in extracted_content)

        complex_count = sum(
            1 for keyword in complex_keywords if keyword in content_text)
        simple_count = sum(
            1 for keyword in simple_keywords if keyword in content_text)

        if total_length > 5000 or complex_count > 3:
            return "high"
        elif total_length > 2000 or complex_count > 0:
            return "medium"
        else:
            return "low"
