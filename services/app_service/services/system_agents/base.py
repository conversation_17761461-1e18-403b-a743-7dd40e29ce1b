"""
系统智能体基础处理器
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from openai import AsyncOpenAI

from shared.logging_config import get_app_service_logger
from ...models.agent import Agent

logger = get_app_service_logger()


class BaseSystemAgentProcessor(ABC):
    """系统智能体基础处理器"""

    def __init__(self):
        self.default_model = "ds-vet-answer-32B"
        self.openai_client = None

    def _get_openai_client(self, model_name: str = None) -> AsyncOpenAI:
        """获取OpenAI客户端"""
        if not model_name:
            model_name = self.default_model

        # 使用环境变量或默认配置
        import os
        api_key = os.getenv(
            "OPENAI_API_KEY", "y03ZvBwVvHAqAo0nosjdlVGCfCNYGlgm8U")
        base_url = os.getenv("OPENAI_BASE_URL", "http://10.201.112.7:8888/v1")

        # 创建OpenAI客户端
        return AsyncOpenAI(
            api_key=api_key,
            base_url=base_url
        )

    def _merge_config(self, agent: Agent, config_override: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """合并配置参数"""
        # 从智能体获取基础配置
        base_config = agent.config or {}

        # 应用覆盖配置
        if config_override:
            base_config.update(config_override)

        # 设置默认值
        default_config = {
            "model": self.default_model,
            "temperature": 0.7,
            "max_tokens": 2048,
            "top_p": 0.9
        }

        for key, value in default_config.items():
            if key not in base_config:
                base_config[key] = value

        return base_config

    def _build_system_prompt(self, agent: Agent, context: Dict[str, Any]) -> str:
        """构建系统提示词"""
        base_prompt = agent.prompt_template or self.get_default_prompt()

        # 替换模板变量
        try:
            return base_prompt.format(**context)
        except KeyError as e:
            logger.warning(f"提示词模板变量缺失: {e}")
            return base_prompt

    @abstractmethod
    def get_default_prompt(self) -> str:
        """获取默认提示词模板"""
        pass

    @abstractmethod
    async def process(
        self,
        agent: Agent,
        input_data: Dict[str, Any],
        config_override: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """处理智能体请求"""
        pass

    async def _call_llm(
        self,
        messages: list,
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """调用LLM服务"""
        try:
            client = self._get_openai_client(config.get("model"))

            response = await client.chat.completions.create(
                model=config.get("model", self.default_model),
                messages=messages,
                temperature=config.get("temperature", 0.7),
                max_tokens=config.get("max_tokens", 2048),
                top_p=config.get("top_p", 0.9),
                stream=False
            )

            # 提取响应内容
            content = response.choices[0].message.content

            # 构建返回结果
            result = {
                "content": content,
                "model": response.model,
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                "finish_reason": response.choices[0].finish_reason
            }

            return result

        except Exception as e:
            logger.error(f"调用LLM服务失败: {e}")
            raise

    async def _call_llm_stream(
        self,
        messages: list,
        config: Dict[str, Any]
    ):
        """调用LLM服务进行流式响应"""
        try:
            client = self._get_openai_client(config.get("model"))

            stream = await client.chat.completions.create(
                model=config.get("model", self.default_model),
                messages=messages,
                temperature=config.get("temperature", 0.7),
                max_tokens=config.get("max_tokens", 2048),
                top_p=config.get("top_p", 0.9),
                stream=True
            )

            # 返回流式响应
            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta.content is not None:
                    yield {
                        "type": "content",
                        "content": chunk.choices[0].delta.content,
                        "model": chunk.model,
                        "finish_reason": chunk.choices[0].finish_reason
                    }
                elif chunk.choices and chunk.choices[0].finish_reason:
                    # 流式响应结束
                    yield {
                        "type": "finish",
                        "finish_reason": chunk.choices[0].finish_reason,
                        "model": chunk.model
                    }

        except Exception as e:
            logger.error(f"调用LLM流式服务失败: {e}")
            raise

    def _validate_input(self, input_data: Dict[str, Any], required_fields: list) -> None:
        """验证输入数据"""
        for field in required_fields:
            if field not in input_data:
                raise ValueError(f"缺少必需字段: {field}")

            if not input_data[field]:
                raise ValueError(f"字段不能为空: {field}")

    def _format_error_response(self, error_message: str) -> Dict[str, Any]:
        """格式化错误响应"""
        return {
            "success": False,
            "error": error_message,
            "content": None
        }

    def _format_success_response(self, content: str, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """格式化成功响应"""
        result = {
            "success": True,
            "content": content,
            "error": None
        }

        if metadata:
            result.update(metadata)

        return result
