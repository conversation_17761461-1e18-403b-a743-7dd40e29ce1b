"""
AI报告生成智能体处理器
"""
import json
from datetime import datetime
from typing import Dict, Any, Optional

from shared.logging_config import get_app_service_logger
from .base import BaseSystemAgentProcessor
from ..system_prompts import ReportGenerationPrompts
from ...models.agent import Agent, ReportType

logger = get_app_service_logger()


class ReportGenerationAgentProcessor(BaseSystemAgentProcessor):
    """AI报告生成智能体处理器"""

    def get_default_prompt(self) -> str:
        """获取默认提示词模板"""
        return """你是一位专业的宠物医疗报告生成专家，能够根据宠物的健康数据生成专业、详细的医疗报告。

报告类型：{report_type}
生成日期：{generation_date}

宠物基本信息：
{pet_basic_info}

健康数据：
{health_data}

请根据以上信息生成一份专业的{report_type}报告，报告应包含以下部分：

## 宠物基本信息
[整理和展示宠物的基本信息]

## 数据概览
[对提供的健康数据进行概览性总结]

## 详细分析
[根据报告类型进行深入的数据分析]

## 健康评估
[基于数据给出健康状况评估]

## 风险提示
[识别潜在的健康风险]

## 建议措施
[提供具体的护理和预防建议]

## 后续计划
[制定后续的健康管理计划]

请确保报告内容专业、准确、易懂，适合宠物主人阅读理解。"""

    async def process(
            self,
            agent: Agent,
            input_data: Dict[str, Any],
            config_override: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """处理AI报告生成请求"""
        try:
            # 验证输入数据
            self._validate_input(input_data, ["pet_data", "report_type"])

            # 提取数据
            pet_data = input_data["pet_data"]
            report_type = input_data["report_type"]
            pet_other_fields = {"nutritional_diet_target", "deworming_plan_target"}
            pet_other_data = {k: input_data[k] for k in pet_other_fields if k in input_data}
            # 验证报告类型
            valid_report_types = [
                ReportType.HEALTH_MANAGEMENT,  # 综合健康管理报告
                ReportType.DISEASE_RISK,  # 疾病风险预警报告
                ReportType.VACCINATION_PLAN,  # 疫苗接种计划
                ReportType.DEWORMING_PLAN,  # 驱虫计划
                ReportType.NUTRITIONAL_DIET,  # 营养饮食方案
                ReportType.CHRONIC_DISEASE,  # 慢病管理方案
            ]

            if report_type not in valid_report_types:
                return self._format_error_response(f"不支持的报告类型: {report_type}")

            # 验证宠物数据
            if not isinstance(pet_data, dict) or not pet_data:
                return self._format_error_response("宠物数据不能为空")

            # 处理和验证宠物数据
            processed_data = self._process_pet_data(pet_data, pet_other_data, report_type)

            # 获取报告模板（如果指定）
            template = self._get_report_template(report_type, pet_other_data)

            # 如果有模板，使用模板提示词
            if template:
                system_prompt = template
            else:
                # 构建上下文
                context = {
                    "report_type": self._get_report_type_description(report_type),
                    "generation_date": datetime.now().strftime("%Y年%m月%d日"),
                    "pet_basic_info": self._format_pet_basic_info(processed_data),
                    "health_data": self._format_health_data(processed_data, report_type)
                }
                system_prompt = self._build_system_prompt(agent, context)

            # 构建用户消息
            user_message = self._build_user_message(report_type, processed_data)

            # 准备消息
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ]

            # 合并配置
            config = self._merge_config(agent, config_override)

            # 调用LLM
            start_time=datetime.now()
            llm_response = await self._call_llm(messages, config)
            logger.info(f"LLM响应时间: {datetime.now() - start_time}")

            # 生成报告元数据
            report_metadata = self._generate_report_metadata(
                report_type, processed_data, llm_response
            )

            # 构建最终响应
            result = self._format_success_response(
                content=llm_response["content"],
                metadata={
                    "report_metadata": report_metadata,
                    "report_type": report_type,
                    "model_used": llm_response["model"],
                    "token_usage": llm_response["usage"],
                    "generation_timestamp": datetime.now().isoformat()
                }
            )

            logger.info(f"AI报告生成完成，类型: {report_type}, 宠物: {pet_data.get('name', '未知')}")
            return result

        except Exception as e:
            logger.error(f"AI报告生成处理失败: {e}")
            return self._format_error_response(f"处理失败: {str(e)}")

    def _process_pet_data(self, pet_data: Dict[str, Any], pet_other_data: Dict[str, Any], report_type: str) -> Dict[
        str, Any]:
        processed = pet_data.copy()
        # 特殊处理宠物性别
        if pet_data.get("gender"):
            processed["gender"] = {"MALE": "雄性", "FEMALE": "雌性"}.get(pet_data.get("gender").upper(),
                                                                         pet_data.get("gender"))
        else:
            processed["gender"] = "未知"
        # 特殊处理宠物绝育情况
        if pet_data.get("is_neutered"):
            processed["is_neutered"] = {True: "已绝育", False: "未绝育"}.get(pet_data.get("is_neutered"),
                                                                             pet_data.get("is_neutered"))
        else:
            processed["gender"] = "未知"
        # 特殊处理宠物疫苗接种情况
        if pet_data.get("is_vaccinated"):
            processed["is_vaccinated"] = {True: "已接种", False: "未接种"}.get(pet_data.get("is_vaccinated"),
                                                                               pet_data.get("is_vaccinated"))
        else:
            processed["is_vaccinated"] = "未知"
        # 特殊处理宠物年龄
        if pet_data.get("age_years") and pet_data.get("age_months"):
            processed["age"] = f"{pet_data['age_years']} 岁 {pet_data['age_months']} 月"
        elif pet_data.get("birth_date"):
            processed["age"] = self._format_birth_date_as_age(pet_data["birth_date"])

        # 根据报告类型提取相关数据
        if report_type == ReportType.NUTRITIONAL_DIET:
            processed["nutritional_diet_target"] = pet_other_data.get("nutritional_diet_target")

        if report_type == ReportType.DEWORMING_PLAN:
            processed["deworming_plan_target"] = pet_other_data.get("deworming_plan_target")

        return processed

    def _format_birth_date_as_age(self, birth_date: str) -> str:
        """将出生日期格式化为 'XX 岁 XX 月' 格式的年龄"""
        today = datetime.today()
        birth_date = birth_date.replace("/", "-")
        birth_year, birth_month, birth_day = map(int, birth_date.split('-'))
        age_years = today.year - birth_year
        age_months = today.month - birth_month
        if age_months < 0 or (age_months == 0 and today.day < birth_day):
            age_years -= 1
            age_months += 12
        if age_months >= 12:
            age_years += age_months // 12
            age_months = age_months % 12
        return f"{age_years} 岁 {age_months} 月"

    def _get_report_template(self, report_type: str, prompt_data: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """获取报告模板"""
        match report_type:
            case ReportType.HEALTH_MANAGEMENT:
                return ReportGenerationPrompts().health_management_prompt()
            case ReportType.DISEASE_RISK:
                return ReportGenerationPrompts().disease_risk_prompt()
            case ReportType.VACCINATION_PLAN:
                return ReportGenerationPrompts().vaccination_plan_prompt()
            case ReportType.DEWORMING_PLAN:
                return ReportGenerationPrompts().deworming_plan_prompt(prompt_data.get("deworming_plan_target"))
            case ReportType.NUTRITIONAL_DIET:
                return ReportGenerationPrompts().nutritional_diet_prompt(prompt_data.get("nutritional_diet_target"))
            case ReportType.CHRONIC_DISEASE:
                return ReportGenerationPrompts().chronic_disease_prompt()
            case _:
                return None

    def _get_report_type_description(self, report_type: str) -> str:
        """获取报告类型描述"""
        try:
            return ReportType(report_type).description
        except ValueError:
            return "未知报告类型"

    def _build_user_message(self, report_type: str, processed_data: Dict[str, Any]) -> str:
        """构建用户消息"""
        pet_name = processed_data.get("name", "我的宠物")
        pet_species = processed_data.get("species", "未知")
        pet_breed = processed_data.get("breed", "未知")
        pet_gender = processed_data.get("gender", "未知")
        pet_weight = processed_data.get("weight", "未知")
        pet_height = processed_data.get("height", "未知")
        pet_birth_date = processed_data.get("birth_date", "未知")
        pet_age = processed_data.get("age", "未知")
        pet_color = processed_data.get("color", "未知")
        pet_is_neutered = processed_data.get("is_neutered", "未知")
        pet_is_vaccinated = processed_data.get("is_vaccinated", "未知")
        pet_description = processed_data.get("description", "未知")
        pet_special_needs = processed_data.get("special_needs", "未知")
        pet_other = processed_data.get("other", "未知")

        return f"""
        "请为{pet_name}生成一份{self._get_report_type_description(report_type)}，宠物信息已结构化提供如下："
        
        === 宠物基础数据 ===
        物种: {pet_species}
        品种: {pet_breed}
        性别: {pet_gender} 
        年龄: {pet_age}
        出生日期: {pet_birth_date}
        体重: {pet_weight}kg
        身高: {pet_height}cm
        毛色: {pet_color}
        绝育情况: {pet_is_neutered}
        疫苗情况: {pet_is_vaccinated}
        宠物描述: {pet_description}
        特殊需求: {pet_special_needs}
        
        === 宠物补充数据 ===
        其他补充信息：{pet_other}
        """

    def _generate_report_metadata(
            self,
            report_type,
            processed_data: Dict[str, Any],
            llm_response: Dict[str, Any]
    ) -> Dict[str, Any]:
        llm_content: str = llm_response.get("content")
        llm_content = llm_content.split('</think>')[-1].strip()
        llm_content = llm_content.replace("```", "")
        if llm_content.startswith('json'):
            llm_content = llm_content[4:].strip()
        logger.info("LLM输出：", llm_content)
        llm_json = json.loads(llm_content)
        # 封装数据
        llm_json["report_name"] = f"{processed_data.get('name', '')}{ReportType(report_type).description}"
        llm_json["name"] = processed_data.get("name", "")
        llm_json["breed"] = processed_data.get("breed", "")
        llm_json["age"] = processed_data.get("age", "")
        llm_json["gender"] = processed_data.get("gender", "")
        llm_json["is_neutered"] = processed_data.get("is_neutered", "")
        match report_type:
            case ReportType.DEWORMING_PLAN:
                if processed_data.get("deworming_plan_target"):
                    llm_json["parasite_risk_assessment"] = processed_data.get("deworming_plan_target")
                llm_json["remark"] = "固定免责声明： “备注：本计划需结合临床检查确认，建议3日内复诊评估驱虫效果。”"
            case ReportType.VACCINATION_PLAN:
                llm_json[
                    "remark"] = "固定免责声明： “备注：本报告由AI根据您提供的宠物信息生成，仅作为宠物疫苗接种的参考建议，不构成医疗诊断结果。宠物的具体接种方案需结合实际健康状况，由专业兽医进行评估后确定。”"
            case ReportType.NUTRITIONAL_DIET:
                if processed_data.get("nutritional_diet_target"):
                    llm_json["nutritional_goals"] = processed_data.get("nutritional_diet_target")
                llm_json["remark"] = "固定免责声明： “备注：本方案需结合临床检查确认，建议3日内复诊评估饮食耐受性。”"
            case _:
                llm_json["remark"] = "固定免责声明： “备注：本报告由AI根据您提供的信息生成，旨在提供常见健康风险的预测性分析，仅作为健康管理的参考建议，具体健康状况需结合临床检查确认。”"
        return llm_json

    def _format_pet_basic_info(self, processed_data: Dict[str, Any]) -> str:
        """格式化宠物基本信息"""
        basic_info = processed_data["basic_info"]
        info_parts = []

        if basic_info.get("name"):
            info_parts.append(f"姓名：{basic_info['name']}")

        if basic_info.get("breed"):
            info_parts.append(f"品种：{basic_info['breed']}")

        if basic_info.get("age"):
            info_parts.append(f"年龄：{basic_info['age']}")

        if basic_info.get("gender"):
            info_parts.append(f"性别：{basic_info['gender']}")

        if basic_info.get("weight"):
            info_parts.append(f"体重：{basic_info['weight']}kg")

        return "\n".join(info_parts) if info_parts else "基本信息不完整"

    def _format_health_data(self, processed_data: Dict[str, Any], report_type: str) -> str:
        """格式化健康数据"""
        data_parts = []

        # 根据报告类型格式化相关数据
        if report_type in ["health_management", "annual_checkup"]:
            if processed_data["health_records"]:
                data_parts.append("健康记录：")
                for record in processed_data["health_records"][-5:]:  # 最近5条记录
                    data_parts.append(f"- {record}")

            if processed_data["test_results"]:
                data_parts.append("\n检查结果：")
                for result in processed_data["test_results"][-3:]:  # 最近3次检查
                    data_parts.append(f"- {result}")

        elif report_type == "disease_risk":
            if processed_data["medical_history"]:
                data_parts.append("病史记录：")
                for history in processed_data["medical_history"]:
                    data_parts.append(f"- {history}")

        elif report_type == "vaccination_plan":
            if processed_data["vaccination_records"]:
                data_parts.append("疫苗记录：")
                for record in processed_data["vaccination_records"]:
                    data_parts.append(f"- {record}")

        elif report_type == "nutrition_analysis":
            nutrition_data = processed_data["nutrition_data"]
            if nutrition_data:
                data_parts.append("营养数据：")
                for key, value in nutrition_data.items():
                    data_parts.append(f"- {key}: {value}")

        elif report_type == "behavior_assessment":
            behavior_data = processed_data["behavior_data"]
            if behavior_data:
                data_parts.append("行为数据：")
                for key, value in behavior_data.items():
                    data_parts.append(f"- {key}: {value}")

        return "\n".join(data_parts) if data_parts else "暂无相关健康数据"
