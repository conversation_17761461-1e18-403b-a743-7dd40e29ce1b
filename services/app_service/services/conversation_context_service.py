"""
会话上下文管理服务
"""
from datetime import datetime, timezone, timedelta
from typing import List, Optional, Dict, Any

from sqlalchemy import select, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession

from shared.logging_config import get_app_service_logger
from ..models.conversation import ConversationContext, ContextType
from ..schemas.conversation import (
    ConversationContextCreate, ConversationContextUpdate
)
from .conversation_service import ConversationService
from .conversation_cache_service import ConversationCacheService

logger = get_app_service_logger()


class ConversationContextService:
    """会话上下文管理服务"""

    def __init__(self, db: AsyncSession, tenant_id: Optional[int] = None):
        self.db = db
        self.tenant_id = tenant_id
        self.conversation_service = ConversationService(db, tenant_id)
        self.cache_service = ConversationCacheService(tenant_id)

    async def create_context(
        self,
        conversation_id: int,
        context_data: ConversationContextCreate,
        user_id: int
    ) -> ConversationContext:
        """创建上下文"""
        try:
            # 验证会话权限
            conversation = await self.conversation_service.get_conversation(conversation_id, user_id)
            if not conversation:
                raise ValueError("会话不存在或无权限访问")

            # 计算过期时间
            expires_at = None
            if context_data.expires_in_hours:
                expires_at = datetime.now(timezone.utc) + timedelta(
                    hours=context_data.expires_in_hours
                )

            # 检查是否已存在相同的上下文
            existing_context = await self._get_context_by_key(
                conversation_id, context_data.context_type, context_data.context_key
            )

            if existing_context:
                # 更新现有上下文
                existing_context.context_value = context_data.context_value
                existing_context.priority = context_data.priority
                existing_context.is_persistent = context_data.is_persistent
                existing_context.expires_at = expires_at
                existing_context.version += 1

                await self.db.commit()
                await self.db.refresh(existing_context)

                # 更新缓存
                await self._update_context_cache(conversation_id)

                logger.info(
                    f"更新上下文成功: conversation_id={conversation_id}, key={context_data.context_key}")
                return existing_context

            # 创建新上下文
            context = ConversationContext(
                conversation_id=conversation_id,
                context_type=context_data.context_type,
                context_key=context_data.context_key,
                context_value=context_data.context_value,
                priority=context_data.priority,
                is_persistent=context_data.is_persistent,
                expires_at=expires_at
            )

            self.db.add(context)
            await self.db.commit()
            await self.db.refresh(context)

            # 更新缓存
            await self._update_context_cache(conversation_id)

            logger.info(
                f"创建上下文成功: id={context.id}, conversation_id={conversation_id}")
            return context

        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建上下文失败: {e}")
            raise

    async def get_context(
        self,
        context_id: int,
        user_id: int
    ) -> Optional[ConversationContext]:
        """获取上下文详情"""
        try:
            query = select(ConversationContext).where(
                ConversationContext.id == context_id,
                ConversationContext.is_deleted == False
            )

            result = await self.db.execute(query)
            context = result.scalar_one_or_none()

            if not context:
                return None

            # 验证会话权限
            conversation = await self.conversation_service.get_conversation(
                context.conversation_id, user_id
            )
            if not conversation:
                return None

            # 检查是否过期
            if context.is_expired():
                return None

            return context

        except Exception as e:
            logger.error(f"获取上下文失败: context_id={context_id}, error={e}")
            raise

    async def get_conversation_contexts(
        self,
        conversation_id: int,
        user_id: int,
        context_type: Optional[ContextType] = None
    ) -> List[ConversationContext]:
        """获取会话上下文列表"""
        try:
            # 验证会话权限
            conversation = await self.conversation_service.get_conversation(conversation_id, user_id)
            if not conversation:
                raise ValueError("会话不存在或无权限访问")

            # 构建查询
            query = select(ConversationContext).where(
                ConversationContext.conversation_id == conversation_id,
                ConversationContext.is_deleted == False,
                or_(
                    ConversationContext.expires_at.is_(None),
                    ConversationContext.expires_at > datetime.now(timezone.utc)
                )
            )

            if context_type:
                query = query.where(
                    ConversationContext.context_type == context_type)

            # 按优先级和创建时间排序
            query = query.order_by(
                ConversationContext.priority.desc(),
                ConversationContext.created_at.asc()
            )

            result = await self.db.execute(query)
            contexts = result.scalars().all()

            logger.debug(
                f"获取会话上下文列表: conversation_id={conversation_id}, count={len(contexts)}")
            return contexts

        except Exception as e:
            logger.error(
                f"获取会话上下文列表失败: conversation_id={conversation_id}, error={e}")
            raise

    async def update_context(
        self,
        context_id: int,
        context_data: ConversationContextUpdate,
        user_id: int
    ) -> Optional[ConversationContext]:
        """更新上下文"""
        try:
            # 获取上下文
            context = await self.get_context(context_id, user_id)
            if not context:
                return None

            # 更新字段
            update_data = context_data.dict(exclude_unset=True)

            # 处理过期时间
            if "expires_in_hours" in update_data:
                expires_in_hours = update_data.pop("expires_in_hours")
                if expires_in_hours:
                    context.expires_at = datetime.now(timezone.utc) + timedelta(
                        hours=expires_in_hours
                    )
                else:
                    context.expires_at = None

            # 更新其他字段
            for field, value in update_data.items():
                setattr(context, field, value)

            # 增加版本号
            context.version += 1

            await self.db.commit()
            await self.db.refresh(context)

            # 更新缓存
            await self._update_context_cache(context.conversation_id)

            logger.info(f"更新上下文成功: id={context_id}")
            return context

        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新上下文失败: context_id={context_id}, error={e}")
            raise

    async def delete_context(
        self,
        context_id: int,
        user_id: int
    ) -> bool:
        """删除上下文（软删除）"""
        try:
            # 获取上下文
            context = await self.get_context(context_id, user_id)
            if not context:
                return False

            # 软删除
            context.is_deleted = True

            await self.db.commit()

            # 更新缓存
            await self._update_context_cache(context.conversation_id)

            logger.info(f"删除上下文成功: id={context_id}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"删除上下文失败: context_id={context_id}, error={e}")
            raise

    async def clear_conversation_contexts(
        self,
        conversation_id: int,
        user_id: int,
        context_type: Optional[ContextType] = None,
        keep_persistent: bool = True
    ) -> int:
        """清空会话上下文"""
        try:
            # 验证会话权限
            conversation = await self.conversation_service.get_conversation(conversation_id, user_id)
            if not conversation:
                raise ValueError("会话不存在或无权限访问")

            # 构建查询
            query = select(ConversationContext).where(
                ConversationContext.conversation_id == conversation_id,
                ConversationContext.is_deleted == False
            )

            if context_type:
                query = query.where(
                    ConversationContext.context_type == context_type)

            if keep_persistent:
                query = query.where(ConversationContext.is_persistent == False)

            result = await self.db.execute(query)
            contexts = result.scalars().all()

            # 软删除上下文
            deleted_count = 0
            for context in contexts:
                context.is_deleted = True
                deleted_count += 1

            await self.db.commit()

            # 更新缓存
            await self._update_context_cache(conversation_id)

            logger.info(
                f"清空会话上下文成功: conversation_id={conversation_id}, deleted_count={deleted_count}")
            return deleted_count

        except Exception as e:
            await self.db.rollback()
            logger.error(
                f"清空会话上下文失败: conversation_id={conversation_id}, error={e}")
            raise

    async def get_context_summary(
        self,
        conversation_id: int,
        user_id: int
    ) -> Dict[str, Any]:
        """获取会话上下文摘要"""
        try:
            # 先尝试从缓存获取
            cached_summary = await self.cache_service.get_conversation_context_from_cache(conversation_id)
            if cached_summary:
                return cached_summary

            # 获取所有有效上下文
            contexts = await self.get_conversation_contexts(conversation_id, user_id)

            # 构建上下文摘要
            summary = {
                "conversation_id": conversation_id,
                "total_contexts": len(contexts),
                "contexts_by_type": {},
                "high_priority_contexts": [],
                "persistent_contexts": [],
                "generated_at": datetime.now(timezone.utc).isoformat()
            }

            for context in contexts:
                # 按类型分组
                context_type = context.context_type.value
                if context_type not in summary["contexts_by_type"]:
                    summary["contexts_by_type"][context_type] = []

                context_info = {
                    "key": context.context_key,
                    "value": context.context_value,
                    "priority": context.priority,
                    "is_persistent": context.is_persistent,
                    "version": context.version
                }
                summary["contexts_by_type"][context_type].append(context_info)

                # 高优先级上下文
                if context.priority > 0:
                    summary["high_priority_contexts"].append(context_info)

                # 持久化上下文
                if context.is_persistent:
                    summary["persistent_contexts"].append(context_info)

            # 缓存摘要
            await self.cache_service.cache_conversation_context(conversation_id, summary)

            return summary

        except Exception as e:
            logger.error(
                f"获取会话上下文摘要失败: conversation_id={conversation_id}, error={e}")
            raise

    async def _get_context_by_key(
        self,
        conversation_id: int,
        context_type: ContextType,
        context_key: str
    ) -> Optional[ConversationContext]:
        """根据键获取上下文"""
        try:
            query = select(ConversationContext).where(
                ConversationContext.conversation_id == conversation_id,
                ConversationContext.context_type == context_type,
                ConversationContext.context_key == context_key,
                ConversationContext.is_deleted == False
            )

            result = await self.db.execute(query)
            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"根据键获取上下文失败: {e}")
            return None

    async def _update_context_cache(self, conversation_id: int):
        """更新上下文缓存"""
        try:
            # 清除现有缓存
            await self.cache_service.invalidate_conversation_cache(conversation_id)

        except Exception as e:
            logger.error(
                f"更新上下文缓存失败: conversation_id={conversation_id}, error={e}")
