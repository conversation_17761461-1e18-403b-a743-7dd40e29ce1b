"""
智能体权限管理服务
"""
from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from shared.logging_config import get_app_service_logger
from ..models.agent import Agent, AgentPermission, AgentType
from ..schemas.agent import AgentPermissionCreate

logger = get_app_service_logger()


class AgentPermissionService:
    """智能体权限管理服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_permission(
        self, 
        agent_id: int, 
        permission_data: AgentPermissionCreate, 
        owner_id: int
    ) -> Optional[AgentPermission]:
        """创建智能体权限"""
        try:
            # 检查智能体是否存在且有权限
            agent = await self._get_agent_with_owner_permission(agent_id, owner_id)
            if not agent:
                return None
            
            # 检查权限是否已存在
            existing_permission = await self._get_existing_permission(
                agent_id, permission_data.user_id, permission_data.permission_type
            )
            if existing_permission:
                raise ValueError("该用户已拥有此类型的权限")
            
            # 创建新权限
            permission = AgentPermission(
                agent_id=agent_id,
                user_id=permission_data.user_id,
                permission_type=permission_data.permission_type,
                expires_at=permission_data.expires_at,
                is_active=True
            )
            
            self.db.add(permission)
            await self.db.commit()
            await self.db.refresh(permission)
            
            logger.info(f"创建智能体权限成功: Agent {agent_id}, User {permission_data.user_id}")
            return permission
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建智能体权限失败: {e}")
            raise
    
    async def list_permissions(self, agent_id: int, owner_id: int) -> List[AgentPermission]:
        """获取智能体权限列表"""
        try:
            # 检查智能体是否存在且有权限
            agent = await self._get_agent_with_owner_permission(agent_id, owner_id)
            if not agent:
                return []
            
            # 获取权限列表
            query = select(AgentPermission).where(
                and_(
                    AgentPermission.agent_id == agent_id,
                    AgentPermission.is_deleted == False
                )
            ).order_by(AgentPermission.created_at.desc())
            
            result = await self.db.execute(query)
            permissions = result.scalars().all()
            
            return list(permissions)
            
        except Exception as e:
            logger.error(f"获取智能体权限列表失败: {e}")
            raise
    
    async def get_permission(
        self, 
        agent_id: int, 
        permission_id: int, 
        owner_id: int
    ) -> Optional[AgentPermission]:
        """获取智能体权限详情"""
        try:
            # 检查智能体是否存在且有权限
            agent = await self._get_agent_with_owner_permission(agent_id, owner_id)
            if not agent:
                return None
            
            # 获取权限详情
            query = select(AgentPermission).where(
                and_(
                    AgentPermission.id == permission_id,
                    AgentPermission.agent_id == agent_id,
                    AgentPermission.is_deleted == False
                )
            )
            
            result = await self.db.execute(query)
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"获取智能体权限详情失败: {e}")
            raise
    
    async def toggle_permission(
        self, 
        agent_id: int, 
        permission_id: int, 
        owner_id: int
    ) -> Optional[AgentPermission]:
        """切换智能体权限状态"""
        try:
            # 获取权限
            permission = await self.get_permission(agent_id, permission_id, owner_id)
            if not permission:
                return None
            
            # 切换状态
            permission.is_active = not permission.is_active
            
            await self.db.commit()
            await self.db.refresh(permission)
            
            logger.info(f"切换智能体权限状态成功: Agent {agent_id}, Permission {permission_id}, Active: {permission.is_active}")
            return permission
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"切换智能体权限状态失败: {e}")
            raise
    
    async def delete_permission(
        self, 
        agent_id: int, 
        permission_id: int, 
        owner_id: int
    ) -> bool:
        """删除智能体权限"""
        try:
            # 获取权限
            permission = await self.get_permission(agent_id, permission_id, owner_id)
            if not permission:
                return False
            
            # 软删除权限
            permission.soft_delete()
            await self.db.commit()
            
            logger.info(f"删除智能体权限成功: Agent {agent_id}, Permission {permission_id}")
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"删除智能体权限失败: {e}")
            raise
    
    async def check_user_permission(
        self, 
        agent_id: int, 
        user_id: int, 
        permission_type: str
    ) -> bool:
        """检查用户是否有指定权限"""
        try:
            query = select(AgentPermission).where(
                and_(
                    AgentPermission.agent_id == agent_id,
                    AgentPermission.user_id == user_id,
                    AgentPermission.permission_type == permission_type,
                    AgentPermission.is_active == True,
                    AgentPermission.is_deleted == False
                )
            )
            
            result = await self.db.execute(query)
            permission = result.scalar_one_or_none()
            
            if not permission:
                return False
            
            # 检查权限是否有效
            return permission.is_valid()
            
        except Exception as e:
            logger.error(f"检查用户权限失败: {e}")
            return False
    
    async def get_user_permissions(self, agent_id: int, user_id: int) -> List[AgentPermission]:
        """获取用户在指定智能体上的所有权限"""
        try:
            query = select(AgentPermission).where(
                and_(
                    AgentPermission.agent_id == agent_id,
                    AgentPermission.user_id == user_id,
                    AgentPermission.is_active == True,
                    AgentPermission.is_deleted == False
                )
            )
            
            result = await self.db.execute(query)
            permissions = result.scalars().all()
            
            # 过滤有效权限
            valid_permissions = [p for p in permissions if p.is_valid()]
            
            return valid_permissions
            
        except Exception as e:
            logger.error(f"获取用户权限失败: {e}")
            return []
    
    async def _get_agent_with_owner_permission(self, agent_id: int, owner_id: int) -> Optional[Agent]:
        """获取智能体并检查所有者权限"""
        try:
            query = select(Agent).where(
                and_(
                    Agent.id == agent_id,
                    Agent.is_deleted == False
                )
            )
            
            result = await self.db.execute(query)
            agent = result.scalar_one_or_none()
            
            if not agent:
                return None
            
            # 检查所有者权限
            if agent.agent_type == AgentType.CUSTOM and agent.owner_id != owner_id:
                return None
            
            # 系统智能体只有超级管理员可以管理权限
            if agent.agent_type == AgentType.SYSTEM:
                # TODO: 检查是否为超级管理员
                return None
            
            return agent
            
        except Exception as e:
            logger.error(f"获取智能体所有者权限检查失败: {e}")
            return None
    
    async def _get_existing_permission(
        self, 
        agent_id: int, 
        user_id: int, 
        permission_type: str
    ) -> Optional[AgentPermission]:
        """检查是否已存在相同权限"""
        try:
            query = select(AgentPermission).where(
                and_(
                    AgentPermission.agent_id == agent_id,
                    AgentPermission.user_id == user_id,
                    AgentPermission.permission_type == permission_type,
                    AgentPermission.is_deleted == False
                )
            )
            
            result = await self.db.execute(query)
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"检查已存在权限失败: {e}")
            return None
