# App Service - 智能体管理服务

宠物医疗AI开放平台的智能体管理和执行服务。

## 🏗️ 架构概览

```
app_service/
├── api/                    # API路由层
│   ├── agents.py          # 智能体CRUD + 执行API (支持流式)
│   ├── system_agents.py   # 系统级智能体专用API
│   └── agent_permissions.py # 权限管理API
├── models/                # 数据模型
│   └── agent.py          # Agent/AgentVersion/AgentPermission/AgentExecution
├── schemas/               # 数据验证
│   └── agent.py          # 请求/响应模式
├── services/              # 业务逻辑
│   ├── agent_service.py   # 核心智能体服务
│   ├── custom_agents.py   # 自定义智能体处理器
│   └── system_agents/     # 系统级智能体处理器
├── scripts/               # 初始化脚本
│   └── init_system_agents.py
├── config.py             # 服务配置
└── main.py              # FastAPI应用入口
```

## ✨ 核心功能

### 1. 智能体管理
- **CRUD操作**: 创建、查询、更新、删除智能体
- **权限管理**: 基于用户的访问控制
- **分类支持**: 系统级 vs 自定义智能体

### 2. 智能体执行引擎
- **同步执行**: 传统请求-响应模式
- **流式执行**: SSE流式响应支持
- **执行记录**: 完整的执行历史和性能统计
- **错误处理**: 完善的异常处理机制

### 3. 系统级智能体 (预置4个)
- 🩺 **AI问诊智能体**: 分析宠物症状，提供诊断建议
- 👁️ **AI视觉识别智能体**: 图像分析和健康状况识别  
- 📊 **报告生成智能体**: 根据数据生成医疗报告
- 📋 **报告解读智能体**: 解读医疗报告，提供通俗解释

### 4. 自定义智能体
- **用户创建**: 支持用户自定义智能体
- **配置管理**: 灵活的配置和提示词模板
- **OpenAI集成**: 与外部LLM服务集成

## 🚀 启动服务

### 1. 环境准备
```bash
# 激活conda环境
conda activate vet_open_platform

# 确保数据库已初始化
python scripts/quick_init_db.py
```

### 2. 初始化系统智能体
```bash
python services/app_service/scripts/init_system_agents.py
```

### 3. 启动服务
```bash
# 直接启动
python services/app_service/main.py

# 或使用uvicorn
uvicorn services.app_service.main:app --host 0.0.0.0 --port 8003 --reload
```

### 4. 验证服务
```bash
# 健康检查
curl http://localhost:8003/health

# API文档
open http://localhost:8003/api/v1/docs
```

## 📡 API使用示例

### 1. 获取智能体列表
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8003/api/v1/agents
```

### 2. 执行AI问诊智能体 (非流式)
```bash
curl -X POST \
  http://localhost:8003/api/v1/agents/1/execute \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "input_data": {
      "pet_info": {
        "breed": "金毛犬",
        "age": "3岁", 
        "gender": "雄性",
        "weight": "25"
      },
      "symptoms": ["食欲不振", "精神萎靡"],
      "additional_info": "症状持续2天"
    },
    "stream": false
  }'
```

### 3. 执行智能体 (流式响应)
```bash
curl -X POST \
  http://localhost:8003/api/v1/agents/1/execute \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  --no-buffer -N \
  -d '{
    "input_data": {
      "pet_info": {
        "breed": "金毛犬",
        "age": "3岁",
        "gender": "雄性", 
        "weight": "25"
      },
      "symptoms": ["食欲不振", "精神萎靡"],
      "additional_info": "症状持续2天"
    },
    "stream": true
  }'
```

### 4. 系统级智能体专用API
```bash
# AI问诊
curl -X POST \
  http://localhost:8003/api/v1/system-agents/diagnosis \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "pet_info": {...},
    "symptoms": [...],
    "additional_info": "..."
  }'

# 报告生成
curl -X POST \
  http://localhost:8003/api/v1/system-agents/report-generation \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "pet_data": {...},
    "report_type": "health_checkup",
    "examination_data": {...}
  }'
```

## 🔧 技术特性

- **微服务架构**: 独立部署，松耦合设计
- **异步处理**: 全异步数据库操作
- **流式响应**: 支持SSE实时流式输出
- **权限控制**: 基于用户的访问控制
- **性能监控**: 执行时间和Token使用统计
- **错误处理**: 完善的异常处理和日志记录

## 📊 数据模型

### Agent (智能体)
- 基本信息: name, display_name, description
- 类型: system (系统级) / custom (自定义)
- 状态: draft / active / inactive / archived
- 配置: prompt_template, config, model设置

### AgentExecution (执行记录)
- 执行信息: execution_id, agent_id, user_id
- 输入输出: input_data, output_data
- 性能数据: duration_ms, token_usage
- 状态跟踪: pending / running / completed / failed

## 🔗 相关服务

- **User Service**: 用户认证和权限管理
- **Pet Service**: 宠物信息管理
- **Gateway**: API网关和请求路由
- **Shared**: 公共组件和工具

## 📝 开发说明

- 所有API都需要认证 (JWT Token 或 API Key)
- 支持跨服务查询 (通过 shared/utils/cross_service_queries.py)
- 日志记录使用 loguru (shared/logging_config.py)
- 数据库操作全异步 (SQLAlchemy + asyncpg)
