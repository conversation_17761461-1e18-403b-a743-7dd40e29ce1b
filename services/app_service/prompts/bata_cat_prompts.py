from typing import List

from services.app_service.schemas.beta_cat_agent import BetaCatPetInfo, BetaCatPetOtherInfo, BetaCatPetExamItem


class BetaCatPrompts:

    @staticmethod
    def _get_gender_display(gender):
        """将英文性别转换为中文显示"""
        gender_map = {
            'male': '雄性',
            'female': '雌性',
            'unknown': '未知'
        }
        return gender_map.get(gender, '暂无信息') if gender else '暂无信息'

    def cdss_prompt(self, pet_info: BetaCatPetInfo, pet_other_info: BetaCatPetOtherInfo):
        return rf"""
        作为资深宠物医学专家，请基于以下宠物基本信息和临床数据，生成一份科学、严谨的辅助诊断建议报告：
        
        宠物基本信息：
        - 宠物名称：{pet_info.name if pet_info.name else '暂无信息'}
        - 宠物物种：{pet_info.category_name if pet_info.category_name else '暂无信息'}
        - 宠物品种：{pet_info.kind_name if pet_info.kind_name else '暂无信息'}
        - 宠物性别：{self._get_gender_display(pet_info.gender)}
        - 宠物出生日期：{pet_info.birthday if pet_info.birthday else '暂无信息'}
        - 宠物体重：{pet_info.weight if pet_info.weight else '暂无信息'}
        
        宠物临床数据：
        - 宠物症状信息：{pet_other_info.pet_symptoms if pet_other_info.pet_symptoms else '暂无信息'}
        - 宠物病史信息：{pet_other_info.context if pet_other_info.context else '暂无信息'}
        - 宠物体征信息：{pet_other_info.pet_signs if pet_other_info.pet_signs else '暂无信息'}
        - 宠物精神状态：{pet_other_info.pet_spirit if pet_other_info.pet_spirit else '暂无信息'}
        - 可视粘膜颜色：{pet_other_info.pet_visual if pet_other_info.pet_visual else '暂无信息'}
        - 口腔情况：{pet_other_info.pet_oral if pet_other_info.pet_oral else '暂无信息'}
        - 皮肤情况：{pet_other_info.pet_skin if pet_other_info.pet_skin else '暂无信息'}
        - 心音听诊断：{pet_other_info.pet_heart if pet_other_info.pet_heart else '暂无信息'}
        - 腹部触诊：{pet_other_info.pet_abdominal if pet_other_info.pet_abdominal else '暂无信息'}
        - 淋巴：{pet_other_info.pet_lymph if pet_other_info.pet_lymph else '暂无信息'}
        
        请根据以上数据生成完整辅助诊断建议报告：
        1. 异常指标分析：逐项说明异常临床指标及其病理生理学意义
        2. 诊断依据：结合病史和检查结果阐明诊断逻辑（需区分主要诊断和次要诊断）
        3. 鉴别诊断：列出最可能的3种疾病，按可能性排序并说明鉴别要点
        4. 治疗方案：
           - 优先治疗方案（药物名称、剂量、频次、疗程）
           - 替代治疗方案（如存在药物禁忌时）
           - 对症支持治疗（如补液、止痛等）
        5. 检查建议：
           - 必需检查项目（注明临床意义）
           - 可选检查项目（说明适用条件）
        6. 风险警示：
           - 红色预警（需立即干预的情况）
           - 黄色预警（需密切监测的指标）
        7. 预后评估：根据当前病情预测短期和长期预后
        8. 护理指导：
           - 家庭护理要点（环境管理、活动限制等）
           - 饮食建议（特殊营养需求或禁忌）
           - 复诊计划（具体时间节点和复查指标）
        9. 转诊建议：如需专科治疗，说明转诊指征和推荐科室
        
        报告要求：
        - 严格遵循循证医学原则
        - 标注引用来源（如《默克兽医手册》第X版）
        - 区分证据等级（A/B/C级证据）
        - 注明药物副作用监测要点
        - 提供医患沟通话术建议（如向主人解释病情的要点）
    """

    def lab_prompt(self, pet_info: BetaCatPetInfo, lab_project: str, instrument_info: str,
                   exam_items: List[BetaCatPetExamItem]):
        exam_results = (
            "\n".join([
                f"- 检测项目：{item.name or '暂无信息'}, "
                f"单位：{item.unit or '暂无信息'}, "
                f"检测结果：{item.value or '暂无信息'}, "
                f"参考范围：{item.range or '暂无信息'}"
                for item in exam_items
            ])
            if exam_items
            else "暂无信息"
        )
        return rf"""
        作为兽医临床病理学专家，请基于以下数据生成专业生化报告解读：
        
        宠物基本信息：
        - 宠物名称：{pet_info.name if pet_info.name else '暂无信息'}
        - 宠物物种：{pet_info.category_name if pet_info.category_name else '暂无信息'}
        - 宠物品种：{pet_info.kind_name if pet_info.kind_name else '暂无信息'}
        - 宠物性别：{self._get_gender_display(pet_info.gender)}
        - 宠物出生日期：{pet_info.birthday if pet_info.birthday else '暂无信息'}
        - 宠物体重：{pet_info.weight if pet_info.weight else '暂无信息'}
        
        设备信息：
        - 实验室检查项目：{lab_project if lab_project else '暂无信息'}
        - 设备名称：{instrument_info if instrument_info else '暂无信息'}
        
        检查结果：
        {exam_results}
        
        请根据以上数据：
        1. 异常值分析：
           - 标注显著异常值（超出参考范围20%以上）
           - 区分生理性异常与病理性异常
           - 重点分析指标组合模式（如BUN/Cr比值）
        2. 病理学关联：
           - 器官系统定位（肝/肾/胰腺等）
           - 潜在疾病推断（按可能性排序）：
             a) 最可能诊断（需说明依据）
             b) 次要诊断
             c) 鉴别诊断
        3. 临床建议：
           ├─ 紧急处置：需立即干预的危急值
           ├─ 药物治疗：具体方案（含剂量调整建议）
           ├─ 复查监测：关键指标复查时间表
           └─ 辅助检查：建议补充的影像学/特殊检查
        4. 风险预警：
           - 红色预警（如高钾血症>7.0mmol/L等）
           - 发展预测（如持续异常可能导致的并发症）
        5. 特别说明：
           - 仪器误差可能性（如溶血对钾值的影响）
           - 品种特异性参考范围（如有）
           - 年龄相关指标解释（如老年犬ALP升高）
        
        输出规范：
        - 使用国际标准缩写（如ALT而非GPT）
        - 标注引用文献（如IRIS分级标准）
        - 区分检测方法差异（如IDEXX vs. 爱德士）
        - 提供主人沟通要点（简化版解释）
    """
