from services.app_service.schemas.xiao_pei_agent import XiaoPeiPetInfo


class XiaoPeiPrompts:

    @staticmethod
    def _get_gender_display(gender):
        """将英文性别转换为中文显示"""
        gender_map = {
            'male': '雄性',
            'female': '雌性',
            'unknown': '未知'
        }
        return gender_map.get(gender, '暂无信息') if gender else '暂无信息'

    @staticmethod
    def _get_age_display(age_years=None, age_months=None):
        """优化版年龄显示"""
        years = age_years or 0
        months = age_months or 0
        return f"{years}岁{months}个月" if (years or months) else "暂无信息"

    def chat_prompt(self, pet_info: XiaoPeiPetInfo, user_message: str):
        return rf"""
        作为资深宠物医学专家，请基于以下宠物基本信息回答用户问题：
        宠物基本信息：
        - 宠物名称：{pet_info.name if pet_info.name else '暂无信息'}
        - 宠物物种：{pet_info.species if pet_info.species else '暂无信息'}
        - 宠物品种：{pet_info.breed if pet_info.breed else '暂无信息'}
        - 宠物年龄：{self._get_age_display(pet_info.age_years, pet_info.age_months)}
        - 宠物性别：{self._get_gender_display(pet_info.gender)}
        - 是否绝育：{"已绝育" if pet_info.is_neutered else "未绝育"}
        - 宠物体重：{pet_info.weight if pet_info.weight else '暂无信息'}kg
        
        用户问题：
        {user_message}
    """
