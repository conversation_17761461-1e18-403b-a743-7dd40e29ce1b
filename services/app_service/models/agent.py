"""
智能体相关数据库模型
"""
from datetime import datetime, timezone
from enum import Enum

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, JSON, Enum as SQLEnum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from shared.models.base import BaseAuditModel
from shared.config.database_schema import create_table_args, get_foreign_key_with_schema


class AgentType(str, Enum):
    """智能体类型枚举"""
    SYSTEM = "system"      # 系统级智能体
    CUSTOM = "custom"      # 用户自定义智能体


class AgentStatus(str, Enum):
    """智能体状态枚举"""
    DRAFT = "draft"        # 草稿状态
    ACTIVE = "active"      # 激活状态
    INACTIVE = "inactive"  # 停用状态
    ARCHIVED = "archived"  # 归档状态


class ExecutionStatus(str, Enum):
    """执行状态枚举"""
    PENDING = "pending"    # 等待执行
    RUNNING = "running"    # 执行中
    COMPLETED = "completed"  # 执行完成
    FAILED = "failed"      # 执行失败
    CANCELLED = "cancelled"  # 已取消


class SystemAgentCategory(str, Enum):
    """系统智能体分类枚举"""
    DIAGNOSIS = "diagnosis"           # AI问诊智能体
    VISION = "vision"                # AI视觉识别智能体
    REPORT_GENERATION = "report_generator"  # AI报告生成智能体
    REPORT_ANALYSIS = "report_analysis"  # AI报告解读智能体


class ReportType(str, Enum):
    def __new__(cls, value, description=""):
        obj = str.__new__(cls, value)
        obj._value_ = value
        obj.description = description
        return obj

    HEALTH_MANAGEMENT = "health_management", "综合健康管理报告"
    DISEASE_RISK = "disease_risk", "疾病风险预警报告"
    VACCINATION_PLAN = "vaccination_plan", "疫苗接种计划"
    DEWORMING_PLAN = "deworming_plan", "驱虫计划"
    NUTRITIONAL_DIET = "nutritional_diet", "营养饮食方案"
    CHRONIC_DISEASE = "chronic_disease", "慢性病管理报告"


class Agent(BaseAuditModel):
    """智能体基础模型"""

    __tablename__ = "agents"
    __table_args__ = create_table_args()  # 使用统一的schema配置

    # 基础信息
    agent_name = Column(String(100), nullable=False, comment="智能体名称")
    display_name = Column(String(200), nullable=False, comment="显示名称")
    description = Column(Text, nullable=True, comment="智能体描述")
    avatar_url = Column(String(500), nullable=True, comment="头像URL")

    # 类型和分类
    agent_type = Column(SQLEnum(AgentType), nullable=False, comment="智能体类型")
    system_category = Column(SQLEnum(SystemAgentCategory),
                             nullable=True, comment="系统智能体分类")

    # 状态管理
    status = Column(SQLEnum(AgentStatus),
                    default=AgentStatus.DRAFT, nullable=False, comment="状态")

    # 权限控制
    is_public = Column(Boolean, default=False, nullable=False, comment="是否公开")
    owner_id = Column(Integer, nullable=True, comment="所有者用户ID")  # 系统智能体为NULL

    # 配置信息
    config = Column(JSON, nullable=True, comment="智能体配置参数")
    prompt_template = Column(Text, nullable=True, comment="提示词模板")

    # 统计信息
    usage_count = Column(Integer, default=0, nullable=False, comment="使用次数")
    last_used_at = Column(DateTime(timezone=True),
                          nullable=True, comment="最后使用时间")

    # 关联关系 (仅保留同一微服务内的关系)
    permissions = relationship(
        "AgentPermission", back_populates="agent", cascade="all, delete-orphan")

    # Note: 跨微服务关系在服务层处理：
    # - User relationship: 通过 owner_id 查询 user_service

    def __repr__(self):
        return f"<Agent(id={self.id}, agent_name='{self.agent_name}', type='{self.agent_type}')>"

    def is_system_agent(self) -> bool:
        """判断是否为系统智能体"""
        return self.agent_type == AgentType.SYSTEM

    def is_custom_agent(self) -> bool:
        """判断是否为用户自定义智能体"""
        return self.agent_type == AgentType.CUSTOM

    def is_active(self) -> bool:
        """判断是否处于激活状态"""
        return self.status == AgentStatus.ACTIVE and not self.is_deleted

    def can_be_used_by(self, user_id: int) -> bool:
        """判断指定用户是否可以使用此智能体"""
        if self.is_system_agent():
            return True  # 系统智能体所有人都可以使用

        if self.owner_id == user_id:
            return True  # 所有者可以使用

        if self.is_public:
            return True  # 公开的智能体所有人都可以使用

        # TODO: 检查是否有特定权限
        return False

    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
        self.last_used_at = func.now()


class AgentPermission(BaseAuditModel):
    """智能体权限管理模型"""

    __tablename__ = "agent_permissions"
    __table_args__ = create_table_args()  # 使用统一的schema配置

    # 关联信息
    agent_id = Column(Integer, ForeignKey(get_foreign_key_with_schema("agents")),
                      nullable=False, comment="智能体ID")
    user_id = Column(Integer, nullable=False, comment="用户ID")

    # 权限类型
    permission_type = Column(String(50), nullable=False,
                             comment="权限类型: view, use, edit, admin")

    # 权限状态
    is_active = Column(Boolean, default=True, nullable=False, comment="权限是否激活")
    expires_at = Column(DateTime(timezone=True),
                        nullable=True, comment="权限过期时间")

    # 关联关系
    agent = relationship("Agent", back_populates="permissions")

    def __repr__(self):
        return f"<AgentPermission(id={self.id}, agent_id={self.agent_id}, user_id={self.user_id}, type='{self.permission_type}')>"

    def is_valid(self) -> bool:
        """检查权限是否有效"""
        if not self.is_active or self.is_deleted:
            return False

        if self.expires_at and datetime.now(timezone.utc) > self.expires_at:
            return False

        return True


class AgentExecution(BaseAuditModel):
    """智能体执行记录模型"""

    __tablename__ = "agent_executions"
    __table_args__ = create_table_args()  # 使用统一的schema配置

    # 关联信息
    agent_id = Column(Integer, ForeignKey(get_foreign_key_with_schema("agents")),
                      nullable=False, comment="智能体ID")
    user_id = Column(Integer, nullable=False, comment="执行用户ID")

    # 执行信息
    execution_id = Column(String(100), unique=True,
                          nullable=False, comment="执行ID")
    input_data = Column(JSON, nullable=True, comment="输入数据")
    output_data = Column(JSON, nullable=True, comment="输出数据")

    # 状态和时间
    status = Column(SQLEnum(ExecutionStatus), default=ExecutionStatus.RUNNING,
                    nullable=False, comment="执行状态")
    started_at = Column(DateTime(timezone=True),
                        server_default=func.now(), nullable=False, comment="开始时间")
    completed_at = Column(DateTime(timezone=True),
                          nullable=True, comment="完成时间")

    # 性能指标
    duration_ms = Column(Integer, nullable=True, comment="执行时长(毫秒)")
    token_usage = Column(JSON, nullable=True, comment="Token使用统计")

    # 错误信息
    error_message = Column(Text, nullable=True, comment="错误信息")

    # Note: 按照微服务架构原则，避免跨表关系
    # 通过 agent_id 在服务层查询 Agent 信息

    def __repr__(self):
        return f"<AgentExecution(id={self.id}, execution_id='{self.execution_id}', status='{self.status}')>"
