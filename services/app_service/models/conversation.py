"""
会话管理相关数据库模型
"""
from datetime import datetime, timezone
# 移除enum导入，改用数值常量

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, JSON, Index, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from shared.models.base import BaseModel, SoftDeleteMixin
from shared.config.database_schema import create_table_args, get_foreign_key_with_schema


class ConversationType:
    """会话类型常量"""
    DIAGNOSIS = 1    # AI问诊会话（主要场景）
    GENERAL = 2      # 通用对话会话

    @classmethod
    def get_name(cls, value):
        """获取类型名称"""
        mapping = {
            cls.DIAGNOSIS: "diagnosis",
            cls.GENERAL: "general"
        }
        return mapping.get(value, "unknown")


class ConversationStatus:
    """会话状态常量"""
    ACTIVE = 1       # 活跃状态
    PAUSED = 2       # 暂停状态
    COMPLETED = 3    # 已完成
    ARCHIVED = 4     # 已归档
    EXPIRED = 5      # 已过期

    @classmethod
    def get_name(cls, value):
        """获取状态名称"""
        mapping = {
            cls.ACTIVE: "active",
            cls.PAUSED: "paused",
            cls.COMPLETED: "completed",
            cls.ARCHIVED: "archived",
            cls.EXPIRED: "expired"
        }
        return mapping.get(value, "unknown")


class MessageRole:
    """消息角色常量"""
    USER = 1         # 用户消息
    ASSISTANT = 2    # 智能体回复
    SYSTEM = 3       # 系统消息

    @classmethod
    def get_name(cls, value):
        """获取角色名称"""
        mapping = {
            cls.USER: "user",
            cls.ASSISTANT: "assistant",
            cls.SYSTEM: "system"
        }
        return mapping.get(value, "unknown")


class MessageType:
    """消息类型常量"""
    TEXT = 1         # 文本消息
    IMAGE = 2        # 图片消息
    FILE = 3         # 文件消息

    @classmethod
    def get_name(cls, value):
        """获取类型名称"""
        mapping = {
            cls.TEXT: "text",
            cls.IMAGE: "image",
            cls.FILE: "file"
        }
        return mapping.get(value, "unknown")


class MessageStatus:
    """消息状态常量"""
    PENDING = 1      # 等待处理
    PROCESSING = 2   # 处理中
    COMPLETED = 3    # 已完成
    FAILED = 4       # 处理失败
    CANCELLED = 5    # 已取消

    @classmethod
    def get_name(cls, value):
        """获取状态名称"""
        mapping = {
            cls.PENDING: "pending",
            cls.PROCESSING: "processing",
            cls.COMPLETED: "completed",
            cls.FAILED: "failed",
            cls.CANCELLED: "cancelled"
        }
        return mapping.get(value, "unknown")


class ContextType:
    """上下文类型常量"""
    SYSTEM_PROMPT = 1      # 系统提示词
    USER_PROFILE = 2       # 用户画像
    PET_INFO = 3          # 宠物信息
    MEDICAL_HISTORY = 4    # 医疗历史
    SUMMARY = 5           # 会话摘要

    @classmethod
    def get_name(cls, value):
        """获取类型名称"""
        mapping = {
            cls.SYSTEM_PROMPT: "system_prompt",
            cls.USER_PROFILE: "user_profile",
            cls.PET_INFO: "pet_info",
            cls.MEDICAL_HISTORY: "medical_history",
            cls.SUMMARY: "summary"
        }
        return mapping.get(value, "unknown")


class Conversation(BaseModel, SoftDeleteMixin):
    """对话会话模型"""

    __tablename__ = "conversations"
    __table_args__ = create_table_args(
        # 复合索引优化查询性能
        Index('ix_conversation_user_tenant', 'user_id', 'tenant_id'),
        Index('ix_conversation_status_type', 'status', 'conversation_type'),
        Index('ix_conversation_activity', 'last_activity_at'),
        Index('ix_conversation_expires', 'expires_at'),
        Index('ix_conversation_conversation_id',
              'conversation_id'),  # 添加conversation_id索引
    )

    # 基础信息
    title = Column(String(200), nullable=False, comment="会话标题")
    conversation_type = Column(Integer, nullable=False,
                               default=ConversationType.GENERAL, comment="会话类型")
    status = Column(Integer, nullable=False,
                    default=ConversationStatus.ACTIVE, comment="会话状态")
    conversation_id = Column(String(100), nullable=True,
                             unique=True, comment="会话ID")

    # 关联信息
    user_id = Column(Integer, nullable=False, comment="用户ID")
    tenant_id = Column(Integer, nullable=True, comment="租户ID")
    primary_agent_id = Column(Integer, ForeignKey("vet.agents.id"),
                              nullable=True, comment="主要智能体ID")

    # 会话配置
    config = Column(JSON, nullable=True, comment="会话配置参数")
    context_window_size = Column(Integer, default=10, comment="上下文窗口大小")
    max_messages = Column(Integer, default=100, comment="最大消息数量")

    # 状态管理
    last_activity_at = Column(DateTime(timezone=True),
                              server_default=func.now(), comment="最后活动时间")
    expires_at = Column(DateTime(timezone=True), nullable=True, comment="过期时间")

    # 统计信息
    message_count = Column(Integer, default=0, comment="消息数量")
    total_tokens = Column(Integer, default=0, comment="总Token使用量")

    # Note: 按照微服务架构原则，避免跨表关系
    # 通过 primary_agent_id 在服务层查询 Agent 信息
    # 通过 conversation_id 在服务层查询 Message 和 ConversationContext 信息
    messages = relationship("Message", back_populates="conversation",
                            cascade="all, delete-orphan", lazy="dynamic")
    contexts = relationship("ConversationContext", back_populates="conversation",
                            cascade="all, delete-orphan", lazy="dynamic")

    def __repr__(self):
        return f"<Conversation(id={self.id}, title='{self.title}', type='{self.conversation_type}')>"

    def is_active(self) -> bool:
        """判断会话是否处于活跃状态"""
        if self.is_deleted:
            return False
        if self.status != ConversationStatus.ACTIVE:
            return False
        if self.expires_at and datetime.now(timezone.utc) > self.expires_at:
            return False
        return True

    def is_expired(self) -> bool:
        """检查会话是否已过期"""
        if not self.expires_at:
            return False
        return datetime.now(timezone.utc) > self.expires_at

    def increment_message_count(self):
        """增加消息计数"""
        self.message_count += 1
        self.last_activity_at = func.now()

    def add_tokens(self, token_count: int):
        """添加Token使用量"""
        if token_count > 0:
            self.total_tokens += token_count


class Message(BaseModel, SoftDeleteMixin):
    """消息记录模型"""

    __tablename__ = "messages"
    __table_args__ = create_table_args(
        # 复合索引优化查询性能
        Index('ix_message_conversation_seq',
              'conversation_id', 'sequence_number'),
        Index('ix_message_conversation_role', 'conversation_id', 'role'),
        Index('ix_message_status', 'status'),
        Index('ix_message_created', 'created_at'),
    )

    # 关联信息
    conversation_id = Column(Integer, ForeignKey(get_foreign_key_with_schema("conversations")),
                             nullable=False, comment="会话ID")
    agent_id = Column(Integer, ForeignKey(get_foreign_key_with_schema("agents")),
                      nullable=True, comment="处理智能体ID")

    # 消息基础信息
    role = Column(Integer, nullable=False, comment="消息角色")
    message_type = Column(Integer, nullable=False,
                          default=MessageType.TEXT, comment="消息类型")
    content = Column(Text, nullable=False, comment="消息内容")

    # 消息元数据
    message_metadata = Column(JSON, nullable=True, comment="消息元数据")
    attachments = Column(JSON, nullable=True, comment="附件信息（图片、文件等）")

    # OCR相关字段
    ocr_result = Column(JSON, nullable=True, comment="OCR识别结果")
    has_ocr_content = Column(Boolean, default=False, comment="是否包含OCR内容")

    # 处理信息
    status = Column(Integer, nullable=False,
                    default=MessageStatus.COMPLETED, comment="消息状态")
    processing_time_ms = Column(Integer, nullable=True, comment="处理时间(毫秒)")
    token_usage = Column(JSON, nullable=True, comment="Token使用统计")

    # 上下文信息
    sequence_number = Column(Integer, nullable=False, comment="消息序号")
    parent_message_id = Column(Integer, ForeignKey("vet.messages.id"),
                               nullable=True, comment="父消息ID")

    # 错误信息
    error_message = Column(Text, nullable=True, comment="错误信息")
    retry_count = Column(Integer, default=0, comment="重试次数")

    # 关系映射
    conversation = relationship("Conversation", back_populates="messages")
    # Note: 按照微服务架构原则，避免跨表关系
    # 通过 agent_id 在服务层查询 Agent 信息
    parent_message = relationship("Message", remote_side="Message.id")
    child_messages = relationship(
        "Message", remote_side="Message.parent_message_id", overlaps="parent_message")

    def __repr__(self):
        return f"<Message(id={self.id}, conversation_id={self.conversation_id}, role='{self.role}')>"

    def is_user_message(self) -> bool:
        """判断是否为用户消息"""
        return self.role == MessageRole.USER

    def is_assistant_message(self) -> bool:
        """判断是否为助手消息"""
        return self.role == MessageRole.ASSISTANT

    def has_attachments(self) -> bool:
        """判断是否有附件"""
        return bool(self.attachments)

    def get_token_count(self) -> int:
        """获取Token使用量"""
        if not self.token_usage:
            return 0
        return self.token_usage.get("total_tokens", 0)


class ConversationContext(BaseModel, SoftDeleteMixin):
    """会话上下文模型"""

    __tablename__ = "conversation_contexts"
    __table_args__ = (
        # 复合索引优化查询性能
        Index('ix_context_conversation_type',
              'conversation_id', 'context_type'),
        Index('ix_context_conversation_key', 'conversation_id', 'context_key'),
        Index('ix_context_priority', 'priority'),
        Index('ix_context_expires', 'expires_at'),
        # 唯一约束
        UniqueConstraint('conversation_id', 'context_type', 'context_key',
                         name='uq_conversation_context'),
        {'schema': 'vet'}  # 指定schema为vet
    )

    # 关联信息
    conversation_id = Column(Integer, ForeignKey("vet.conversations.id"),
                             nullable=False, comment="会话ID")

    # 上下文信息
    context_type = Column(Integer, nullable=False, comment="上下文类型")
    context_key = Column(String(100), nullable=False, comment="上下文键")
    context_value = Column(JSON, nullable=False, comment="上下文值")

    # 生命周期管理
    priority = Column(Integer, default=0, comment="优先级")
    expires_at = Column(DateTime(timezone=True), nullable=True, comment="过期时间")
    is_persistent = Column(Boolean, default=False, comment="是否持久化")

    # 版本控制
    version = Column(Integer, default=1, comment="版本号")

    # 关系映射
    conversation = relationship("Conversation", back_populates="contexts")

    def __repr__(self):
        return f"<ConversationContext(id={self.id}, conversation_id={self.conversation_id}, type='{self.context_type}')>"

    def is_expired(self) -> bool:
        """检查上下文是否已过期"""
        if not self.expires_at:
            return False
        return datetime.now(timezone.utc) > self.expires_at

    def is_valid(self) -> bool:
        """检查上下文是否有效"""
        return not self.is_deleted and not self.is_expired()
