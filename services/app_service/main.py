#!/usr/bin/env python3
"""
应用服务主应用
"""
import sys
from pathlib import Path
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi_offline import FastAPIOffline
from sqlalchemy import text

from shared.env_loader import load_env
from shared.database import close_db, AsyncSessionLocal
from shared.redis_simple import redis_client
from shared.logging_config import setup_app_service_logger
from shared.middleware.request_id import get_request_id, add_request_id_middleware
from shared.utils.exception_handlers import BusinessException
from shared.models.response import create_error_response, ErrorCode

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
sys.path.insert(0, str(project_root))

# 加载环境变量 - 必须在其他导入之前
load_env()

# 导入路由 - 支持直接运行和包内运行
try:
    # 尝试相对导入（在包内运行时）
    from .api import (
        agents_router, system_agents_router, agent_permissions_router,
        conversations_router, messages_router,beta_cat_agents_router,
        xiao_pei_agents_router
    )
    from .config import settings
except ImportError:
    # 回退到绝对导入（直接运行时）
    from services.app_service.api import (
        agents_router, system_agents_router, agent_permissions_router,
        conversations_router, messages_router,beta_cat_agents_router,
        xiao_pei_agents_router
    )
    from services.app_service.config import settings


# 初始化应用服务日志系统（避免重复初始化）
logger = None


def get_logger():
    global logger
    if logger is None:
        logger = setup_app_service_logger()
    return logger


async def check_database_connection():
    """检查数据库连接"""
    logger = get_logger()
    try:
        async with AsyncSessionLocal() as session:
            # 简单的连接测试
            await session.execute(text("SELECT 1"))
        logger.info("数据库连接正常")
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        raise


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用程序生命周期事件"""
    logger = get_logger()

    # 启动阶段
    logger.info("启动应用服务...")
    try:
        # 数据库连接检查
        await check_database_connection()

        # 测试Redis连接
        try:
            await redis_client.set("health_check", "ok", expire=10)
            await redis_client.get("health_check")
        except Exception as e:
            logger.warning(f"Redis连接失败: {e}")

        logger.info("应用服务启动完成")
    except Exception as e:
        logger.error(f"应用服务启动失败: {e}")
        raise

    yield

    # 关闭阶段
    logger.info("关闭应用服务...")
    try:
        await close_db()
        await redis_client.close()
        logger.info("应用服务已关闭")
    except Exception as e:
        logger.error(f"关闭过程中出错: {e}")


# 创建FastAPI应用程序
app = FastAPIOffline(
    title=f"{settings.PROJECT_NAME} - 应用服务",
    version=settings.PROJECT_VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=f"{settings.API_V1_STR}/docs",
    redoc_url=f"{settings.API_V1_STR}/redoc",
    lifespan=lifespan
)

# 添加请求ID中间件
add_request_id_middleware(app)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 简单的Redis连接测试
        await redis_client.set("health_check", "ok", expire=5)
        redis_status = await redis_client.get("health_check")

        return {
            "status": "healthy",
            "service": "app_service",
            "version": settings.PROJECT_VERSION,
            "redis": "connected" if redis_status == "ok" else "disconnected"
        }
    except Exception:
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "service": "app_service",
                "version": settings.PROJECT_VERSION
            }
        )


# 根路径端点
@app.get("/")
async def root():
    """根路径端点"""
    return {
        "message": "应用服务正在运行",
        "service": "app_service",
        "version": settings.PROJECT_VERSION
    }


# 包含路由器

app.include_router(
    agents_router,
    prefix=f"{settings.API_V1_STR}",
    tags=["智能体管理"]
)

app.include_router(
    system_agents_router,
    prefix=f"{settings.API_V1_STR}",
    tags=["系统级智能体"]
)


app.include_router(
    agent_permissions_router,
    prefix=f"{settings.API_V1_STR}",
    tags=["智能体权限管理"]
)

# 会话管理路由
app.include_router(
    conversations_router,
    prefix=f"{settings.API_V1_STR}",
    tags=["会话管理"]
)

# 贝塔猫智能体路由
app.include_router(
    beta_cat_agents_router,
    prefix=f"{settings.API_V1_STR}",
    tags=["贝塔猫智能体"]
)

# 小佩智能体路由
app.include_router(
    xiao_pei_agents_router,
    prefix=f"{settings.API_V1_STR}",
    tags=["小佩智能体"]
)

# 消息管理路由
app.include_router(
    messages_router,
    prefix=f"{settings.API_V1_STR}",
    tags=["消息管理"]
)


# 全局异常处理器
@app.exception_handler(BusinessException)
async def business_exception_handler(request, exc: BusinessException):
    """业务异常处理器"""
    logger.warning(f"业务异常: {exc.message}")
    request_id = get_request_id(request)

    return JSONResponse(
        status_code=exc.status_code,
        content=create_error_response(
            msg=exc.message,
            error_code=exc.error_code,
            error_details=exc.details,
            request_id=request_id
        ).model_dump()
    )


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {exc}")
    request_id = get_request_id(request)

    return JSONResponse(
        status_code=500,
        content=create_error_response(
            msg="内部服务器错误",
            error_code=ErrorCode.INTERNAL_ERROR,
            request_id=request_id
        ).model_dump()
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8003,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
