"""
会话上下文管理API接口
"""
from typing import List, Optional, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from shared.database.session import get_db
from shared.auth.dependencies import get_current_user
from shared.models.response import ApiResponse
from shared.logging_config import get_logger

from ..schemas.conversation import (
    ConversationContextCreate, ConversationContextUpdate, ConversationContextResponse,
    ConversationContextListResponse
)
from ..services.conversation_context_service import ConversationContextService

logger = get_logger(__name__)

router = APIRouter(
    prefix="/conversations/{conversation_id}/context", tags=["会话上下文管理"])


@router.post("", response_model=ApiResponse[ConversationContextResponse])
async def create_context(
    conversation_id: int,
    context_data: ConversationContextCreate,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """创建或更新上下文"""
    try:
        user_id = current_user["user_id"]
        tenant_id = current_user.get("tenant_id")

        service = ConversationContextService(db, tenant_id)
        context = await service.create_context(conversation_id, context_data, user_id)

        return ApiResponse.success(
            data=ConversationContextResponse.from_orm(context),
            message="上下文创建成功"
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"创建上下文失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建上下文失败: {str(e)}"
        )


@router.get("", response_model=ApiResponse[ConversationContextListResponse])
async def get_contexts(
    conversation_id: int,
    context_type: Optional[str] = Query(None, description="上下文类型"),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取会话上下文列表"""
    try:
        user_id = current_user["user_id"]
        tenant_id = current_user.get("tenant_id")

        service = ConversationContextService(db, tenant_id)
        contexts = await service.get_conversation_contexts(
            conversation_id, user_id, context_type
        )

        # 转换为响应格式
        context_responses = [
            ConversationContextResponse.from_orm(ctx) for ctx in contexts
        ]

        response_data = ConversationContextListResponse(
            items=context_responses,
            total=len(context_responses)
        )

        return ApiResponse.success(
            data=response_data,
            message="获取上下文列表成功"
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"获取上下文列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取上下文列表失败: {str(e)}"
        )


@router.get("/{context_id}", response_model=ApiResponse[ConversationContextResponse])
async def get_context(
    conversation_id: int,
    context_id: int,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取上下文详情"""
    try:
        user_id = current_user["user_id"]
        tenant_id = current_user.get("tenant_id")

        service = ConversationContextService(db, tenant_id)
        context = await service.get_context(context_id, user_id)

        if not context:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="上下文不存在或无权限访问"
            )

        # 验证上下文属于指定会话
        if context.conversation_id != conversation_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="上下文不属于指定会话"
            )

        return ApiResponse.success(
            data=ConversationContextResponse.from_orm(context),
            message="获取上下文详情成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取上下文详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取上下文详情失败: {str(e)}"
        )


@router.put("/{context_id}", response_model=ApiResponse[ConversationContextResponse])
async def update_context(
    conversation_id: int,
    context_id: int,
    context_data: ConversationContextUpdate,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新上下文"""
    try:
        user_id = current_user["user_id"]
        tenant_id = current_user.get("tenant_id")

        service = ConversationContextService(db, tenant_id)
        context = await service.update_context(context_id, context_data, user_id)

        if not context:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="上下文不存在或无权限访问"
            )

        # 验证上下文属于指定会话
        if context.conversation_id != conversation_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="上下文不属于指定会话"
            )

        return ApiResponse.success(
            data=ConversationContextResponse.from_orm(context),
            message="上下文更新成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新上下文失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新上下文失败: {str(e)}"
        )


@router.delete("/{context_id}", response_model=ApiResponse[dict])
async def delete_context(
    conversation_id: int,
    context_id: int,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """删除上下文"""
    try:
        user_id = current_user["user_id"]
        tenant_id = current_user.get("tenant_id")

        service = ConversationContextService(db, tenant_id)

        # 先获取上下文验证权限和会话归属
        context = await service.get_context(context_id, user_id)
        if not context:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="上下文不存在或无权限访问"
            )

        if context.conversation_id != conversation_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="上下文不属于指定会话"
            )

        success = await service.delete_context(context_id, user_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="上下文不存在或无权限访问"
            )

        return ApiResponse.success(
            data={"deleted": True},
            message="上下文删除成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除上下文失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除上下文失败: {str(e)}"
        )


@router.post("/clear", response_model=ApiResponse[dict])
async def clear_contexts(
    conversation_id: int,
    context_type: Optional[str] = Query(None, description="上下文类型"),
    keep_persistent: bool = Query(True, description="保留持久化上下文"),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """清空会话上下文"""
    try:
        user_id = current_user["user_id"]
        tenant_id = current_user.get("tenant_id")

        service = ConversationContextService(db, tenant_id)
        deleted_count = await service.clear_conversation_contexts(
            conversation_id, user_id, context_type, keep_persistent
        )

        return ApiResponse.success(
            data={"deleted_count": deleted_count},
            message=f"清空上下文成功，删除了 {deleted_count} 个上下文"
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"清空上下文失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清空上下文失败: {str(e)}"
        )


@router.get("/summary", response_model=ApiResponse[Dict[str, Any]])
async def get_context_summary(
    conversation_id: int,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取会话上下文摘要"""
    try:
        user_id = current_user["user_id"]
        tenant_id = current_user.get("tenant_id")

        service = ConversationContextService(db, tenant_id)
        summary = await service.get_context_summary(conversation_id, user_id)

        return ApiResponse.success(
            data=summary,
            message="获取上下文摘要成功"
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"获取上下文摘要失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取上下文摘要失败: {str(e)}"
        )
