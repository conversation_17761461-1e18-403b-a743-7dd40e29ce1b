"""
智能体管理API路由
"""
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession

from shared.database import get_async_db
from shared.utils.auth import get_current_user_optional
from shared.logging_config import get_app_service_logger
from shared.models.response import (
    SuccessResponse, PaginatedResponse, create_success_response,
    create_paginated_response, ErrorCode, SuccessMessage
)
from shared.middleware.request_id import get_request_id
from shared.utils.exception_handlers import BusinessException
from ..services.agent_service import AgentService
from ..schemas.agent import (
    AgentCreate, AgentUpdate, AgentResponse, AgentListResponse,
    AgentQueryParams, AgentStatsResponse, AgentExecutionRequest, AgentExecutionResponse
)
from ..models.agent import AgentType, AgentStatus, SystemAgentCategory

logger = get_app_service_logger()
router = APIRouter(prefix="/agents", tags=["智能体管理"])


@router.post("/", response_model=SuccessResponse[AgentResponse], status_code=status.HTTP_201_CREATED)
async def create_agent(
    request: Request,
    agent_data: AgentCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """
    创建智能体

    - **系统级智能体**: 只有管理员可以创建
    - **用户自定义智能体**: 登录用户可以创建
    """
    try:
        # 检查权限
        if agent_data.agent_type == AgentType.SYSTEM:
            if not current_user or not getattr(current_user, 'is_superuser', False):
                raise BusinessException(
                    message="只有管理员可以创建系统级智能体",
                    error_code=ErrorCode.FORBIDDEN,
                    status_code=status.HTTP_403_FORBIDDEN
                )
            owner_id = None  # 系统智能体没有所有者
        else:
            if not current_user:
                raise BusinessException(
                    message="需要登录才能创建自定义智能体",
                    error_code=ErrorCode.UNAUTHORIZED,
                    status_code=status.HTTP_401_UNAUTHORIZED
                )
            owner_id = current_user.get("id")

        # 创建智能体
        agent_service = AgentService(db)
        agent = await agent_service.create_agent(agent_data, owner_id)

        logger.info(f"创建智能体成功: {agent.agent_name} (用户: {owner_id})")

        # 获取请求ID
        request_id = get_request_id(request)

        # 转换为响应模式
        agent_response = AgentResponse.from_orm(agent)

        # 返回统一成功响应格式
        return create_success_response(
            data=agent_response,
            msg=SuccessMessage.CREATED,
            request_id=request_id
        )

    except BusinessException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建智能体失败: {e}")
        raise BusinessException(
            message="创建智能体失败",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/", response_model=PaginatedResponse[AgentResponse])
async def list_agents(
    request: Request,
    agent_type: Optional[AgentType] = Query(None, description="智能体类型"),
    system_category: Optional[SystemAgentCategory] = Query(
        None, description="系统智能体分类"),
    status_filter: Optional[AgentStatus] = Query(
        None, alias="status", description="状态"),
    is_public: Optional[bool] = Query(None, description="是否公开"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """
    获取智能体列表

    支持按类型、状态、公开性等条件筛选和搜索
    """
    try:
        # 构建查询参数
        query_params = AgentQueryParams(
            agent_type=agent_type,
            system_category=system_category,
            status=status_filter,
            is_public=is_public,
            search=search,
            page=page,
            page_size=page_size
        )

        # 获取智能体列表
        agent_service = AgentService(db)
        user_id = current_user.get("id") if current_user else None
        agents, total = await agent_service.list_agents(query_params, user_id)

        # 转换为响应模式
        agent_responses = [AgentResponse.from_orm(agent) for agent in agents]

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一分页响应格式
        return create_paginated_response(
            items=agent_responses,
            total=total,
            page=page,
            size=page_size,
            msg=SuccessMessage.RETRIEVED,
            request_id=request_id
        )

    except Exception as e:
        logger.error(f"获取智能体列表失败: {e}")
        raise BusinessException(
            message="获取智能体列表失败",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/{agent_id}", response_model=SuccessResponse[AgentResponse])
async def get_agent(
    request: Request,
    agent_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """
    获取智能体详情
    """
    try:
        agent_service = AgentService(db)
        user_id = current_user.get('id') if isinstance(
            current_user, dict) else (current_user.id if current_user else None)
        agent = await agent_service.get_agent(agent_id, user_id)

        if not agent:
            raise BusinessException(
                message="智能体不存在或无权限访问",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        # 获取请求ID
        request_id = get_request_id(request)

        # 转换为响应模式
        agent_response = AgentResponse.from_orm(agent)

        # 返回统一成功响应格式
        return create_success_response(
            data=agent_response,
            msg=SuccessMessage.RETRIEVED,
            request_id=request_id
        )

    except BusinessException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取智能体详情失败: {e}")
        raise BusinessException(
            message="获取智能体详情失败",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.put("/{agent_id}", response_model=SuccessResponse[AgentResponse])
async def update_agent(
    request: Request,
    agent_id: int,
    agent_data: AgentUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """
    更新智能体

    只有智能体的所有者或管理员可以更新
    """
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能更新智能体",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        agent_service = AgentService(db)
        agent = await agent_service.update_agent(agent_id, agent_data, current_user.get('id'))

        if not agent:
            raise BusinessException(
                message="智能体不存在或无权限修改",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        logger.info(
            f"更新智能体成功: {agent.agent_name} (用户: {current_user.get('id')})")

        # 获取请求ID
        request_id = get_request_id(request)

        # 转换为响应模式
        agent_response = AgentResponse.from_orm(agent)

        # 返回统一成功响应格式
        return create_success_response(
            data=agent_response,
            msg=SuccessMessage.UPDATED,
            request_id=request_id
        )

    except BusinessException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新智能体失败: {e}")
        raise BusinessException(
            message="更新智能体失败",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.delete("/{agent_id}", response_model=SuccessResponse[None])
async def delete_agent(
    request: Request,
    agent_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """
    删除智能体（软删除）

    只有智能体的所有者或管理员可以删除
    系统级智能体不能删除
    """
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能删除智能体",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        agent_service = AgentService(db)
        success = await agent_service.delete_agent(agent_id, current_user.get("id"))

        if not success:
            raise BusinessException(
                message="智能体不存在、无权限删除或不允许删除",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        logger.info(f"删除智能体成功: ID {agent_id} (用户: {current_user.get('id')})")

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一成功响应格式
        return create_success_response(
            data=None,
            msg=SuccessMessage.DELETED,
            request_id=request_id
        )

    except BusinessException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除智能体失败: {e}")
        raise BusinessException(
            message="删除智能体失败",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/{agent_id}/execute")
async def execute_agent(
    request: Request,
    agent_id: int,
    request_data: AgentExecutionRequest,
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """
    执行智能体 - 支持流式和非流式响应

    调用智能体处理用户输入并返回结果
    """
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能执行智能体",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        agent_service = AgentService(db)
        user_id = current_user.get('id') if isinstance(
            current_user, dict) else current_user.id

        # 检查是否请求流式响应
        if request_data.stream:
            # 返回流式响应 - 包装生成器以添加结束事件
            from fastapi.responses import StreamingResponse

            async def stream_with_end():
                """包装流式生成器，确保在最后发送结束事件"""
                try:
                    async for chunk in agent_service.execute_agent_stream(agent_id, request_data, user_id):
                        yield chunk
                    # 发送标准的SSE结束事件
                    yield "data: [DONE]\n\n"
                except Exception as e:
                    logger.error(f"流式执行智能体失败: {e}")
                    # 发送错误事件
                    import json
                    error_event = {
                        "event": "error",
                        "agent_id": agent_id,
                        "error": str(e)
                    }
                    yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"
                    # 即使出现错误也要发送结束事件
                    yield "data: [DONE]\n\n"

            return StreamingResponse(
                stream_with_end(),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Headers": "*",
                }
            )
        else:
            # 返回普通响应 - 使用统一格式
            result = await agent_service.execute_agent(agent_id, request_data, user_id)
            logger.info(f"执行智能体成功: ID {agent_id} (用户: {user_id})")

            # 获取请求ID
            request_id = get_request_id(request)

            # 返回统一成功响应格式
            return create_success_response(
                data=result,
                msg="智能体执行成功",
                request_id=request_id
            )

    except BusinessException:
        raise
    except HTTPException:
        raise
    except ValueError as e:
        raise BusinessException(
            message=str(e),
            error_code=ErrorCode.INVALID_REQUEST,
            status_code=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        logger.error(f"执行智能体失败: {e}")
        raise BusinessException(
            message="执行智能体失败",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/stats/overview", response_model=SuccessResponse[AgentStatsResponse])
async def get_agent_stats(
    request: Request,
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """
    获取智能体统计信息

    包括总数、分类统计、使用情况等
    """
    try:
        # 这里可以实现统计逻辑
        # 暂时返回模拟数据
        stats = AgentStatsResponse(
            total_agents=0,
            system_agents=0,
            custom_agents=0,
            active_agents=0,
            total_executions=0,
            executions_today=0,
            popular_agents=[]
        )

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一成功响应格式
        return create_success_response(
            data=stats,
            msg=SuccessMessage.RETRIEVED,
            request_id=request_id
        )

    except Exception as e:
        logger.error(f"获取智能体统计失败: {e}")
        raise BusinessException(
            message="获取统计信息失败",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
