"""
智能体权限管理API路由
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession

from shared.database import get_async_db
from shared.utils.auth import get_current_user_optional
from shared.logging_config import get_app_service_logger
from shared.models.response import (
    SuccessResponse, create_success_response, ErrorCode, SuccessMessage
)
from shared.middleware.request_id import get_request_id
from shared.utils.exception_handlers import BusinessException
from ..services.agent_permission_service import AgentPermissionService
from ..schemas.agent import (
    AgentPermissionCreate, AgentPermissionResponse
)

logger = get_app_service_logger()
router = APIRouter(prefix="/agents/{agent_id}/permissions", tags=["智能体权限管理"])


@router.post("/", response_model=SuccessResponse[AgentPermissionResponse], status_code=status.HTTP_201_CREATED)
async def create_agent_permission(
    request: Request,
    agent_id: int,
    permission_data: AgentPermission<PERSON>reate,
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """
    创建智能体权限

    只有智能体的所有者可以分配权限
    """
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能分配权限",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        permission_service = AgentPermissionService(db)
        permission = await permission_service.create_permission(
            agent_id, permission_data, current_user.get("id")
        )

        if not permission:
            raise BusinessException(
                message="智能体不存在或无权限操作",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        logger.info(
            f"创建智能体权限成功: Agent {agent_id}, User {permission_data.user_id}")

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一成功响应格式
        return create_success_response(
            data=permission,
            msg=SuccessMessage.CREATED,
            request_id=request_id
        )

    except BusinessException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建智能体权限失败: {e}")
        raise BusinessException(
            message="创建权限失败",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/", response_model=SuccessResponse[List[AgentPermissionResponse]])
async def list_agent_permissions(
    request: Request,
    agent_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """
    获取智能体权限列表

    只有智能体的所有者可以查看权限列表
    """
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能查看权限",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        permission_service = AgentPermissionService(db)
        permissions = await permission_service.list_permissions(agent_id, current_user.get('id'))

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一成功响应格式
        return create_success_response(
            data=permissions,
            msg=SuccessMessage.RETRIEVED,
            request_id=request_id
        )

    except BusinessException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取智能体权限列表失败: {e}")
        raise BusinessException(
            message="获取权限列表失败",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/{permission_id}", response_model=SuccessResponse[AgentPermissionResponse])
async def get_agent_permission(
    request: Request,
    agent_id: int,
    permission_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """
    获取智能体权限详情
    """
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能查看权限详情",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        permission_service = AgentPermissionService(db)
        user_id = current_user.get('id') if isinstance(
            current_user, dict) else current_user.id
        permission = await permission_service.get_permission(agent_id, permission_id, user_id)

        if not permission:
            raise BusinessException(
                message="权限不存在或无权限访问",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一成功响应格式
        return create_success_response(
            data=permission,
            msg=SuccessMessage.RETRIEVED,
            request_id=request_id
        )

    except BusinessException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取智能体权限详情失败: {e}")
        raise BusinessException(
            message="获取权限详情失败",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.put("/{permission_id}/toggle", response_model=SuccessResponse[AgentPermissionResponse])
async def toggle_agent_permission(
    request: Request,
    agent_id: int,
    permission_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """
    切换智能体权限状态（启用/禁用）
    """
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能修改权限",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        permission_service = AgentPermissionService(db)
        permission = await permission_service.toggle_permission(agent_id, permission_id, current_user.get('id'))

        if not permission:
            raise BusinessException(
                message="权限不存在或无权限操作",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        logger.info(
            f"切换智能体权限状态成功: Agent {agent_id}, Permission {permission_id}")

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一成功响应格式
        return create_success_response(
            data=permission,
            msg=SuccessMessage.UPDATED,
            request_id=request_id
        )

    except BusinessException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"切换智能体权限状态失败: {e}")
        raise BusinessException(
            message="修改权限状态失败",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.delete("/{permission_id}", response_model=SuccessResponse[None])
async def delete_agent_permission(
    request: Request,
    agent_id: int,
    permission_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """
    删除智能体权限
    """
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能删除权限",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        permission_service = AgentPermissionService(db)
        success = await permission_service.delete_permission(agent_id, permission_id, current_user.get("id"))

        if not success:
            raise BusinessException(
                message="权限不存在或无权限删除",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        logger.info(f"删除智能体权限成功: Agent {agent_id}, Permission {permission_id}")

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一成功响应格式
        return create_success_response(
            data=None,
            msg=SuccessMessage.DELETED,
            request_id=request_id
        )

    except BusinessException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除智能体权限失败: {e}")
        raise BusinessException(
            message="删除权限失败",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
