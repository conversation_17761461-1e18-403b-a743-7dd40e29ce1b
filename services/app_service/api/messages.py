"""
消息管理API接口 - 简化版本
"""
from typing import List, Optional
from math import ceil
import json
import asyncio
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Query, status, Request
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession

from shared.database import get_async_db
from shared.utils.auth import get_current_user_optional
from shared.logging_config import get_app_service_logger
from shared.models.response import (
    SuccessResponse, PaginatedResponse, create_success_response,
    create_paginated_response, ErrorCode, SuccessMessage
)
from shared.middleware.request_id import get_request_id
from shared.utils.exception_handlers import BusinessException

from ..schemas.conversation import (
    MessageCreate, MessageUpdate, MessageResponse,
    MessageListResponse, MessageQueryParams
)
from ..services.conversation_service import ConversationService
from ..services.message_service import MessageService

logger = get_app_service_logger()

router = APIRouter(
    prefix="/conversations/{conversation_id}/messages", tags=["消息管理"])


@router.post("", response_model=SuccessResponse[MessageResponse], status_code=status.HTTP_201_CREATED)
async def create_message(
    request: Request,
    conversation_id: int,
    message_data: MessageCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """发送消息"""
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能发送消息",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        user_id = current_user.get('id') or current_user.get('user_id', 1)
        tenant_id = current_user.get('tenant_id')

        service = MessageService(db, tenant_id)
        message = await service.create_message(
            conversation_id, message_data, user_id, 1  # MessageRole.USER = 1
        )

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一成功响应格式
        return create_success_response(
            data=MessageResponse.model_validate(message),
            msg=SuccessMessage.CREATED,
            request_id=request_id
        )

    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"发送消息失败: {e}")
        raise BusinessException(
            message=f"发送消息失败: {str(e)}",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/stream", response_class=StreamingResponse)
async def create_message_stream(
    conversation_id: int,
    message_data: MessageCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """发送消息并流式返回AI回复"""
    try:
        if not current_user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="需要登录才能发送消息"
            )

        user_id = current_user.get('id') or current_user.get('user_id', 1)
        tenant_id = current_user.get('tenant_id')

        # 创建用户消息
        service = MessageService(db, tenant_id)
        user_message = await service.create_message(
            conversation_id, message_data, user_id, 1  # MessageRole.USER = 1
        )

        # 生成流式响应
        async def generate_ai_response():
            try:
                # 发送用户消息事件
                user_msg_dict = {
                    'id': user_message.id,
                    'conversation_id': user_message.conversation_id,
                    'content': user_message.content,
                    'role': user_message.role,
                    'message_type': user_message.message_type,
                    'status': user_message.status,
                    'sequence_number': user_message.sequence_number,
                    'created_at': user_message.created_at.isoformat() if user_message.created_at else None,
                    'metadata': user_message.message_metadata
                }
                yield f"data: {json.dumps({'type': 'user_message', 'data': user_msg_dict})}\n\n"

                # 开始AI处理
                yield f"data: {json.dumps({'type': 'ai_start', 'data': {'message': 'AI正在思考中...'}})}\n\n"

                # 模拟AI流式回复（这里需要集成实际的AI服务）
                ai_response_content = ""
                ai_chunks = [
                    "根据您描述的症状，",
                    "您的宠物可能出现了消化系统问题。",
                    "建议您：\n1. 观察宠物的精神状态\n",
                    "2. 检查是否有其他症状\n",
                    "3. 如症状持续，请及时就医"
                ]

                for chunk in ai_chunks:
                    ai_response_content += chunk
                    yield f"data: {json.dumps({'type': 'ai_chunk', 'data': {'content': chunk, 'full_content': ai_response_content}})}\n\n"
                    await asyncio.sleep(0.5)  # 模拟AI处理时间

                # 创建AI回复消息
                ai_message_data = MessageCreate(
                    content=ai_response_content,
                    message_type=1,  # text
                    metadata={"ai_generated": True, "model": "qwen-plus"}
                )

                ai_message = await service.create_message(
                    conversation_id, ai_message_data, None, 2  # MessageRole.ASSISTANT = 2
                )

                # 发送完成事件
                ai_msg_dict = {
                    'id': ai_message.id,
                    'conversation_id': ai_message.conversation_id,
                    'content': ai_message.content,
                    'role': ai_message.role,
                    'message_type': ai_message.message_type,
                    'status': ai_message.status,
                    'sequence_number': ai_message.sequence_number,
                    'created_at': ai_message.created_at.isoformat() if ai_message.created_at else None,
                    'metadata': ai_message.message_metadata
                }
                yield f"data: {json.dumps({'type': 'ai_complete', 'data': ai_msg_dict})}\n\n"
                yield f"data: {json.dumps({'type': 'done'})}\n\n"

            except Exception as e:
                logger.error(f"AI回复生成失败: {e}")
                yield f"data: {json.dumps({'type': 'error', 'data': {'message': f'AI回复失败: {str(e)}'}})}\n\n"

        return StreamingResponse(
            generate_ai_response(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"流式消息发送失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"流式消息发送失败: {str(e)}"
        )


@router.get("", response_model=PaginatedResponse[MessageResponse])
async def get_messages(
    request: Request,
    conversation_id: str,
    role: Optional[int] = Query(
        None, description="消息角色: 1=user, 2=assistant, 3=system"),
    message_type: Optional[int] = Query(
        None, description="消息类型: 1=text, 2=image, 3=file"),
    status_filter: Optional[int] = Query(
        None, alias="status", description="消息状态"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    order_by: str = Query("sequence_number", description="排序字段"),
    order_desc: bool = Query(False, description="是否降序"),
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """获取消息列表"""
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能查看消息",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        user_id = current_user.get('id') or current_user.get('user_id', 1)
        tenant_id = current_user.get('tenant_id')

        # 构建查询参数
        params = MessageQueryParams(
            role=role,
            message_type=message_type,
            status=status_filter,
            page=page,
            size=size,
            order_by=order_by,
            order_desc=order_desc
        )

        conversation_service = ConversationService(db, tenant_id)
        conversation = await conversation_service.get_by_conversation_id(conversation_id, user_id)

        if not conversation:
            raise BusinessException(
                message="会话不存在或无权限访问",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        service = MessageService(db, tenant_id)
        messages, total = await service.get_messages(conversation.id, user_id, params)

        # 转换为响应格式
        message_responses = [
            MessageResponse.model_validate(msg.to_dict()) for msg in messages
        ]

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一分页响应格式
        return create_paginated_response(
            items=message_responses,
            total=total,
            page=page,
            size=size,
            msg=SuccessMessage.RETRIEVED,
            request_id=request_id
        )

    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"获取消息列表失败: {e}")
        raise BusinessException(
            message=f"获取消息列表失败: {str(e)}",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/{message_id}", response_model=SuccessResponse[MessageResponse])
async def get_message(
    request: Request,
    conversation_id: int,
    message_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """获取消息详情"""
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能查看消息",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        user_id = current_user.get('id') or current_user.get('user_id', 1)
        tenant_id = current_user.get('tenant_id')

        service = MessageService(db, tenant_id)
        message = await service.get_message(message_id, user_id)

        if not message:
            raise BusinessException(
                message="消息不存在或无权限访问",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        # 验证消息属于指定会话
        if message.conversation_id != conversation_id:
            raise BusinessException(
                message="消息不属于指定会话",
                error_code=ErrorCode.INVALID_REQUEST,
                status_code=status.HTTP_400_BAD_REQUEST
            )

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一成功响应格式
        return create_success_response(
            data=MessageResponse.model_validate(message),
            msg=SuccessMessage.RETRIEVED,
            request_id=request_id
        )

    except BusinessException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取消息详情失败: {e}")
        raise BusinessException(
            message=f"获取消息详情失败: {str(e)}",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.put("/{message_id}", response_model=SuccessResponse[MessageResponse])
async def update_message(
    request: Request,
    conversation_id: int,
    message_id: int,
    message_data: MessageUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """更新消息"""
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能更新消息",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        user_id = current_user.get('id') or current_user.get('user_id', 1)
        tenant_id = current_user.get('tenant_id')

        service = MessageService(db, tenant_id)
        message = await service.update_message(message_id, message_data, user_id)

        if not message:
            raise BusinessException(
                message="消息不存在或无权限访问",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        # 验证消息属于指定会话
        if message.conversation_id != conversation_id:
            raise BusinessException(
                message="消息不属于指定会话",
                error_code=ErrorCode.INVALID_REQUEST,
                status_code=status.HTTP_400_BAD_REQUEST
            )

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一成功响应格式
        return create_success_response(
            data=MessageResponse.model_validate(message),
            msg=SuccessMessage.UPDATED,
            request_id=request_id
        )

    except BusinessException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新消息失败: {e}")
        raise BusinessException(
            message=f"更新消息失败: {str(e)}",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.delete("/{message_id}", response_model=SuccessResponse[None])
async def delete_message(
    request: Request,
    conversation_id: int,
    message_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """删除消息"""
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能删除消息",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        user_id = current_user.get('id') or current_user.get('user_id', 1)
        tenant_id = current_user.get('tenant_id')

        service = MessageService(db, tenant_id)

        # 先获取消息验证权限和会话归属
        message = await service.get_message(message_id, user_id)
        if not message:
            raise BusinessException(
                message="消息不存在或无权限访问",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        if message.conversation_id != conversation_id:
            raise BusinessException(
                message="消息不属于指定会话",
                error_code=ErrorCode.INVALID_REQUEST,
                status_code=status.HTTP_400_BAD_REQUEST
            )

        success = await service.delete_message(message_id, user_id)

        if not success:
            raise BusinessException(
                message="消息不存在或无权限访问",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一成功响应格式
        return create_success_response(
            data=None,
            msg=SuccessMessage.DELETED,
            request_id=request_id
        )

    except BusinessException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除消息失败: {e}")
        raise BusinessException(
            message=f"删除消息失败: {str(e)}",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/context/window", response_model=SuccessResponse[List[MessageResponse]])
async def get_context_messages(
    request: Request,
    conversation_id: int,
    window_size: Optional[int] = Query(
        None, ge=1, le=50, description="上下文窗口大小"),
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """获取会话上下文消息"""
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能查看上下文消息",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        user_id = current_user.get('id') or current_user.get('user_id', 1)
        tenant_id = current_user.get('tenant_id')

        service = MessageService(db, tenant_id)
        messages = await service.get_conversation_context_messages(
            conversation_id, user_id, window_size
        )

        # 转换为响应格式
        message_responses = [
            MessageResponse.model_validate(msg) for msg in messages
        ]

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一成功响应格式
        return create_success_response(
            data=message_responses,
            msg=SuccessMessage.RETRIEVED,
            request_id=request_id
        )

    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"获取上下文消息失败: {e}")
        raise BusinessException(
            message=f"获取上下文消息失败: {str(e)}",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
