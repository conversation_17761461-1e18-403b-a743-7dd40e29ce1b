"""
应用服务API路由模块
"""
from .agents import router as agents_router
from .system_agents import router as system_agents_router
from .agent_permissions import router as agent_permissions_router
from .conversations import router as conversations_router
from .messages import router as messages_router
from .beta_cat_agents import router as beta_cat_agents_router
from .xiao_pei_agents import router as xiao_pei_agents_router
# from .conversation_contexts import router as conversation_contexts_router

__all__ = [
    "agents_router",
    "system_agents_router",
    "agent_permissions_router",
    "conversations_router",
    "messages_router",
    "beta_cat_agents_router",
    "xiao_pei_agents_router",
    # "conversation_contexts_router"
]
