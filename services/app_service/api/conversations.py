"""
会话管理API接口 - 简化版本
"""
import uuid
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status, Request
from sqlalchemy.ext.asyncio import AsyncSession

from shared.database import get_async_db
from shared.utils.auth import get_current_user_optional
from shared.logging_config import get_app_service_logger
from shared.models.response import (
    SuccessResponse, PaginatedResponse, create_success_response,
    create_paginated_response, ErrorCode, SuccessMessage
)
from shared.middleware.request_id import get_request_id
from shared.utils.exception_handlers import BusinessException

from ..schemas.conversation import (
    ConversationCreate, ConversationUpdate, ConversationResponse,
    ConversationQueryParams, ConversationStatsResponse
)
from ..services.conversation_service import ConversationService

logger = get_app_service_logger()

router = APIRouter(prefix="/conversations", tags=["会话管理"])


@router.post("/", response_model=SuccessResponse[ConversationResponse], status_code=status.HTTP_201_CREATED)
async def create_conversation(
    request: Request,
    conversation_data: ConversationCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """创建会话"""
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能创建会话",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        user_id = current_user.get('id') or current_user.get(
            'user_id', 1)  # 临时使用默认用户ID
        tenant_id = current_user.get('tenant_id')
        extend = conversation_data.extend

        if extend is not None and "report_id" in extend:
            report_id = extend.get("report_id")
            conversation_id = f"conv_report_{report_id}"
        else:
            conversation_id = f"conv_{uuid.uuid4()}"
        service = ConversationService(db, tenant_id)
        conversation = await service.get_by_conversation_id(conversation_id)
        if conversation is None:
            conversation_data.conversation_id = conversation_id
            conversation = await service.create_conversation(conversation_data, user_id)

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一成功响应格式
        return create_success_response(
            data=ConversationResponse.from_orm(conversation),
            msg=SuccessMessage.CREATED,
            request_id=request_id
        )

    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"创建会话失败: {e}")
        raise BusinessException(
            message=f"创建会话失败: {str(e)}",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/", response_model=PaginatedResponse[ConversationResponse])
async def get_conversations(
    request: Request,
    conversation_type: Optional[str] = Query(None, description="会话类型"),
    status_filter: Optional[str] = Query(
        None, alias="status", description="会话状态"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    order_by: str = Query("created_at", description="排序字段"),
    order_desc: bool = Query(True, description="是否降序"),
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """获取会话列表"""
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能查看会话",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        user_id = current_user.get('id') or current_user.get('user_id', 1)
        tenant_id = current_user.get('tenant_id')

        # 构建查询参数
        params = ConversationQueryParams(
            conversation_type=conversation_type,
            status=status_filter,
            page=page,
            size=size,
            order_by=order_by,
            order_desc=order_desc
        )

        service = ConversationService(db, tenant_id)
        conversations, total = await service.get_conversations(user_id, params)

        # 转换为响应格式
        conversation_responses = [
            ConversationResponse.from_orm(conv) for conv in conversations
        ]

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一分页响应格式
        return create_paginated_response(
            items=conversation_responses,
            total=total,
            page=page,
            size=size,
            msg=SuccessMessage.RETRIEVED,
            request_id=request_id
        )

    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"获取会话列表失败: {e}")
        raise BusinessException(
            message=f"获取会话列表失败: {str(e)}",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/{conversation_id}", response_model=SuccessResponse[ConversationResponse])
async def get_conversation(
    request: Request,
    conversation_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """获取会话详情"""
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能查看会话",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        user_id = current_user.get('id') or current_user.get('user_id', 1)
        tenant_id = current_user.get('tenant_id')

        service = ConversationService(db, tenant_id)
        conversation = await service.get_conversation(conversation_id, user_id)

        if not conversation:
            raise BusinessException(
                message="会话不存在或无权限访问",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一成功响应格式
        return create_success_response(
            data=ConversationResponse.from_orm(conversation),
            msg=SuccessMessage.RETRIEVED,
            request_id=request_id
        )

    except BusinessException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取会话详情失败: {e}")
        raise BusinessException(
            message=f"获取会话详情失败: {str(e)}",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.put("/{conversation_id}", response_model=SuccessResponse[ConversationResponse])
async def update_conversation(
    request: Request,
    conversation_id: int,
    conversation_data: ConversationUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """更新会话"""
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能更新会话",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        user_id = current_user.get('id') or current_user.get('user_id', 1)
        tenant_id = current_user.get('tenant_id')

        service = ConversationService(db, tenant_id)
        conversation = await service.update_conversation(
            conversation_id, conversation_data, user_id
        )

        if not conversation:
            raise BusinessException(
                message="会话不存在或无权限访问",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一成功响应格式
        return create_success_response(
            data=ConversationResponse.from_orm(conversation),
            msg=SuccessMessage.UPDATED,
            request_id=request_id
        )

    except BusinessException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新会话失败: {e}")
        raise BusinessException(
            message=f"更新会话失败: {str(e)}",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.delete("/{conversation_id}", response_model=SuccessResponse[None])
async def delete_conversation(
    request: Request,
    conversation_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """删除会话"""
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能删除会话",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        user_id = current_user.get('id') or current_user.get('user_id', 1)
        tenant_id = current_user.get('tenant_id')

        service = ConversationService(db, tenant_id)
        success = await service.delete_conversation(conversation_id, user_id)

        if not success:
            raise BusinessException(
                message="会话不存在或无权限访问",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一成功响应格式
        return create_success_response(
            data=None,
            msg=SuccessMessage.DELETED,
            request_id=request_id
        )

    except BusinessException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除会话失败: {e}")
        raise BusinessException(
            message=f"删除会话失败: {str(e)}",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/stats/summary", response_model=SuccessResponse[ConversationStatsResponse])
async def get_conversation_stats(
    request: Request,
    db: AsyncSession = Depends(get_async_db),
    current_user=Depends(get_current_user_optional)
):
    """获取会话统计信息"""
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能查看统计信息",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        user_id = current_user.get('id') or current_user.get('user_id', 1)
        tenant_id = current_user.get('tenant_id')

        service = ConversationService(db, tenant_id)
        stats = await service.get_conversation_stats(user_id)

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一成功响应格式
        return create_success_response(
            data=stats,
            msg=SuccessMessage.RETRIEVED,
            request_id=request_id
        )

    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"获取会话统计失败: {e}")
        raise BusinessException(
            message=f"获取会话统计失败: {str(e)}",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
