"""
贝塔猫智能体专用API路由
"""

import json
import uuid
from datetime import datetime, timezone
from typing import AsyncGenerator

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession

from shared.database import get_async_db
from shared.logging_config import get_app_service_logger
from shared.models.response import (
    ErrorCode
)
from shared.utils.auth import get_current_user_optional
from shared.utils.exception_handlers import BusinessException
from shared.utils.pet import save_or_update_by_tenant_pet_id
from ..models.agent import SystemAgentCategory
from ..models.conversation import MessageRole, MessageType, ConversationType
from ..prompts.bata_cat_prompts import BetaCatPrompts
from ..schemas.agent import (
    AgentExecutionRequest
)
from ..schemas.beta_cat_agent import BetaCatCdssAgentInput, BetaCatCdssAgentOutput, BetaCatLabAgentInput, \
    BetaCatPetOtherInfo, BetaCatPetInfo
from ..schemas.conversation import ConversationCreate, MessageCreate
from ..services.agent_service import AgentService
from ..services.conversation_service import ConversationService
from ..services.message_service import MessageService

logger = get_app_service_logger()
router = APIRouter(prefix="/beta-cat-agents", tags=["贝塔猫智能体"])


@router.post("/cdss")
async def cdss(
        input_data: BetaCatCdssAgentInput,
        db: AsyncSession = Depends(get_async_db),
        current_user=Depends(get_current_user_optional)
):
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能使用AI问诊服务",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        # 获取用户ID和租户ID
        user_id = current_user.get('id') if isinstance(
            current_user, dict) else current_user.id
        tenant_id = current_user.get('tenant_id')

        # 获取AI问诊智能体
        agent_service = AgentService(db)
        agent = await agent_service._get_system_agent(SystemAgentCategory.DIAGNOSIS)

        if not agent:
            raise BusinessException(
                message="AI问诊智能体未找到",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        # 初始化服务
        conversation_service = ConversationService(db, tenant_id)
        message_service = MessageService(db, tenant_id)
        bata_cat_prompts = BetaCatPrompts()

        # 创建新会话
        conversation_data = ConversationCreate(
            title="贝塔猫Cdss辅助诊断",
            conversation_type=ConversationType.DIAGNOSIS,
            primary_agent_id=agent.id,
            conversation_id=f"diagnosis_temp_{str(uuid.uuid4())}",
            context_window_size=20,
            max_messages=100
        )
        conversation = await conversation_service.create_conversation(conversation_data, user_id)
        logger.info(f"创建新的AI问诊会话: {conversation.conversation_id}")

        # 提取用户输入内容用于消息记录
        user_content = _extract_user_content(input_data.model_dump())

        # 创建用户消息记录
        user_message_data = MessageCreate(
            content=user_content,
            message_type=MessageType.TEXT,
            metadata={"input_format": _detect_input_format(input_data.model_dump())}
        )
        user_message = await message_service.create_message(
            conversation.id, user_message_data, user_id, MessageRole.USER
        )
        logger.info(f"创建用户消息记录: {user_message.id}")

        # 构建执行请求数据
        request_data = dict(input_data)
        request_data.pop('stream', None)
        request_data.pop('conversation_id', None)

        # 添加会话上下文信息用于短期记忆（不包含数据库会话，避免序列化问题）
        request_data['_conversation_context'] = {
            'conversation_id': conversation.id,
            'conversation_string_id': conversation.conversation_id,
            'user_id': user_id,
            'tenant_id': tenant_id
        }
        request_data['pet_info'] = dict(request_data['pet_info'])
        request_data['pet_other_info'] = dict(request_data['pet_other_info'])

        request_data['messages'] = [
            {
                "role": "user",
                "content": bata_cat_prompts.cdss_prompt(input_data.pet_info, input_data.pet_other_info)
            },
        ]

        # 构建执行请求
        execution_request = AgentExecutionRequest(
            input_data=request_data,
            stream=input_data.stream,
            enable_short_term_memory=False
        )

        # 保存宠物信息
        await _save_or_update_pet_info(current_user.get('id'), input_data.pet_info, input_data.pet_other_info)

        if input_data.stream:
            # 流式响应
            return await _handle_stream_diagnosis_with_messages(
                agent, execution_request, user_id, agent_service,
                conversation, message_service, msg_type=2
            )
        else:
            # 非流式响应
            result = await agent_service.execute_agent(agent.id, execution_request, user_id)

            # 创建助手消息记录
            if result and result.output_data:
                assistant_content = result.output_data.get('content', '处理完成')
                token_usage = result.output_data.get('token_usage')
                processing_time = result.duration_ms

                assistant_message = await message_service.create_assistant_message(
                    conversation.id,
                    assistant_content,
                    user_id,
                    agent.id,
                    metadata=result.output_data.get('metadata'),
                    token_usage=token_usage,
                    processing_time_ms=processing_time
                )
                logger.info(f"创建助手消息记录: {assistant_message.id}")

            logger.info(
                f"AI问诊执行成功 (用户: {user_id}, 会话: {conversation.conversation_id})")

            response_data = BetaCatCdssAgentOutput(
                request_id=input_data.request_id,
                content=result.output_data.get('content'),
                pushTime=result.duration_ms,
                msgType=2
            ).model_dump()
            # 返回统一成功响应格式
            return response_data

    except BusinessException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"AI问诊执行失败: {e}")
        raise BusinessException(
            message="AI问诊服务执行失败",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/lab")
async def lab(
        input_data: BetaCatLabAgentInput,
        db: AsyncSession = Depends(get_async_db),
        current_user=Depends(get_current_user_optional)
):
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能使用AI问诊服务",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        # 获取用户ID和租户ID
        user_id = current_user.get('id') if isinstance(
            current_user, dict) else current_user.id
        tenant_id = current_user.get('tenant_id')

        # 获取AI问诊智能体
        agent_service = AgentService(db)
        agent = await agent_service._get_system_agent(SystemAgentCategory.DIAGNOSIS)

        if not agent:
            raise BusinessException(
                message="AI问诊智能体未找到",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        # 初始化服务
        conversation_service = ConversationService(db, tenant_id)
        message_service = MessageService(db, tenant_id)
        bata_cat_prompts = BetaCatPrompts()

        # 创建新会话
        conversation_data = ConversationCreate(
            title="贝塔猫实验室检查",
            conversation_type=ConversationType.DIAGNOSIS,
            primary_agent_id=agent.id,
            conversation_id=f"diagnosis_temp_{str(uuid.uuid4())}",
            context_window_size=20,
            max_messages=100
        )
        conversation = await conversation_service.create_conversation(conversation_data, user_id)
        logger.info(f"创建新的AI问诊会话: {conversation.conversation_id}")

        # 提取用户输入内容用于消息记录
        user_content = _extract_user_content(input_data.model_dump())

        # 创建用户消息记录
        user_message_data = MessageCreate(
            content=user_content,
            message_type=MessageType.TEXT,
            metadata={"input_format": _detect_input_format(input_data.model_dump())}
        )
        user_message = await message_service.create_message(
            conversation.id, user_message_data, user_id, MessageRole.USER
        )
        logger.info(f"创建用户消息记录: {user_message.id}")

        # 构建执行请求数据
        request_data = dict(input_data)
        request_data.pop('stream', None)
        request_data.pop('conversation_id', None)

        # 添加会话上下文信息用于短期记忆（不包含数据库会话，避免序列化问题）
        request_data['_conversation_context'] = {
            'conversation_id': conversation.id,
            'conversation_string_id': conversation.conversation_id,
            'user_id': user_id,
            'tenant_id': tenant_id
        }
        request_data['pet_info'] = dict(request_data['pet_info'])
        request_data['item_values'] = [item.model_dump() for item in request_data['item_values']]

        request_data['messages'] = [
            {
                "role": "user",
                "content": bata_cat_prompts.lab_prompt(input_data.pet_info, input_data.lab_project,
                                                       input_data.instrument_info, input_data.item_values)
            },
        ]

        # 构建执行请求
        execution_request = AgentExecutionRequest(
            input_data=request_data,
            stream=input_data.stream,
            enable_short_term_memory=False
        )

        # 保存宠物信息
        await _save_or_update_pet_info(current_user.get('id'), input_data.pet_info)

        if input_data.stream:
            # 流式响应
            return await _handle_stream_diagnosis_with_messages(
                agent, execution_request, user_id, agent_service,
                conversation, message_service, msg_type=1
            )
        else:
            # 非流式响应
            result = await agent_service.execute_agent(agent.id, execution_request, user_id)

            # 创建助手消息记录
            if result and result.output_data:
                assistant_content = result.output_data.get('content', '处理完成')
                token_usage = result.output_data.get('token_usage')
                processing_time = result.duration_ms

                assistant_message = await message_service.create_assistant_message(
                    conversation.id,
                    assistant_content,
                    user_id,
                    agent.id,
                    metadata=result.output_data.get('metadata'),
                    token_usage=token_usage,
                    processing_time_ms=processing_time
                )
                logger.info(f"创建助手消息记录: {assistant_message.id}")

            logger.info(
                f"AI问诊执行成功 (用户: {user_id}, 会话: {conversation.conversation_id})")

            response_data = BetaCatCdssAgentOutput(
                request_id=input_data.request_id,
                content=result.output_data.get('content'),
                pushTime=result.duration_ms,
                msgType=1
            ).model_dump()
            # 返回统一成功响应格式
            return response_data

    except BusinessException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"AI问诊执行失败: {e}")
        raise BusinessException(
            message="AI问诊服务执行失败",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


async def _save_or_update_pet_info(current_user_id: int, pet_info: BetaCatPetInfo,
                                   pet_other_info: BetaCatPetOtherInfo = None):
    pet_data = pet_info.model_dump()
    if pet_info.category_name:
        pet_data['species'] = pet_data.pop('category_name')
    if pet_info.kind_name:
        pet_data['breed'] = pet_data.pop('kind_name')
    if pet_info.birthday:
        pet_data['birth_date'] = pet_data.pop('birthday')
    if pet_info.weight:
        pet_data['weight'] = float(pet_data.pop('weight').replace("kg", "").strip())
    pet_data['owner_id'] = current_user_id
    pet_data['tenant_pet_id'] = f"betacat-{pet_data.pop('pet_id')}"
    if pet_other_info:
        pet_data["other_info"] = pet_other_info.model_dump()
    result = await save_or_update_by_tenant_pet_id(pet_data)
    logger.info(f"保存宠物信息: ID-{result.get('data').get('pet')}")


def _extract_user_content(input_data: dict) -> str:
    """从输入数据中提取用户内容用于消息记录"""
    try:
        if "messages" in input_data:
            # OpenAI风格的messages输入
            messages = input_data.get("messages", [])
            user_messages = [msg.get("content", "")
                             for msg in messages if msg.get("role") == "user"]
            return "\n".join(user_messages) if user_messages else "用户消息"
        elif "prompt" in input_data:
            # 直接文本输入
            return input_data.get("prompt", "用户提问")
        elif "structured_input" in input_data:
            # 结构化输入（递归处理）
            return _extract_user_content(input_data.get("structured_input", {}))
        elif "pet_info" in input_data and "symptoms" in input_data:
            # 传统格式
            pet_info = input_data.get("pet_info", {})
            symptoms = input_data.get("symptoms", [])
            additional_info = input_data.get("additional_info", "")

            content_parts = []
            if pet_info:
                content_parts.append(
                    f"宠物信息: {json.dumps(pet_info, ensure_ascii=False)}")
            if symptoms:
                content_parts.append(
                    f"症状描述: {json.dumps(symptoms, ensure_ascii=False)}")
            if additional_info:
                content_parts.append(f"补充信息: {additional_info}")

            return "\n".join(content_parts) if content_parts else "AI问诊请求"
        else:
            # 未识别格式，使用整个输入数据的字符串表示
            return f"AI问诊请求: {json.dumps(input_data, ensure_ascii=False)[:500]}"
    except Exception as e:
        logger.warning(f"提取用户内容失败: {e}")
        return "AI问诊请求"


def _detect_input_format(input_data: dict) -> str:
    """检测输入数据格式"""
    if "messages" in input_data:
        return "messages"
    elif "prompt" in input_data:
        return "prompt"
    elif "structured_input" in input_data:
        return "structured_input"
    elif "pet_info" in input_data and "symptoms" in input_data:
        return "traditional"
    else:
        return "unknown"


async def _handle_stream_diagnosis_with_messages(
        agent, execution_request, user_id, agent_service,
        conversation, message_service, msg_type
):
    """处理流式AI问诊响应并记录消息 - 优化版本"""

    async def stream_generator(start_time: datetime) -> AsyncGenerator[str, None]:
        assistant_content_parts = []  # 收集助手响应内容
        token_usage = None
        execution_id = None

        try:
            # 发送开始事件 - 包含模型信息
            start_event = {
                "event": "start",
                "request_id": execution_request.input_data.get('request_id'),
                "content": "",
                "pushTime": round((datetime.now(timezone.utc) - start_time).total_seconds() * 1000),
                "msgType": msg_type,
            }
            yield f"data: {json.dumps(start_event, ensure_ascii=False)}\n\n"

            # 调用智能体服务的流式执行
            async for chunk in agent_service.execute_agent_stream(agent.id, execution_request, user_id):
                # 解析并转发流式数据
                if chunk.startswith("data: "):
                    try:
                        chunk_data = json.loads(chunk[6:])
                        # 添加调试日志
                        logger.debug(f"处理流式数据块: {chunk_data}")

                        # 安全地提取数据
                        data_section = chunk_data.get("data", {})
                        if isinstance(data_section, dict):
                            inner_data = data_section.get("data", {})
                            data_type = data_section.get(
                                "type") or inner_data.get("type")

                            if data_type == "content":
                                # 提取实际内容 - 支持多层嵌套
                                content = ""
                                if isinstance(inner_data, dict) and "content" in inner_data:
                                    content = inner_data.get("content", "")
                                elif "content" in data_section:
                                    content = data_section.get("content", "")

                                # 收集内容用于最终的消息记录
                                if content:
                                    assistant_content_parts.append(content)

                                # 发送简化的内容事件 - 不包含模型信息
                                content_event = {
                                    "event": "content",
                                    "request_id": execution_request.input_data.get('request_id'),
                                    "pushTime": round((datetime.fromisoformat(
                                        chunk_data.get("timestamp")) - start_time).total_seconds() * 1000),
                                    "msgType": msg_type,
                                    "content": content
                                }
                                yield f"data: {json.dumps(content_event, ensure_ascii=False)}\n\n"

                            elif data_type == "finish":
                                # 处理完成事件
                                finish_data = inner_data if isinstance(
                                    inner_data, dict) else data_section
                                token_usage = finish_data.get(
                                    "token_usage") or finish_data.get("usage")

                                # 发送完成事件
                                finish_event = {
                                    "event": "finish",
                                    "msgType": msg_type,
                                    "request_id": execution_request.input_data.get('request_id'),
                                    "finish_reason": finish_data.get("finish_reason"),
                                    "total_content": finish_data.get("total_content"),
                                    "token_usage": token_usage
                                }
                                yield f"data: {json.dumps(finish_event, ensure_ascii=False)}\n\n"

                            elif data_type == "error":
                                # 处理错误事件
                                error_data = inner_data if isinstance(
                                    inner_data, dict) else data_section
                                error_event = {
                                    "event": "error",
                                    "msgType": msg_type,
                                    "request_id": execution_request.input_data.get('request_id'),
                                    "error": error_data.get("error", "未知错误")
                                }
                                yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"
                        else:
                            # 如果data不是字典，记录警告
                            logger.warning(f"意外的数据格式: {chunk_data}")

                    except json.JSONDecodeError:
                        # 如果不是JSON格式，跳过
                        logger.warning(f"无法解析的流式数据块: {chunk}")
                        continue

            # 创建助手消息记录
            assistant_content = "".join(
                assistant_content_parts) if assistant_content_parts else "AI问诊完成"
            try:
                assistant_message = await message_service.create_assistant_message(
                    conversation.id,
                    assistant_content,
                    user_id,
                    agent.id,
                    metadata={
                        "stream_response": True,
                        "input_format": _detect_input_format(execution_request.input_data),
                        "execution_id": execution_id
                    },
                    token_usage=token_usage,
                    processing_time_ms=None  # 流式响应暂时不计算精确处理时间
                )
                logger.info(f"创建助手消息记录: {assistant_message.id}")
            except Exception as e:
                logger.error(f"创建助手消息记录失败: {e}")
            # 发送标准的SSE结束事件
            yield "data: [DONE]\n\n"

        except Exception as e:
            logger.error(f"流式AI问诊处理失败: {e}")
            error_event = {
                "event": "error",
                "conversation_id": conversation.conversation_id,
                "error": str(e)
            }
            yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"

            # 即使出现错误也要发送结束事件
            yield "data: [DONE]\n\n"

    return StreamingResponse(
        stream_generator(datetime.now(timezone.utc)),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )
