"""
系统级智能体专用API路由
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from typing import AsyncGenerator
import json
import uuid

from shared.database import get_async_db
from shared.logging_config import get_app_service_logger
from shared.utils.auth import get_current_user_optional
from shared.utils.pet import get_pet_by_id
from shared.models.response import (
    SuccessResponse, create_success_response, ErrorCode, SuccessMessage
)
from shared.middleware.request_id import get_request_id
from shared.utils.exception_handlers import BusinessException
from ..models.agent import SystemAgentCategory
from ..models.conversation import MessageRole, MessageType, ConversationType
from ..schemas.agent import (
    VisionAgentInput,
    ReportGenerationAgentInput, ReportAnalysisAgentInput,
    AgentExecutionResponse, AgentPermissionCreate, AgentExecutionRequest
)
from ..schemas.conversation import ConversationCreate, MessageCreate
from ..schemas.report import HealthManagementReportResponse, DiseaseRiskResponse, DewormingPlanResponse, \
    NutritionalDietResponse, ChronicDiseaseResponse, VaccinationPlanResponse
from ..services.agent_permission_service import AgentPermissionService
from ..services.agent_service import AgentService
from ..services.conversation_service import ConversationService
from ..services.message_service import MessageService

logger = get_app_service_logger()
router = APIRouter(prefix="/system-agents", tags=["系统级智能体"])


@router.post("/diagnosis")
async def execute_diagnosis_agent(
        request: Request,
        input_data: dict,
        db: AsyncSession = Depends(get_async_db),
        current_user=Depends(get_current_user_optional)
):
    """
    执行AI问诊智能体 - 支持灵活输入和流式响应

    支持多种输入格式：
    1. OpenAI风格的messages数组
    2. 直接的prompt文本
    3. 传统的pet_info + symptoms结构化输入

    支持流式和非流式响应，并将对话消息记录到数据库
    """
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能使用AI问诊服务",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        # 获取用户ID和租户ID
        user_id = current_user.get('id') if isinstance(
            current_user, dict) else current_user.id
        tenant_id = current_user.get('tenant_id')

        # 获取AI问诊智能体
        agent_service = AgentService(db)
        agent = await agent_service._get_system_agent(SystemAgentCategory.DIAGNOSIS)

        if not agent:
            raise BusinessException(
                message="AI问诊智能体未找到",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        # 检查是否为流式请求
        is_stream = input_data.get('stream', False)

        # 获取或创建会话ID
        conversation_id = input_data.get('conversation_id')

        # 初始化服务
        conversation_service = ConversationService(db, tenant_id)
        message_service = MessageService(db, tenant_id)

        # 如果没有提供会话ID，创建新会话
        conversation = None
        if conversation_id:
            # 尝试获取现有会话并验证权限
            conversation = await conversation_service.get_by_conversation_id(conversation_id)

            # 验证会话的有效性和权限
            if conversation:
                # 检查会话是否属于当前用户
                if conversation.user_id != user_id:
                    raise BusinessException(
                        message="无权限访问此会话",
                        error_code=ErrorCode.FORBIDDEN,
                        status_code=status.HTTP_403_FORBIDDEN
                    )

                # 检查会话是否在同一租户下
                if conversation.tenant_id != tenant_id:
                    raise BusinessException(
                        message="会话不在当前租户范围内",
                        error_code=ErrorCode.FORBIDDEN,
                        status_code=status.HTTP_403_FORBIDDEN
                    )

                # 检查会话是否处于活跃状态
                if not conversation.is_active():
                    raise BusinessException(
                        message="会话已过期或不可用",
                        error_code=ErrorCode.INVALID_REQUEST,
                        status_code=status.HTTP_400_BAD_REQUEST
                    )

                logger.info(f"验证会话权限成功: {conversation_id} (用户: {user_id})")
            else:
                # 如果提供了会话ID但找不到会话，返回错误
                raise BusinessException(
                    message="指定的会话不存在",
                    error_code=ErrorCode.NOT_FOUND,
                    status_code=status.HTTP_404_NOT_FOUND
                )

        if not conversation:
            # 创建新会话
            conversation_data = ConversationCreate(
                title="AI问诊会话",
                conversation_type=ConversationType.DIAGNOSIS,
                primary_agent_id=agent.id,
                conversation_id=conversation_id or f"diagnosis_{uuid.uuid4().hex[:8]}",
                context_window_size=20,
                max_messages=100
            )
            conversation = await conversation_service.create_conversation(conversation_data, user_id)
            logger.info(f"创建新的AI问诊会话: {conversation.conversation_id}")

        # 提取用户输入内容用于消息记录
        user_content = _extract_user_content(input_data)

        # 创建用户消息记录
        user_message_data = MessageCreate(
            content=user_content,
            message_type=MessageType.TEXT,
            metadata={"input_format": _detect_input_format(input_data)}
        )
        user_message = await message_service.create_message(
            conversation.id, user_message_data, user_id, MessageRole.USER
        )
        logger.info(f"创建用户消息记录: {user_message.id}")

        # 构建执行请求数据
        request_data = dict(input_data)
        request_data.pop('stream', None)
        request_data.pop('conversation_id', None)

        # 添加会话上下文信息用于短期记忆（不包含数据库会话，避免序列化问题）
        request_data['_conversation_context'] = {
            'conversation_id': conversation.id,
            'conversation_string_id': conversation.conversation_id,
            'user_id': user_id,
            'tenant_id': tenant_id
        }

        # 构建执行请求
        execution_request = AgentExecutionRequest(
            input_data=request_data,
            stream=is_stream
        )

        if is_stream:
            # 流式响应
            return await _handle_stream_diagnosis_with_messages(
                agent, execution_request, user_id, agent_service,
                conversation, message_service, user_message
            )
        else:
            # 非流式响应
            result = await agent_service.execute_agent(agent.id, execution_request, user_id)

            # 创建助手消息记录
            if result and result.output_data:
                assistant_content = result.output_data.get('content', '处理完成')
                token_usage = result.output_data.get('token_usage')
                processing_time = result.duration_ms

                assistant_message = await message_service.create_assistant_message(
                    conversation.id,
                    assistant_content,
                    user_id,
                    agent.id,
                    metadata=result.output_data.get('metadata'),
                    token_usage=token_usage,
                    processing_time_ms=processing_time
                )
                logger.info(f"创建助手消息记录: {assistant_message.id}")

            logger.info(
                f"AI问诊执行成功 (用户: {user_id}, 会话: {conversation.conversation_id})")

            # 获取请求ID
            request_id = get_request_id(request)

            # 返回统一成功响应格式
            return create_success_response(
                data=result,
                msg="AI问诊执行成功",
                request_id=request_id
            )

    except BusinessException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"AI问诊执行失败: {e}")
        raise BusinessException(
            message="AI问诊服务执行失败",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def _extract_user_content(input_data: dict) -> str:
    """从输入数据中提取用户内容用于消息记录"""
    try:
        if "messages" in input_data:
            # OpenAI风格的messages输入
            messages = input_data.get("messages", [])
            user_messages = [msg.get("content", "")
                             for msg in messages if msg.get("role") == "user"]
            return "\n".join(user_messages) if user_messages else "用户消息"
        elif "prompt" in input_data:
            # 直接文本输入
            return input_data.get("prompt", "用户提问")
        elif "structured_input" in input_data:
            # 结构化输入（递归处理）
            return _extract_user_content(input_data.get("structured_input", {}))
        elif "pet_info" in input_data and "symptoms" in input_data:
            # 传统格式
            pet_info = input_data.get("pet_info", {})
            symptoms = input_data.get("symptoms", [])
            additional_info = input_data.get("additional_info", "")

            content_parts = []
            if pet_info:
                content_parts.append(
                    f"宠物信息: {json.dumps(pet_info, ensure_ascii=False)}")
            if symptoms:
                content_parts.append(
                    f"症状描述: {json.dumps(symptoms, ensure_ascii=False)}")
            if additional_info:
                content_parts.append(f"补充信息: {additional_info}")

            return "\n".join(content_parts) if content_parts else "AI问诊请求"
        else:
            # 未识别格式，使用整个输入数据的字符串表示
            return f"AI问诊请求: {json.dumps(input_data, ensure_ascii=False)[:500]}"
    except Exception as e:
        logger.warning(f"提取用户内容失败: {e}")
        return "AI问诊请求"


def _detect_input_format(input_data: dict) -> str:
    """检测输入数据格式"""
    if "messages" in input_data:
        return "messages"
    elif "prompt" in input_data:
        return "prompt"
    elif "structured_input" in input_data:
        return "structured_input"
    elif "pet_info" in input_data and "symptoms" in input_data:
        return "traditional"
    else:
        return "unknown"


async def _handle_stream_diagnosis_with_messages(
    agent, execution_request, user_id, agent_service,
    conversation, message_service, user_message
):
    """处理流式AI问诊响应并记录消息 - 优化版本"""

    async def stream_generator() -> AsyncGenerator[str, None]:
        assistant_content_parts = []  # 收集助手响应内容
        token_usage = None
        execution_id = None
        model_name = None  # 记录模型名称，只在开始和结束时发送

        try:
            # 发送开始事件 - 包含模型信息
            start_event = {
                "event": "start",
                "conversation_id": conversation.conversation_id,
                "agent_id": agent.id,
                "agent_name": agent.display_name,
                "user_message_id": user_message.id,
                "model": None  # 将在第一个内容事件中获取模型信息
            }
            yield f"data: {json.dumps(start_event, ensure_ascii=False)}\n\n"

            # 调用智能体服务的流式执行
            async for chunk in agent_service.execute_agent_stream(agent.id, execution_request, user_id):
                # 解析并转发流式数据
                if chunk.startswith("data: "):
                    try:
                        chunk_data = json.loads(chunk[6:])

                        # 提取执行ID（仅在第一次）
                        if not execution_id and "request_id" in chunk_data:
                            execution_id = chunk_data["request_id"]

                        # 处理不同类型的数据块
                        # 添加调试日志
                        logger.debug(f"处理流式数据块: {chunk_data}")

                        # 安全地提取数据
                        data_section = chunk_data.get("data", {})
                        if isinstance(data_section, dict):
                            inner_data = data_section.get("data", {})
                            data_type = data_section.get(
                                "type") or inner_data.get("type")

                            if data_type == "content":
                                # 提取实际内容 - 支持多层嵌套
                                content = ""
                                if isinstance(inner_data, dict) and "content" in inner_data:
                                    content = inner_data.get("content", "")
                                elif "content" in data_section:
                                    content = data_section.get("content", "")

                                # 记录模型名称（仅在第一次）
                                if not model_name:
                                    model_name = inner_data.get(
                                        "model") or data_section.get("model")
                                    # 发送模型信息更新事件
                                    if model_name:
                                        model_event = {
                                            "event": "model_info",
                                            "conversation_id": conversation.conversation_id,
                                            "model": model_name
                                        }
                                        yield f"data: {json.dumps(model_event, ensure_ascii=False)}\n\n"

                                # 收集内容用于最终的消息记录
                                if content:
                                    assistant_content_parts.append(content)

                                # 发送简化的内容事件 - 不包含模型信息
                                content_event = {
                                    "event": "content",
                                    "conversation_id": conversation.conversation_id,
                                    "content": content
                                }
                                yield f"data: {json.dumps(content_event, ensure_ascii=False)}\n\n"

                            elif data_type == "finish":
                                # 处理完成事件
                                finish_data = inner_data if isinstance(
                                    inner_data, dict) else data_section
                                token_usage = finish_data.get(
                                    "token_usage") or finish_data.get("usage")

                                # 发送完成事件
                                finish_event = {
                                    "event": "finish",
                                    "conversation_id": conversation.conversation_id,
                                    "finish_reason": finish_data.get("finish_reason"),
                                    "model": finish_data.get("model"),
                                    "total_content": finish_data.get("total_content"),
                                    "token_usage": token_usage
                                }
                                yield f"data: {json.dumps(finish_event, ensure_ascii=False)}\n\n"

                            elif data_type == "error":
                                # 处理错误事件
                                error_data = inner_data if isinstance(
                                    inner_data, dict) else data_section
                                error_event = {
                                    "event": "error",
                                    "conversation_id": conversation.conversation_id,
                                    "error": error_data.get("error", "未知错误")
                                }
                                yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"
                        else:
                            # 如果data不是字典，记录警告
                            logger.warning(f"意外的数据格式: {chunk_data}")

                    except json.JSONDecodeError:
                        # 如果不是JSON格式，跳过
                        logger.warning(f"无法解析的流式数据块: {chunk}")
                        continue

            # 创建助手消息记录
            assistant_content = "".join(
                assistant_content_parts) if assistant_content_parts else "AI问诊完成"

            assistant_message = None
            try:
                assistant_message = await message_service.create_assistant_message(
                    conversation.id,
                    assistant_content,
                    user_id,
                    agent.id,
                    metadata={
                        "stream_response": True,
                        "input_format": _detect_input_format(execution_request.input_data),
                        "execution_id": execution_id
                    },
                    token_usage=token_usage,
                    processing_time_ms=None  # 流式响应暂时不计算精确处理时间
                )
                logger.info(f"创建助手消息记录: {assistant_message.id}")
            except Exception as e:
                logger.error(f"创建助手消息记录失败: {e}")

            # 发送最终完成事件 - 包含模型信息
            # complete_event = {
            #     "event": "complete",
            #     "conversation_id": conversation.conversation_id,
            #     "assistant_message_id": getattr(assistant_message, 'id', None),
            #     # "total_content": assistant_content,
            #     "model": model_name,
            #     "token_usage": token_usage,
            #     "execution_id": execution_id
            # }
            # yield f"data: {json.dumps(complete_event, ensure_ascii=False)}\n\n"

            # 发送标准的SSE结束事件
            yield "data: [DONE]\n\n"

        except Exception as e:
            logger.error(f"流式AI问诊处理失败: {e}")
            error_event = {
                "event": "error",
                "conversation_id": conversation.conversation_id,
                "error": str(e)
            }
            yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"

            # 即使出现错误也要发送结束事件
            yield "data: [DONE]\n\n"

    return StreamingResponse(
        stream_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )


@router.post("/vision", response_model=SuccessResponse[AgentExecutionResponse])
async def execute_vision_agent(
        request: Request,
        input_data: VisionAgentInput,
        db: AsyncSession = Depends(get_async_db),
        current_user=Depends(get_current_user_optional)
):
    """
    执行AI视觉识别智能体

    分析宠物图片并提供健康评估
    """
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能使用AI视觉识别服务",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        # 获取AI视觉识别智能体
        agent_service = AgentService(db)
        agent = await agent_service._get_system_agent(SystemAgentCategory.VISION)

        if not agent:
            raise BusinessException(
                message="AI视觉识别智能体未找到",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        # 构建执行请求
        from ..schemas.agent import AgentExecutionRequest
        execution_request = AgentExecutionRequest(
            input_data=input_data.model_dump()
        )

        # 执行智能体
        user_id = current_user.get('id') if isinstance(
            current_user, dict) else current_user.id
        result = await agent_service.execute_agent(agent.id, execution_request, user_id)

        logger.info(f"AI视觉识别执行成功 (用户: {user_id})")

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一成功响应格式
        return create_success_response(
            data=result,
            msg="AI视觉识别执行成功",
            request_id=request_id
        )

    except BusinessException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"AI视觉识别执行失败: {e}")
        raise BusinessException(
            message="AI视觉识别服务执行失败",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/report-generation")
async def execute_report_generation_agent(
        request: Request,
        input_data: ReportGenerationAgentInput,
        db: AsyncSession = Depends(get_async_db),
        current_user=Depends(get_current_user_optional)
):
    """
    执行AI报告生成智能体

    根据宠物数据生成健康报告
    """
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能使用AI报告生成服务",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        # 获取用户id
        user_id = current_user.get('id') if isinstance(
            current_user, dict) else current_user.id

        # 获取AI报告生成智能体
        agent_service = AgentService(db)
        agent = await agent_service._get_system_agent(SystemAgentCategory.REPORT_GENERATION)

        if not agent:
            raise BusinessException(
                message="AI报告生成智能体未找到",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        # 检查或创建用户权限
        permission_service = AgentPermissionService(db)
        has_permission = await permission_service.check_user_permission(agent.id, user_id, "use")
        if not has_permission:
            # 如果没有权限，则创建权限
            permission_data = AgentPermissionCreate(
                user_id=user_id,
                permission_type="use"
            )
            await permission_service.create_permission(agent.id, permission_data, user_id)

        # 补充宠物信息
        pet_data = input_data.pet_data
        if input_data.pet_id:
            pet_data = await get_pet_by_id(input_data.pet_id,request.headers.get("Authorization"))
        else:
            pet_data = pet_data.model_dump()

        execution_request = AgentExecutionRequest(
            input_data=input_data.model_dump(exclude={"pet_data"}),
            config_override={
                "max_tokens": 5120
            }
        )
        execution_request.input_data["pet_data"] = pet_data

        # 执行智能体
        result = await agent_service.execute_agent(agent.id, execution_request, user_id)

        # 记录日志
        logger.info(f"AI报告生成执行成功 (用户: {user_id})")

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一成功响应格式
        return create_success_response(
            data=result.output_data.get("report_metadata"),
            msg="AI报告生成执行成功",
            request_id=request_id
        )

    except BusinessException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"AI报告生成执行失败: {e}")
        raise BusinessException(
            message="AI报告生成服务执行失败",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/report-analysis", response_model=SuccessResponse[AgentExecutionResponse])
async def execute_report_analysis_agent(
        request: Request,
        input_data: ReportAnalysisAgentInput,
        db: AsyncSession = Depends(get_async_db),
        current_user=Depends(get_current_user_optional)
):
    """
    执行AI报告解读智能体

    解读医疗报告并提供通俗易懂的解释
    """
    try:
        if not current_user:
            raise BusinessException(
                message="需要登录才能使用AI报告解读服务",
                error_code=ErrorCode.UNAUTHORIZED,
                status_code=status.HTTP_401_UNAUTHORIZED
            )

        # 获取AI报告解读智能体
        agent_service = AgentService(db)
        agent = await agent_service._get_system_agent(SystemAgentCategory.REPORT_ANALYSIS)

        if not agent:
            raise BusinessException(
                message="AI报告解读智能体未找到",
                error_code=ErrorCode.NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        # 构建执行请求
        from ..schemas.agent import AgentExecutionRequest
        execution_request = AgentExecutionRequest(
            input_data=input_data.model_dump()
        )

        # 执行智能体
        user_id = current_user.get('id') if isinstance(
            current_user, dict) else current_user.id
        result = await agent_service.execute_agent(agent.id, execution_request, user_id)

        logger.info(f"AI报告解读执行成功 (用户: {user_id})")

        # 获取请求ID
        request_id = get_request_id(request)

        # 返回统一成功响应格式
        return create_success_response(
            data=result,
            msg="AI报告解读执行成功",
            request_id=request_id
        )

    except BusinessException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"AI报告解读执行失败: {e}")
        raise BusinessException(
            message="AI报告解读服务执行失败",
            error_code=ErrorCode.OPERATION_FAILED,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
