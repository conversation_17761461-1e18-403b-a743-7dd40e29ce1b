"""
OCR服务基础接口和抽象类
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
import asyncio


@dataclass
class OCRResult:
    """OCR识别结果数据类"""
    text: str                           # 提取的文本
    confidence: float                   # 置信度分数 (0-1)
    bounding_boxes: List[Dict[str, Any]] = None  # 文本边界框信息
    language: str = "auto"              # 识别的语言
    processing_time_ms: int = 0         # 处理耗时(毫秒)
    metadata: Dict[str, Any] = None     # 额外元数据
    
    def __post_init__(self):
        if self.bounding_boxes is None:
            self.bounding_boxes = []
        if self.metadata is None:
            self.metadata = {}


@dataclass
class OCRConfig:
    """OCR配置参数数据类"""
    model_name: str = "default"         # 模型名称
    language: str = "auto"              # 识别语言
    confidence_threshold: float = 0.8   # 置信度阈值
    prompt: str = ""                    # 提示词
    timeout: int = 300                  # 超时时间(秒)
    retry_count: int = 3                # 重试次数
    custom_params: Dict[str, Any] = None  # 自定义参数
    
    def __post_init__(self):
        if self.custom_params is None:
            self.custom_params = {}


class BaseOCRService(ABC):
    """OCR服务基础抽象类
    
    定义了所有OCR服务必须实现的接口，支持可插拔的OCR模型架构。
    """
    
    def __init__(self, config: OCRConfig):
        """初始化OCR服务
        
        Args:
            config: OCR配置参数
        """
        self.config = config
        self.is_initialized = False
    
    @abstractmethod
    async def initialize(self) -> bool:
        """初始化OCR服务
        
        Returns:
            bool: 初始化是否成功
        """
        pass
    
    @abstractmethod
    async def extract_text(self, image_data: bytes, config: Optional[OCRConfig] = None) -> OCRResult:
        """从图片中提取文本
        
        Args:
            image_data: 图片二进制数据
            config: 可选的配置参数，覆盖默认配置
            
        Returns:
            OCRResult: OCR识别结果
            
        Raises:
            OCRServiceError: OCR服务异常
        """
        pass
    
    @abstractmethod
    async def extract_text_from_url(self, image_url: str, config: Optional[OCRConfig] = None) -> OCRResult:
        """从图片URL提取文本
        
        Args:
            image_url: 图片URL
            config: 可选的配置参数
            
        Returns:
            OCRResult: OCR识别结果
        """
        pass
    
    @abstractmethod
    async def batch_extract_text(self, image_data_list: List[bytes], 
                                config: Optional[OCRConfig] = None) -> List[OCRResult]:
        """批量提取文本
        
        Args:
            image_data_list: 图片二进制数据列表
            config: 可选的配置参数
            
        Returns:
            List[OCRResult]: OCR识别结果列表
        """
        pass
    
    @abstractmethod
    async def get_supported_languages(self) -> List[str]:
        """获取支持的语言列表
        
        Returns:
            List[str]: 支持的语言代码列表
        """
        pass
    
    @abstractmethod
    async def health_check(self) -> Dict[str, Any]:
        """健康检查
        
        Returns:
            Dict[str, Any]: 健康状态信息
        """
        pass
    
    async def cleanup(self):
        """清理资源"""
        self.is_initialized = False


class OCRServiceError(Exception):
    """OCR服务异常类"""
    
    def __init__(self, message: str, error_code: str = "OCR_ERROR", 
                 original_error: Exception = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.original_error = original_error
    
    def __str__(self):
        return f"[{self.error_code}] {self.message}"


class OCRTimeoutError(OCRServiceError):
    """OCR超时异常"""
    
    def __init__(self, message: str = "OCR处理超时"):
        super().__init__(message, "OCR_TIMEOUT")


class OCRModelNotFoundError(OCRServiceError):
    """OCR模型未找到异常"""
    
    def __init__(self, model_name: str):
        message = f"OCR模型未找到: {model_name}"
        super().__init__(message, "OCR_MODEL_NOT_FOUND")


class OCRInvalidImageError(OCRServiceError):
    """无效图片异常"""
    
    def __init__(self, message: str = "无效的图片格式或数据"):
        super().__init__(message, "OCR_INVALID_IMAGE")


class OCRServiceFactory:
    """OCR服务工厂类
    
    用于创建和管理不同类型的OCR服务实例。
    """
    
    _services: Dict[str, type] = {}
    
    @classmethod
    def register_service(cls, service_type: str, service_class: type):
        """注册OCR服务类型
        
        Args:
            service_type: 服务类型名称
            service_class: 服务类
        """
        cls._services[service_type] = service_class
    
    @classmethod
    def create_service(cls, service_type: str, config: OCRConfig) -> BaseOCRService:
        """创建OCR服务实例
        
        Args:
            service_type: 服务类型名称
            config: OCR配置
            
        Returns:
            BaseOCRService: OCR服务实例
            
        Raises:
            OCRModelNotFoundError: 服务类型未找到
        """
        if service_type not in cls._services:
            raise OCRModelNotFoundError(service_type)
        
        service_class = cls._services[service_type]
        return service_class(config)
    
    @classmethod
    def get_available_services(cls) -> List[str]:
        """获取可用的服务类型列表
        
        Returns:
            List[str]: 服务类型名称列表
        """
        return list(cls._services.keys())
