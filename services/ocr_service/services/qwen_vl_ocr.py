"""
QwenVL OCR服务实现
"""
import asyncio
import base64
import time
from typing import Dict, Any, List, Optional
import httpx
from io import BytesIO
from PIL import Image

from shared.logging_config import setup_ocr_service_logger
from .base_ocr import BaseOCRService, OCRResult, OCRConfig, OCRServiceError, OCRTimeoutError, OCRInvalidImageError, OCRServiceFactory

logger = setup_ocr_service_logger()


class QwenVLOCRService(BaseOCRService):
    """QwenVL视觉模型OCR服务实现"""

    def __init__(self, config: OCRConfig):
        super().__init__(config)
        self.api_key = None
        self.base_url = None
        self.model_name = None
        self.client = None

    async def initialize(self) -> bool:
        """初始化QwenVL OCR服务"""
        try:
            from ..config import settings

            # 获取配置
            self.api_key = settings.VL_QWEN_API_KEY
            self.base_url = settings.VL_QWEN_BASE_URL
            self.model_name = settings.VL_QWEN_MODEL_NAME or self.config.model_name

            if not self.api_key:
                raise OCRServiceError("QwenVL API密钥未配置", "QWEN_VL_NO_API_KEY")

            # 创建HTTP客户端
            self.client = httpx.AsyncClient(
                timeout=httpx.Timeout(self.config.timeout),
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }
            )

            # 测试连接
            health_result = await self.health_check()
            if not health_result.get("available", False):
                raise OCRServiceError("QwenVL服务不可用", "QWEN_VL_UNAVAILABLE")

            self.is_initialized = True
            logger.info("✅ QwenVL OCR服务初始化成功")
            logger.info(f"🤖 QwenVL模型名称: {self.model_name}")
            logger.info(f"🤖 QwenVL API端点: {self.base_url}")
            return True

        except Exception as e:
            logger.error(f"❌ QwenVL OCR服务初始化失败: {e}")
            self.is_initialized = False
            return False

    async def extract_text(self, image_data: bytes, config: Optional[OCRConfig] = None) -> OCRResult:
        """从图片中提取文本"""
        if not self.is_initialized:
            await self.initialize()

        start_time = time.time()
        effective_config = config or self.config

        try:
            # 验证图片数据
            await self._validate_image(image_data)

            # 将图片转换为base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')

            # 构建请求
            request_data = self._build_request(image_base64, effective_config)

            # 发送请求
            response = await self._send_request(request_data)

            # 解析响应
            result = await self._parse_response(response, start_time)

            logger.info(
                f"✅ QwenVL OCR处理成功，耗时: {result.processing_time_ms}ms，使用模型: {self.model_name}")
            return result

        except Exception as e:
            processing_time = int((time.time() - start_time) * 1000)
            logger.error(f"❌ QwenVL OCR处理失败: {e}, 耗时: {processing_time}ms")

            if isinstance(e, OCRServiceError):
                raise e
            else:
                raise OCRServiceError(
                    f"OCR处理失败: {str(e)}", "QWEN_VL_PROCESSING_ERROR", e)

    async def extract_text_from_url(self, image_url: str, config: Optional[OCRConfig] = None) -> OCRResult:
        """从图片URL提取文本"""
        try:
            # 下载图片
            async with httpx.AsyncClient() as client:
                response = await client.get(image_url)
                response.raise_for_status()
                image_data = response.content

            return await self.extract_text(image_data, config)

        except httpx.HTTPError as e:
            raise OCRServiceError(
                f"图片下载失败: {str(e)}", "QWEN_VL_DOWNLOAD_ERROR", e)

    async def batch_extract_text(self, image_data_list: List[bytes],
                                 config: Optional[OCRConfig] = None) -> List[OCRResult]:
        """批量提取文本"""
        if not image_data_list:
            return []

        logger.info(f"🔄 开始批量OCR处理，图片数量: {len(image_data_list)}")

        # 创建并发任务
        tasks = []
        for i, image_data in enumerate(image_data_list):
            task = self._extract_text_with_retry(
                image_data, config, f"batch_{i}")
            tasks.append(task)

        # 并发执行
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        ocr_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ 批量OCR第{i+1}张图片处理失败: {result}")
                # 创建失败结果
                ocr_results.append(OCRResult(
                    text="",
                    confidence=0.0,
                    metadata={"error": str(result), "index": i}
                ))
            else:
                ocr_results.append(result)

        successful_count = sum(1 for r in ocr_results if r.confidence > 0)
        logger.info(
            f"✅ 批量OCR处理完成，成功: {successful_count}/{len(image_data_list)}")

        return ocr_results

    async def get_supported_languages(self) -> List[str]:
        """获取支持的语言列表"""
        # QwenVL支持的主要语言
        return [
            "auto",      # 自动检测
            "zh",        # 中文
            "en",        # 英文
            "ja",        # 日文
            "ko",        # 韩文
            "fr",        # 法文
            "de",        # 德文
            "es",        # 西班牙文
            "ru",        # 俄文
            "ar",        # 阿拉伯文
        ]

    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            if not self.client:
                return {"available": False, "error": "客户端未初始化"}

            # 发送简单的模型列表请求来测试连接
            test_url = f"{self.base_url}/models"
            response = await self.client.get(test_url)

            return {
                "available": response.status_code == 200,
                "model_name": self.model_name,
                "api_endpoint": self.base_url,
                "response_time_ms": response.elapsed.total_seconds() * 1000 if hasattr(response, 'elapsed') else 0
            }

        except Exception as e:
            return {
                "available": False,
                "error": str(e)
            }

    async def cleanup(self):
        """清理资源"""
        if self.client:
            await self.client.aclose()
            self.client = None
        await super().cleanup()

    async def _validate_image(self, image_data: bytes):
        """验证图片数据"""
        try:
            # 检查文件大小
            if len(image_data) == 0:
                raise OCRInvalidImageError("图片数据为空")

            # 尝试打开图片验证格式
            image = Image.open(BytesIO(image_data))
            image.verify()

        except Exception as e:
            raise OCRInvalidImageError(f"无效的图片数据: {str(e)}")

    def _build_request(self, image_base64: str, config: OCRConfig) -> Dict[str, Any]:
        """构建请求数据"""
        # 构建消息内容
        content = [
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{image_base64}"
                }
            }
        ]

        # 添加提示词
        from ..config import settings
        prompt = config.prompt or settings.DEFAULT_OCR_PROMPT
        content.append({
            "type": "text",
            "text": prompt
        })

        request_data = {
            "model": self.model_name,
            "messages": [
                {
                    "role": "user",
                    "content": content
                }
            ],
            "max_tokens": 2000,
            "temperature": 0.1
        }

        logger.info(f"🤖 构建OCR请求，使用模型: {self.model_name}")
        return request_data

    async def _send_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """发送请求"""
        try:
            url = f"{self.base_url}/chat/completions"
            response = await self.client.post(url, json=request_data)
            response.raise_for_status()
            return response.json()

        except httpx.TimeoutException:
            raise OCRTimeoutError("QwenVL请求超时")
        except httpx.HTTPStatusError as e:
            raise OCRServiceError(
                f"QwenVL API请求失败: {e.response.status_code}", "QWEN_VL_API_ERROR", e)
        except Exception as e:
            raise OCRServiceError(
                f"QwenVL请求异常: {str(e)}", "QWEN_VL_REQUEST_ERROR", e)

    async def _parse_response(self, response: Dict[str, Any], start_time: float) -> OCRResult:
        """解析响应"""
        try:
            # 提取文本内容
            choices = response.get("choices", [])
            if not choices:
                raise OCRServiceError("QwenVL响应中没有结果", "QWEN_VL_NO_RESULT")

            message = choices[0].get("message", {})
            text = message.get("content", "").strip()

            # 计算处理时间
            processing_time = int((time.time() - start_time) * 1000)

            # 估算置信度（QwenVL不直接提供置信度，基于文本长度和完整性估算）
            confidence = min(0.95, max(0.1, len(text) / 100)) if text else 0.0

            return OCRResult(
                text=text,
                confidence=confidence,
                processing_time_ms=processing_time,
                language="auto",
                metadata={
                    "model": self.model_name,
                    "usage": response.get("usage", {}),
                    "finish_reason": choices[0].get("finish_reason")
                }
            )

        except Exception as e:
            raise OCRServiceError(
                f"QwenVL响应解析失败: {str(e)}", "QWEN_VL_PARSE_ERROR", e)

    async def _extract_text_with_retry(self, image_data: bytes, config: Optional[OCRConfig],
                                       task_name: str) -> OCRResult:
        """带重试的文本提取"""
        effective_config = config or self.config
        last_error = None

        for attempt in range(effective_config.retry_count):
            try:
                return await self.extract_text(image_data, config)
            except Exception as e:
                last_error = e
                if attempt < effective_config.retry_count - 1:
                    wait_time = 2 ** attempt  # 指数退避
                    logger.warning(
                        f"⚠️ {task_name} 第{attempt+1}次尝试失败，{wait_time}秒后重试: {e}")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f"❌ {task_name} 所有重试均失败")

        raise last_error


# 注册QwenVL OCR服务
OCRServiceFactory.register_service("qwen_vl", QwenVLOCRService)
