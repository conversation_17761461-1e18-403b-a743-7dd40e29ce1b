"""
OCR服务管理器 - 简化版无状态服务
"""
import base64
from typing import Dict, Any, List
from shared.logging_config import setup_ocr_service_logger
from .base_ocr import OCRConfig, OCRResult, OCRServiceFactory, OCRServiceError
from .qwen_vl_ocr import QwenVLOCRService  # 确保服务已注册

logger = setup_ocr_service_logger()


class OCRManager:
    """OCR服务管理器 - 简化版

    负责管理OCR服务的初始化和直接处理图片识别。
    """

    def __init__(self):
        self.services: Dict[str, Any] = {}
        self.default_service_type = "qwen_vl"

    async def initialize(self):
        """初始化OCR管理器"""
        try:
            # 初始化默认OCR服务
            await self._initialize_service(self.default_service_type)

            # 打印当前使用的模型信息
            from ..config import settings
            logger.info(f"🤖 当前默认OCR服务类型: {self.default_service_type}")
            logger.info(f"🤖 当前配置的模型名称: {settings.VL_QWEN_MODEL_NAME}")
            logger.info(f"🤖 当前API端点: {settings.VL_QWEN_BASE_URL}")

            logger.info("✅ OCR管理器初始化成功")

        except Exception as e:
            logger.error(f"❌ OCR管理器初始化失败: {e}")
            raise

    async def process_image_directly(self,
                                     image_url: str,
                                     model_type: str = None,
                                     prompt: str = None,
                                     language: str = "auto",
                                     confidence_threshold: float = 0.8) -> OCRResult:
        """直接处理图片OCR识别

        Args:
            image_url: 图片URL
            model_type: OCR模型类型
            prompt: OCR提示词
            language: 识别语言
            confidence_threshold: 置信度阈值

        Returns:
            OCRResult: OCR识别结果
        """
        try:
            # 确定使用的服务类型
            service_type = model_type or self.default_service_type

            # 获取OCR服务
            service = self.services.get(service_type)
            if not service:
                raise OCRServiceError(
                    f"OCR服务不可用: {service_type}", "SERVICE_UNAVAILABLE")

            # 创建OCR配置，使用默认提示词
            from ..config import settings
            effective_prompt = prompt or settings.DEFAULT_OCR_PROMPT
            config = OCRConfig(
                model_name=service_type,
                language=language,
                confidence_threshold=confidence_threshold,
                prompt=effective_prompt
            )

            # 执行OCR识别
            logger.info(f"🔍 开始OCR识别: {image_url}")
            logger.info(
                f"🤖 当前使用的模型: {service_type}, 模型名称: {config.model_name}")
            result = await service.extract_text_from_url(image_url, config)
            logger.info(
                f"✅ OCR识别完成，置信度: {result.confidence}, 使用模型: {result.metadata.get('model', '未知')}")

            return result

        except OCRServiceError:
            raise
        except Exception as e:
            logger.error(f"❌ OCR识别失败: {e}")
            raise OCRServiceError(f"OCR识别失败: {str(e)}", "PROCESSING_ERROR")

    async def process_image_from_base64(self,
                                        image_base64: str,
                                        model_type: str = None,
                                        prompt: str = None,
                                        language: str = "auto",
                                        confidence_threshold: float = 0.8) -> OCRResult:
        """从base64图片数据处理OCR识别

        Args:
            image_base64: base64编码的图片数据（不包含data:image前缀）
            model_type: OCR模型类型
            prompt: OCR提示词
            language: 识别语言
            confidence_threshold: 置信度阈值

        Returns:
            OCRResult: OCR识别结果
        """
        try:
            # 验证base64数据
            if not image_base64 or not image_base64.strip():
                raise OCRServiceError("base64图片数据不能为空", "INVALID_INPUT")

            # 解码base64数据为字节
            try:
                image_data = base64.b64decode(image_base64)
            except Exception as e:
                raise OCRServiceError(
                    f"base64数据解码失败: {str(e)}", "INVALID_BASE64")

            # 确定使用的服务类型
            service_type = model_type or self.default_service_type

            # 获取OCR服务
            service = self.services.get(service_type)
            if not service:
                raise OCRServiceError(
                    f"OCR服务不可用: {service_type}", "SERVICE_UNAVAILABLE")

            # 创建OCR配置，使用默认提示词
            from ..config import settings
            effective_prompt = prompt or settings.DEFAULT_OCR_PROMPT
            config = OCRConfig(
                model_name=service_type,
                language=language,
                confidence_threshold=confidence_threshold,
                prompt=effective_prompt
            )

            # 执行OCR识别
            logger.info(f"🔍 开始OCR识别: base64图片数据 (大小: {len(image_data)} 字节)")
            logger.info(
                f"🤖 当前使用的模型: {service_type}, 模型名称: {config.model_name}")
            result = await service.extract_text(image_data, config)
            logger.info(
                f"✅ OCR识别完成，置信度: {result.confidence}, 使用模型: {result.metadata.get('model', '未知')}")

            return result

        except OCRServiceError:
            raise
        except Exception as e:
            logger.error(f"❌ base64图片OCR识别失败: {e}")
            raise OCRServiceError(
                f"base64图片OCR识别失败: {str(e)}", "PROCESSING_ERROR")

    async def process_images_batch(
        self,
        image_data_list: List[bytes],
        model_type: str = None,
        prompt: str = None,
        language: str = "auto",
        confidence_threshold: float = 0.8
    ) -> List[OCRResult]:
        """批量处理多个图片的OCR识别

        Args:
            image_data_list: 图片二进制数据列表
            model_type: OCR模型类型
            prompt: OCR提示词
            language: 识别语言
            confidence_threshold: 置信度阈值

        Returns:
            List[OCRResult]: OCR识别结果列表
        """
        try:
            if not image_data_list:
                return []

            # 确定使用的服务类型
            service_type = model_type or self.default_service_type

            # 获取OCR服务
            service = self.services.get(service_type)
            if not service:
                raise OCRServiceError(
                    f"OCR服务不可用: {service_type}", "SERVICE_UNAVAILABLE")

            # 创建OCR配置，使用默认提示词
            from ..config import settings
            effective_prompt = prompt or settings.DEFAULT_OCR_PROMPT
            config = OCRConfig(
                model_name=service_type,
                language=language,
                confidence_threshold=confidence_threshold,
                prompt=effective_prompt
            )

            logger.info(f"🔄 开始批量OCR识别: {len(image_data_list)}个图片")
            logger.info(f"🤖 使用模型: {service_type}, 语言: {language}")

            # 使用服务的批量处理方法
            results = await service.batch_extract_text(image_data_list, config)

            successful_count = sum(1 for r in results if r.confidence > 0)
            logger.info(
                f"✅ 批量OCR识别完成: 成功 {successful_count}/{len(image_data_list)}")

            return results

        except OCRServiceError:
            raise
        except Exception as e:
            logger.error(f"❌ 批量OCR识别失败: {e}")
            raise OCRServiceError(
                f"批量OCR识别失败: {str(e)}", "BATCH_PROCESSING_ERROR")

    async def get_available_models(self) -> List[Dict[str, Any]]:
        """获取可用的OCR模型列表"""
        models = []

        try:
            for service_type in OCRServiceFactory.get_available_services():
                try:
                    service = self.services.get(service_type)
                    if service:
                        # 检查服务健康状态
                        health_status = await service.health_check()

                        models.append({
                            "id": service_type,
                            "name": service_type,
                            "description": f"{service_type} OCR服务",
                            "available": health_status.get("available", False),
                            "supported_languages": ["auto", "zh", "en"],
                            "max_image_size": 10 * 1024 * 1024,  # 10MB
                            "supported_formats": ["jpg", "jpeg", "png", "bmp", "gif"]
                        })
                    else:
                        models.append({
                            "id": service_type,
                            "name": service_type,
                            "description": f"{service_type} OCR服务",
                            "available": False,
                            "supported_languages": [],
                            "max_image_size": 0,
                            "supported_formats": []
                        })
                except Exception as e:
                    logger.warning(f"⚠️ 检查服务状态失败 {service_type}: {e}")
                    models.append({
                        "id": service_type,
                        "name": service_type,
                        "description": "服务状态未知",
                        "available": False,
                        "supported_languages": [],
                        "max_image_size": 0,
                        "supported_formats": []
                    })

            return models

        except Exception as e:
            logger.error(f"❌ 获取模型列表失败: {e}")
            return []

    async def cleanup(self):
        """清理资源"""
        try:
            for service_type, service in self.services.items():
                try:
                    await service.cleanup()
                    logger.info(f"✅ OCR服务清理完成: {service_type}")
                except Exception as e:
                    logger.warning(f"⚠️ OCR服务清理失败 {service_type}: {e}")

            self.services.clear()
            logger.info("✅ OCR管理器清理完成")

        except Exception as e:
            logger.error(f"❌ OCR管理器清理失败: {e}")

    async def _initialize_service(self, service_type: str):
        """初始化指定类型的OCR服务"""
        try:
            logger.info(f"🔧 初始化OCR服务: {service_type}")

            # 创建默认配置
            default_config = OCRConfig(
                model_name=service_type,
                language="auto",
                confidence_threshold=0.8
            )

            # 创建服务实例
            service = OCRServiceFactory.create_service(
                service_type, default_config)

            # 初始化服务
            await service.initialize()

            # 存储服务实例
            self.services[service_type] = service

            logger.info(f"✅ OCR服务初始化成功: {service_type}")

        except Exception as e:
            logger.error(f"❌ OCR服务初始化失败 {service_type}: {e}")
            raise OCRServiceError(
                f"OCR服务初始化失败: {service_type}", "INITIALIZATION_ERROR")


# 全局OCR管理器实例
ocr_manager = OCRManager()
