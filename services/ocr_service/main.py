#!/usr/bin/env python3
"""
OCR服务主应用
"""
from services.ocr_service.services.ocr_manager import ocr_manager
from services.ocr_service.api.ocr import router as ocr_router
from services.ocr_service.config import settings
from shared.logging_config import setup_ocr_service_logger
from fastapi_offline import FastAPIOffline
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi import FastAPI

from contextlib import asynccontextmanager


# 设置日志
logger = setup_ocr_service_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("🚀 OCR服务启动中...")

    try:
        # 初始化OCR管理器
        await ocr_manager.initialize()
        logger.info("✅ OCR管理器初始化成功")

        logger.info("🎉 OCR服务启动完成")

    except Exception as e:
        logger.error(f"❌ OCR服务启动失败: {e}")
        raise

    yield

    # 清理资源
    logger.info("🛑 OCR服务关闭中...")
    try:
        await ocr_manager.cleanup()
        logger.info("✅ OCR服务关闭完成")
    except Exception as e:
        logger.error(f"❌ OCR服务关闭时出错: {e}")


# 创建FastAPI应用程序
app = FastAPIOffline(
    title=settings.PROJECT_NAME,
    version=settings.PROJECT_VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=f"{settings.API_V1_STR}/docs",
    redoc_url=f"{settings.API_V1_STR}/redoc",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 移除了请求日志中间件，简化为无状态服务


@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 获取当前使用的模型信息
        model_info = {
            "default_service_type": ocr_manager.default_service_type,
            "model_name": settings.VL_QWEN_MODEL_NAME,
            "api_endpoint": settings.VL_QWEN_BASE_URL
        }

        return {
            "status": "healthy",
            "service": "ocr_service",
            "version": settings.PROJECT_VERSION,
            "model_info": model_info,
            "timestamp": "2025-01-14T12:00:00Z"
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "service": "ocr_service",
                "error": str(e),
                "timestamp": "2025-01-14T12:00:00Z"
            }
        )


# 包含路由器

app.include_router(
    ocr_router,
    prefix=f"{settings.API_V1_STR}/ocr",
    tags=["OCR识别"]
)

# 移除了内部接口，简化为无状态服务

# 全局异常处理器


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "detail": "内部服务器错误",
            "type": "internal_error"
        }
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.SERVICE_PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
