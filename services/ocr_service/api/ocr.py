"""
OCR API端点 - 简化版无状态服务
"""
import time
from datetime import datetime, timezone
from typing import Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, status, File, UploadFile, Form

from shared.utils.auth import get_current_active_user, get_current_user_optional
from shared.logging_config import setup_ocr_service_logger
from ..schemas.ocr_schemas import (
    OCRExtractRequest, OCRExtractResponse,
    OCRBatchExtractRequest, OCRBatchExtractResponse, OCRBatchResult,
    OCRModelsResponse, OCRHealthResponse
)
from ..services.ocr_manager import ocr_manager
from ..services.base_ocr import OCRServiceError

logger = setup_ocr_service_logger()
router = APIRouter()


@router.post("/extract", response_model=OCRExtractResponse)
async def extract_text_from_image(
    request: OCRExtractRequest,
    current_user: Dict[str, Any] = Depends(get_current_user_optional)
):
    """单张图片OCR识别

    支持两种图片输入方式：
    1. 通过image_url提供图片URL
    2. 通过image_base64提供base64编码的图片数据

    如果同时提供两种方式，优先使用image_base64。
    """
    try:
        start_time = time.time()

        # 确定图片输入方式并记录日志
        if request.image_base64:
            logger.info(
                f"🔍 用户 {current_user.get('email')} 请求OCR识别: base64图片数据")
            # 处理base64图片数据
            ocr_result = await ocr_manager.process_image_from_base64(
                image_base64=request.image_base64,
                model_type=request.model_type,
                prompt=request.prompt,
                language=request.language,
                confidence_threshold=request.confidence_threshold
            )
        else:
            logger.info(
                f"🔍 用户 {current_user.get('email')} 请求OCR识别: {request.image_url}")
            # 处理图片URL
            ocr_result = await ocr_manager.process_image_directly(
                image_url=request.image_url,
                model_type=request.model_type,
                prompt=request.prompt,
                language=request.language,
                confidence_threshold=request.confidence_threshold
            )

        processing_time_ms = int((time.time() - start_time) * 1000)

        return OCRExtractResponse(
            success=True,
            result={
                "text": ocr_result.text,
                "confidence": ocr_result.confidence,
                "language": ocr_result.language,
                "processing_time_ms": processing_time_ms,
                "bounding_boxes": ocr_result.bounding_boxes,
                "metadata": ocr_result.metadata
            },
            processing_time_ms=processing_time_ms
        )

    except OCRServiceError as e:
        logger.error(f"❌ OCR服务错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error_code": e.error_code,
                "error_message": e.message
            }
        )
    except ValueError as e:
        logger.error(f"❌ 请求参数错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error_code": "INVALID_REQUEST",
                "error_message": str(e)
            }
        )
    except Exception as e:
        logger.error(f"❌ OCR请求处理失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error_code": "INTERNAL_ERROR",
                "error_message": "内部服务器错误"
            }
        )


@router.post("/extract/batch", response_model=OCRBatchExtractResponse)
async def extract_text_batch(
    files: List[UploadFile] = File(..., description="要处理的图片文件列表"),
    model_type: str = Form("qwen_vl", description="OCR模型类型"),
    prompt: str = Form(None, description="OCR提示词"),
    language: str = Form("auto", description="识别语言"),
    confidence_threshold: float = Form(0.8, description="置信度阈值"),
    current_user: Dict[str, Any] = Depends(get_current_active_user)
):
    """多图片批量OCR识别（同步）

    支持同时上传多个图片文件进行批量OCR识别。
    每个图片都会独立处理，单个图片失败不影响其他图片的处理。

    支持的图片格式：JPEG、PNG、GIF、BMP、WebP
    最大文件大小：10MB（单个文件）
    最大批量数量：建议不超过10个文件以确保性能
    """
    try:
        start_time = time.time()

        # 验证文件数量
        if not files:
            raise ValueError("至少需要上传一个图片文件")

        if len(files) > 20:  # 限制最大批量数量
            raise ValueError("批量处理最多支持20个文件")

        logger.info(
            f"🔍 用户 {current_user.get('email')} 请求批量OCR识别: {len(files)}个文件")

        # 验证文件类型和大小
        from ..config import settings
        image_data_list = []
        file_info_list = []

        for i, file in enumerate(files):
            # 检查文件类型
            if file.content_type not in settings.ALLOWED_IMAGE_TYPES:
                raise ValueError(
                    f"文件 {file.filename} 格式不支持，支持的格式: {', '.join(settings.ALLOWED_IMAGE_TYPES)}")

            # 读取文件内容
            file_content = await file.read()

            # 检查文件大小
            if len(file_content) > settings.MAX_FILE_SIZE:
                raise ValueError(
                    f"文件 {file.filename} 大小超过限制 ({settings.MAX_FILE_SIZE} 字节)")

            image_data_list.append(file_content)
            file_info_list.append({
                "index": i,
                "filename": file.filename,
                "size": len(file_content),
                "content_type": file.content_type
            })

        # 执行批量OCR处理
        logger.info(f"🔄 开始批量OCR处理: {len(image_data_list)}个图片")
        ocr_results = await ocr_manager.process_images_batch(
            image_data_list=image_data_list,
            model_type=model_type,
            prompt=prompt,
            language=language,
            confidence_threshold=confidence_threshold
        )

        # 构建响应结果
        batch_results = []
        successful_count = 0

        for i, (ocr_result, file_info) in enumerate(zip(ocr_results, file_info_list)):
            if hasattr(ocr_result, 'text') and ocr_result.confidence > 0:
                # 成功的结果
                batch_results.append(OCRBatchResult(
                    index=file_info["index"],
                    filename=file_info["filename"],
                    success=True,
                    result={
                        "text": ocr_result.text,
                        "confidence": ocr_result.confidence,
                        "language": ocr_result.language,
                        "processing_time_ms": ocr_result.processing_time_ms,
                        "bounding_boxes": ocr_result.bounding_boxes,
                        "metadata": {
                            **(ocr_result.metadata or {}),
                            "file_info": file_info
                        }
                    },
                    error=None
                ))
                successful_count += 1
            else:
                # 失败的结果
                error_msg = ocr_result.metadata.get('error', '未知错误') if hasattr(
                    ocr_result, 'metadata') else '处理失败'
                batch_results.append(OCRBatchResult(
                    index=file_info["index"],
                    filename=file_info["filename"],
                    success=False,
                    result=None,
                    error=error_msg
                ))

        total_processing_time_ms = int((time.time() - start_time) * 1000)
        failed_count = len(files) - successful_count

        logger.info(
            f"✅ 批量OCR处理完成: 成功 {successful_count}/{len(files)}，耗时 {total_processing_time_ms}ms")

        return OCRBatchExtractResponse(
            success=successful_count > 0,  # 只要有一个成功就算整体成功
            total_images=len(files),
            successful_count=successful_count,
            failed_count=failed_count,
            results=batch_results,
            total_processing_time_ms=total_processing_time_ms,
            error=None if successful_count > 0 else "所有图片处理失败"
        )

    except ValueError as e:
        logger.error(f"❌ 批量OCR请求参数错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error_code": "INVALID_REQUEST",
                "error_message": str(e)
            }
        )
    except Exception as e:
        logger.error(f"❌ 批量OCR请求处理失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error_code": "INTERNAL_ERROR",
                "error_message": "批量OCR处理失败"
            }
        )


@router.get("/models", response_model=OCRModelsResponse)
async def get_available_models(
    current_user: Dict[str, Any] = Depends(get_current_active_user)
):
    """获取可用的OCR模型列表

    返回当前支持的所有OCR模型及其状态信息。
    """
    try:
        logger.info(f"📋 用户 {current_user.get('email')} 查询可用模型")

        # 获取可用模型
        models = await ocr_manager.get_available_models()

        return OCRModelsResponse(
            models=models,
            total=len(models)
        )

    except Exception as e:
        logger.error(f"❌ 获取模型列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error_code": "INTERNAL_ERROR",
                "error_message": "内部服务器错误"
            }
        )


@router.get("/health", response_model=OCRHealthResponse)
async def health_check():
    """OCR服务健康检查

    检查OCR服务及各个模型的健康状态。
    """
    try:
        # 获取模型状态
        models = await ocr_manager.get_available_models()
        model_status = {model["id"]: model["available"] for model in models}

        return OCRHealthResponse(
            status="healthy",
            service="ocr_service",
            version="1.0.0",
            models=model_status,
            timestamp=datetime.now(timezone.utc)
        )

    except Exception as e:
        logger.error(f"❌ 健康检查失败: {e}")
        return OCRHealthResponse(
            status="unhealthy",
            service="ocr_service",
            version="1.0.0",
            models={},
            timestamp=datetime.now(timezone.utc)
        )
