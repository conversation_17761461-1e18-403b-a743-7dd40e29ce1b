"""
OCR服务数据模式定义 - 简化版
"""
from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field, validator, model_validator


# 请求模式
class OCRExtractRequest(BaseModel):
    """单张图片OCR请求"""
    model_config = {"protected_namespaces": ()}

    image_url: Optional[str] = Field(None, description="图片URL")
    image_base64: Optional[str] = Field(
        None, description="base64编码的图片数据（不包含data:image前缀）")
    model_type: Optional[str] = Field("qwen_vl", description="OCR模型类型")
    prompt: Optional[str] = Field(None, description="OCR提示词")
    language: Optional[str] = Field("auto", description="识别语言")
    confidence_threshold: Optional[float] = Field(
        0.8, ge=0.0, le=1.0, description="置信度阈值")

    @validator('image_url')
    def validate_image_url(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('图片URL不能为空')
        return v.strip() if v else None

    @validator('image_base64')
    def validate_image_base64(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('base64图片数据不能为空')
        return v.strip() if v else None

    @model_validator(mode='after')
    def validate_image_input(self):
        """验证图片输入方式"""
        # 验证至少提供一种图片输入方式
        if not self.image_url and not self.image_base64:
            raise ValueError('必须提供image_url或image_base64中的至少一种图片输入方式')

        # 如果同时提供两种方式，优先使用image_base64
        if self.image_url and self.image_base64:
            self.image_url = None

        return self


class OCRBatchExtractRequest(BaseModel):
    """多图片批量OCR请求"""
    model_config = {"protected_namespaces": ()}

    model_type: Optional[str] = Field("qwen_vl", description="OCR模型类型")
    prompt: Optional[str] = Field(None, description="OCR提示词")
    language: Optional[str] = Field("auto", description="识别语言")
    confidence_threshold: Optional[float] = Field(
        0.8, ge=0.0, le=1.0, description="置信度阈值")


# 响应模式
class OCRBoundingBox(BaseModel):
    """文本边界框"""
    x: float = Field(..., description="X坐标")
    y: float = Field(..., description="Y坐标")
    width: float = Field(..., description="宽度")
    height: float = Field(..., description="高度")
    text: str = Field(..., description="文本内容")
    confidence: float = Field(..., description="置信度")


class OCRResult(BaseModel):
    """OCR识别结果"""
    text: str = Field(..., description="提取的文本")
    confidence: float = Field(..., description="置信度分数")
    language: str = Field(..., description="识别的语言")
    processing_time_ms: int = Field(..., description="处理耗时(毫秒)")
    bounding_boxes: Optional[List[OCRBoundingBox]
                             ] = Field(None, description="文本边界框列表")
    metadata: Optional[Dict[str, Any]] = Field(None, description="额外元数据")


class OCRExtractResponse(BaseModel):
    """单张图片OCR响应"""
    success: bool = Field(..., description="是否成功")
    result: Optional[OCRResult] = Field(None, description="OCR结果")
    processing_time_ms: Optional[int] = Field(None, description="处理耗时(毫秒)")
    error: Optional[str] = Field(None, description="错误信息")


class OCRBatchResult(BaseModel):
    """单个图片的OCR批量处理结果"""
    index: int = Field(..., description="图片索引（从0开始）")
    filename: Optional[str] = Field(None, description="原始文件名")
    success: bool = Field(..., description="是否处理成功")
    result: Optional[OCRResult] = Field(None, description="OCR结果")
    error: Optional[str] = Field(None, description="错误信息")


class OCRBatchExtractResponse(BaseModel):
    """多图片批量OCR响应"""
    success: bool = Field(..., description="整体是否成功")
    total_images: int = Field(..., description="总图片数量")
    successful_count: int = Field(..., description="成功处理的图片数量")
    failed_count: int = Field(..., description="失败的图片数量")
    results: List[OCRBatchResult] = Field(..., description="每个图片的处理结果")
    total_processing_time_ms: int = Field(..., description="总处理耗时(毫秒)")
    error: Optional[str] = Field(None, description="整体错误信息")


class OCRModelInfo(BaseModel):
    """OCR模型信息"""
    id: str = Field(..., description="模型ID")
    name: str = Field(..., description="模型名称")
    description: str = Field(..., description="模型描述")
    available: bool = Field(..., description="是否可用")
    supported_languages: List[str] = Field(..., description="支持的语言")
    max_image_size: int = Field(..., description="最大图片大小(字节)")
    supported_formats: List[str] = Field(..., description="支持的图片格式")


class OCRModelsResponse(BaseModel):
    """模型列表响应"""
    models: List[OCRModelInfo] = Field(..., description="模型列表")
    total: int = Field(..., description="模型总数")


class OCRHealthResponse(BaseModel):
    """健康检查响应"""
    status: str = Field(..., description="服务状态")
    service: str = Field(..., description="服务名称")
    version: str = Field(..., description="服务版本")
    models: Dict[str, bool] = Field(..., description="模型状态")
    timestamp: datetime = Field(..., description="检查时间")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class OCRErrorResponse(BaseModel):
    """错误响应"""
    error_code: str = Field(..., description="错误代码")
    error_message: str = Field(..., description="错误消息")
    timestamp: datetime = Field(..., description="错误时间")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }
