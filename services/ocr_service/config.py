"""
OCR服务配置模块
"""
import os
from typing import List


class Settings:
    """OCR服务配置类"""

    # 项目基本信息
    PROJECT_NAME: str = os.getenv("PROJECT_NAME", "宠物医疗AI开放平台 - OCR服务")
    PROJECT_VERSION: str = os.getenv("PROJECT_VERSION", "1.0.0")

    # API配置
    API_V1_STR: str = os.getenv("API_V1_STR", "/api/v1")

    # 服务端口
    SERVICE_PORT: int = int(os.getenv("OCR_SERVICE_PORT", "8004"))

    # 调试模式
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"

    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")

    # 数据库配置
    DATABASE_URL: str = os.getenv("DATABASE_URL", "")

    # Redis配置
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")

    # JWT配置
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-here")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # CORS配置
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:8080",
        "http://localhost:8000",
    ]

    # 分页配置
    DEFAULT_PAGE_SIZE: int = 10
    MAX_PAGE_SIZE: int = 100

    # 文件上传配置
    MAX_FILE_SIZE: int = int(os.getenv("MAX_FILE_SIZE", "10485760"))
    UPLOAD_DIR: str = os.getenv("UPLOAD_DIR", "./uploads")
    ALLOWED_IMAGE_TYPES: List[str] = [
        "image/jpeg", "image/png", "image/gif", "image/bmp", "image/webp"]

    # QwenVL配置
    VL_QWEN_API_KEY: str = os.getenv("VL_QWEN_API_KEY", "")
    VL_QWEN_MODEL_NAME: str = os.getenv("VL_QWEN_MODEL_NAME", "qwen-vl-plus")
    VL_QWEN_BASE_URL: str = os.getenv(
        "VL_QWEN_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1")

    # OCR默认提示词配置
    DEFAULT_OCR_PROMPT: str = os.getenv("DEFAULT_OCR_PROMPT", """
请仔细识别这份医学检查报告中的所有文字内容，要求：

1. 完整识别所有文字，包括：
   - 报告标题和基本信息（患者姓名、性别、年龄、检查日期等）
   - 检查项目名称和分类
   - 检测数值、单位和参考范围
   - 检查结果描述和医生建议
   - 报告日期和医院信息

2. 保持原有格式和布局：
   - 保留表格结构和对齐关系
   - 维持段落分隔和层次结构
   - 保持数值与单位的正确对应关系

3. 特别注意医学术语的准确性：
   - 正确识别医学专业术语和缩写
   - 准确识别数值、小数点和科学计数法
   - 确保单位符号的正确性（如mg/L、mmol/L、%等）
   - 正确识别参考范围的格式（如"正常值：3.5-5.0"）

4. 对于表格内容应使用markdown形式表格语法：
   - 保持行列对应关系
   - 正确识别表头和数据行
   - 保留异常标记（如↑、↓、*、+等）

5. 输出格式要求：
   - 使用清晰的换行和缩进
   - 保持原文的逻辑结构
   - 如遇模糊文字，标注[不清楚]而非猜测

请按照以上要求，完整、准确地识别图片中的所有文字内容。
""".strip())

    # OCR任务配置
    OCR_TASK_TIMEOUT: int = int(os.getenv("OCR_TASK_TIMEOUT", "300"))  # 5分钟
    OCR_MAX_RETRY_COUNT: int = int(os.getenv("OCR_MAX_RETRY_COUNT", "3"))
    OCR_BATCH_SIZE: int = int(os.getenv("OCR_BATCH_SIZE", "10"))

    # 服务健康检查配置
    HEALTH_CHECK_TIMEOUT: int = int(os.getenv("HEALTH_CHECK_TIMEOUT", "30"))


settings = Settings()
