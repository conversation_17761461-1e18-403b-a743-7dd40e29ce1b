#!/usr/bin/env python3
"""
数据库迁移管理脚本

该脚本提供了便捷的数据库迁移管理功能，支持：
- 生成初始迁移脚本
- 自动检测模型变更并生成迁移
- 执行和回滚迁移
- 迁移状态检查和历史查看

使用示例：
    python scripts/db_migrate.py init                    # 生成初始迁移
    python scripts/db_migrate.py auto "添加新字段"        # 自动生成迁移
    python scripts/db_migrate.py upgrade                 # 执行迁移
    python scripts/db_migrate.py downgrade -1            # 回滚一个版本
    python scripts/db_migrate.py status                  # 查看迁移状态
"""

import argparse
import asyncio
import os
import subprocess
import sys
from pathlib import Path

# 确保项目根目录在Python路径中
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 加载环境变量
try:
    from shared.env_loader import get_current_environment
    current_env = get_current_environment()
except ImportError:
    try:
        from dotenv import load_dotenv
        load_dotenv(project_root / ".env")
    except ImportError:
        pass


def run_alembic_command(command_args, capture_output=False):
    """运行Alembic命令"""
    cmd = ["alembic"] + command_args
    try:
        print(f"🔄 执行命令: {' '.join(cmd)}")
        result = subprocess.run(
            cmd,
            check=True,
            cwd=project_root,
            capture_output=capture_output,
            text=True
        )
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ Alembic命令执行失败: {e}")
        if capture_output and e.stdout:
            print(f"输出: {e.stdout}")
        if capture_output and e.stderr:
            print(f"错误: {e.stderr}")
        return None
    except Exception as e:
        print(f"❌ 执行错误: {e}")
        return None


def check_migrations_directory():
    """检查migrations目录是否存在"""
    migrations_dir = project_root / "migrations"
    return migrations_dir.exists()


def init_migration():
    """生成初始迁移脚本"""
    print("🔄 生成初始数据库迁移...")

    if not check_migrations_directory():
        print("❌ migrations目录不存在，请先运行 alembic init migrations")
        return False

    # 检查是否已有迁移文件
    versions_dir = project_root / "migrations" / "versions"
    if versions_dir.exists() and list(versions_dir.glob("*.py")):
        print("⚠️  已存在迁移文件，是否要生成新的初始迁移？")
        response = input("输入 'yes' 继续: ")
        if response.lower() != 'yes':
            print("❌ 操作已取消")
            return False

    # 生成初始迁移
    result = run_alembic_command([
        "revision",
        "--autogenerate",
        "-m", "初始数据库结构 - 微服务架构"
    ])

    if result:
        print("✅ 初始迁移脚本生成完成")
        print("📝 请检查生成的迁移文件，确认无误后执行 upgrade 命令")
        return True
    else:
        print("❌ 初始迁移脚本生成失败")
        return False


def auto_migration(message):
    """自动生成迁移脚本"""
    print(f"🔄 自动生成迁移脚本: {message}")

    result = run_alembic_command([
        "revision",
        "--autogenerate",
        "-m", message
    ])

    if result:
        print("✅ 迁移脚本生成完成")
        print("📝 请检查生成的迁移文件，确认无误后执行 upgrade 命令")
        return True
    else:
        print("❌ 迁移脚本生成失败")
        return False


def upgrade_database(revision="head"):
    """执行数据库迁移"""
    print(f"🔄 执行数据库迁移到: {revision}")

    result = run_alembic_command(["upgrade", revision])

    if result:
        print("✅ 数据库迁移完成")
        return True
    else:
        print("❌ 数据库迁移失败")
        return False


def downgrade_database(revision):
    """回滚数据库迁移"""
    print(f"🔄 回滚数据库迁移到: {revision}")

    # 确认回滚操作
    print("⚠️  数据库回滚可能导致数据丢失！")
    response = input("确认要继续吗？输入 'yes' 继续: ")
    if response.lower() != 'yes':
        print("❌ 操作已取消")
        return False

    result = run_alembic_command(["downgrade", revision])

    if result:
        print("✅ 数据库回滚完成")
        return True
    else:
        print("❌ 数据库回滚失败")
        return False


def show_migration_status():
    """显示迁移状态"""
    print("📍 当前迁移状态:")

    # 获取当前版本
    current_result = run_alembic_command(["current"], capture_output=True)
    if not current_result:
        return False

    # 获取最新版本
    heads_result = run_alembic_command(["heads"], capture_output=True)
    if not heads_result:
        return False

    # 提取版本号（去除额外的输出）
    current_lines = [line for line in current_result.stdout.split(
        '\n') if line.strip() and not line.startswith('✅')]
    latest_lines = [line for line in heads_result.stdout.split(
        '\n') if line.strip() and not line.startswith('✅')]

    current_version = current_lines[-1].strip() if current_lines else ""
    latest_version = latest_lines[-1].strip() if latest_lines else ""

    # 提取版本ID（如果有的话）
    if " " in current_version:
        current_version = current_version.split()[0]
    if " " in latest_version:
        latest_version = latest_version.split()[0]

    print(f"   当前版本: {current_version if current_version else '无'}")
    print(f"   最新版本: {latest_version if latest_version else '无'}")

    if current_version and current_version == latest_version:
        print("✅ 数据库迁移状态正常，无待执行迁移")
    elif not current_version:
        print("⚠️  数据库未初始化，请运行 upgrade 命令")
    else:
        print("⚠️  发现待执行的迁移，请运行 upgrade 命令")

    return True


def show_migration_history():
    """显示迁移历史"""
    print("📋 迁移历史:")
    result = run_alembic_command(["history", "--verbose"])
    return result is not None


def merge_heads():
    """合并多个头部修订版本"""
    print("🔄 合并多个头部修订版本...")

    # 首先检查是否有多个头部
    heads_result = run_alembic_command(["heads"], capture_output=True)
    if not heads_result:
        return False

    heads_lines = [line.strip()
                   for line in heads_result.stdout.split('\n') if line.strip()]
    if len(heads_lines) <= 1:
        print("✅ 只有一个头部修订版本，无需合并")
        return True

    print(f"📍 发现 {len(heads_lines)} 个头部修订版本:")
    for line in heads_lines:
        print(f"   - {line}")

    # 生成合并迁移
    result = run_alembic_command([
        "merge",
        "-m", "合并多个头部修订版本"
    ])

    if result:
        print("✅ 头部修订版本合并完成")
        print("📝 请检查生成的合并迁移文件，确认无误后执行 upgrade 命令")
        return True
    else:
        print("❌ 头部修订版本合并失败")
        return False


def upgrade_to_heads():
    """升级到所有头部修订版本"""
    print("🔄 升级到所有头部修订版本...")

    result = run_alembic_command(["upgrade", "heads"])

    if result:
        print("✅ 数据库升级到所有头部完成")
        return True
    else:
        print("❌ 数据库升级到所有头部失败")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="数据库迁移管理工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # 初始化迁移
    subparsers.add_parser("init", help="生成初始迁移脚本")

    # 自动生成迁移
    auto_parser = subparsers.add_parser("auto", help="自动生成迁移脚本")
    auto_parser.add_argument("message", help="迁移描述信息")

    # 执行迁移
    upgrade_parser = subparsers.add_parser("upgrade", help="执行数据库迁移")
    upgrade_parser.add_argument(
        "revision", nargs="?", default="head", help="目标修订版本")

    # 回滚迁移
    downgrade_parser = subparsers.add_parser("downgrade", help="回滚数据库迁移")
    downgrade_parser.add_argument("revision", help="目标修订版本")

    # 查看状态
    subparsers.add_parser("status", help="查看迁移状态")
    subparsers.add_parser("history", help="查看迁移历史")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    # 检查环境
    if not os.getenv("DATABASE_URL"):
        print("❌ 未找到DATABASE_URL环境变量")
        return

    # 执行命令
    if args.command == "init":
        init_migration()
    elif args.command == "auto":
        auto_migration(args.message)
    elif args.command == "upgrade":
        upgrade_database(args.revision)
    elif args.command == "downgrade":
        downgrade_database(args.revision)
    elif args.command == "status":
        show_migration_status()
    elif args.command == "history":
        show_migration_history()
    else:
        print(f"❌ 未知命令: {args.command}")


if __name__ == "__main__":
    main()
