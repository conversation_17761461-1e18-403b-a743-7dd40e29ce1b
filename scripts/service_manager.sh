#!/bin/bash
# 微服务管理脚本
# 用法: ./service_manager.sh [start|stop|restart|status] [service|all]
# 注意: 需要预先安装并配置好Conda环境

# 服务列表 - 网关放在最后
SERVICES=("user" "pet" "app" "ocr" "gateway")

# 获取项目根目录的绝对路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_DIR="$PROJECT_ROOT/logs"
PID_DIR="$PROJECT_ROOT/run"

CONDA_ENV="vet_open_platform"  # 您的Conda环境名称

# 检查Conda环境
check_conda_env() {
    # 尝试激活Conda环境
    if ! source $(conda info --base)/etc/profile.d/conda.sh 2>/dev/null; then
        echo "❌ 无法加载Conda环境，请确保Conda已安装并正确配置"
        return 1
    fi
    
    # 检查指定环境是否存在
    if ! conda env list | grep -q $CONDA_ENV; then
        echo "❌ Conda环境 '$CONDA_ENV' 不存在，请创建环境或修改脚本中的环境名称"
        return 1
    fi
    
    # 激活环境
    conda activate $CONDA_ENV
    if [ $? -ne 0 ]; then
        echo "❌ 无法激活Conda环境 '$CONDA_ENV'"
        return 1
    fi
    
    echo "✅ 使用Conda环境: $CONDA_ENV"
    return 0
}

# 检查run_service进程是否存在
check_service_running() {
    local service=$1
    # 使用更精确的匹配模式，支持两种可能的路径
    local running_pid=$(pgrep -f "python.*run_service.py $service" | head -1)
    if [ -n "$running_pid" ]; then
        echo "$running_pid"
        return 0
    else
        return 1
    fi
}

# 创建必要目录
mkdir -p "$LOG_DIR"
mkdir -p "$PID_DIR"

start_service() {
    local service=$1
    local show_logs=${2:-true}  # 默认显示日志，可通过第二个参数控制
    local log_file="$LOG_DIR/${service}_service.log"
    local pid_file="$PID_DIR/${service}_service.pid"

    # 检查run_service进程是否已在运行
    local running_pid=$(check_service_running "$service")
    local check_result=$?

    if [ $check_result -eq 0 ] && [ -n "$running_pid" ]; then
        echo "⚠️ ${service}服务已在运行 (PID: $running_pid)"
        # 更新PID文件为实际运行的进程PID
        echo "$running_pid" > "$pid_file"
        return
    fi

    # 确保激活Conda环境
    if ! check_conda_env; then
        return 1
    fi

    echo "🚀 启动${service}服务..."
    echo "📁 项目目录: $PROJECT_ROOT"
    echo "📄 日志文件: $log_file"

    # 使用 setsid 创建新的会话，完全脱离当前终端
    setsid nohup python scripts/run_service.py $service >> "$log_file" 2>&1 &
    local service_pid=$!
    echo $service_pid > "$pid_file"

    # 等待进程启动并检测是否成功
    echo "⏳ 检测服务启动状态..."
    local max_wait=10  # 最大等待10秒
    local wait_count=0
    local process_started=false
    local actual_pid=""

    while [ $wait_count -lt $max_wait ]; do
        # 使用统一的进程检查函数
        actual_pid=$(check_service_running "$service")
        local check_result=$?

        if [ $check_result -eq 0 ] && [ -n "$actual_pid" ]; then
            process_started=true
            # 更新PID文件为实际的进程PID
            echo $actual_pid > "$pid_file"
            echo "✅ ${service}服务启动成功! PID: $actual_pid, 日志: $log_file"
            service_pid=$actual_pid  # 更新变量用于后续显示
            break
        fi
        sleep 1
        wait_count=$((wait_count + 1))
        echo -n "."
    done
    echo ""

    if [ "$process_started" = false ]; then
        echo "❌ ${service}服务启动失败，进程不存在"
        rm -f "$pid_file"
        echo "📋 查看日志获取错误信息: tail -f $log_file"
        return 1
    fi

    # 根据参数决定是否显示日志
    if [ "$show_logs" = "true" ]; then
        # 进程启动成功后，提供日志查看选项
        echo "📌 正在实时打印日志（按 Ctrl+C 可退出日志查看，服务将继续在后台运行）"
        tail -f "$log_file" &
        local tail_pid=$!

        # 等待用户中断 tail
        trap "kill $tail_pid > /dev/null 2>&1 || true; echo ''; echo '✅ 已退出日志查看，${service}服务继续在后台运行 (PID: $service_pid)'; return 0" INT
        wait $tail_pid 2>/dev/null || true
        trap - INT # 恢复默认行为
    else
        echo "✅ ${service}服务已在后台启动 (PID: $service_pid)"
        echo "💡 使用 '$0 logs $service' 查看实时日志"
    fi
}

stop_service() {
    local service=$1
    local pid_file="$PID_DIR/${service}_service.pid"

    # 首先检查run_service进程是否真的在运行
    local running_pid=$(check_service_running "$service")
    local check_result=$?

    if [ $check_result -eq 0 ] && [ -n "$running_pid" ]; then
        echo "🛑 停止${service}服务 (PID: $running_pid)..."
        kill -9 $running_pid
        rm -f "$pid_file"
        echo "✅ ${service}服务已停止"
    else
        # 如果进程不存在，但PID文件存在，清理PID文件
        if [ -f "$pid_file" ]; then
            echo "⚠️ ${service}服务未运行，清理残留PID文件"
            rm -f "$pid_file"
        else
            echo "⚠️ ${service}服务未运行"
        fi
    fi
}

service_status() {
    local service=$1
    local pid_file="$PID_DIR/${service}_service.pid"

    # 使用统一的进程检查函数
    local running_pid=$(check_service_running "$service")
    local check_result=$?

    # 检查返回值和PID是否都有效
    if [ $check_result -eq 0 ] && [ -n "$running_pid" ]; then
        echo "✅ ${service}服务正在运行 (PID: $running_pid)"
        # 确保PID文件是最新的
        echo "$running_pid" > "$pid_file"
    else
        # 如果进程不存在，但PID文件存在，说明是残留文件
        if [ -f "$pid_file" ]; then
            echo "❌ ${service}服务已停止 (残留PID文件，已清理)"
            rm -f "$pid_file"
        else
            echo "❌ ${service}服务未运行"
        fi
    fi
}

# 显示所有run_service进程
show_all_processes() {
    echo "📋 所有run_service.py进程:"
    echo "----------------------------------------"

    # 查找所有run_service.py进程
    local processes=$(ps -ef | grep "python.*run_service.py" | grep -v grep)

    if [ -z "$processes" ]; then
        echo "❌ 没有发现运行中的run_service.py进程"
    else
        echo "$processes"
        echo ""
        echo "📊 进程统计:"
        for service in "${SERVICES[@]}"; do
            local running_pid=$(check_service_running "$service")
            local check_result=$?

            if [ $check_result -eq 0 ] && [ -n "$running_pid" ]; then
                echo "  ✅ $service: PID $running_pid"
            else
                echo "  ❌ $service: 未运行"
            fi
        done
    fi
}

# 查看服务日志
show_logs() {
    local service=$1
    local log_file="$LOG_DIR/${service}_service.log"

    if [ ! -f "$log_file" ]; then
        echo "❌ 日志文件不存在: $log_file"
        return 1
    fi

    echo "📋 查看${service}服务日志 (按 Ctrl+C 退出):"
    echo "📄 日志文件: $log_file"
    echo "----------------------------------------"

    # 显示最后50行日志，然后实时跟踪
    tail -n 50 -f "$log_file"
}

# 查看所有服务日志
show_all_logs() {
    echo "📋 查看所有服务日志 (按 Ctrl+C 退出):"
    echo "----------------------------------------"

    # 构建所有存在的日志文件列表
    local log_files=()
    for service in "${SERVICES[@]}"; do
        local log_file="$LOG_DIR/${service}_service.log"
        if [ -f "$log_file" ]; then
            log_files+=("$log_file")
        fi
    done

    if [ ${#log_files[@]} -eq 0 ]; then
        echo "❌ 没有找到任何服务日志文件"
        return 1
    fi

    # 使用tail -f同时监控多个日志文件
    tail -n 10 -f "${log_files[@]}"
}

start_all_services() {
    echo "🚀 启动所有微服务 (网关将在最后启动)..."

    # 确保激活Conda环境
    if ! check_conda_env; then
        return 1
    fi

    local failed_services=()

    for service in "${SERVICES[@]}"; do
        # 批量启动时不显示日志，避免阻塞
        start_service "$service" "false"
        local start_result=$?

        if [ $start_result -ne 0 ]; then
            failed_services+=("$service")
        fi

        # 微服务之间短暂延迟
        if [ "$service" != "gateway" ]; then
            sleep 1
        fi
    done

    echo ""
    echo "📊 启动结果汇总:"
    if [ ${#failed_services[@]} -eq 0 ]; then
        echo "✅ 所有服务已成功启动! 网关地址: http://localhost:8000"
        echo ""
        echo "💡 常用命令:"
        echo "  查看所有服务状态: $0 status all"
        echo "  查看特定服务日志: $0 logs <service_name>"
        echo "  查看所有服务日志: $0 logs all"
    else
        echo "⚠️ 部分服务启动失败: ${failed_services[*]}"
        echo "📋 请检查失败服务的日志文件获取详细错误信息"
        for failed_service in "${failed_services[@]}"; do
            echo "  $0 logs $failed_service"
        done
        return 1
    fi
}

stop_all_services() {
    echo "🛑 停止所有微服务..."
    # 反向停止，先停止网关
    for ((idx=${#SERVICES[@]}-1; idx>=0; idx--)); do
        service="${SERVICES[$idx]}"
        stop_service "$service"
    done
    echo "✅ 所有服务已停止"
}

# 主程序入口
main() {
    case "$1" in
        start)
            if [ "$2" == "all" ]; then
                start_all_services
            else
                start_service "$2"
            fi
            ;;
        stop)
            if [ "$2" == "all" ]; then
                stop_all_services
            else
                stop_service "$2"
            fi
            ;;
        restart)
            if [ "$2" == "all" ]; then
                stop_all_services
                sleep 2
                start_all_services
            else
                stop_service "$2"
                sleep 1
                start_service "$2"
            fi
            ;;
        status)
            if [ "$2" == "all" ]; then
                for service in "${SERVICES[@]}"; do
                    service_status "$service"
                done
            else
                service_status "$2"
            fi
            ;;
        logs)
            if [ "$2" == "all" ]; then
                show_all_logs
            elif [ -n "$2" ]; then
                show_logs "$2"
            else
                echo "❌ 请指定服务名称或使用 'all'"
                echo "可用服务: ${SERVICES[*]}"
                exit 1
            fi
            ;;
        ps|processes)
            show_all_processes
            ;;
        *)
            echo "用法: $0 {start|stop|restart|status|logs|ps} {service|all}"
            echo "可用服务: ${SERVICES[*]}"
            echo ""
            echo "命令说明:"
            echo "  start <service|all>  - 启动指定服务或所有服务"
            echo "  stop <service|all>   - 停止指定服务或所有服务"
            echo "  restart <service|all> - 重启指定服务或所有服务"
            echo "  status <service|all> - 查看指定服务或所有服务状态"
            echo "  logs <service|all>   - 查看指定服务或所有服务日志"
            echo "  ps/processes         - 显示所有run_service.py进程"
            echo ""
            echo "示例:"
            echo "  $0 start all         - 启动所有服务（后台运行，不阻塞终端）"
            echo "  $0 start user        - 启动用户服务（显示实时日志）"
            echo "  $0 logs gateway      - 查看网关服务日志"
            echo "  $0 logs all          - 查看所有服务日志"
            exit 1
    esac
}

# 确保能找到run_service.py文件
if [ ! -f "$PROJECT_ROOT/scripts/run_service.py" ]; then
    echo "⚠️ 无法找到run_service.py文件，请检查项目结构"
    echo "项目根目录: $PROJECT_ROOT"
    exit 1
fi

# 切换到项目根目录执行
cd "$PROJECT_ROOT"

# 执行主函数
main "$@"