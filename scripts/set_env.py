#!/usr/bin/env python3
"""
简单的环境设置脚本

用法:
    python scripts/set_env.py development
    python scripts/set_env.py production
    python scripts/set_env.py  # 显示当前环境
"""

import os
import sys
from pathlib import Path

# 确保项目根目录在Python路径中
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))


def show_usage():
    """显示使用说明"""
    print("环境设置脚本")
    print("=" * 30)
    print("用法:")
    print("  python scripts/set_env.py                    # 显示当前环境")
    print("  python scripts/set_env.py development        # 设置为开发环境")
    print("  python scripts/set_env.py testing           # 设置为测试环境")
    print("  python scripts/set_env.py staging           # 设置为预发布环境")
    print("  python scripts/set_env.py production        # 设置为生产环境")
    print()
    print("支持的环境: development, testing, staging, production")


def set_environment(environment: str) -> bool:
    """
    设置环境

    Args:
        environment: 环境名称

    Returns:
        bool: 设置是否成功
    """
    supported_environments = ["development",
                              "testing", "staging", "production"]

    if environment not in supported_environments:
        print(f"❌ 不支持的环境: {environment}")
        print(f"支持的环境: {', '.join(supported_environments)}")
        return False

    # 检查配置文件是否存在
    config_files = [
        project_root / f".env.{environment}"
    ]

    config_file = None
    for file_path in config_files:
        if file_path.exists():
            config_file = file_path
            break

    if not config_file:
        print(f"❌ 找不到 {environment} 环境的配置文件")
        print("请确保以下文件之一存在:")
        for file_path in config_files:
            print(f"  - {file_path}")
        return False

    # 设置环境变量
    os.environ["ENVIRONMENT"] = environment

    print(f"✅ 环境已设置为: {environment}")
    print(f"📁 配置文件: {config_file}")

    # 显示设置环境变量的命令
    print()
    print("💡 要在当前shell中永久设置环境变量，请运行:")
    print(f"   export ENVIRONMENT={environment}")
    print()
    print("💡 要在启动脚本中使用，请在运行前设置:")
    print(f"   ENVIRONMENT={environment} python scripts/start_dev.py")

    return True


def show_current_environment():
    """显示当前环境信息"""
    try:
        from shared.env_loader import load_env

        # 重新加载以获取最新状态
        current_env = load_env()

        print("当前环境信息")
        print("=" * 30)
        print(f"环境名称: {current_env}")

        # 显示配置文件位置
        config_files = [
            project_root / f".env.{current_env}",
            project_root / ".env"
        ]

        for config_file in config_files:
            if config_file.exists():
                print(f"配置文件: {config_file}")
                break

        # 显示可用环境
        available_envs = []
        for env in ["development", "testing", "staging", "production"]:
            env_files = [
                project_root / f".env.{env}"
            ]
            if any(f.exists() for f in env_files):
                available_envs.append(env)

        print(f"\n可用环境:")
        for env in available_envs:
            marker = " (当前)" if env == current_env else ""
            print(f"  - {env}{marker}")

    except ImportError:
        print("❌ 无法加载环境信息，请检查shared/env_loader.py")


def main():
    """主函数"""
    if len(sys.argv) == 1:
        # 没有参数，显示当前环境
        show_current_environment()
    elif len(sys.argv) == 2:
        if sys.argv[1] in ["-h", "--help", "help"]:
            show_usage()
        else:
            environment = sys.argv[1]
            success = set_environment(environment)
            if not success:
                sys.exit(1)
    else:
        print("❌ 参数错误")
        show_usage()
        sys.exit(1)


if __name__ == "__main__":
    main()
