#!/usr/bin/env python3
"""
应用服务启动脚本（通过 run_service 启动）
"""

import sys
from pathlib import Path

# 将项目根目录加入 Python 路径
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 导入 run_service 模块
try:
    from run_service import run_service
except ImportError:
    print("❌ 无法导入 run_service.py，请确保路径正确")
    sys.exit(1)

if __name__ == "__main__":
    print("🚀 通过 run_service 启动应用服务...")
    run_service("pet")
