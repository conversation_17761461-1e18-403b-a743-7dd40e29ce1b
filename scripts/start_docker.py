#!/usr/bin/env python3
"""
Docker启动脚本 - 宠物医疗平台
使用Docker Compose启动所有微服务（默认使用宿主机Redis）
"""

import os
import sys
import subprocess
import time
import signal
import requests
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def print_banner():
    """打印启动横幅"""
    print("=" * 60)
    print("🐳 宠物医疗平台 - Docker启动脚本")
    print("=" * 60)
    print()


def check_docker():
    """检查Docker是否安装并运行"""
    try:
        result = subprocess.run(['docker', '--version'],
                                capture_output=True, text=True, check=True)
        print(f"✅ Docker已安装: {result.stdout.strip()}")

        result = subprocess.run(['docker-compose', '--version'],
                                capture_output=True, text=True, check=True)
        print(f"✅ Docker Compose已安装: {result.stdout.strip()}")

        # 检查Docker守护进程是否运行
        result = subprocess.run(['docker', 'info'],
                                capture_output=True, text=True, check=True)
        print("✅ Docker守护进程正在运行")
        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ Docker检查失败: {e}")
        print("请确保Docker已安装并正在运行")
        return False
    except FileNotFoundError:
        print("❌ Docker未安装，请先安装Docker")
        return False


def check_ports():
    """检查端口是否被占用"""
    ports = [8000, 8001, 8002, 8003]
    occupied_ports = []

    for port in ports:
        try:
            result = subprocess.run(['lsof', '-i', f':{port}'],
                                    capture_output=True, text=True)
            if result.returncode == 0 and result.stdout.strip():
                occupied_ports.append(port)
        except FileNotFoundError:
            # lsof命令不存在，跳过端口检查
            break

    if occupied_ports:
        print(f"⚠️  以下端口被占用: {occupied_ports}")
        print("请停止占用这些端口的进程，或使用 docker-compose down 停止现有容器")
        return False

    print("✅ 所有必需端口都可用")
    return True


def build_images():
    """构建Docker镜像"""
    print("\n🔨 构建Docker镜像...")
    try:
        cmd = ['docker-compose', 'build', '--no-cache']
        result = subprocess.run(cmd, cwd=project_root, check=True)
        print("✅ Docker镜像构建完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Docker镜像构建失败: {e}")
        return False


def start_services():
    """启动所有服务（使用宿主机Redis）"""
    print("\n🚀 启动所有服务 (Redis模式: 宿主机)...")
    try:
        cmd = ['docker-compose', '-f', 'docker-compose.yml', '-f',
               'docker-compose.host-redis.yml', 'up', '-d', '--scale', 'redis=0']
        result = subprocess.run(cmd, cwd=project_root, check=True)
        print("✅ 所有服务已启动")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 服务启动失败: {e}")
        return False


def wait_for_services():
    """等待服务启动完成"""
    print("\n⏳ 等待服务启动完成...")

    services = [
        ("API网关", "http://localhost:8000/health"),
        ("用户服务", "http://localhost:8001/health"),
        ("宠物服务", "http://localhost:8002/health"),
        ("应用服务", "http://localhost:8003/health"),
    ]

    max_retries = 30
    retry_interval = 2

    for service_name, health_url in services:
        print(f"检查 {service_name}...")

        for attempt in range(max_retries):
            try:
                response = requests.get(health_url, timeout=5)
                if response.status_code == 200:
                    print(f"✅ {service_name} 已就绪")
                    break
            except requests.exceptions.RequestException:
                pass

            if attempt < max_retries - 1:
                print(f"   等待中... ({attempt + 1}/{max_retries})")
                time.sleep(retry_interval)
        else:
            print(f"⚠️  {service_name} 启动超时，请检查日志")

    print("\n🎉 所有服务启动检查完成！")


def show_service_info():
    """显示服务信息"""
    print("\n" + "=" * 60)
    print("📋 服务信息")
    print("=" * 60)

    services = [
        ("API网关", "http://localhost:8000", "http://localhost:8000/api/v1/docs"),
        ("用户服务", "http://localhost:8001", "http://localhost:8001/api/v1/docs"),
        ("宠物服务", "http://localhost:8002", "http://localhost:8002/api/v1/docs"),
        ("应用服务", "http://localhost:8003", "http://localhost:8003/api/v1/docs"),
    ]

    for name, url, docs_url in services:
        print(f"🔗 {name}:")
        print(f"   主页: {url}")
        print(f"   API文档: {docs_url}")
        print()

    print("🔧 Redis配置: 宿主机Redis")
    print()

    print("📊 管理命令:")
    print("   查看日志: docker-compose -f docker-compose.yml -f docker-compose.host-redis.yml logs -f")
    print("   停止服务: docker-compose -f docker-compose.yml -f docker-compose.host-redis.yml down")
    print("   重启服务: docker-compose -f docker-compose.yml -f docker-compose.host-redis.yml restart")
    print("   查看状态: docker-compose -f docker-compose.yml -f docker-compose.host-redis.yml ps")
    print()


def show_logs():
    """显示服务日志"""
    print("\n📋 显示服务日志 (按Ctrl+C退出)...")
    try:
        cmd = ['docker-compose', '-f', 'docker-compose.yml',
               '-f', 'docker-compose.host-redis.yml', 'logs', '-f']
        subprocess.run(cmd, cwd=project_root)
    except KeyboardInterrupt:
        print("\n日志查看已停止")


def main():
    """主函数"""
    print_banner()

    # 检查Docker环境
    if not check_docker():
        sys.exit(1)

    # 检查端口
    if not check_ports():
        response = input("是否继续启动? (y/N): ")
        if response.lower() != 'y':
            sys.exit(1)

    # 构建镜像
    if not build_images():
        sys.exit(1)

    # 启动服务
    if not start_services():
        sys.exit(1)

    # 等待服务就绪
    wait_for_services()

    # 显示服务信息
    show_service_info()

    # 询问是否查看日志
    response = input("是否查看实时日志? (y/N): ")
    if response.lower() == 'y':
        show_logs()


if __name__ == "__main__":
    main()
