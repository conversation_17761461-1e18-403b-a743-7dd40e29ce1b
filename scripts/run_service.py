#!/usr/bin/env python3
"""
通用服务启动脚本
用法: python run_service.py <service_name>
支持的服务: user, pet, app, ocr, gateway
"""

import os
import sys
import importlib
from pathlib import Path
import argparse

# 服务配置映射 - 网关放在最后
SERVICE_CONFIG = {
    "user": {
        "name": "用户服务",
        "logger": "shared.logging_config.setup_user_service_logger",
        "app": "services.user_service.main.app",
        "port": 8001
    },
    "pet": {
        "name": "宠物服务",
        "logger": "shared.logging_config.setup_pet_service_logger",
        "app": "services.pet_service.main.app",
        "port": 8002
    },
    "app": {
        "name": "应用服务",
        "logger": "shared.logging_config.setup_app_service_logger",
        "app": "services.app_service.main.app",
        "port": 8003
    },
    "ocr": {
        "name": "OCR服务",
        "logger": "shared.logging_config.setup_ocr_service_logger",
        "app": "services.ocr_service.main.app",
        "port": 8004
    },
    "gateway": {
        "name": "API网关",
        "logger": "shared.logging_config.setup_gateway_logger",
        "app": "gateway.main.app",
        "port": 8000,
        "extra_info": [
            "📊 服务状态: http://localhost:8000/status"
        ]
    }
}


def load_environment(project_root):
    """加载环境变量"""
    try:
        from shared.env_loader import get_current_environment
        current_env = get_current_environment()
        print(f"✅ 环境变量加载成功 (当前环境: {current_env})")
        return current_env
    except ImportError:
        try:
            from dotenv import load_dotenv
            load_dotenv(project_root / ".env")
            print("✅ 环境变量加载成功 (传统模式)")
            return "traditional"
        except ImportError:
            print("⚠️ dotenv未安装，使用系统环境变量")
            return "system"


def import_service_module(import_path):
    """动态导入服务模块"""
    module_path, obj_name = import_path.rsplit('.', 1)
    module = importlib.import_module(module_path)
    return getattr(module, obj_name)


def run_service(service_name):
    """启动指定服务"""
    config = SERVICE_CONFIG.get(service_name)
    if not config:
        print(f"❌ 未知服务: {service_name}")
        sys.exit(1)

    print(f"🚀 启动{config['name']}...")

    # 确保项目根目录在Python路径中
    project_root = Path(__file__).parent.parent
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))

    # 加载环境变量
    load_environment(project_root)

    try:
        # 初始化日志系统
        setup_logger = import_service_module(config["logger"])
        logger = setup_logger()

        # 导入应用
        app = import_service_module(config["app"])
        print(f"✅ {config['name']}应用导入成功")

        # 启动服务
        import uvicorn
        port = config["port"]
        print(f"🌟 {config['name']}启动中...")
        print(f"📍 服务地址: http://localhost:{port}")
        print(f"📖 API文档: http://localhost:{port}/api/v1/docs")
        print(f"❤️ 健康检查: http://localhost:{port}/health")

        # 打印额外信息（网关专用）
        if "extra_info" in config:
            for info in config["extra_info"]:
                print(info)

        uvicorn.run(
            app,
            host="0.0.0.0",
            port=port,
            log_level="info",
            reload=False
        )
    except Exception as e:
        print(f"❌ {config['name']}启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="启动微服务")
    parser.add_argument("service", help="服务名称 (user, pet, app, ocr, gateway)")
    args = parser.parse_args()

    run_service(args.service.lower())
