#!/usr/bin/env python3
"""
宠物医疗平台管理工具

该脚本提供了一系列管理命令，用于：
- 数据库初始化和管理
- 服务启动和停止
- 开发环境设置
- 数据备份和恢复
- 多环境配置管理
"""

import argparse
import asyncio
import os
import subprocess
import sys
from pathlib import Path

# 确保项目根目录在Python路径中
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 加载环境变量 - 使用简化的环境加载器
try:
    from shared.env_loader import get_current_environment
    # 环境配置在导入时已自动加载
    current_env = get_current_environment()
except ImportError:
    # 回退到传统方式
    try:
        from dotenv import load_dotenv
        load_dotenv(project_root / ".env")
    except ImportError:
        pass


def run_script(script_name: str, args: list = None):
    """运行指定的脚本"""
    script_path = project_root / "scripts" / script_name
    if not script_path.exists():
        print(f"❌ 脚本不存在: {script_path}")
        return False

    cmd = [sys.executable, str(script_path)]
    if args:
        cmd.extend(args)

    try:
        result = subprocess.run(cmd, cwd=project_root)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ 运行脚本失败: {e}")
        return False


def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")

    # 检查.env文件
    env_file = project_root / ".env"
    if not env_file.exists():
        print("❌ .env文件不存在")
        return False

    # 检查必要的环境变量
    required_vars = ["DATABASE_URL", "REDIS_URL", "SECRET_KEY"]
    missing_vars = []

    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
        return False

    print("✅ 环境配置检查通过")
    return True


async def test_connections():
    """测试数据库和Redis连接"""
    print("🔍 测试服务连接...")

    try:
        # 测试数据库连接
        from shared.database import AsyncSessionLocal
        from sqlalchemy import text

        async with AsyncSessionLocal() as db:
            await db.execute(text("SELECT 1"))
        print("✅ 数据库连接正常")

        # 测试Redis连接
        from shared.redis_simple import redis_client
        await redis_client.set("test_key", "test_value", expire=10)
        value = await redis_client.get("test_key")
        if value == "test_value":
            print("✅ Redis连接正常")
            await redis_client.delete("test_key")
        else:
            print("❌ Redis连接异常")
            return False

        return True

    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False


def run_script(script_name, args=None):
    """运行指定的脚本"""
    script_path = project_root / "scripts" / script_name
    if not script_path.exists():
        print(f"❌ 脚本不存在: {script_path}")
        return False

    cmd = [sys.executable, str(script_path)]
    if args:
        cmd.extend(args)

    try:
        result = subprocess.run(cmd, check=True, cwd=project_root)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"❌ 脚本执行失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 执行错误: {e}")
        return False


def run_alembic_command(command_args):
    """运行Alembic命令"""
    cmd = ["alembic"] + command_args
    try:
        print(f"🔄 执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True, cwd=project_root,
                                capture_output=False, text=True)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"❌ Alembic命令执行失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 执行错误: {e}")
        return False


def handle_migrate_command(args):
    """处理数据库迁移命令"""
    if not args.migrate_command:
        print("❌ 请指定迁移子命令")
        return

    if args.migrate_command == "init":
        print("🔄 初始化迁移环境...")
        # 检查migrations目录是否已存在
        migrations_dir = project_root / "migrations"
        if migrations_dir.exists():
            print("✅ 迁移环境已存在")
        else:
            success = run_alembic_command(["init", "migrations"])
            if success:
                print("✅ 迁移环境初始化完成")
            else:
                print("❌ 迁移环境初始化失败")

    elif args.migrate_command == "revision":
        print(f"🔄 生成迁移脚本: {args.message}")
        cmd_args = ["revision", "-m", args.message]
        if args.autogenerate:
            cmd_args.append("--autogenerate")
        success = run_alembic_command(cmd_args)
        if success:
            print("✅ 迁移脚本生成完成")
        else:
            print("❌ 迁移脚本生成失败")

    elif args.migrate_command == "upgrade":
        print(f"🔄 执行数据库迁移到: {args.revision}")
        success = run_alembic_command(["upgrade", args.revision])
        if success:
            print("✅ 数据库迁移完成")
        else:
            print("❌ 数据库迁移失败")

    elif args.migrate_command == "downgrade":
        print(f"🔄 回滚数据库迁移到: {args.revision}")
        success = run_alembic_command(["downgrade", args.revision])
        if success:
            print("✅ 数据库回滚完成")
        else:
            print("❌ 数据库回滚失败")

    elif args.migrate_command == "history":
        print("📋 迁移历史:")
        run_alembic_command(["history", "--verbose"])

    elif args.migrate_command == "current":
        print("📍 当前迁移版本:")
        run_alembic_command(["current", "--verbose"])

    elif args.migrate_command == "heads":
        print("🔝 迁移头部版本:")
        run_alembic_command(["heads", "--verbose"])

    elif args.migrate_command == "check":
        print("🔍 检查迁移状态...")
        # 检查是否有待执行的迁移
        try:
            result = subprocess.run(
                ["alembic", "current"],
                capture_output=True, text=True, cwd=project_root
            )
            if result.returncode == 0:
                current_output = result.stdout.strip()

                result = subprocess.run(
                    ["alembic", "heads"],
                    capture_output=True, text=True, cwd=project_root
                )
                if result.returncode == 0:
                    heads_output = result.stdout.strip()

                    if current_output == heads_output:
                        print("✅ 数据库迁移状态正常，无待执行迁移")
                    else:
                        print("⚠️  发现待执行的迁移:")
                        print(f"   当前版本: {current_output}")
                        print(f"   最新版本: {heads_output}")
                        print(
                            "   请运行 'python scripts/manage.py db migrate upgrade' 执行迁移")
                else:
                    print("❌ 无法获取迁移头部版本")
            else:
                print("❌ 无法获取当前迁移版本")
        except Exception as e:
            print(f"❌ 检查迁移状态失败: {e}")

    elif args.migrate_command == "show":
        print(f"📄 显示迁移脚本: {args.revision}")
        run_alembic_command(["show", args.revision])

    else:
        print(f"❌ 未知的迁移命令: {args.migrate_command}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="宠物医疗平台管理工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # 数据库相关命令
    db_parser = subparsers.add_parser("db", help="数据库管理")
    db_subparsers = db_parser.add_subparsers(dest="db_command")

    init_parser = db_subparsers.add_parser("init", help="初始化数据库")
    init_parser.add_argument(
        "--drop-tables", action="store_true", help="删除现有表")
    init_parser.add_argument(
        "--no-sample-data", action="store_true", help="不创建示例数据")
    init_parser.add_argument("--force", action="store_true", help="强制执行")

    db_subparsers.add_parser("test", help="测试数据库连接")
    db_subparsers.add_parser("health", help="检查数据库健康状态")
    db_subparsers.add_parser("fix", help="修复数据库连接问题")

    monitor_parser = db_subparsers.add_parser("monitor", help="启动数据库健康监控")
    monitor_parser.add_argument(
        "--interval", type=int, default=30, help="监控间隔（秒）")

    # 数据库迁移相关命令
    migrate_parser = db_subparsers.add_parser("migrate", help="数据库迁移管理")
    migrate_subparsers = migrate_parser.add_subparsers(dest="migrate_command")

    # 初始化迁移环境
    migrate_subparsers.add_parser("init", help="初始化迁移环境")

    # 生成迁移脚本
    revision_parser = migrate_subparsers.add_parser(
        "revision", help="生成新的迁移脚本")
    revision_parser.add_argument(
        "-m", "--message", required=True, help="迁移描述信息")
    revision_parser.add_argument(
        "--autogenerate", action="store_true", help="自动生成迁移脚本")

    # 执行迁移
    upgrade_parser = migrate_subparsers.add_parser("upgrade", help="执行数据库迁移")
    upgrade_parser.add_argument(
        "revision", nargs="?", default="head", help="目标修订版本")

    # 回滚迁移
    downgrade_parser = migrate_subparsers.add_parser(
        "downgrade", help="回滚数据库迁移")
    downgrade_parser.add_argument("revision", help="目标修订版本")

    # 查看迁移历史
    migrate_subparsers.add_parser("history", help="查看迁移历史")
    migrate_subparsers.add_parser("current", help="查看当前迁移版本")
    migrate_subparsers.add_parser("heads", help="查看迁移头部版本")

    # 迁移状态检查
    migrate_subparsers.add_parser("check", help="检查迁移状态")

    # 显示迁移脚本内容
    show_parser = migrate_subparsers.add_parser("show", help="显示迁移脚本内容")
    show_parser.add_argument("revision", help="修订版本")

    # 服务相关命令
    service_parser = subparsers.add_parser("service", help="服务管理")
    service_subparsers = service_parser.add_subparsers(dest="service_command")

    service_subparsers.add_parser("start", help="启动所有服务")

    start_single_parser = service_subparsers.add_parser(
        "start-single", help="启动单个服务")
    start_single_parser.add_argument(
        "name", choices=["gateway", "user", "pet", "app", "ocr"], help="服务名称")

    # Docker相关命令
    docker_parser = subparsers.add_parser("docker", help="Docker管理")
    docker_subparsers = docker_parser.add_subparsers(dest="docker_command")

    start_docker_parser = docker_subparsers.add_parser(
        "start", help="启动Docker服务 (使用本机Redis)")

    stop_parser = docker_subparsers.add_parser("stop", help="停止Docker服务")
    stop_parser.add_argument(
        "--clean", action="store_true", help="完全清理Docker资源")

    docker_subparsers.add_parser("restart", help="重启Docker服务")
    docker_subparsers.add_parser("build", help="构建Docker镜像")
    docker_subparsers.add_parser("status", help="查看Docker状态")

    logs_parser = docker_subparsers.add_parser("logs", help="查看Docker日志")
    logs_parser.add_argument(
        "--follow", "-f", action="store_true", help="跟踪日志输出")

    # 开发相关命令
    dev_parser = subparsers.add_parser("dev", help="开发工具")
    dev_subparsers = dev_parser.add_subparsers(dest="dev_command")

    dev_subparsers.add_parser("setup", help="设置开发环境")
    dev_subparsers.add_parser("check", help="检查环境配置")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    print("🎯 宠物医疗平台管理工具")
    print("=" * 50)

    # 数据库命令
    if args.command == "db":
        if args.db_command == "init":
            script_args = []
            if args.drop_tables:
                script_args.append("--drop-tables")
            if args.no_sample_data:
                script_args.append("--no-sample-data")
            if args.force:
                script_args.append("--force")

        elif args.db_command == "test":
            if not check_environment():
                sys.exit(1)

            success = asyncio.run(test_connections())
            if success:
                print("✅ 数据库连接测试通过")
            else:
                print("❌ 数据库连接测试失败")
                sys.exit(1)

        elif args.db_command == "health":
            print("🔍 检查数据库健康状态...")
            success = run_script("db_health_monitor.py", ["--mode", "check"])
            if not success:
                sys.exit(1)

        elif args.db_command == "fix":
            print("🔧 修复数据库连接...")
            success = run_script("fix_db_connection.py")
            if not success:
                sys.exit(1)

        elif args.db_command == "migrate":
            handle_migrate_command(args)

        elif args.db_command == "monitor":
            print(f"🔍 启动数据库健康监控 (间隔: {args.interval}秒)...")
            success = run_script("db_health_monitor.py", [
                "--mode", "monitor",
                "--interval", str(args.interval)
            ])
            if not success:
                sys.exit(1)

    # 服务命令
    elif args.command == "service":
        if args.service_command == "start":
            success = run_script("start_dev.py")
            if not success:
                print("❌ 服务启动失败")
                sys.exit(1)

        elif args.service_command == "start-single":
            script_map = {
                "gateway": "start_gateway.py",
                "user": "start_user_service.py",
                "pet": "start_pet_service.py",
                "app": "start_app_service.py",
                "ocr": "start_ocr_service.py"
            }

            script_name = script_map.get(args.name)
            if script_name:
                success = run_script(script_name)
                if not success:
                    print(f"❌ {args.name}服务启动失败")
                    sys.exit(1)
            else:
                print(f"❌ 未知服务: {args.name}")
                sys.exit(1)

    # Docker命令
    elif args.command == "docker":
        if args.docker_command == "start":
            print("🐳 启动Docker服务 (使用外部Redis)...")

            try:
                # 使用外部Redis（docker-compose.yml已配置为使用host.docker.internal）
                cmd = ['docker-compose', 'up', '-d', '--build']

                result = subprocess.run(cmd, cwd=project_root, check=True)
                print("✅ Docker服务启动成功")
                print(f"\n🔗 服务访问地址:")
                print("   🚪 API网关: http://localhost:8000/api/v1/docs (最后启动)")
                print("   👤 用户服务: http://localhost:8001/api/v1/docs")
                print("   🐾 宠物服务: http://localhost:8002/api/v1/docs")
                print("   🤖 应用服务: http://localhost:8003/api/v1/docs")
                print("   🔍 OCR服务: http://localhost:8004/api/v1/docs")
                print(f"\n💡 提示:")
                print("   确保外部Redis服务正在运行: brew services start redis")
                print("   服务启动顺序: User → Pet → App → OCR → Gateway")

            except subprocess.CalledProcessError as e:
                print(f"❌ Docker服务启动失败: {e}")
                print("💡 可能的解决方案:")
                print("   1. 确保外部Redis正在运行: brew services start redis")
                print("   2. 检查端口是否被占用: lsof -i :8000-8003")
                print("   3. 清理Docker资源: python scripts/manage.py docker stop --clean")
                print("   4. 检查服务依赖关系和启动顺序")
                sys.exit(1)

        elif args.docker_command == "stop":
            print("🛑 停止Docker服务...")
            script_args = []
            if args.clean:
                script_args.append("--clean")
            success = run_script("stop_docker.py", script_args)
            if not success:
                print("❌ Docker服务停止失败")
                sys.exit(1)

        elif args.docker_command == "restart":
            print("🔄 重启Docker服务...")
            # 先停止
            success = run_script("stop_docker.py")
            if not success:
                print("❌ Docker服务停止失败")
                sys.exit(1)
            # 再启动
            success = run_script("start_docker.py")
            if not success:
                print("❌ Docker服务启动失败")
                sys.exit(1)

        elif args.docker_command == "build":
            print("🔨 构建Docker镜像...")
            try:
                cmd = ['docker-compose', 'build', '--no-cache']
                result = subprocess.run(cmd, cwd=project_root, check=True)
                print("✅ Docker镜像构建完成")
            except subprocess.CalledProcessError as e:
                print(f"❌ Docker镜像构建失败: {e}")
                sys.exit(1)

        elif args.docker_command == "status":
            print("📊 Docker状态:")
            try:
                cmd = ['docker-compose', 'ps']
                subprocess.run(cmd, cwd=project_root, check=True)
            except subprocess.CalledProcessError as e:
                print(f"❌ 状态查看失败: {e}")
                sys.exit(1)

        elif args.docker_command == "logs":
            print("📋 Docker日志:")
            try:
                cmd = ['docker-compose', 'logs']
                if args.follow:
                    cmd.append('-f')
                subprocess.run(cmd, cwd=project_root, check=True)
            except subprocess.CalledProcessError as e:
                print(f"❌ 日志查看失败: {e}")
                sys.exit(1)
            except KeyboardInterrupt:
                print("\n日志查看已停止")

    # 开发命令
    elif args.command == "dev":
        if args.dev_command == "setup":
            print("🚀 设置开发环境...")

            # 检查环境
            if not check_environment():
                print("❌ 环境检查失败")
                sys.exit(1)

            # 测试连接
            success = asyncio.run(test_connections())
            if not success:
                print("❌ 连接测试失败")
                sys.exit(1)

            print("\n🎉 开发环境设置完成！")
            print("\n📋 下一步:")
            print("   python scripts/manage.py service start")

        elif args.dev_command == "check":
            if check_environment():
                success = asyncio.run(test_connections())
                if success:
                    print("✅ 环境检查全部通过")
                else:
                    print("❌ 连接测试失败")
                    sys.exit(1)
            else:
                print("❌ 环境配置检查失败")
                sys.exit(1)


if __name__ == "__main__":
    main()
