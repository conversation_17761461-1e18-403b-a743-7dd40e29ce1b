"""初始数据库结构 - 微服务架构

修订ID: 53b3f20a1cba
修订时间: 2025-07-25 16:23:46.227379+08:00
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# 修订标识符，由Alembic使用
revision = '53b3f20a1cba'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """升级数据库结构"""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('temporary_api_keys',
    sa.Column('tenant_id', sa.Integer(), nullable=True, comment='所属租户ID'),
    sa.Column('name', sa.String(length=100), nullable=False, comment='临时API密钥名称'),
    sa.Column('temp_key', sa.String(length=100), nullable=False, comment='临时API密钥值（tt_开头）'),
    sa.Column('description', sa.Text(), nullable=True, comment='临时API密钥描述'),
    sa.Column('real_api_key_id', sa.Integer(), nullable=False, comment='关联的真实API密钥ID'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否激活'),
    sa.Column('last_used_at', sa.DateTime(timezone=True), nullable=True, comment='最后使用时间'),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False, comment='过期时间'),
    sa.Column('usage_count', sa.Integer(), nullable=False, comment='使用次数'),
    sa.Column('max_usage_count', sa.Integer(), nullable=True, comment='最大使用次数限制'),
    sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('created_by', sa.String(length=255), nullable=True),
    sa.Column('updated_by', sa.String(length=255), nullable=True),
    sa.Column('created_by_id', sa.Integer(), nullable=True),
    sa.Column('updated_by_id', sa.Integer(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['real_api_key_id'], ['vet.api_keys.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['vet.tenants.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['vet.users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('tenant_id', 'user_id', 'name', name='uq_tenant_user_temp_api_key_name')
    )
    op.create_index('ix_temp_api_key_expires', 'temporary_api_keys', ['expires_at'], unique=False)
    op.create_index('ix_temp_api_key_real_key', 'temporary_api_keys', ['real_api_key_id'], unique=False)
    op.create_index(op.f('ix_temporary_api_keys_id'), 'temporary_api_keys', ['id'], unique=False)
    op.create_index(op.f('ix_temporary_api_keys_temp_key'), 'temporary_api_keys', ['temp_key'], unique=True)
    op.create_index(op.f('ix_temporary_api_keys_tenant_id'), 'temporary_api_keys', ['tenant_id'], unique=False)
    op.create_index('ix_tenant_temp_api_key_active', 'temporary_api_keys', ['tenant_id', 'is_active'], unique=False)
    op.create_index('ix_tenant_user_temp_api_key', 'temporary_api_keys', ['tenant_id', 'user_id'], unique=False)
    op.drop_index('ix_ocr_request_logs_api_key_id', table_name='ocr_request_logs', schema='vet')
    op.drop_index('ix_ocr_request_logs_auth_type', table_name='ocr_request_logs', schema='vet')
    op.drop_index('ix_ocr_request_logs_batch_id', table_name='ocr_request_logs', schema='vet')
    op.drop_index('ix_ocr_request_logs_client_ip', table_name='ocr_request_logs', schema='vet')
    op.drop_index('ix_ocr_request_logs_error_type', table_name='ocr_request_logs', schema='vet')
    op.drop_index('ix_ocr_request_logs_id', table_name='ocr_request_logs', schema='vet')
    op.drop_index('ix_ocr_request_logs_is_successful', table_name='ocr_request_logs', schema='vet')
    op.drop_index('ix_ocr_request_logs_method', table_name='ocr_request_logs', schema='vet')
    op.drop_index('ix_ocr_request_logs_model_type', table_name='ocr_request_logs', schema='vet')
    op.drop_index('ix_ocr_request_logs_path', table_name='ocr_request_logs', schema='vet')
    op.drop_index('ix_ocr_request_logs_request_id', table_name='ocr_request_logs', schema='vet')
    op.drop_index('ix_ocr_request_logs_request_type', table_name='ocr_request_logs', schema='vet')
    op.drop_index('ix_ocr_request_logs_status_code', table_name='ocr_request_logs', schema='vet')
    op.drop_index('ix_ocr_request_logs_task_id', table_name='ocr_request_logs', schema='vet')
    op.drop_index('ix_ocr_request_logs_user_email', table_name='ocr_request_logs', schema='vet')
    op.drop_index('ix_ocr_request_logs_user_id', table_name='ocr_request_logs', schema='vet')
    op.drop_table('ocr_request_logs', schema='vet')
    op.drop_index('ix_ocr_batches_batch_id', table_name='ocr_batches', schema='vet')
    op.drop_index('ix_ocr_batches_id', table_name='ocr_batches', schema='vet')
    op.drop_index('ix_ocr_batches_status', table_name='ocr_batches', schema='vet')
    op.drop_index('ix_ocr_batches_user_email', table_name='ocr_batches', schema='vet')
    op.drop_index('ix_ocr_batches_user_id', table_name='ocr_batches', schema='vet')
    op.drop_table('ocr_batches', schema='vet')
    op.drop_index('ix_temp_api_key_expires', table_name='temporary_api_keys', schema='vet')
    op.drop_index('ix_temp_api_key_real_key', table_name='temporary_api_keys', schema='vet')
    op.drop_index('ix_temporary_api_keys_id', table_name='temporary_api_keys', schema='vet')
    op.drop_index('ix_temporary_api_keys_temp_key', table_name='temporary_api_keys', schema='vet')
    op.drop_index('ix_temporary_api_keys_tenant_id', table_name='temporary_api_keys', schema='vet')
    op.drop_index('ix_tenant_temp_api_key_active', table_name='temporary_api_keys', schema='vet')
    op.drop_index('ix_tenant_user_temp_api_key', table_name='temporary_api_keys', schema='vet')
    op.drop_table('temporary_api_keys', schema='vet')
    op.drop_index('ix_ocr_tasks_external_task_id', table_name='ocr_tasks', schema='vet')
    op.drop_index('ix_ocr_tasks_id', table_name='ocr_tasks', schema='vet')
    op.drop_index('ix_ocr_tasks_status', table_name='ocr_tasks', schema='vet')
    op.drop_index('ix_ocr_tasks_task_id', table_name='ocr_tasks', schema='vet')
    op.drop_index('ix_ocr_tasks_user_id', table_name='ocr_tasks', schema='vet')
    op.drop_table('ocr_tasks', schema='vet')
    op.drop_index('ix_temp_token_active_expires', table_name='temporary_tokens', schema='vet')
    op.drop_index('ix_temp_token_api_key', table_name='temporary_tokens', schema='vet')
    op.drop_index('ix_temp_token_hash', table_name='temporary_tokens', schema='vet')
    op.drop_index('ix_temp_token_tenant', table_name='temporary_tokens', schema='vet')
    op.drop_index('ix_temp_token_user_scope', table_name='temporary_tokens', schema='vet')
    op.drop_index('ix_temporary_tokens_token', table_name='temporary_tokens', schema='vet')
    op.drop_table('temporary_tokens', schema='vet')
    op.alter_column('agent_executions', 'status',
               existing_type=sa.VARCHAR(length=20),
               type_=sa.Enum('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', name='executionstatus'),
               existing_comment='执行状态',
               existing_nullable=False,
               schema='vet')
    op.drop_index('ix_agent_executions_id', table_name='agent_executions', schema='vet')
    op.create_index(op.f('ix_vet_agent_executions_id'), 'agent_executions', ['id'], unique=False, schema='vet')
    op.create_foreign_key(None, 'agent_executions', 'agents', ['agent_id'], ['id'], source_schema='vet', referent_schema='vet')
    op.drop_index('ix_agent_permissions_id', table_name='agent_permissions', schema='vet')
    op.create_index(op.f('ix_vet_agent_permissions_id'), 'agent_permissions', ['id'], unique=False, schema='vet')
    op.create_foreign_key(None, 'agent_permissions', 'agents', ['agent_id'], ['id'], source_schema='vet', referent_schema='vet')
    op.alter_column('agents', 'agent_type',
               existing_type=postgresql.ENUM('SYSTEM', 'CUSTOM', name='agenttype', schema='vet'),
               type_=sa.Enum('SYSTEM', 'CUSTOM', name='agenttype'),
               existing_comment='智能体类型',
               existing_nullable=False,
               schema='vet')
    op.alter_column('agents', 'system_category',
               existing_type=postgresql.ENUM('DIAGNOSIS', 'VISION', 'REPORT_GENERATION', 'REPORT_ANALYSIS', name='systemagentcategory', schema='vet'),
               type_=sa.Enum('DIAGNOSIS', 'VISION', 'REPORT_GENERATION', 'REPORT_ANALYSIS', name='systemagentcategory'),
               existing_comment='系统智能体分类',
               existing_nullable=True,
               schema='vet')
    op.alter_column('agents', 'status',
               existing_type=postgresql.ENUM('DRAFT', 'ACTIVE', 'INACTIVE', 'ARCHIVED', name='agentstatus', schema='vet'),
               server_default=None,
               type_=sa.Enum('DRAFT', 'ACTIVE', 'INACTIVE', 'ARCHIVED', name='agentstatus'),
               existing_comment='状态',
               existing_nullable=False,
               schema='vet')
    op.alter_column('agents', 'is_public',
               existing_type=sa.BOOLEAN(),
               server_default=None,
               existing_comment='是否公开',
               existing_nullable=False,
               schema='vet')
    op.alter_column('agents', 'usage_count',
               existing_type=sa.INTEGER(),
               server_default=None,
               existing_comment='使用次数',
               existing_nullable=False,
               schema='vet')
    op.alter_column('agents', 'is_deleted',
               existing_type=sa.BOOLEAN(),
               server_default=None,
               existing_nullable=False,
               schema='vet')
    op.drop_index('ix_agents_agent_type', table_name='agents', schema='vet')
    op.drop_index('ix_agents_is_deleted', table_name='agents', schema='vet')
    op.drop_index('ix_agents_status', table_name='agents', schema='vet')
    op.drop_index('ix_agents_system_category', table_name='agents', schema='vet')
    op.create_index(op.f('ix_vet_agents_id'), 'agents', ['id'], unique=False, schema='vet')
    op.alter_column('api_keys', 'tenant_id',
               existing_type=sa.INTEGER(),
               comment='所属租户ID',
               existing_nullable=True,
               schema='vet')
    op.alter_column('api_keys', 'name',
               existing_type=sa.VARCHAR(length=100),
               comment='API密钥名称',
               existing_nullable=False,
               schema='vet')
    op.alter_column('api_keys', 'key',
               existing_type=sa.VARCHAR(length=100),
               comment='API密钥值',
               existing_nullable=False,
               schema='vet')
    op.alter_column('api_keys', 'description',
               existing_type=sa.TEXT(),
               comment='API密钥描述',
               existing_nullable=True,
               schema='vet')
    op.alter_column('api_keys', 'is_active',
               existing_type=sa.BOOLEAN(),
               comment='是否激活',
               existing_nullable=False,
               schema='vet')
    op.alter_column('api_keys', 'last_used_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='最后使用时间',
               existing_nullable=True,
               schema='vet')
    op.alter_column('api_keys', 'expires_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='过期时间',
               existing_nullable=True,
               schema='vet')
    op.alter_column('api_keys', 'rate_limit_per_minute',
               existing_type=sa.INTEGER(),
               comment='每分钟请求限制',
               existing_nullable=False,
               schema='vet')
    op.alter_column('api_keys', 'rate_limit_per_day',
               existing_type=sa.INTEGER(),
               comment='每日请求限制',
               existing_nullable=False,
               schema='vet')
    op.alter_column('api_keys', 'user_id',
               existing_type=sa.INTEGER(),
               comment='用户ID',
               existing_nullable=False,
               schema='vet')
    op.drop_constraint('api_keys_key_hash_key', 'api_keys', schema='vet', type_='unique')
    op.drop_index('ix_api_keys_id', table_name='api_keys', schema='vet')
    op.drop_index('ix_api_keys_key', table_name='api_keys', schema='vet')
    op.drop_index('ix_api_keys_key_hash', table_name='api_keys', schema='vet')
    op.drop_index('ix_api_keys_tenant_id', table_name='api_keys', schema='vet')
    op.drop_index('ix_api_keys_tenant_user', table_name='api_keys', schema='vet')
    op.create_index('ix_api_key_expires', 'api_keys', ['expires_at'], unique=False, schema='vet')
    op.create_index('ix_tenant_api_key_active', 'api_keys', ['tenant_id', 'is_active'], unique=False, schema='vet')
    op.create_index('ix_tenant_user_api_key', 'api_keys', ['tenant_id', 'user_id'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_api_keys_id'), 'api_keys', ['id'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_api_keys_key'), 'api_keys', ['key'], unique=True, schema='vet')
    op.create_index(op.f('ix_vet_api_keys_tenant_id'), 'api_keys', ['tenant_id'], unique=False, schema='vet')
    op.create_unique_constraint('uq_tenant_user_api_key_name', 'api_keys', ['tenant_id', 'user_id', 'name'], schema='vet')
    op.drop_column('api_keys', 'key_hash', schema='vet')
    op.alter_column('api_request_logs', 'request_type',
               existing_type=postgresql.ENUM('BUSINESS_API', 'OPENAI_API', 'PUBLIC_API', 'STATIC_RESOURCE', name='requesttype', schema='vet'),
               type_=sa.Enum('BUSINESS_API', 'OPENAI_API', 'PUBLIC_API', 'STATIC_RESOURCE', name='requesttype'),
               existing_comment='请求类型',
               existing_nullable=False,
               schema='vet')
    op.alter_column('api_request_logs', 'auth_type',
               existing_type=postgresql.ENUM('JWT_TOKEN', 'API_KEY', 'PUBLIC', name='authtype', schema='vet'),
               type_=sa.Enum('JWT_TOKEN', 'API_KEY', 'PUBLIC', name='authtype'),
               existing_comment='认证类型',
               existing_nullable=True,
               schema='vet')
    op.drop_index('ix_api_request_logs_api_key_id', table_name='api_request_logs', schema='vet')
    op.drop_index('ix_api_request_logs_auth_type', table_name='api_request_logs', schema='vet')
    op.drop_index('ix_api_request_logs_client_ip', table_name='api_request_logs', schema='vet')
    op.drop_index('ix_api_request_logs_error_type', table_name='api_request_logs', schema='vet')
    op.drop_index('ix_api_request_logs_id', table_name='api_request_logs', schema='vet')
    op.drop_index('ix_api_request_logs_is_successful', table_name='api_request_logs', schema='vet')
    op.drop_index('ix_api_request_logs_method', table_name='api_request_logs', schema='vet')
    op.drop_index('ix_api_request_logs_path', table_name='api_request_logs', schema='vet')
    op.drop_index('ix_api_request_logs_request_id', table_name='api_request_logs', schema='vet')
    op.drop_index('ix_api_request_logs_request_type', table_name='api_request_logs', schema='vet')
    op.drop_index('ix_api_request_logs_status_code', table_name='api_request_logs', schema='vet')
    op.drop_index('ix_api_request_logs_user_email', table_name='api_request_logs', schema='vet')
    op.drop_index('ix_api_request_logs_user_id', table_name='api_request_logs', schema='vet')
    op.create_index(op.f('ix_vet_api_request_logs_api_key_id'), 'api_request_logs', ['api_key_id'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_api_request_logs_auth_type'), 'api_request_logs', ['auth_type'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_api_request_logs_client_ip'), 'api_request_logs', ['client_ip'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_api_request_logs_error_type'), 'api_request_logs', ['error_type'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_api_request_logs_id'), 'api_request_logs', ['id'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_api_request_logs_is_successful'), 'api_request_logs', ['is_successful'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_api_request_logs_method'), 'api_request_logs', ['method'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_api_request_logs_path'), 'api_request_logs', ['path'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_api_request_logs_request_id'), 'api_request_logs', ['request_id'], unique=True, schema='vet')
    op.create_index(op.f('ix_vet_api_request_logs_request_type'), 'api_request_logs', ['request_type'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_api_request_logs_status_code'), 'api_request_logs', ['status_code'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_api_request_logs_user_email'), 'api_request_logs', ['user_email'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_api_request_logs_user_id'), 'api_request_logs', ['user_id'], unique=False, schema='vet')
    op.drop_index('ix_breeds_id', table_name='breeds', schema='vet')
    op.create_index('ix_breed_species_name', 'breeds', ['species', 'name'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_breeds_id'), 'breeds', ['id'], unique=False, schema='vet')
    op.alter_column('conversation_contexts', 'context_type',
               existing_type=sa.INTEGER(),
               comment='上下文类型',
               existing_comment='上下文类型: 1=system_prompt, 2=user_profile, 3=pet_info, 4=medical_history, 5=summary',
               existing_nullable=False,
               schema='vet')
    op.alter_column('conversation_contexts', 'context_value',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_comment='上下文值',
               existing_nullable=False,
               schema='vet')
    op.alter_column('conversation_contexts', 'priority',
               existing_type=sa.INTEGER(),
               server_default=None,
               nullable=True,
               existing_comment='优先级',
               schema='vet')
    op.alter_column('conversation_contexts', 'is_persistent',
               existing_type=sa.BOOLEAN(),
               server_default=None,
               nullable=True,
               existing_comment='是否持久化',
               schema='vet')
    op.alter_column('conversation_contexts', 'version',
               existing_type=sa.INTEGER(),
               server_default=None,
               nullable=True,
               existing_comment='版本号',
               schema='vet')
    op.alter_column('conversation_contexts', 'is_deleted',
               existing_type=sa.BOOLEAN(),
               server_default=None,
               existing_nullable=False,
               schema='vet')
    op.alter_column('conversation_contexts', 'deleted_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='删除时间',
               existing_nullable=True,
               schema='vet')
    op.drop_index('ix_context_deleted', table_name='conversation_contexts', schema='vet', postgresql_where='(is_deleted = false)')
    op.drop_index('ix_context_deleted_at', table_name='conversation_contexts', schema='vet', postgresql_where='(deleted_at IS NOT NULL)')
    op.drop_index('ix_context_persistent', table_name='conversation_contexts', schema='vet')
    op.drop_index('ix_context_type', table_name='conversation_contexts', schema='vet')
    op.create_index('ix_context_conversation_key', 'conversation_contexts', ['conversation_id', 'context_key'], unique=False, schema='vet')
    op.create_index('ix_context_conversation_type', 'conversation_contexts', ['conversation_id', 'context_type'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_conversation_contexts_id'), 'conversation_contexts', ['id'], unique=False, schema='vet')
    op.create_unique_constraint('uq_conversation_context', 'conversation_contexts', ['conversation_id', 'context_type', 'context_key'], schema='vet')
    op.drop_constraint('conversation_contexts_conversation_id_fkey', 'conversation_contexts', schema='vet', type_='foreignkey')
    op.create_foreign_key(None, 'conversation_contexts', 'conversations', ['conversation_id'], ['id'], source_schema='vet', referent_schema='vet')
    op.drop_table_comment(
        'conversation_contexts',
        existing_comment='会话上下文表',
        schema='vet'
    )
    op.alter_column('conversations', 'conversation_type',
               existing_type=sa.INTEGER(),
               server_default=None,
               comment='会话类型',
               existing_comment='会话类型: 1=diagnosis, 2=general',
               existing_nullable=False,
               schema='vet')
    op.alter_column('conversations', 'status',
               existing_type=sa.INTEGER(),
               server_default=None,
               comment='会话状态',
               existing_comment='会话状态: 1=active, 2=paused, 3=completed, 4=archived, 5=expired',
               existing_nullable=False,
               schema='vet')
    op.alter_column('conversations', 'conversation_id',
               existing_type=sa.VARCHAR(length=100),
               comment='会话ID',
               existing_comment='会话唯一标识符',
               existing_nullable=True,
               schema='vet')
    op.alter_column('conversations', 'config',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_comment='会话配置参数',
               existing_nullable=True,
               schema='vet')
    op.alter_column('conversations', 'context_window_size',
               existing_type=sa.INTEGER(),
               server_default=None,
               nullable=True,
               existing_comment='上下文窗口大小',
               schema='vet')
    op.alter_column('conversations', 'max_messages',
               existing_type=sa.INTEGER(),
               server_default=None,
               nullable=True,
               existing_comment='最大消息数量',
               schema='vet')
    op.alter_column('conversations', 'last_activity_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_comment='最后活动时间',
               existing_server_default=sa.text('now()'),
               schema='vet')
    op.alter_column('conversations', 'message_count',
               existing_type=sa.INTEGER(),
               server_default=None,
               nullable=True,
               existing_comment='消息数量',
               schema='vet')
    op.alter_column('conversations', 'total_tokens',
               existing_type=sa.INTEGER(),
               server_default=None,
               nullable=True,
               existing_comment='总Token使用量',
               schema='vet')
    op.alter_column('conversations', 'is_deleted',
               existing_type=sa.BOOLEAN(),
               server_default=None,
               existing_nullable=False,
               schema='vet')
    op.alter_column('conversations', 'deleted_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='删除时间',
               existing_nullable=True,
               schema='vet')
    op.drop_index('ix_conversation_created', table_name='conversations', schema='vet')
    op.drop_index('ix_conversation_deleted', table_name='conversations', schema='vet', postgresql_where='(is_deleted = false)')
    op.drop_index('ix_conversation_deleted_at', table_name='conversations', schema='vet', postgresql_where='(deleted_at IS NOT NULL)')
    op.drop_index('ix_conversation_type_status', table_name='conversations', schema='vet')
    op.drop_index('ix_conversation_conversation_id', table_name='conversations', schema='vet')
    op.create_index('ix_conversation_conversation_id', 'conversations', ['conversation_id'], unique=False, schema='vet')
    op.create_index('ix_conversation_status_type', 'conversations', ['status', 'conversation_type'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_conversations_id'), 'conversations', ['id'], unique=False, schema='vet')
    op.create_unique_constraint(None, 'conversations', ['conversation_id'], schema='vet')
    op.create_foreign_key(None, 'conversations', 'agents', ['primary_agent_id'], ['id'], source_schema='vet', referent_schema='vet')
    op.drop_table_comment(
        'conversations',
        existing_comment='对话会话表',
        schema='vet'
    )
    op.drop_index('ix_medical_records_id', table_name='medical_records', schema='vet')
    op.create_index('ix_medical_record_date', 'medical_records', ['visit_date'], unique=False, schema='vet')
    op.create_index('ix_medical_record_pet_date', 'medical_records', ['pet_id', 'visit_date'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_medical_records_id'), 'medical_records', ['id'], unique=False, schema='vet')
    op.alter_column('messages', 'role',
               existing_type=sa.INTEGER(),
               comment='消息角色',
               existing_comment='消息角色: 1=user, 2=assistant, 3=system',
               existing_nullable=False,
               schema='vet')
    op.alter_column('messages', 'message_type',
               existing_type=sa.INTEGER(),
               server_default=None,
               comment='消息类型',
               existing_comment='消息类型: 1=text, 2=image, 3=file',
               existing_nullable=False,
               schema='vet')
    op.alter_column('messages', 'message_metadata',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_comment='消息元数据',
               existing_nullable=True,
               schema='vet')
    op.alter_column('messages', 'attachments',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               comment='附件信息（图片、文件等）',
               existing_comment='附件信息',
               existing_nullable=True,
               schema='vet')
    op.alter_column('messages', 'ocr_result',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_comment='OCR识别结果',
               existing_nullable=True,
               schema='vet')
    op.alter_column('messages', 'has_ocr_content',
               existing_type=sa.BOOLEAN(),
               server_default=None,
               nullable=True,
               existing_comment='是否包含OCR内容',
               schema='vet')
    op.alter_column('messages', 'status',
               existing_type=sa.INTEGER(),
               server_default=None,
               comment='消息状态',
               existing_comment='消息状态: 1=pending, 2=processing, 3=completed, 4=failed, 5=cancelled',
               existing_nullable=False,
               schema='vet')
    op.alter_column('messages', 'token_usage',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_comment='Token使用统计',
               existing_nullable=True,
               schema='vet')
    op.alter_column('messages', 'retry_count',
               existing_type=sa.INTEGER(),
               server_default=None,
               nullable=True,
               existing_comment='重试次数',
               schema='vet')
    op.alter_column('messages', 'is_deleted',
               existing_type=sa.BOOLEAN(),
               server_default=None,
               existing_nullable=False,
               schema='vet')
    op.alter_column('messages', 'deleted_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='删除时间',
               existing_nullable=True,
               schema='vet')
    op.drop_index('ix_message_deleted', table_name='messages', schema='vet', postgresql_where='(is_deleted = false)')
    op.drop_index('ix_message_deleted_at', table_name='messages', schema='vet', postgresql_where='(deleted_at IS NOT NULL)')
    op.drop_index('ix_message_ocr', table_name='messages', schema='vet', postgresql_where='(has_ocr_content = true)')
    op.drop_index('ix_message_role_type', table_name='messages', schema='vet')
    op.create_index('ix_message_conversation_role', 'messages', ['conversation_id', 'role'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_messages_id'), 'messages', ['id'], unique=False, schema='vet')
    op.drop_constraint('messages_conversation_id_fkey', 'messages', schema='vet', type_='foreignkey')
    op.create_foreign_key(None, 'messages', 'conversations', ['conversation_id'], ['id'], source_schema='vet', referent_schema='vet')
    op.create_foreign_key(None, 'messages', 'agents', ['agent_id'], ['id'], source_schema='vet', referent_schema='vet')
    op.drop_table_comment(
        'messages',
        existing_comment='消息记录表',
        schema='vet'
    )
    op.drop_index('ix_permissions_id', table_name='permissions', schema='vet')
    op.drop_index('ix_permissions_name', table_name='permissions', schema='vet')
    op.create_index(op.f('ix_vet_permissions_id'), 'permissions', ['id'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_permissions_name'), 'permissions', ['name'], unique=True, schema='vet')
    op.alter_column('pets', 'gender',
               existing_type=postgresql.ENUM('MALE', 'FEMALE', 'UNKNOWN', name='petgender', schema='vet'),
               type_=sa.Enum('MALE', 'FEMALE', 'UNKNOWN', name='petgender'),
               existing_comment='性别',
               existing_nullable=True,
               schema='vet')
    op.alter_column('pets', 'status',
               existing_type=postgresql.ENUM('ACTIVE', 'INACTIVE', 'DECEASED', 'LOST', name='petstatus', schema='vet'),
               type_=sa.Enum('ACTIVE', 'INACTIVE', 'DECEASED', 'LOST', name='petstatus'),
               existing_comment='状态',
               existing_nullable=True,
               schema='vet')
    op.drop_index('ix_pets_id', table_name='pets', schema='vet')
    op.create_index('ix_pet_microchip', 'pets', ['microchip_id'], unique=False, schema='vet')
    op.create_index('ix_pet_owner_status', 'pets', ['owner_id', 'status'], unique=False, schema='vet')
    op.create_index('ix_pet_species_breed', 'pets', ['species', 'breed'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_pets_id'), 'pets', ['id'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_pets_owner_id'), 'pets', ['owner_id'], unique=False, schema='vet')
    op.drop_index('ix_tenant_roles_id', table_name='tenant_roles', schema='vet')
    op.create_index(op.f('ix_vet_tenant_roles_id'), 'tenant_roles', ['id'], unique=False, schema='vet')
    op.drop_index('ix_tenants_id', table_name='tenants', schema='vet')
    op.drop_index('ix_tenants_name', table_name='tenants', schema='vet')
    op.drop_index('ix_tenants_slug', table_name='tenants', schema='vet')
    op.create_index(op.f('ix_vet_tenants_id'), 'tenants', ['id'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_tenants_name'), 'tenants', ['name'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_tenants_slug'), 'tenants', ['slug'], unique=True, schema='vet')
    op.alter_column('user_sessions', 'tenant_id',
               existing_type=sa.INTEGER(),
               comment='所属租户ID',
               existing_nullable=True,
               schema='vet')
    op.alter_column('user_sessions', 'session_id',
               existing_type=sa.VARCHAR(length=255),
               comment='会话ID',
               existing_nullable=False,
               schema='vet')
    op.alter_column('user_sessions', 'refresh_token',
               existing_type=sa.VARCHAR(length=500),
               comment='刷新令牌',
               existing_nullable=False,
               schema='vet')
    op.alter_column('user_sessions', 'user_agent',
               existing_type=sa.VARCHAR(length=500),
               comment='用户代理',
               existing_nullable=True,
               schema='vet')
    op.alter_column('user_sessions', 'ip_address',
               existing_type=sa.VARCHAR(length=45),
               comment='IP地址',
               existing_nullable=True,
               schema='vet')
    op.alter_column('user_sessions', 'is_active',
               existing_type=sa.BOOLEAN(),
               comment='是否激活',
               existing_nullable=False,
               schema='vet')
    op.alter_column('user_sessions', 'expires_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='过期时间',
               existing_nullable=False,
               schema='vet')
    op.alter_column('user_sessions', 'last_activity_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='最后活动时间',
               existing_nullable=False,
               schema='vet')
    op.alter_column('user_sessions', 'user_id',
               existing_type=sa.INTEGER(),
               comment='用户ID',
               existing_nullable=False,
               schema='vet')
    op.drop_index('ix_user_sessions_id', table_name='user_sessions', schema='vet')
    op.drop_index('ix_user_sessions_session_id', table_name='user_sessions', schema='vet')
    op.drop_index('ix_user_sessions_tenant_id', table_name='user_sessions', schema='vet')
    op.create_index('ix_session_expires', 'user_sessions', ['expires_at'], unique=False, schema='vet')
    op.create_index('ix_tenant_session_active', 'user_sessions', ['tenant_id', 'is_active'], unique=False, schema='vet')
    op.create_index('ix_tenant_user_session', 'user_sessions', ['tenant_id', 'user_id'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_user_sessions_id'), 'user_sessions', ['id'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_user_sessions_session_id'), 'user_sessions', ['session_id'], unique=True, schema='vet')
    op.create_index(op.f('ix_vet_user_sessions_tenant_id'), 'user_sessions', ['tenant_id'], unique=False, schema='vet')
    op.alter_column('users', 'tenant_id',
               existing_type=sa.INTEGER(),
               comment='所属租户ID',
               existing_nullable=True,
               schema='vet')
    op.alter_column('users', 'email',
               existing_type=sa.VARCHAR(length=255),
               comment='用户邮箱',
               existing_nullable=False,
               schema='vet')
    op.alter_column('users', 'username',
               existing_type=sa.VARCHAR(length=100),
               comment='用户名',
               existing_nullable=False,
               schema='vet')
    op.alter_column('users', 'hashed_password',
               existing_type=sa.VARCHAR(length=255),
               comment='哈希密码',
               existing_nullable=False,
               schema='vet')
    op.alter_column('users', 'tenant_role_id',
               existing_type=sa.INTEGER(),
               comment='租户角色ID',
               existing_nullable=True,
               schema='vet')
    op.drop_index('ix_users_email', table_name='users', schema='vet')
    op.drop_index('ix_users_id', table_name='users', schema='vet')
    op.drop_index('ix_users_tenant_id', table_name='users', schema='vet')
    op.drop_index('ix_users_username', table_name='users', schema='vet')
    op.create_index('ix_tenant_user_active', 'users', ['tenant_id', 'is_active'], unique=False, schema='vet')
    op.create_index('ix_tenant_user_email', 'users', ['tenant_id', 'email'], unique=False, schema='vet')
    op.create_index('ix_tenant_user_username', 'users', ['tenant_id', 'username'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_users_id'), 'users', ['id'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_users_tenant_id'), 'users', ['tenant_id'], unique=False, schema='vet')
    op.create_unique_constraint('uq_tenant_email', 'users', ['tenant_id', 'email'], schema='vet')
    op.create_unique_constraint('uq_tenant_username', 'users', ['tenant_id', 'username'], schema='vet')
    op.drop_index('ix_vaccinations_id', table_name='vaccinations', schema='vet')
    op.create_index('ix_vaccination_due_date', 'vaccinations', ['next_due_date'], unique=False, schema='vet')
    op.create_index('ix_vaccination_pet_date', 'vaccinations', ['pet_id', 'vaccination_date'], unique=False, schema='vet')
    op.create_index('ix_vaccination_vaccine', 'vaccinations', ['vaccine_name'], unique=False, schema='vet')
    op.create_index(op.f('ix_vet_vaccinations_id'), 'vaccinations', ['id'], unique=False, schema='vet')
    # ### end Alembic commands ###


def downgrade() -> None:
    """降级数据库结构"""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_vet_vaccinations_id'), table_name='vaccinations', schema='vet')
    op.drop_index('ix_vaccination_vaccine', table_name='vaccinations', schema='vet')
    op.drop_index('ix_vaccination_pet_date', table_name='vaccinations', schema='vet')
    op.drop_index('ix_vaccination_due_date', table_name='vaccinations', schema='vet')
    op.create_index('ix_vaccinations_id', 'vaccinations', ['id'], unique=False, schema='vet')
    op.drop_constraint('uq_tenant_username', 'users', schema='vet', type_='unique')
    op.drop_constraint('uq_tenant_email', 'users', schema='vet', type_='unique')
    op.drop_index(op.f('ix_vet_users_tenant_id'), table_name='users', schema='vet')
    op.drop_index(op.f('ix_vet_users_id'), table_name='users', schema='vet')
    op.drop_index('ix_tenant_user_username', table_name='users', schema='vet')
    op.drop_index('ix_tenant_user_email', table_name='users', schema='vet')
    op.drop_index('ix_tenant_user_active', table_name='users', schema='vet')
    op.create_index('ix_users_username', 'users', ['username'], unique=True, schema='vet')
    op.create_index('ix_users_tenant_id', 'users', ['tenant_id'], unique=False, schema='vet')
    op.create_index('ix_users_id', 'users', ['id'], unique=False, schema='vet')
    op.create_index('ix_users_email', 'users', ['email'], unique=True, schema='vet')
    op.alter_column('users', 'tenant_role_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='租户角色ID',
               existing_nullable=True,
               schema='vet')
    op.alter_column('users', 'hashed_password',
               existing_type=sa.VARCHAR(length=255),
               comment=None,
               existing_comment='哈希密码',
               existing_nullable=False,
               schema='vet')
    op.alter_column('users', 'username',
               existing_type=sa.VARCHAR(length=100),
               comment=None,
               existing_comment='用户名',
               existing_nullable=False,
               schema='vet')
    op.alter_column('users', 'email',
               existing_type=sa.VARCHAR(length=255),
               comment=None,
               existing_comment='用户邮箱',
               existing_nullable=False,
               schema='vet')
    op.alter_column('users', 'tenant_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='所属租户ID',
               existing_nullable=True,
               schema='vet')
    op.drop_index(op.f('ix_vet_user_sessions_tenant_id'), table_name='user_sessions', schema='vet')
    op.drop_index(op.f('ix_vet_user_sessions_session_id'), table_name='user_sessions', schema='vet')
    op.drop_index(op.f('ix_vet_user_sessions_id'), table_name='user_sessions', schema='vet')
    op.drop_index('ix_tenant_user_session', table_name='user_sessions', schema='vet')
    op.drop_index('ix_tenant_session_active', table_name='user_sessions', schema='vet')
    op.drop_index('ix_session_expires', table_name='user_sessions', schema='vet')
    op.create_index('ix_user_sessions_tenant_id', 'user_sessions', ['tenant_id'], unique=False, schema='vet')
    op.create_index('ix_user_sessions_session_id', 'user_sessions', ['session_id'], unique=True, schema='vet')
    op.create_index('ix_user_sessions_id', 'user_sessions', ['id'], unique=False, schema='vet')
    op.alter_column('user_sessions', 'user_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='用户ID',
               existing_nullable=False,
               schema='vet')
    op.alter_column('user_sessions', 'last_activity_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='最后活动时间',
               existing_nullable=False,
               schema='vet')
    op.alter_column('user_sessions', 'expires_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='过期时间',
               existing_nullable=False,
               schema='vet')
    op.alter_column('user_sessions', 'is_active',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='是否激活',
               existing_nullable=False,
               schema='vet')
    op.alter_column('user_sessions', 'ip_address',
               existing_type=sa.VARCHAR(length=45),
               comment=None,
               existing_comment='IP地址',
               existing_nullable=True,
               schema='vet')
    op.alter_column('user_sessions', 'user_agent',
               existing_type=sa.VARCHAR(length=500),
               comment=None,
               existing_comment='用户代理',
               existing_nullable=True,
               schema='vet')
    op.alter_column('user_sessions', 'refresh_token',
               existing_type=sa.VARCHAR(length=500),
               comment=None,
               existing_comment='刷新令牌',
               existing_nullable=False,
               schema='vet')
    op.alter_column('user_sessions', 'session_id',
               existing_type=sa.VARCHAR(length=255),
               comment=None,
               existing_comment='会话ID',
               existing_nullable=False,
               schema='vet')
    op.alter_column('user_sessions', 'tenant_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='所属租户ID',
               existing_nullable=True,
               schema='vet')
    op.drop_index(op.f('ix_vet_tenants_slug'), table_name='tenants', schema='vet')
    op.drop_index(op.f('ix_vet_tenants_name'), table_name='tenants', schema='vet')
    op.drop_index(op.f('ix_vet_tenants_id'), table_name='tenants', schema='vet')
    op.create_index('ix_tenants_slug', 'tenants', ['slug'], unique=True, schema='vet')
    op.create_index('ix_tenants_name', 'tenants', ['name'], unique=False, schema='vet')
    op.create_index('ix_tenants_id', 'tenants', ['id'], unique=False, schema='vet')
    op.drop_index(op.f('ix_vet_tenant_roles_id'), table_name='tenant_roles', schema='vet')
    op.create_index('ix_tenant_roles_id', 'tenant_roles', ['id'], unique=False, schema='vet')
    op.drop_index(op.f('ix_vet_pets_owner_id'), table_name='pets', schema='vet')
    op.drop_index(op.f('ix_vet_pets_id'), table_name='pets', schema='vet')
    op.drop_index('ix_pet_species_breed', table_name='pets', schema='vet')
    op.drop_index('ix_pet_owner_status', table_name='pets', schema='vet')
    op.drop_index('ix_pet_microchip', table_name='pets', schema='vet')
    op.create_index('ix_pets_id', 'pets', ['id'], unique=False, schema='vet')
    op.alter_column('pets', 'status',
               existing_type=sa.Enum('ACTIVE', 'INACTIVE', 'DECEASED', 'LOST', name='petstatus'),
               type_=postgresql.ENUM('ACTIVE', 'INACTIVE', 'DECEASED', 'LOST', name='petstatus', schema='vet'),
               existing_comment='状态',
               existing_nullable=True,
               schema='vet')
    op.alter_column('pets', 'gender',
               existing_type=sa.Enum('MALE', 'FEMALE', 'UNKNOWN', name='petgender'),
               type_=postgresql.ENUM('MALE', 'FEMALE', 'UNKNOWN', name='petgender', schema='vet'),
               existing_comment='性别',
               existing_nullable=True,
               schema='vet')
    op.drop_index(op.f('ix_vet_permissions_name'), table_name='permissions', schema='vet')
    op.drop_index(op.f('ix_vet_permissions_id'), table_name='permissions', schema='vet')
    op.create_index('ix_permissions_name', 'permissions', ['name'], unique=True, schema='vet')
    op.create_index('ix_permissions_id', 'permissions', ['id'], unique=False, schema='vet')
    op.create_table_comment(
        'messages',
        '消息记录表',
        existing_comment=None,
        schema='vet'
    )
    op.drop_constraint(None, 'messages', schema='vet', type_='foreignkey')
    op.drop_constraint(None, 'messages', schema='vet', type_='foreignkey')
    op.create_foreign_key('messages_conversation_id_fkey', 'messages', 'conversations', ['conversation_id'], ['id'], source_schema='vet', referent_schema='vet', ondelete='CASCADE')
    op.drop_index(op.f('ix_vet_messages_id'), table_name='messages', schema='vet')
    op.drop_index('ix_message_conversation_role', table_name='messages', schema='vet')
    op.create_index('ix_message_role_type', 'messages', ['role', 'message_type'], unique=False, schema='vet')
    op.create_index('ix_message_ocr', 'messages', ['has_ocr_content'], unique=False, schema='vet', postgresql_where='(has_ocr_content = true)')
    op.create_index('ix_message_deleted_at', 'messages', ['deleted_at'], unique=False, schema='vet', postgresql_where='(deleted_at IS NOT NULL)')
    op.create_index('ix_message_deleted', 'messages', ['is_deleted'], unique=False, schema='vet', postgresql_where='(is_deleted = false)')
    op.alter_column('messages', 'deleted_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='删除时间',
               existing_nullable=True,
               schema='vet')
    op.alter_column('messages', 'is_deleted',
               existing_type=sa.BOOLEAN(),
               server_default=sa.text('false'),
               existing_nullable=False,
               schema='vet')
    op.alter_column('messages', 'retry_count',
               existing_type=sa.INTEGER(),
               server_default=sa.text('0'),
               nullable=False,
               existing_comment='重试次数',
               schema='vet')
    op.alter_column('messages', 'token_usage',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_comment='Token使用统计',
               existing_nullable=True,
               schema='vet')
    op.alter_column('messages', 'status',
               existing_type=sa.INTEGER(),
               server_default=sa.text('3'),
               comment='消息状态: 1=pending, 2=processing, 3=completed, 4=failed, 5=cancelled',
               existing_comment='消息状态',
               existing_nullable=False,
               schema='vet')
    op.alter_column('messages', 'has_ocr_content',
               existing_type=sa.BOOLEAN(),
               server_default=sa.text('false'),
               nullable=False,
               existing_comment='是否包含OCR内容',
               schema='vet')
    op.alter_column('messages', 'ocr_result',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_comment='OCR识别结果',
               existing_nullable=True,
               schema='vet')
    op.alter_column('messages', 'attachments',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               comment='附件信息',
               existing_comment='附件信息（图片、文件等）',
               existing_nullable=True,
               schema='vet')
    op.alter_column('messages', 'message_metadata',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_comment='消息元数据',
               existing_nullable=True,
               schema='vet')
    op.alter_column('messages', 'message_type',
               existing_type=sa.INTEGER(),
               server_default=sa.text('1'),
               comment='消息类型: 1=text, 2=image, 3=file',
               existing_comment='消息类型',
               existing_nullable=False,
               schema='vet')
    op.alter_column('messages', 'role',
               existing_type=sa.INTEGER(),
               comment='消息角色: 1=user, 2=assistant, 3=system',
               existing_comment='消息角色',
               existing_nullable=False,
               schema='vet')
    op.drop_index(op.f('ix_vet_medical_records_id'), table_name='medical_records', schema='vet')
    op.drop_index('ix_medical_record_pet_date', table_name='medical_records', schema='vet')
    op.drop_index('ix_medical_record_date', table_name='medical_records', schema='vet')
    op.create_index('ix_medical_records_id', 'medical_records', ['id'], unique=False, schema='vet')
    op.create_table_comment(
        'conversations',
        '对话会话表',
        existing_comment=None,
        schema='vet'
    )
    op.drop_constraint(None, 'conversations', schema='vet', type_='foreignkey')
    op.drop_constraint(None, 'conversations', schema='vet', type_='unique')
    op.drop_index(op.f('ix_vet_conversations_id'), table_name='conversations', schema='vet')
    op.drop_index('ix_conversation_status_type', table_name='conversations', schema='vet')
    op.drop_index('ix_conversation_conversation_id', table_name='conversations', schema='vet')
    op.create_index('ix_conversation_conversation_id', 'conversations', ['conversation_id'], unique=True, schema='vet')
    op.create_index('ix_conversation_type_status', 'conversations', ['conversation_type', 'status'], unique=False, schema='vet')
    op.create_index('ix_conversation_deleted_at', 'conversations', ['deleted_at'], unique=False, schema='vet', postgresql_where='(deleted_at IS NOT NULL)')
    op.create_index('ix_conversation_deleted', 'conversations', ['is_deleted'], unique=False, schema='vet', postgresql_where='(is_deleted = false)')
    op.create_index('ix_conversation_created', 'conversations', ['created_at'], unique=False, schema='vet')
    op.alter_column('conversations', 'deleted_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='删除时间',
               existing_nullable=True,
               schema='vet')
    op.alter_column('conversations', 'is_deleted',
               existing_type=sa.BOOLEAN(),
               server_default=sa.text('false'),
               existing_nullable=False,
               schema='vet')
    op.alter_column('conversations', 'total_tokens',
               existing_type=sa.INTEGER(),
               server_default=sa.text('0'),
               nullable=False,
               existing_comment='总Token使用量',
               schema='vet')
    op.alter_column('conversations', 'message_count',
               existing_type=sa.INTEGER(),
               server_default=sa.text('0'),
               nullable=False,
               existing_comment='消息数量',
               schema='vet')
    op.alter_column('conversations', 'last_activity_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_comment='最后活动时间',
               existing_server_default=sa.text('now()'),
               schema='vet')
    op.alter_column('conversations', 'max_messages',
               existing_type=sa.INTEGER(),
               server_default=sa.text('100'),
               nullable=False,
               existing_comment='最大消息数量',
               schema='vet')
    op.alter_column('conversations', 'context_window_size',
               existing_type=sa.INTEGER(),
               server_default=sa.text('10'),
               nullable=False,
               existing_comment='上下文窗口大小',
               schema='vet')
    op.alter_column('conversations', 'config',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_comment='会话配置参数',
               existing_nullable=True,
               schema='vet')
    op.alter_column('conversations', 'conversation_id',
               existing_type=sa.VARCHAR(length=100),
               comment='会话唯一标识符',
               existing_comment='会话ID',
               existing_nullable=True,
               schema='vet')
    op.alter_column('conversations', 'status',
               existing_type=sa.INTEGER(),
               server_default=sa.text('1'),
               comment='会话状态: 1=active, 2=paused, 3=completed, 4=archived, 5=expired',
               existing_comment='会话状态',
               existing_nullable=False,
               schema='vet')
    op.alter_column('conversations', 'conversation_type',
               existing_type=sa.INTEGER(),
               server_default=sa.text('2'),
               comment='会话类型: 1=diagnosis, 2=general',
               existing_comment='会话类型',
               existing_nullable=False,
               schema='vet')
    op.create_table_comment(
        'conversation_contexts',
        '会话上下文表',
        existing_comment=None,
        schema='vet'
    )
    op.drop_constraint(None, 'conversation_contexts', schema='vet', type_='foreignkey')
    op.create_foreign_key('conversation_contexts_conversation_id_fkey', 'conversation_contexts', 'conversations', ['conversation_id'], ['id'], source_schema='vet', referent_schema='vet', ondelete='CASCADE')
    op.drop_constraint('uq_conversation_context', 'conversation_contexts', schema='vet', type_='unique')
    op.drop_index(op.f('ix_vet_conversation_contexts_id'), table_name='conversation_contexts', schema='vet')
    op.drop_index('ix_context_conversation_type', table_name='conversation_contexts', schema='vet')
    op.drop_index('ix_context_conversation_key', table_name='conversation_contexts', schema='vet')
    op.create_index('ix_context_type', 'conversation_contexts', ['context_type'], unique=False, schema='vet')
    op.create_index('ix_context_persistent', 'conversation_contexts', ['is_persistent'], unique=False, schema='vet')
    op.create_index('ix_context_deleted_at', 'conversation_contexts', ['deleted_at'], unique=False, schema='vet', postgresql_where='(deleted_at IS NOT NULL)')
    op.create_index('ix_context_deleted', 'conversation_contexts', ['is_deleted'], unique=False, schema='vet', postgresql_where='(is_deleted = false)')
    op.alter_column('conversation_contexts', 'deleted_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='删除时间',
               existing_nullable=True,
               schema='vet')
    op.alter_column('conversation_contexts', 'is_deleted',
               existing_type=sa.BOOLEAN(),
               server_default=sa.text('false'),
               existing_nullable=False,
               schema='vet')
    op.alter_column('conversation_contexts', 'version',
               existing_type=sa.INTEGER(),
               server_default=sa.text('1'),
               nullable=False,
               existing_comment='版本号',
               schema='vet')
    op.alter_column('conversation_contexts', 'is_persistent',
               existing_type=sa.BOOLEAN(),
               server_default=sa.text('false'),
               nullable=False,
               existing_comment='是否持久化',
               schema='vet')
    op.alter_column('conversation_contexts', 'priority',
               existing_type=sa.INTEGER(),
               server_default=sa.text('0'),
               nullable=False,
               existing_comment='优先级',
               schema='vet')
    op.alter_column('conversation_contexts', 'context_value',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_comment='上下文值',
               existing_nullable=False,
               schema='vet')
    op.alter_column('conversation_contexts', 'context_type',
               existing_type=sa.INTEGER(),
               comment='上下文类型: 1=system_prompt, 2=user_profile, 3=pet_info, 4=medical_history, 5=summary',
               existing_comment='上下文类型',
               existing_nullable=False,
               schema='vet')
    op.drop_index(op.f('ix_vet_breeds_id'), table_name='breeds', schema='vet')
    op.drop_index('ix_breed_species_name', table_name='breeds', schema='vet')
    op.create_index('ix_breeds_id', 'breeds', ['id'], unique=False, schema='vet')
    op.drop_index(op.f('ix_vet_api_request_logs_user_id'), table_name='api_request_logs', schema='vet')
    op.drop_index(op.f('ix_vet_api_request_logs_user_email'), table_name='api_request_logs', schema='vet')
    op.drop_index(op.f('ix_vet_api_request_logs_status_code'), table_name='api_request_logs', schema='vet')
    op.drop_index(op.f('ix_vet_api_request_logs_request_type'), table_name='api_request_logs', schema='vet')
    op.drop_index(op.f('ix_vet_api_request_logs_request_id'), table_name='api_request_logs', schema='vet')
    op.drop_index(op.f('ix_vet_api_request_logs_path'), table_name='api_request_logs', schema='vet')
    op.drop_index(op.f('ix_vet_api_request_logs_method'), table_name='api_request_logs', schema='vet')
    op.drop_index(op.f('ix_vet_api_request_logs_is_successful'), table_name='api_request_logs', schema='vet')
    op.drop_index(op.f('ix_vet_api_request_logs_id'), table_name='api_request_logs', schema='vet')
    op.drop_index(op.f('ix_vet_api_request_logs_error_type'), table_name='api_request_logs', schema='vet')
    op.drop_index(op.f('ix_vet_api_request_logs_client_ip'), table_name='api_request_logs', schema='vet')
    op.drop_index(op.f('ix_vet_api_request_logs_auth_type'), table_name='api_request_logs', schema='vet')
    op.drop_index(op.f('ix_vet_api_request_logs_api_key_id'), table_name='api_request_logs', schema='vet')
    op.create_index('ix_api_request_logs_user_id', 'api_request_logs', ['user_id'], unique=False, schema='vet')
    op.create_index('ix_api_request_logs_user_email', 'api_request_logs', ['user_email'], unique=False, schema='vet')
    op.create_index('ix_api_request_logs_status_code', 'api_request_logs', ['status_code'], unique=False, schema='vet')
    op.create_index('ix_api_request_logs_request_type', 'api_request_logs', ['request_type'], unique=False, schema='vet')
    op.create_index('ix_api_request_logs_request_id', 'api_request_logs', ['request_id'], unique=True, schema='vet')
    op.create_index('ix_api_request_logs_path', 'api_request_logs', ['path'], unique=False, schema='vet')
    op.create_index('ix_api_request_logs_method', 'api_request_logs', ['method'], unique=False, schema='vet')
    op.create_index('ix_api_request_logs_is_successful', 'api_request_logs', ['is_successful'], unique=False, schema='vet')
    op.create_index('ix_api_request_logs_id', 'api_request_logs', ['id'], unique=False, schema='vet')
    op.create_index('ix_api_request_logs_error_type', 'api_request_logs', ['error_type'], unique=False, schema='vet')
    op.create_index('ix_api_request_logs_client_ip', 'api_request_logs', ['client_ip'], unique=False, schema='vet')
    op.create_index('ix_api_request_logs_auth_type', 'api_request_logs', ['auth_type'], unique=False, schema='vet')
    op.create_index('ix_api_request_logs_api_key_id', 'api_request_logs', ['api_key_id'], unique=False, schema='vet')
    op.alter_column('api_request_logs', 'auth_type',
               existing_type=sa.Enum('JWT_TOKEN', 'API_KEY', 'PUBLIC', name='authtype'),
               type_=postgresql.ENUM('JWT_TOKEN', 'API_KEY', 'PUBLIC', name='authtype', schema='vet'),
               existing_comment='认证类型',
               existing_nullable=True,
               schema='vet')
    op.alter_column('api_request_logs', 'request_type',
               existing_type=sa.Enum('BUSINESS_API', 'OPENAI_API', 'PUBLIC_API', 'STATIC_RESOURCE', name='requesttype'),
               type_=postgresql.ENUM('BUSINESS_API', 'OPENAI_API', 'PUBLIC_API', 'STATIC_RESOURCE', name='requesttype', schema='vet'),
               existing_comment='请求类型',
               existing_nullable=False,
               schema='vet')
    op.add_column('api_keys', sa.Column('key_hash', sa.VARCHAR(length=255), autoincrement=False, nullable=True), schema='vet')
    op.drop_constraint('uq_tenant_user_api_key_name', 'api_keys', schema='vet', type_='unique')
    op.drop_index(op.f('ix_vet_api_keys_tenant_id'), table_name='api_keys', schema='vet')
    op.drop_index(op.f('ix_vet_api_keys_key'), table_name='api_keys', schema='vet')
    op.drop_index(op.f('ix_vet_api_keys_id'), table_name='api_keys', schema='vet')
    op.drop_index('ix_tenant_user_api_key', table_name='api_keys', schema='vet')
    op.drop_index('ix_tenant_api_key_active', table_name='api_keys', schema='vet')
    op.drop_index('ix_api_key_expires', table_name='api_keys', schema='vet')
    op.create_index('ix_api_keys_tenant_user', 'api_keys', ['tenant_id', 'user_id'], unique=False, schema='vet')
    op.create_index('ix_api_keys_tenant_id', 'api_keys', ['tenant_id'], unique=False, schema='vet')
    op.create_index('ix_api_keys_key_hash', 'api_keys', ['key_hash'], unique=False, schema='vet')
    op.create_index('ix_api_keys_key', 'api_keys', ['key'], unique=True, schema='vet')
    op.create_index('ix_api_keys_id', 'api_keys', ['id'], unique=False, schema='vet')
    op.create_unique_constraint('api_keys_key_hash_key', 'api_keys', ['key_hash'], schema='vet')
    op.alter_column('api_keys', 'user_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='用户ID',
               existing_nullable=False,
               schema='vet')
    op.alter_column('api_keys', 'rate_limit_per_day',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='每日请求限制',
               existing_nullable=False,
               schema='vet')
    op.alter_column('api_keys', 'rate_limit_per_minute',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='每分钟请求限制',
               existing_nullable=False,
               schema='vet')
    op.alter_column('api_keys', 'expires_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='过期时间',
               existing_nullable=True,
               schema='vet')
    op.alter_column('api_keys', 'last_used_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='最后使用时间',
               existing_nullable=True,
               schema='vet')
    op.alter_column('api_keys', 'is_active',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='是否激活',
               existing_nullable=False,
               schema='vet')
    op.alter_column('api_keys', 'description',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='API密钥描述',
               existing_nullable=True,
               schema='vet')
    op.alter_column('api_keys', 'key',
               existing_type=sa.VARCHAR(length=100),
               comment=None,
               existing_comment='API密钥值',
               existing_nullable=False,
               schema='vet')
    op.alter_column('api_keys', 'name',
               existing_type=sa.VARCHAR(length=100),
               comment=None,
               existing_comment='API密钥名称',
               existing_nullable=False,
               schema='vet')
    op.alter_column('api_keys', 'tenant_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='所属租户ID',
               existing_nullable=True,
               schema='vet')
    op.drop_index(op.f('ix_vet_agents_id'), table_name='agents', schema='vet')
    op.create_index('ix_agents_system_category', 'agents', ['system_category'], unique=False, schema='vet')
    op.create_index('ix_agents_status', 'agents', ['status'], unique=False, schema='vet')
    op.create_index('ix_agents_is_deleted', 'agents', ['is_deleted'], unique=False, schema='vet')
    op.create_index('ix_agents_agent_type', 'agents', ['agent_type'], unique=False, schema='vet')
    op.alter_column('agents', 'is_deleted',
               existing_type=sa.BOOLEAN(),
               server_default=sa.text('false'),
               existing_nullable=False,
               schema='vet')
    op.alter_column('agents', 'usage_count',
               existing_type=sa.INTEGER(),
               server_default=sa.text('0'),
               existing_comment='使用次数',
               existing_nullable=False,
               schema='vet')
    op.alter_column('agents', 'is_public',
               existing_type=sa.BOOLEAN(),
               server_default=sa.text('false'),
               existing_comment='是否公开',
               existing_nullable=False,
               schema='vet')
    op.alter_column('agents', 'status',
               existing_type=sa.Enum('DRAFT', 'ACTIVE', 'INACTIVE', 'ARCHIVED', name='agentstatus'),
               server_default=sa.text("'ACTIVE'::vet.agentstatus"),
               type_=postgresql.ENUM('DRAFT', 'ACTIVE', 'INACTIVE', 'ARCHIVED', name='agentstatus', schema='vet'),
               existing_comment='状态',
               existing_nullable=False,
               schema='vet')
    op.alter_column('agents', 'system_category',
               existing_type=sa.Enum('DIAGNOSIS', 'VISION', 'REPORT_GENERATION', 'REPORT_ANALYSIS', name='systemagentcategory'),
               type_=postgresql.ENUM('DIAGNOSIS', 'VISION', 'REPORT_GENERATION', 'REPORT_ANALYSIS', name='systemagentcategory', schema='vet'),
               existing_comment='系统智能体分类',
               existing_nullable=True,
               schema='vet')
    op.alter_column('agents', 'agent_type',
               existing_type=sa.Enum('SYSTEM', 'CUSTOM', name='agenttype'),
               type_=postgresql.ENUM('SYSTEM', 'CUSTOM', name='agenttype', schema='vet'),
               existing_comment='智能体类型',
               existing_nullable=False,
               schema='vet')
    op.drop_constraint(None, 'agent_permissions', schema='vet', type_='foreignkey')
    op.drop_index(op.f('ix_vet_agent_permissions_id'), table_name='agent_permissions', schema='vet')
    op.create_index('ix_agent_permissions_id', 'agent_permissions', ['id'], unique=False, schema='vet')
    op.drop_constraint(None, 'agent_executions', schema='vet', type_='foreignkey')
    op.drop_index(op.f('ix_vet_agent_executions_id'), table_name='agent_executions', schema='vet')
    op.create_index('ix_agent_executions_id', 'agent_executions', ['id'], unique=False, schema='vet')
    op.alter_column('agent_executions', 'status',
               existing_type=sa.Enum('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', name='executionstatus'),
               type_=sa.VARCHAR(length=20),
               existing_comment='执行状态',
               existing_nullable=False,
               schema='vet')
    op.create_table('temporary_tokens',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('vet.temporary_tokens_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('token', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('token_hash', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('api_key_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('tenant_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('scope', sa.VARCHAR(length=50), server_default=sa.text("'chat'::character varying"), autoincrement=False, nullable=False),
    sa.Column('context', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), server_default=sa.text('true'), autoincrement=False, nullable=False),
    sa.Column('is_single_use', sa.BOOLEAN(), server_default=sa.text('false'), autoincrement=False, nullable=False),
    sa.Column('used_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('expires_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('usage_count', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False),
    sa.Column('max_usage', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('client_ip', sa.VARCHAR(length=45), autoincrement=False, nullable=True),
    sa.Column('user_agent', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('is_deleted', sa.BOOLEAN(), server_default=sa.text('false'), autoincrement=False, nullable=False),
    sa.Column('deleted_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('created_by', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('updated_by', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('created_by_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('updated_by_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['api_key_id'], ['vet.api_keys.id'], name='temporary_tokens_api_key_id_fkey'),
    sa.ForeignKeyConstraint(['tenant_id'], ['vet.tenants.id'], name='temporary_tokens_tenant_id_fkey'),
    sa.ForeignKeyConstraint(['user_id'], ['vet.users.id'], name='temporary_tokens_user_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='temporary_tokens_pkey'),
    sa.UniqueConstraint('token', name='temporary_tokens_token_key'),
    sa.UniqueConstraint('token_hash', name='temporary_tokens_token_hash_key'),
    schema='vet'
    )
    op.create_index('ix_temporary_tokens_token', 'temporary_tokens', ['token'], unique=False, schema='vet')
    op.create_index('ix_temp_token_user_scope', 'temporary_tokens', ['user_id', 'scope'], unique=False, schema='vet')
    op.create_index('ix_temp_token_tenant', 'temporary_tokens', ['tenant_id'], unique=False, schema='vet')
    op.create_index('ix_temp_token_hash', 'temporary_tokens', ['token_hash'], unique=False, schema='vet')
    op.create_index('ix_temp_token_api_key', 'temporary_tokens', ['api_key_id'], unique=False, schema='vet')
    op.create_index('ix_temp_token_active_expires', 'temporary_tokens', ['is_active', 'expires_at'], unique=False, schema='vet')
    op.create_table('ocr_tasks',
    sa.Column('task_id', sa.UUID(), autoincrement=False, nullable=True, comment='任务唯一标识'),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False, comment='用户ID'),
    sa.Column('status', postgresql.ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', 'TIMEOUT', name='taskstatus', schema='vet'), autoincrement=False, nullable=False, comment='任务状态'),
    sa.Column('priority', postgresql.ENUM('LOW', 'NORMAL', 'HIGH', 'URGENT', name='taskpriority', schema='vet'), autoincrement=False, nullable=False, comment='任务优先级'),
    sa.Column('file_path', sa.VARCHAR(length=500), autoincrement=False, nullable=True, comment='上传文件路径'),
    sa.Column('file_url', sa.VARCHAR(length=1000), autoincrement=False, nullable=True, comment='文件URL'),
    sa.Column('file_name', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='原始文件名'),
    sa.Column('file_size', sa.INTEGER(), autoincrement=False, nullable=True, comment='文件大小(字节)'),
    sa.Column('file_type', sa.VARCHAR(length=50), autoincrement=False, nullable=True, comment='文件类型'),
    sa.Column('lang', sa.VARCHAR(length=20), autoincrement=False, nullable=True, comment='语言设置'),
    sa.Column('parse_method', sa.VARCHAR(length=50), autoincrement=False, nullable=True, comment='解析方法'),
    sa.Column('formula_enable', sa.BOOLEAN(), autoincrement=False, nullable=True, comment='是否启用公式识别'),
    sa.Column('table_enable', sa.BOOLEAN(), autoincrement=False, nullable=True, comment='是否启用表格识别'),
    sa.Column('dump_md', sa.BOOLEAN(), autoincrement=False, nullable=True, comment='是否输出Markdown格式'),
    sa.Column('dump_images', sa.BOOLEAN(), autoincrement=False, nullable=True, comment='是否保存图片'),
    sa.Column('save_to_disk', sa.BOOLEAN(), autoincrement=False, nullable=True, comment='是否保存到磁盘'),
    sa.Column('output_dir', sa.VARCHAR(length=500), autoincrement=False, nullable=True, comment='输出目录'),
    sa.Column('gpu_id', sa.INTEGER(), autoincrement=False, nullable=True, comment='GPU ID'),
    sa.Column('external_task_id', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='外部OCR服务任务ID'),
    sa.Column('started_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True, comment='开始处理时间'),
    sa.Column('completed_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True, comment='完成时间'),
    sa.Column('processing_time', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='处理耗时(秒)'),
    sa.Column('result_data', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='OCR结果数据'),
    sa.Column('result_text', sa.TEXT(), autoincrement=False, nullable=True, comment='识别的文本内容'),
    sa.Column('result_markdown', sa.TEXT(), autoincrement=False, nullable=True, comment='Markdown格式结果'),
    sa.Column('result_images', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='提取的图片信息'),
    sa.Column('error_message', sa.TEXT(), autoincrement=False, nullable=True, comment='错误信息'),
    sa.Column('error_code', sa.VARCHAR(length=50), autoincrement=False, nullable=True, comment='错误代码'),
    sa.Column('retry_count', sa.INTEGER(), autoincrement=False, nullable=True, comment='重试次数'),
    sa.Column('page_count', sa.INTEGER(), autoincrement=False, nullable=True, comment='页面数量'),
    sa.Column('character_count', sa.INTEGER(), autoincrement=False, nullable=True, comment='字符数量'),
    sa.Column('confidence_score', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='识别置信度'),
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('vet.ocr_tasks_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('created_by', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('updated_by', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('created_by_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('updated_by_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('is_deleted', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('deleted_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='ocr_tasks_pkey'),
    schema='vet'
    )
    op.create_index('ix_ocr_tasks_user_id', 'ocr_tasks', ['user_id'], unique=False, schema='vet')
    op.create_index('ix_ocr_tasks_task_id', 'ocr_tasks', ['task_id'], unique=True, schema='vet')
    op.create_index('ix_ocr_tasks_status', 'ocr_tasks', ['status'], unique=False, schema='vet')
    op.create_index('ix_ocr_tasks_id', 'ocr_tasks', ['id'], unique=False, schema='vet')
    op.create_index('ix_ocr_tasks_external_task_id', 'ocr_tasks', ['external_task_id'], unique=False, schema='vet')
    op.create_table('temporary_api_keys',
    sa.Column('tenant_id', sa.INTEGER(), autoincrement=False, nullable=True, comment='所属租户ID'),
    sa.Column('name', sa.VARCHAR(length=100), autoincrement=False, nullable=False, comment='临时API密钥名称'),
    sa.Column('temp_key', sa.VARCHAR(length=100), autoincrement=False, nullable=False, comment='临时API密钥值（tt_开头）'),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True, comment='临时API密钥描述'),
    sa.Column('real_api_key_id', sa.INTEGER(), autoincrement=False, nullable=False, comment='关联的真实API密钥ID'),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=False, comment='是否激活'),
    sa.Column('last_used_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True, comment='最后使用时间'),
    sa.Column('expires_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False, comment='过期时间'),
    sa.Column('usage_count', sa.INTEGER(), autoincrement=False, nullable=False, comment='使用次数'),
    sa.Column('max_usage_count', sa.INTEGER(), autoincrement=False, nullable=True, comment='最大使用次数限制'),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False, comment='用户ID'),
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('vet.temporary_api_keys_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('created_by', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('updated_by', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('created_by_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('updated_by_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('is_deleted', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('deleted_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['real_api_key_id'], ['vet.api_keys.id'], name='temporary_api_keys_real_api_key_id_fkey'),
    sa.ForeignKeyConstraint(['tenant_id'], ['vet.tenants.id'], name='temporary_api_keys_tenant_id_fkey'),
    sa.ForeignKeyConstraint(['user_id'], ['vet.users.id'], name='temporary_api_keys_user_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='temporary_api_keys_pkey'),
    sa.UniqueConstraint('tenant_id', 'user_id', 'name', name='uq_tenant_user_temp_api_key_name'),
    schema='vet'
    )
    op.create_index('ix_tenant_user_temp_api_key', 'temporary_api_keys', ['tenant_id', 'user_id'], unique=False, schema='vet')
    op.create_index('ix_tenant_temp_api_key_active', 'temporary_api_keys', ['tenant_id', 'is_active'], unique=False, schema='vet')
    op.create_index('ix_temporary_api_keys_tenant_id', 'temporary_api_keys', ['tenant_id'], unique=False, schema='vet')
    op.create_index('ix_temporary_api_keys_temp_key', 'temporary_api_keys', ['temp_key'], unique=True, schema='vet')
    op.create_index('ix_temporary_api_keys_id', 'temporary_api_keys', ['id'], unique=False, schema='vet')
    op.create_index('ix_temp_api_key_real_key', 'temporary_api_keys', ['real_api_key_id'], unique=False, schema='vet')
    op.create_index('ix_temp_api_key_expires', 'temporary_api_keys', ['expires_at'], unique=False, schema='vet')
    op.create_table('ocr_batches',
    sa.Column('batch_id', sa.UUID(), autoincrement=False, nullable=False, comment='批量任务唯一ID'),
    sa.Column('batch_name', sa.VARCHAR(length=200), autoincrement=False, nullable=True, comment='批量任务名称'),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True, comment='批量任务描述'),
    sa.Column('status', postgresql.ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'PARTIAL_FAILED', 'FAILED', 'CANCELLED', name='ocrbatchstatus', schema='vet'), autoincrement=False, nullable=False, comment='批量任务状态'),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False, comment='用户ID'),
    sa.Column('user_email', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='用户邮箱'),
    sa.Column('model_type', postgresql.ENUM('QWEN_VL', 'TESSERACT', 'PADDLE_OCR', 'CUSTOM', name='ocrmodeltype', schema='vet'), autoincrement=False, nullable=False, comment='OCR模型类型'),
    sa.Column('model_name', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='具体模型名称'),
    sa.Column('model_config', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='模型配置参数'),
    sa.Column('total_images', sa.INTEGER(), autoincrement=False, nullable=True, comment='总图片数量'),
    sa.Column('processed_images', sa.INTEGER(), autoincrement=False, nullable=True, comment='已处理图片数量'),
    sa.Column('successful_images', sa.INTEGER(), autoincrement=False, nullable=True, comment='成功处理图片数量'),
    sa.Column('failed_images', sa.INTEGER(), autoincrement=False, nullable=True, comment='失败图片数量'),
    sa.Column('prompt', sa.TEXT(), autoincrement=False, nullable=True, comment='OCR提示词'),
    sa.Column('language', sa.VARCHAR(length=20), autoincrement=False, nullable=True, comment='识别语言'),
    sa.Column('confidence_threshold', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='置信度阈值'),
    sa.Column('parallel_count', sa.INTEGER(), autoincrement=False, nullable=True, comment='并行处理数量'),
    sa.Column('started_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True, comment='开始处理时间'),
    sa.Column('completed_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True, comment='完成时间'),
    sa.Column('estimated_completion_time', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True, comment='预计完成时间'),
    sa.Column('progress_percentage', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='进度百分比'),
    sa.Column('current_processing_image', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='当前处理的图片'),
    sa.Column('total_processing_time_ms', sa.INTEGER(), autoincrement=False, nullable=True, comment='总处理耗时(毫秒)'),
    sa.Column('average_processing_time_ms', sa.INTEGER(), autoincrement=False, nullable=True, comment='平均处理耗时(毫秒)'),
    sa.Column('total_extracted_characters', sa.INTEGER(), autoincrement=False, nullable=True, comment='总提取字符数'),
    sa.Column('average_confidence_score', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='平均置信度分数'),
    sa.Column('error_summary', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='错误汇总'),
    sa.Column('extra_metadata', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='额外元数据'),
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('vet.ocr_batches_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('created_by', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('updated_by', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('created_by_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('updated_by_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('is_deleted', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('deleted_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='ocr_batches_pkey'),
    schema='vet'
    )
    op.create_index('ix_ocr_batches_user_id', 'ocr_batches', ['user_id'], unique=False, schema='vet')
    op.create_index('ix_ocr_batches_user_email', 'ocr_batches', ['user_email'], unique=False, schema='vet')
    op.create_index('ix_ocr_batches_status', 'ocr_batches', ['status'], unique=False, schema='vet')
    op.create_index('ix_ocr_batches_id', 'ocr_batches', ['id'], unique=False, schema='vet')
    op.create_index('ix_ocr_batches_batch_id', 'ocr_batches', ['batch_id'], unique=True, schema='vet')
    op.create_table('ocr_request_logs',
    sa.Column('request_id', sa.UUID(), autoincrement=False, nullable=False, comment='请求唯一ID'),
    sa.Column('request_start_time', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False, comment='请求开始时间'),
    sa.Column('request_end_time', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True, comment='请求结束时间'),
    sa.Column('processing_time_ms', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='处理耗时(毫秒)'),
    sa.Column('method', sa.VARCHAR(length=10), autoincrement=False, nullable=False, comment='HTTP方法'),
    sa.Column('path', sa.VARCHAR(length=500), autoincrement=False, nullable=False, comment='请求路径'),
    sa.Column('full_url', sa.TEXT(), autoincrement=False, nullable=True, comment='完整URL'),
    sa.Column('query_params', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='查询参数'),
    sa.Column('request_type', postgresql.ENUM('SINGLE_IMAGE', 'BATCH_IMAGES', 'TASK_QUERY', 'MODEL_LIST', name='ocrrequesttype', schema='vet'), autoincrement=False, nullable=False, comment='OCR请求类型'),
    sa.Column('client_ip', postgresql.INET(), autoincrement=False, nullable=True, comment='客户端IP地址'),
    sa.Column('user_agent', sa.TEXT(), autoincrement=False, nullable=True, comment='用户代理'),
    sa.Column('referer', sa.VARCHAR(length=500), autoincrement=False, nullable=True, comment='来源页面'),
    sa.Column('auth_type', postgresql.ENUM('JWT_TOKEN', 'API_KEY', 'PUBLIC', name='ocrauthtype', schema='vet'), autoincrement=False, nullable=True, comment='认证类型'),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=True, comment='用户ID'),
    sa.Column('user_email', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='用户邮箱'),
    sa.Column('api_key_id', sa.INTEGER(), autoincrement=False, nullable=True, comment='API密钥ID'),
    sa.Column('api_key_name', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='API密钥名称'),
    sa.Column('model_type', sa.VARCHAR(length=50), autoincrement=False, nullable=True, comment='使用的OCR模型类型'),
    sa.Column('model_name', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='具体模型名称'),
    sa.Column('image_count', sa.INTEGER(), autoincrement=False, nullable=True, comment='处理的图片数量'),
    sa.Column('total_image_size', sa.INTEGER(), autoincrement=False, nullable=True, comment='图片总大小(字节)'),
    sa.Column('content_type', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='请求内容类型'),
    sa.Column('content_length', sa.INTEGER(), autoincrement=False, nullable=True, comment='请求内容长度'),
    sa.Column('status_code', sa.INTEGER(), autoincrement=False, nullable=True, comment='HTTP状态码'),
    sa.Column('response_content_type', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='响应内容类型'),
    sa.Column('response_content_length', sa.INTEGER(), autoincrement=False, nullable=True, comment='响应内容长度'),
    sa.Column('extracted_text_length', sa.INTEGER(), autoincrement=False, nullable=True, comment='提取文本长度'),
    sa.Column('confidence_score', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='OCR置信度分数'),
    sa.Column('character_count', sa.INTEGER(), autoincrement=False, nullable=True, comment='识别字符数'),
    sa.Column('error_type', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='错误类型'),
    sa.Column('error_message', sa.TEXT(), autoincrement=False, nullable=True, comment='错误消息'),
    sa.Column('error_code', sa.VARCHAR(length=50), autoincrement=False, nullable=True, comment='错误代码'),
    sa.Column('is_successful', sa.BOOLEAN(), autoincrement=False, nullable=True, comment='请求是否成功'),
    sa.Column('is_cached', sa.BOOLEAN(), autoincrement=False, nullable=True, comment='响应是否来自缓存'),
    sa.Column('task_id', sa.UUID(), autoincrement=False, nullable=True, comment='关联的OCR任务ID'),
    sa.Column('batch_id', sa.UUID(), autoincrement=False, nullable=True, comment='关联的批量任务ID'),
    sa.Column('billing_units', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='计费单位数'),
    sa.Column('cost_amount', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='费用金额'),
    sa.Column('extra_metadata', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='额外的元数据信息'),
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('vet.ocr_request_logs_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='ocr_request_logs_pkey'),
    schema='vet'
    )
    op.create_index('ix_ocr_request_logs_user_id', 'ocr_request_logs', ['user_id'], unique=False, schema='vet')
    op.create_index('ix_ocr_request_logs_user_email', 'ocr_request_logs', ['user_email'], unique=False, schema='vet')
    op.create_index('ix_ocr_request_logs_task_id', 'ocr_request_logs', ['task_id'], unique=False, schema='vet')
    op.create_index('ix_ocr_request_logs_status_code', 'ocr_request_logs', ['status_code'], unique=False, schema='vet')
    op.create_index('ix_ocr_request_logs_request_type', 'ocr_request_logs', ['request_type'], unique=False, schema='vet')
    op.create_index('ix_ocr_request_logs_request_id', 'ocr_request_logs', ['request_id'], unique=True, schema='vet')
    op.create_index('ix_ocr_request_logs_path', 'ocr_request_logs', ['path'], unique=False, schema='vet')
    op.create_index('ix_ocr_request_logs_model_type', 'ocr_request_logs', ['model_type'], unique=False, schema='vet')
    op.create_index('ix_ocr_request_logs_method', 'ocr_request_logs', ['method'], unique=False, schema='vet')
    op.create_index('ix_ocr_request_logs_is_successful', 'ocr_request_logs', ['is_successful'], unique=False, schema='vet')
    op.create_index('ix_ocr_request_logs_id', 'ocr_request_logs', ['id'], unique=False, schema='vet')
    op.create_index('ix_ocr_request_logs_error_type', 'ocr_request_logs', ['error_type'], unique=False, schema='vet')
    op.create_index('ix_ocr_request_logs_client_ip', 'ocr_request_logs', ['client_ip'], unique=False, schema='vet')
    op.create_index('ix_ocr_request_logs_batch_id', 'ocr_request_logs', ['batch_id'], unique=False, schema='vet')
    op.create_index('ix_ocr_request_logs_auth_type', 'ocr_request_logs', ['auth_type'], unique=False, schema='vet')
    op.create_index('ix_ocr_request_logs_api_key_id', 'ocr_request_logs', ['api_key_id'], unique=False, schema='vet')
    op.drop_index('ix_tenant_user_temp_api_key', table_name='temporary_api_keys')
    op.drop_index('ix_tenant_temp_api_key_active', table_name='temporary_api_keys')
    op.drop_index(op.f('ix_temporary_api_keys_tenant_id'), table_name='temporary_api_keys')
    op.drop_index(op.f('ix_temporary_api_keys_temp_key'), table_name='temporary_api_keys')
    op.drop_index(op.f('ix_temporary_api_keys_id'), table_name='temporary_api_keys')
    op.drop_index('ix_temp_api_key_real_key', table_name='temporary_api_keys')
    op.drop_index('ix_temp_api_key_expires', table_name='temporary_api_keys')
    op.drop_table('temporary_api_keys')
    # ### end Alembic commands ###
