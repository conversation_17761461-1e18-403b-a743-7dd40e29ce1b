"""重命名agents表name字段为agent_name

修订ID: a97eaa753ec1
修订时间: 2025-07-25 16:55:07.509563+08:00
"""
from alembic import op
import sqlalchemy as sa


# 修订标识符，由Alembic使用
revision = 'a97eaa753ec1'
down_revision = '53b3f20a1cba'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """升级数据库结构"""
    # 重命名agents表的name字段为agent_name
    # 使用ALTER TABLE RENAME COLUMN语句
    op.execute('ALTER TABLE vet.agents RENAME COLUMN name TO agent_name')


def downgrade() -> None:
    """降级数据库结构"""
    # 回滚：将agent_name字段重命名回name
    op.execute('ALTER TABLE vet.agents RENAME COLUMN agent_name TO name')
