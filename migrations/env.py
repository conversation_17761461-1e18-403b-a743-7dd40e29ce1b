"""
宠物医疗平台数据库迁移环境配置

该文件配置Alembic迁移环境，支持：
- 微服务架构下的统一数据库迁移
- 自动发现所有微服务的数据库模型
- 环境变量配置支持
- 开发、测试、生产环境适配
"""
from shared.database import get_async_database_url, Base
from sqlalchemy import text
import asyncio
import os
import sys
from logging.config import fileConfig
from pathlib import Path

from sqlalchemy import pool
from sqlalchemy.engine import Connection
from sqlalchemy.ext.asyncio import async_engine_from_config

from alembic import context

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 加载环境变量
try:
    from shared.env_loader import get_current_environment
    # 环境配置在导入时已自动加载
    current_env = get_current_environment()
except ImportError:
    # 回退到传统方式
    try:
        from dotenv import load_dotenv
        load_dotenv(project_root / ".env")
    except ImportError:
        pass

# 导入数据库配置

# 导入所有微服务的数据库模型以确保它们被注册到Base.metadata
# User Service 模型
try:
    from services.user_service.models.user import User, APIKey, TemporaryAPIKey, UserSession
    from services.user_service.models.permission import Permission
    from services.user_service.models.tenant import Tenant, TenantRole
except ImportError as e:
    print(f"警告: 无法导入User Service模型: {e}")

# Pet Service 模型
try:
    from services.pet_service.models.pet import Pet, MedicalRecord, Vaccination, Breed
except ImportError as e:
    print(f"警告: 无法导入Pet Service模型: {e}")

# App Service 模型
try:
    from services.app_service.models.agent import Agent, AgentPermission, AgentExecution
    from services.app_service.models.conversation import Conversation, Message, ConversationContext
except ImportError as e:
    print(f"警告: 无法导入App Service模型: {e}")

# Gateway 模型
try:
    from gateway.models.request_log import APIRequestLog
except ImportError as e:
    print(f"警告: 无法导入Gateway模型: {e}")

# Alembic配置对象
config = context.config

# 解释日志配置文件
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# 设置目标元数据
target_metadata = Base.metadata

# 其他值从config对象获取，这些值在env.ini中定义
# 可以在这里访问，例如：
# my_important_option = config.get_main_option("my_important_option")
# ... 等等


def get_database_url():
    """获取数据库URL，优先使用环境变量"""
    # 首先尝试从环境变量获取
    database_url = os.getenv("DATABASE_URL")
    if database_url:
        return database_url

    # 回退到配置文件中的URL
    return config.get_main_option("sqlalchemy.url")


def run_migrations_offline() -> None:
    """在'离线'模式下运行迁移。

    这将配置上下文仅使用URL而不是Engine，
    尽管这里也需要一个Engine，但我们不创建连接。
    通过跳过Engine创建，我们甚至不需要DBAPI可用。

    调用context.execute()将发出给定的字符串到脚本输出。
    """
    url = get_database_url()
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        # 配置schema搜索路径
        version_table_schema="vet",
        include_schemas=True,
        # 比较类型时的配置
        compare_type=True,
        compare_server_default=True,
        # 设置搜索路径以包含vet schema
        postgresql_include_object=lambda obj, name, type_, reflected, compare_to: True,
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection: Connection) -> None:
    """运行迁移的核心函数"""
    # 设置搜索路径以包含vet schema
    connection.execute(text("SET search_path TO vet, public"))

    context.configure(
        connection=connection,
        target_metadata=target_metadata,
        # 配置schema搜索路径
        version_table_schema="vet",
        include_schemas=True,
        # 比较配置
        compare_type=True,
        compare_server_default=True,
        # 渲染配置
        render_as_batch=False,
        # 设置搜索路径以包含vet schema
        postgresql_include_object=lambda obj, name, type_, reflected, compare_to: True,
    )

    with context.begin_transaction():
        context.run_migrations()


async def run_async_migrations() -> None:
    """在'在线'模式下运行异步迁移。

    在这种情况下，我们需要创建一个Engine并将连接与上下文关联。
    """
    # 获取数据库URL
    database_url = get_database_url()

    # 确保使用异步驱动
    if not database_url.startswith("postgresql+asyncpg://"):
        database_url = database_url.replace(
            "postgresql://", "postgresql+asyncpg://")

    # 创建异步引擎配置
    configuration = config.get_section(config.config_ini_section) or {}
    configuration["sqlalchemy.url"] = database_url

    connectable = async_engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    async with connectable.connect() as connection:
        await connection.run_sync(do_run_migrations)

    await connectable.dispose()


def run_migrations_online() -> None:
    """在'在线'模式下运行迁移。"""
    asyncio.run(run_async_migrations())


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
