# 管理脚本使用指南

## 🚀 快速启动

### 启动Docker服务（推荐）

```bash
# 启动所有微服务（使用本机Redis）
python scripts/manage.py docker start

# 停止所有服务
python scripts/manage.py docker stop

# 重启所有服务
python scripts/manage.py docker restart

# 构建Docker镜像
python scripts/manage.py docker build
```

### 数据库管理

```bash
# 初始化数据库
python scripts/manage.py db init

# 测试数据库和Redis连接
python scripts/manage.py db test

# 创建测试数据
python scripts/manage.py db create-test-data
```

### 单独启动服务（开发调试用）

```bash
# 启动网关
python scripts/manage.py start gateway

# 启动用户服务
python scripts/manage.py start user

# 启动宠物服务
python scripts/manage.py start pet
```

## 📋 完整命令列表

### Docker命令
- `docker start` - 启动Docker服务（使用本机Redis）
- `docker stop [--clean]` - 停止Docker服务，可选择清理资源
- `docker restart` - 重启Docker服务
- `docker build` - 构建Docker镜像
- `docker status` - 查看Docker状态
- `docker logs` - 查看Docker日志

### 数据库命令
- `db init [--drop-tables] [--create-test-data]` - 初始化数据库
- `db test` - 测试数据库和Redis连接
- `db create-test-data` - 创建测试数据

### 服务命令
- `start <service>` - 启动单个服务（gateway/user/pet）

## 🔧 环境要求

### 必需服务
- **PostgreSQL**: 远程数据库服务
- **Redis**: 本机Redis服务
  ```bash
  # macOS安装Redis
  brew install redis
  
  # 启动Redis
  brew services start redis
  
  # 检查Redis状态
  brew services list | grep redis
  ```

### Docker环境
- Docker Desktop
- docker-compose

## 💡 使用建议

### 开发环境
1. **首次设置**:
   ```bash
   # 1. 启动Redis
   brew services start redis
   
   # 2. 初始化数据库
   python scripts/manage.py db init --create-test-data
   
   # 3. 启动服务
   python scripts/manage.py docker start
   ```

2. **日常开发**:
   ```bash
   # 启动服务
   python scripts/manage.py docker start
   
   # 查看日志
   python scripts/manage.py docker logs
   
   # 停止服务
   python scripts/manage.py docker stop
   ```

### 调试单个服务
如果需要调试特定服务，可以：
1. 停止Docker中的对应服务
2. 本地启动该服务进行调试

```bash
# 例如调试用户服务
python scripts/manage.py start user
```

### 故障排除
```bash
# 清理Docker资源
python scripts/manage.py docker stop --clean

# 重新构建镜像
python scripts/manage.py docker build

# 测试连接
python scripts/manage.py db test
```

## 🌐 服务访问地址

启动成功后，可以访问以下地址：

- **API网关**: http://localhost:8000/api/v1/docs
- **用户服务**: http://localhost:8001/api/v1/docs  
- **宠物服务**: http://localhost:8002/api/v1/docs
- **应用服务**: http://localhost:8003/api/v1/docs

## 📝 注意事项

1. **Redis依赖**: 确保本机Redis服务正在运行
2. **端口占用**: 确保8000-8003端口未被占用
3. **数据库连接**: 确保.env文件中的数据库配置正确
4. **Docker资源**: 定期清理Docker资源以释放空间
