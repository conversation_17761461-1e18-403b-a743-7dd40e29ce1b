# 快速开始指南

本指南将帮助你快速搭建和运行宠物医疗AI开放平台的开发环境。

## 环境要求

- **Python**: 3.9+
- **Docker**: 20.0+
- **Docker Compose**: 2.0+
- **Git**: 2.0+

## 快速启动

### 方法一：使用自动化脚本（推荐）

```bash
# 1. 克隆项目
git clone <repository-url>
cd vet_open_platform

# 2. 安装Python依赖
pip install -r requirements.txt

# 3. 配置数据库连接
cp .env.example .env
# 编辑 .env 文件，设置你的远程数据库连接信息

# 4. 运行自动化启动脚本
python scripts/start_dev.py
```

这个脚本会自动：
- 检查依赖和环境
- 测试远程数据库连接
- 启动必要的本地服务（如本地Redis）
- 初始化数据库表结构
- 启动用户服务和API网关
- 显示服务状态和访问地址

### 方法二：手动启动

```bash
# 1. 复制并配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置你的远程数据库连接

# 2. 启动本地服务（如果需要本地Redis）
docker-compose up -d redis  # 仅启动Redis，如果使用远程Redis则跳过

# 3. 初始化数据库
python scripts/init_db.py

# 4. 启动用户服务
cd services/user_service
python main.py

# 5. 在新终端启动API网关
cd gateway
python main.py
```

## 数据库配置

### 远程数据库配置（推荐）

编辑 `.env` 文件，配置你的远程数据库：

```bash
# PostgreSQL配置
DATABASE_URL=postgresql://username:<EMAIL>:5432/vet_platform

# Redis配置
REDIS_URL=redis://<EMAIL>:6379/0
```

详细配置说明请参考：[数据库配置指南](database-setup.md)

## 服务访问地址

启动成功后，你可以访问以下地址：

- **API网关**: http://localhost:8000
- **用户服务**: http://localhost:8001
- **API文档**: 
  - 网关文档: http://localhost:8000/api/v1/docs
  - 用户服务文档: http://localhost:8001/api/v1/docs

## 测试API

### 1. 用户注册

```bash
curl -X POST "http://localhost:8000/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "testuser",
    "password": "TestPass123",
    "first_name": "Test",
    "last_name": "User"
  }'
```

### 2. 用户登录

```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPass123"
  }'
```

### 3. 获取用户信息

```bash
# 使用登录返回的access_token
curl -X GET "http://localhost:8000/api/v1/auth/me" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 开发工作流

### 1. 添加新的API端点

在相应的服务中添加新的路由：

```python
# services/user_service/api/users.py
from fastapi import APIRouter

router = APIRouter()

@router.get("/users")
async def list_users():
    return {"users": []}
```

然后在主应用中包含路由：

```python
# services/user_service/main.py
from services.user_service.api.users import router as users_router

app.include_router(
    users_router,
    prefix=f"{settings.API_V1_STR}/users",
    tags=["users"]
)
```

### 2. 数据库迁移

```bash
# 创建新的迁移
alembic revision --autogenerate -m "Add new table"

# 应用迁移
alembic upgrade head
```

### 3. 添加新的中间件

```python
# gateway/middleware/custom.py
from starlette.middleware.base import BaseHTTPMiddleware

class CustomMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request, call_next):
        # 处理请求
        response = await call_next(request)
        # 处理响应
        return response
```

### 4. 配置新的服务路由

在网关配置中添加新的路由映射：

```python
# gateway/config.py
SERVICE_ROUTES: Dict[str, str] = {
    "/api/v1/auth": USER_SERVICE_URL,
    "/api/v1/new-service": NEW_SERVICE_URL,  # 新服务
}
```

## 常用命令

### Docker相关

```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 停止所有服务
docker-compose down

# 清理数据（谨慎使用）
docker-compose down -v
```

### 数据库相关

```bash
# 连接到PostgreSQL
docker-compose exec postgres psql -U vet_user -d vet_platform

# 连接到Redis
docker-compose exec redis redis-cli

# 重置数据库
python scripts/init_db.py

# 创建管理员用户
python scripts/create_admin.py
```

### 开发工具

```bash
# 代码格式化
black .

# 代码检查
flake8 .

# 类型检查
mypy .

# 运行测试
pytest
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :8000
   
   # 杀死进程
   kill -9 <PID>
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose ps postgres
   
   # 重启数据库
   docker-compose restart postgres
   ```

3. **Redis连接失败**
   ```bash
   # 检查Redis状态
   docker-compose ps redis
   
   # 重启Redis
   docker-compose restart redis
   ```

4. **依赖安装失败**
   ```bash
   # 升级pip
   pip install --upgrade pip
   
   # 清理缓存重新安装
   pip cache purge
   pip install -r requirements.txt
   ```

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f postgres
docker-compose logs -f redis

# 查看Python服务日志
tail -f logs/user_service.log
tail -f logs/gateway.log
```

## 下一步

1. **完善用户服务**: 添加用户管理、权限管理API
2. **实现宠物服务**: 创建宠物管理功能
3. **实现应用服务**: 创建AI应用管理功能
4. **添加测试**: 编写单元测试和集成测试
5. **部署配置**: 配置生产环境部署

## 获取帮助

- 查看API文档: http://localhost:8000/api/v1/docs
- 检查服务健康状态: http://localhost:8000/health
- 查看项目README: [README.md](../../README.md)
