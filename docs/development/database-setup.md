# 数据库配置指南

本指南将帮助你配置远程数据库连接，而不是使用本地Docker数据库。

## 环境变量配置

### 1. 编辑 .env 文件

复制环境变量示例文件并编辑：

```bash
cp .env.example .env
```

### 2. 配置PostgreSQL连接

在 `.env` 文件中设置你的远程PostgreSQL数据库连接：

```bash
# PostgreSQL 数据库配置
DATABASE_URL=postgresql://username:password@hostname:port/database_name

# 或者分别配置各个参数
DATABASE_HOST=your-postgres-host.com
DATABASE_PORT=5432
DATABASE_NAME=vet_platform
DATABASE_USER=your_username
DATABASE_PASSWORD=your_password
```

#### PostgreSQL URL 格式说明：

```
postgresql://[username]:[password]@[hostname]:[port]/[database_name]
```

**示例：**
```bash
# 阿里云RDS示例
DATABASE_URL=postgresql://vet_user:<EMAIL>:5432/vet_platform

# 腾讯云CDB示例  
DATABASE_URL=postgresql://vet_user:<EMAIL>:5432/vet_platform

# AWS RDS示例
DATABASE_URL=postgresql://vet_user:<EMAIL>:5432/vet_platform

# 自建服务器示例
DATABASE_URL=****************************************************************
```

### 3. 配置Redis连接

```bash
# Redis 配置
REDIS_URL=redis://[password@]hostname:port/database_number

# 示例
REDIS_URL=redis://<EMAIL>:6379/0

# 如果没有密码
REDIS_URL=redis://your-redis-host.com:6379/0

# 本地Redis（如果你想使用本地Redis）
REDIS_URL=redis://localhost:6379/0
```

#### Redis URL 格式说明：

```
redis://[password@]hostname:port/database_number
```

**示例：**
```bash
# 阿里云Redis示例
REDIS_URL=redis://<EMAIL>:6379/0

# 腾讯云Redis示例
REDIS_URL=redis://<EMAIL>:6379/0

# AWS ElastiCache示例
REDIS_URL=redis://<EMAIL>:6379/0
```

## 数据库准备

### 1. 创建数据库

在你的PostgreSQL服务器上创建数据库：

```sql
-- 连接到PostgreSQL服务器
psql -h your-host -U your-admin-user -d postgres

-- 创建数据库用户
CREATE USER vet_user WITH PASSWORD 'your_password';

-- 创建数据库
CREATE DATABASE vet_platform OWNER vet_user;

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE vet_platform TO vet_user;

-- 如果需要，授予创建数据库的权限（用于测试）
ALTER USER vet_user CREATEDB;
```

### 2. 配置防火墙和安全组

确保你的数据库服务器允许来自你的开发机器的连接：

- **端口**: PostgreSQL默认5432，Redis默认6379
- **IP白名单**: 添加你的开发机器IP
- **SSL**: 建议在生产环境启用SSL连接

### 3. 测试连接

使用命令行工具测试连接：

```bash
# 测试PostgreSQL连接
psql "********************************************/vet_platform"

# 测试Redis连接
redis-cli -h hostname -p 6379 -a password ping
```

## 启动开发环境

### 1. 使用优化后的启动脚本

```bash
# 安装依赖
pip install -r requirements.txt

# 运行启动脚本（会自动检测远程数据库）
python scripts/start_dev.py
```

启动脚本会自动：
- ✅ 检查远程PostgreSQL连接
- ✅ 检查远程Redis连接  
- ✅ 只启动必要的本地服务
- ✅ 初始化数据库表结构
- ✅ 启动应用服务

### 2. 手动启动（如果需要）

```bash
# 1. 测试数据库连接
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
print('DATABASE_URL:', os.getenv('DATABASE_URL'))
print('REDIS_URL:', os.getenv('REDIS_URL'))
"

# 2. 初始化数据库
python scripts/init_db.py

# 3. 启动服务
python services/user_service/main.py &
python gateway/main.py &
```

## 常见问题解决

### 1. PostgreSQL连接问题

**错误**: `psycopg2.OperationalError: could not connect to server`

**解决方案**:
- 检查主机名和端口是否正确
- 确认防火墙/安全组设置
- 验证用户名和密码
- 检查数据库是否存在

```bash
# 调试连接
python -c "
import psycopg2
from urllib.parse import urlparse
url = 'your_database_url_here'
parsed = urlparse(url)
print(f'Host: {parsed.hostname}')
print(f'Port: {parsed.port}')
print(f'Database: {parsed.path[1:]}')
print(f'User: {parsed.username}')
"
```

### 2. Redis连接问题

**错误**: `redis.exceptions.ConnectionError`

**解决方案**:
- 检查Redis服务是否运行
- 验证主机名、端口和密码
- 确认网络连接

```bash
# 调试Redis连接
python -c "
import redis
from urllib.parse import urlparse
url = 'your_redis_url_here'
parsed = urlparse(url)
r = redis.Redis(host=parsed.hostname, port=parsed.port, password=parsed.password)
print(r.ping())
"
```

### 3. SSL连接问题

如果数据库要求SSL连接，更新URL：

```bash
# PostgreSQL SSL连接
DATABASE_URL=********************************/db?sslmode=require

# Redis SSL连接
REDIS_URL=rediss://user:pass@host:6380/0
```

### 4. 权限问题

**错误**: `permission denied for database`

**解决方案**:
```sql
-- 授予必要权限
GRANT ALL PRIVILEGES ON DATABASE vet_platform TO vet_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO vet_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO vet_user;
```

## 生产环境注意事项

### 1. 安全配置

- 使用强密码
- 启用SSL/TLS连接
- 配置IP白名单
- 定期更新密码
- 使用专用数据库用户

### 2. 性能优化

- 配置连接池
- 设置合适的超时时间
- 监控数据库性能
- 定期备份数据

### 3. 环境变量管理

```bash
# 开发环境
DATABASE_URL=********************************************/vet_platform_dev

# 测试环境  
DATABASE_URL=***********************************************/vet_platform_test

# 生产环境
DATABASE_URL=***********************************************/vet_platform_prod
```

## 数据库迁移

使用Alembic管理数据库结构变更：

```bash
# 创建迁移
alembic revision --autogenerate -m "Initial migration"

# 应用迁移
alembic upgrade head

# 查看迁移历史
alembic history

# 回滚迁移
alembic downgrade -1
```
