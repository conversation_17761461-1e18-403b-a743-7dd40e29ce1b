# 📚 宠物医疗AI开放平台 - 文档中心

欢迎来到宠物医疗AI开放平台的文档中心！这里包含了平台的完整技术文档和使用指南。

## 📖 核心文档

### 🤖 [OpenAI兼容API网关](openai_api_gateway.md)
**完整的OpenAI兼容API网关文档**
- 架构设计和核心特性
- API端点详解和使用示例
- 认证授权和错误处理
- 部署配置和性能优化
- 扩展指南和最佳实践

**适用对象**: 开发者、系统集成商、API用户

## 🏗️ 开发文档

### [开发指南](development/)
- 项目结构和代码规范
- 本地开发环境搭建
- 调试和测试指南
- 贡献指南

### [API参考](api/)
- 业务API接口文档
- 数据模型定义
- 错误码参考

## 🚀 部署文档

### [部署指南](deployment/)
- 生产环境部署
- 容器化部署
- 监控和日志配置
- 性能调优

## 🔗 快速链接

### OpenAI兼容API
- **基础URL**: `http://localhost:8000/v1`
- **支持的端点**:
  - `POST /v1/chat/completions` - 聊天完成
  - `GET /v1/models` - 模型列表
  - `POST /v1/embeddings` - 嵌入向量
  - `GET /v1/usage` - 使用统计

### 业务API
- **网关**: http://localhost:8000/docs
- **用户服务**: http://localhost:8001/docs
- **宠物服务**: http://localhost:8002/docs
- **应用服务**: http://localhost:8003/docs

## 🧪 快速测试

### 测试OpenAI API
```bash
# 运行完整测试套件
python test_openai_api.py

# 快速测试聊天API
curl -X POST "http://localhost:8000/v1/chat/completions" \
     -H "Authorization: Bearer sk-proj-1oy7NLULP93kNFR9fEh9nYib1qLtleVYlEiw1BKzg4tIz6Ba" \
     -H "Content-Type: application/json" \
     -d '{
       "model": "ds-vet-answer-32B",
       "messages": [{"role": "user", "content": "Hello"}]
     }'
```

### 使用Python SDK
```python
import openai

openai.api_base = "http://localhost:8000/v1"
openai.api_key = "sk-proj-1oy7NLULP93kNFR9fEh9nYib1qLtleVYlEiw1BKzg4tIz6Ba"

response = openai.ChatCompletion.create(
    model="ds-vet-answer-32B",
    messages=[{"role": "user", "content": "Hello"}]
)
print(response.choices[0].message.content)
```

## 📋 支持的模型

| 模型名称 | 描述 | 最大Token |
|---------|------|----------|
| `ds-vet-answer-32B` | 宠物医疗专用32B参数模型 | 4096 |
| `ds-vet-answer-72B` | 宠物医疗专用72B参数模型 | 8192 |

## 🔐 认证信息

### 测试API Key
- **OpenAI格式**: `sk-proj-1oy7NLULP93kNFR9fEh9nYib1qLtleVYlEiw1BKzg4tIz6Ba`
- **企业格式**: `y03ZvBwVvHAqAo0nosjdlVGCfCNYGlgm8U`

### 用户等级
- **免费**: 100请求/月, 10,000 tokens/月
- **高级**: 1,000请求/月, 100,000 tokens/月
- **企业**: 10,000请求/月, 1,000,000 tokens/月

## 🆘 获取帮助

### 常见问题
1. **API Key无效**: 检查Authorization头格式是否正确
2. **模型不存在**: 确认使用的模型名称在支持列表中
3. **请求超时**: 检查网络连接和服务状态
4. **超出限制**: 查看使用统计，考虑升级套餐

### 技术支持
- **GitHub Issues**: 提交bug报告和功能请求
- **文档更新**: 欢迎提交文档改进建议
- **社区讨论**: 参与技术讨论和经验分享

---

**最后更新**: 2025-07-04  
**文档版本**: v1.0.0  
**平台版本**: v1.0.0
