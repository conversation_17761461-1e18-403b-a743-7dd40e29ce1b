# App Service 网关集成文档

## 概述

本文档描述了如何通过API网关访问app_service微服务的所有功能，包括智能体管理、系统级智能体和权限管理等。

## 网关路由配置

### 已配置的路由

API网关已配置以下app_service相关路由：

| 路径前缀 | 目标服务 | 功能描述 |
|---------|---------|---------|
| `/api/v1/applications` | app_service | 第三方应用管理接口 |
| `/api/v1/agents` | app_service | AI智能体管理接口 |
| `/api/v1/system-agents` | app_service | 系统级智能体专用接口 |

### 路由匹配规则

网关使用路径前缀匹配，支持以下模式：
- 精确匹配：`/api/v1/agents`
- 路径参数匹配：`/api/v1/agents/{agent_id}`
- 嵌套路径匹配：`/api/v1/agents/{agent_id}/permissions`

## 访问方式

### 基础URL

通过网关访问app_service的基础URL：
```
http://localhost:8000
```

### 主要端点

#### 1. 智能体管理

```bash
# 获取智能体列表（支持无认证访问公开智能体）
GET http://localhost:8000/api/v1/agents

# 获取特定智能体详情（支持无认证访问公开智能体）
GET http://localhost:8000/api/v1/agents/{agent_id}

# 创建智能体（需要认证）
POST http://localhost:8000/api/v1/agents

# 更新智能体（需要认证）
PUT http://localhost:8000/api/v1/agents/{agent_id}

# 删除智能体（需要认证）
DELETE http://localhost:8000/api/v1/agents/{agent_id}

# 执行智能体（需要认证）
POST http://localhost:8000/api/v1/agents/{agent_id}/execute
```

#### 2. 系统级智能体

```bash
# AI问诊智能体（需要认证）
POST http://localhost:8000/api/v1/system-agents/diagnosis

# AI视觉识别智能体（需要认证）
POST http://localhost:8000/api/v1/system-agents/vision

# 报告生成智能体（需要认证）
POST http://localhost:8000/api/v1/system-agents/report-generation

# 报告解读智能体（需要认证）
POST http://localhost:8000/api/v1/system-agents/report-analysis
```

#### 3. 智能体权限管理

```bash
# 获取智能体权限列表（需要认证）
GET http://localhost:8000/api/v1/agents/{agent_id}/permissions

# 创建智能体权限（需要认证）
POST http://localhost:8000/api/v1/agents/{agent_id}/permissions

# 切换权限状态（需要认证）
PUT http://localhost:8000/api/v1/agents/{agent_id}/permissions/{permission_id}/toggle

# 删除权限（需要认证）
DELETE http://localhost:8000/api/v1/agents/{agent_id}/permissions/{permission_id}
```

## 认证机制

### 公开端点

以下端点支持无认证访问：
- `GET /api/v1/agents` - 查看公开智能体列表
- `GET /api/v1/agents/{agent_id}` - 查看公开智能体详情

### 认证端点

其他所有端点都需要认证，支持以下认证方式：

#### 1. JWT令牌认证

```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:8000/api/v1/agents
```

#### 2. API密钥认证

```bash
curl -H "Authorization: Bearer sk-your-api-key" \
     http://localhost:8000/api/v1/agents
```

## 使用示例

### 1. 获取智能体列表

```bash
# 无认证访问（仅返回公开智能体）
curl http://localhost:8000/api/v1/agents

# 带认证访问（返回用户可访问的所有智能体）
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8000/api/v1/agents
```

### 2. 执行AI问诊智能体

```bash
curl -X POST \
  http://localhost:8000/api/v1/system-agents/diagnosis \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "pet_info": {
      "breed": "金毛犬",
      "age": "3岁",
      "gender": "雄性",
      "weight": "25"
    },
    "symptoms": ["食欲不振", "精神萎靡"],
    "additional_info": "症状持续2天"
  }'
```

### 3. 流式执行智能体

```bash
curl -X POST \
  http://localhost:8000/api/v1/agents/1/execute \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  --no-buffer -N \
  -d '{
    "input_data": {
      "pet_info": {
        "breed": "金毛犬",
        "age": "3岁",
        "gender": "雄性",
        "weight": "25"
      },
      "symptoms": ["食欲不振", "精神萎靡"],
      "additional_info": "症状持续2天"
    },
    "stream": true
  }'
```

## 错误处理

### 常见错误码

- `401 Unauthorized`: 缺少认证或认证无效
- `403 Forbidden`: 权限不足
- `404 Not Found`: 资源不存在或路由未找到
- `500 Internal Server Error`: 服务内部错误
- `502 Bad Gateway`: 网关错误
- `503 Service Unavailable`: 服务不可用
- `504 Gateway Timeout`: 网关超时

### 错误响应格式

```json
{
  "detail": "错误描述",
  "type": "error_type"
}
```

## 性能和限制

### 请求限制

- 默认请求超时：30秒
- 速率限制：每分钟60次请求
- 突发限制：10次请求

### 流式响应

支持Server-Sent Events (SSE)流式响应：
- 设置`stream: true`参数
- 使用`Accept: text/event-stream`头
- 支持实时输出

## 监控和日志

### 健康检查

```bash
# 网关健康检查
curl http://localhost:8000/health

# app_service健康检查（通过网关）
curl http://localhost:8000/api/v1/agents
```

### 日志记录

网关会记录所有请求的详细日志，包括：
- 请求路径和方法
- 响应状态码
- 处理时间
- 用户信息（如果已认证）

## 故障排除

### 常见问题

1. **路由未找到 (404)**
   - 检查路径是否正确
   - 确认网关配置是否包含该路由

2. **认证失败 (401)**
   - 检查JWT令牌是否有效
   - 确认API密钥格式是否正确

3. **服务不可用 (503)**
   - 检查app_service是否正在运行
   - 确认网络连接是否正常

4. **网关超时 (504)**
   - 检查app_service响应时间
   - 考虑增加超时配置

### 调试工具

使用提供的测试脚本进行调试：

```bash
# 测试网关路由
python scripts/test_gateway_routes.py

# 完整功能测试
python scripts/test_app_service_gateway.py
```

## 配置文件

相关配置文件位置：
- 网关配置：`gateway/config/settings.py`
- 路由配置：`gateway/config/settings.py` (SERVICE_ROUTES)
- 认证配置：`gateway/middleware/auth.py`
- 文档配置：`docs/api_gateway.md`
