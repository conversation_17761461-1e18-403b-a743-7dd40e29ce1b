# 🐳 Docker部署指南

本文档详细介绍如何使用Docker部署宠物医疗平台。

## 📋 目录

- [环境要求](#环境要求)
- [快速开始](#快速开始)
- [配置说明](#配置说明)
- [启动服务](#启动服务)
- [管理命令](#管理命令)
- [故障排除](#故障排除)
- [生产部署](#生产部署)

## 🔧 环境要求

### 必需软件

- **Docker**: 版本 20.10 或更高
- **Docker Compose**: 版本 2.0 或更高
- **Python**: 版本 3.11 或更高 (用于管理脚本)

### 系统要求

- **内存**: 至少 4GB RAM
- **存储**: 至少 10GB 可用空间
- **网络**: 确保以下端口可用：
  - 8000 (API网关)
  - 8001 (用户服务)
  - 8002 (宠物服务)
  - 8003 (应用服务)

## 🚀 快速开始

### 1. 检查Docker环境

```bash
# 检查Docker版本
docker --version
docker-compose --version

# 确保Docker守护进程运行
docker info
```

### 2. 配置环境变量

项目使用远程数据库和Redis，确保 `.env` 文件配置正确：

```env
# 数据库配置 (远程PostgreSQL)
DATABASE_URL=*******************************************************/vet_platform
DATABASE_HOST=*********
DATABASE_USER=vet_platform_rw
DATABASE_PASSWORD=VetPlatform
DATABASE_NAME=vet_platform

# Redis配置 (本地Redis)
REDIS_URL=redis://localhost:6379/2
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=2

# 安全配置
SECRET_KEY=your-super-secret-key-change-this-in-production
JWT_SECRET_KEY=your-jwt-secret
```

### 3. 一键启动

```bash
# 使用管理脚本启动 (推荐)
python scripts/manage.py docker start

# 或者直接使用Docker启动脚本
python scripts/start_docker.py
```

## ⚙️ 配置说明

### Docker Compose配置

项目的 `docker-compose.yml` 已经配置为使用远程数据库和本地Redis：

- **网络配置**: 使用 `extra_hosts` 允许容器访问宿主机的Redis
- **环境变量**: 自动从 `.env` 文件读取配置
- **卷挂载**: 日志目录挂载到宿主机的 `./logs` 目录
- **健康检查**: 每个服务都配置了健康检查

### 服务架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │    │   User Service  │    │   Pet Service   │
│   (Port 8000)   │    │   (Port 8001)   │    │   (Port 8002)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────┐    ┌─────────────────┐
         │   App Service   │    │  Celery Worker  │
         │   (Port 8003)   │    │   & Beat        │
         └─────────────────┘    └─────────────────┘
                                 │
         ┌─────────────────┐    ┌─────────────────┐
         │  PostgreSQL     │    │     Redis       │
         │   (Remote)      │    │   (Local)       │
         └─────────────────┘    └─────────────────┘
```

## 🎮 管理命令

### 使用管理脚本 (推荐)

```bash
# 启动所有服务
python scripts/manage.py docker start

# 停止所有服务
python scripts/manage.py docker stop

# 完全清理 (停止服务并删除容器、镜像)
python scripts/manage.py docker stop --clean

# 重启所有服务
python scripts/manage.py docker restart

# 构建镜像
python scripts/manage.py docker build

# 查看服务状态
python scripts/manage.py docker status

# 查看日志
python scripts/manage.py docker logs

# 跟踪日志输出
python scripts/manage.py docker logs --follow
```

### 直接使用Docker Compose

```bash
# 构建并启动
docker-compose up -d --build

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 完全清理
docker-compose down --volumes --remove-orphans
```

## 🔍 服务访问

启动成功后，可以通过以下地址访问服务：

### API文档

- **API网关**: http://localhost:8000/api/v1/docs
- **用户服务**: http://localhost:8001/api/v1/docs
- **宠物服务**: http://localhost:8002/api/v1/docs
- **应用服务**: http://localhost:8003/api/v1/docs

### 健康检查

- **API网关**: http://localhost:8000/health
- **用户服务**: http://localhost:8001/health
- **宠物服务**: http://localhost:8002/health
- **应用服务**: http://localhost:8003/health

### 服务状态

- **整体状态**: http://localhost:8000/status

## 🐛 故障排除

### 常见问题

#### 1. 端口被占用

```bash
# 检查端口占用
lsof -i :8000
lsof -i :8001
lsof -i :8002
lsof -i :8003

# 停止占用端口的进程
kill -9 <PID>
```

#### 2. 数据库连接失败

```bash
# 检查数据库连接
python scripts/manage.py db test

# 检查环境配置
python scripts/manage.py dev check
```

#### 3. Redis连接失败

```bash
# 检查Redis服务
redis-cli ping

# 检查Redis配置
echo $REDIS_URL
```

#### 4. 容器启动失败

```bash
# 查看详细日志
docker-compose logs <service_name>

# 查看容器状态
docker-compose ps

# 重新构建镜像
docker-compose build --no-cache <service_name>
```

#### 5. 镜像构建失败

```bash
# 清理Docker缓存
docker system prune -a

# 重新构建
python scripts/manage.py docker build
```

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs gateway
docker-compose logs user_service
docker-compose logs pet_service

# 跟踪实时日志
docker-compose logs -f

# 查看最近的日志
docker-compose logs --tail=100
```

### 性能监控

```bash
# 查看容器资源使用
docker stats

# 查看容器详细信息
docker inspect <container_name>
```

## 🚀 生产部署

### 环境变量配置

生产环境需要修改以下配置：

```env
# 安全配置
DEBUG=false
SECRET_KEY=<strong-random-secret-key>
JWT_SECRET_KEY=<strong-random-jwt-secret>

# 数据库配置
DATABASE_URL=***************************************************/prod_database

# Redis配置
REDIS_URL=redis://prod_redis_host:6379/0

# 日志配置
LOG_LEVEL=WARNING
```

### 生产部署命令

```bash
# 生产环境启动
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# 使用外部网络
docker-compose --env-file .env.prod up -d
```

### 备份和恢复

```bash
# 备份数据库
docker exec vet_postgres pg_dump -U vet_user vet_platform > backup.sql

# 恢复数据库
docker exec -i vet_postgres psql -U vet_user vet_platform < backup.sql
```

## 📊 监控和维护

### 健康检查

所有服务都配置了健康检查，Docker会自动监控服务状态：

```bash
# 查看健康状态
docker-compose ps
```

### 日志轮转

生产环境建议配置日志轮转：

```yaml
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

### 自动重启

```yaml
restart: unless-stopped
```

## 🔗 相关文档

- [开发环境设置](../development/setup.md)
- [API网关文档](../api_gateway.md)
- [OpenAI API网关](../openai_api_gateway.md)
- [请求日志系统](../request-logging-system.md)
