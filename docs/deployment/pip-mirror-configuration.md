# 🚀 Pip镜像源配置说明

本文档说明了项目中如何配置pip使用清华大学镜像源来加速Python包的安装。

## 📋 配置概述

为了解决Docker构建过程中Python包安装缓慢的问题，项目已配置使用清华大学的PyPI镜像源。

## 🔧 配置方法

### 1. pip配置文件

项目根目录下的 `pip.conf` 文件：

```ini
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple
trusted-host = pypi.tuna.tsinghua.edu.cn

[install]
trusted-host = pypi.tuna.tsinghua.edu.cn
```

### 2. Dockerfile修改

所有Dockerfile都已修改为使用清华源：

**修改前：**
```dockerfile
# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt
```

**修改后：**
```dockerfile
# Configure pip to use Tsinghua mirror
COPY pip.conf /etc/pip.conf

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt
```

## 📁 影响的文件

以下Dockerfile已经更新：

1. `Dockerfile.base` - 基础镜像
2. `gateway/Dockerfile` - API网关
3. `services/user_service/Dockerfile` - 用户服务
4. `services/pet_service/Dockerfile` - 宠物服务
5. `services/app_service/Dockerfile` - 应用服务

## 🌐 镜像源信息

### 清华大学镜像源

- **URL**: https://pypi.tuna.tsinghua.edu.cn/simple
- **说明**: 清华大学开源软件镜像站提供的PyPI镜像
- **更新频率**: 每5分钟同步一次
- **覆盖范围**: 完整的PyPI包

### 备用镜像源

如果清华源不可用，可以考虑以下备用源：

```ini
# 阿里云镜像源
index-url = https://mirrors.aliyun.com/pypi/simple/
trusted-host = mirrors.aliyun.com

# 中科大镜像源
index-url = https://pypi.mirrors.ustc.edu.cn/simple/
trusted-host = pypi.mirrors.ustc.edu.cn

# 豆瓣镜像源
index-url = https://pypi.douban.com/simple/
trusted-host = pypi.douban.com
```

## 🚀 使用效果

### 安装速度对比

| 镜像源 | 平均下载速度 | 连接稳定性 |
|--------|-------------|-----------|
| 官方PyPI | 100-500 KB/s | 一般 |
| 清华镜像 | 5-20 MB/s | 优秀 |
| 阿里云镜像 | 3-15 MB/s | 良好 |

### Docker构建时间

- **使用官方源**: 5-10分钟
- **使用清华源**: 1-3分钟

## 🔄 切换镜像源

### 临时使用其他源

```bash
# 临时使用阿里云源
pip install -i https://mirrors.aliyun.com/pypi/simple/ package_name

# 临时使用中科大源
pip install -i https://pypi.mirrors.ustc.edu.cn/simple/ package_name
```

### 修改配置文件

如需更换镜像源，修改 `pip.conf` 文件：

```ini
[global]
index-url = https://mirrors.aliyun.com/pypi/simple/
trusted-host = mirrors.aliyun.com

[install]
trusted-host = mirrors.aliyun.com
```

## 🐛 故障排除

### 常见问题

#### 1. SSL证书错误

```
SSL: CERTIFICATE_VERIFY_FAILED
```

**解决方案：**
确保 `trusted-host` 配置正确：
```ini
trusted-host = pypi.tuna.tsinghua.edu.cn
```

#### 2. 镜像源不可用

```
Could not find a version that satisfies the requirement
```

**解决方案：**
1. 检查网络连接
2. 尝试其他镜像源
3. 临时使用官方源

#### 3. 包版本不同步

某些最新包可能在镜像源中不可用。

**解决方案：**
```bash
# 临时使用官方源安装特定包
pip install --index-url https://pypi.org/simple/ package_name
```

## 📊 监控和维护

### 检查镜像源状态

```bash
# 测试镜像源连接
curl -I https://pypi.tuna.tsinghua.edu.cn/simple/

# 检查pip配置
pip config list

# 测试包安装
pip install --dry-run requests
```

### 更新配置

定期检查镜像源的可用性和速度，必要时更新配置。

## 🔗 相关链接

- [清华大学开源软件镜像站](https://mirrors.tuna.tsinghua.edu.cn/)
- [PyPI镜像使用帮助](https://mirrors.tuna.tsinghua.edu.cn/help/pypi/)
- [pip配置文档](https://pip.pypa.io/en/stable/topics/configuration/)

## 📝 注意事项

1. **安全性**: 使用镜像源时要确保来源可信
2. **同步延迟**: 镜像源可能有几分钟的同步延迟
3. **网络环境**: 根据网络环境选择最优的镜像源
4. **企业环境**: 企业内网可能需要配置代理或使用内部镜像源
