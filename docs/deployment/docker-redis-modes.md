# 🔧 Docker Redis配置

本文档说明宠物医疗平台Docker部署中的Redis配置。

## 📋 当前配置

### 外部Redis模式（当前使用）

**特点：**
- 使用外部主机的Redis服务
- 容器通过网络连接外部Redis
- 统一的Redis配置管理

**优点：**
- ✅ 节省容器资源
- ✅ 统一的Redis服务管理
- ✅ 便于集中监控和维护
- ✅ 避免容器内Redis的复杂性

**配置：**
- Redis地址：`host.docker.internal:6379`
- 数据库分配：
  - 数据库1：Celery Broker
  - 数据库2：应用缓存和Celery结果存储

## 🚀 使用方法

### 方法1：使用管理脚本（推荐）

```bash
# 启动Docker服务（使用外部Redis）
python scripts/manage.py docker start

# 查看服务状态
python scripts/manage.py docker status

# 查看日志
python scripts/manage.py docker logs

# 停止服务
python scripts/manage.py docker stop
```

### 方法2：直接使用Docker Compose

```bash
# 启动服务（使用外部Redis）
docker-compose up -d --build

# 停止服务
docker-compose down
```

## ⚙️ 配置详情

### 服务启动顺序

**启动顺序设计：**
1. **User Service** (8001端口) - 用户管理服务
2. **Pet Service** (8002端口) - 宠物管理服务
3. **App Service** (8003端口) - 应用服务（AI智能体等）
4. **Gateway** (8000端口) - API网关，最后启动

### 外部Redis配置

**docker-compose.yml中的服务配置：**
```yaml
services:
  user_service:
    environment:
      - REDIS_URL=redis://host.docker.internal:6379/2
    extra_hosts:
      - "host.docker.internal:host-gateway"

  pet_service:
    environment:
      - REDIS_URL=redis://host.docker.internal:6379/2
    extra_hosts:
      - "host.docker.internal:host-gateway"

  app_service:
    environment:
      - REDIS_URL=redis://host.docker.internal:6379/2
    extra_hosts:
      - "host.docker.internal:host-gateway"

  gateway:
    environment:
      - REDIS_URL=redis://host.docker.internal:6379/2
    depends_on:
      - user_service
      - pet_service
      - app_service
    extra_hosts:
      - "host.docker.internal:host-gateway"
```

**网络配置说明：**
- `host.docker.internal`：Docker提供的特殊域名，指向宿主机
- `extra_hosts`：确保容器能正确解析宿主机地址
- `depends_on`：确保Gateway在所有业务服务启动后再启动
- Redis数据库2：用于应用缓存和会话存储

## 🔍 环境检查

### 检查外部Redis

```bash
# 检查Redis是否运行
redis-cli ping

# 启动Redis服务 (macOS)
brew services start redis

# 检查Redis端口
lsof -i :6379

# 测试连接
redis-cli -h localhost -p 6379 -n 2 ping

# 测试从Docker容器连接
docker run --rm redis:7-alpine redis-cli -h host.docker.internal -p 6379 ping
```

## 🐛 故障排除

### 外部Redis连接问题

**问题：** 容器无法连接外部Redis
```
redis.exceptions.ConnectionError: Error connecting to Redis
```

**解决方案：**
1. 确保外部Redis正在运行：
   ```bash
   # 检查Redis状态
   redis-cli ping

   # 如果是本地Redis，启动服务
   brew services start redis
   ```

2. 检查网络连接：
   ```bash
   # 测试从容器连接Redis
   docker run --rm redis:7-alpine redis-cli -h host.docker.internal -p 6379 ping
   ```

3. 检查Redis配置：
   ```bash
   # 确保Redis允许外部连接
   redis-cli CONFIG GET bind
   redis-cli CONFIG GET protected-mode
   ```

4. 检查防火墙设置：
   ```bash
   # 确保6379端口可访问
   telnet localhost 6379
   ```

### Docker网络问题

**问题：** host.docker.internal无法解析
```
Name or service not known: host.docker.internal
```

**解决方案：**
1. 确保Docker版本支持host.docker.internal（Docker Desktop for Mac/Windows）
2. 在Linux环境中，使用宿主机IP地址替代host.docker.internal
3. 检查extra_hosts配置是否正确

## 📊 配置优势

| 特性 | 外部Redis模式 |
|------|---------------|
| 资源使用 | 节省容器资源 |
| 网络延迟 | 低延迟连接 |
| 部署复杂度 | 简化容器配置 |
| 服务管理 | 统一Redis管理 |
| 扩展性 | 便于集群扩展 |

## 🎯 适用场景

### 外部Redis适用于：
- 🏢 企业级部署环境
- 🔧 统一Redis服务管理
- 📊 需要Redis集群的场景
- 🚀 微服务架构
- 💾 共享缓存需求
- 🔄 多应用Redis复用

## 📝 最佳实践

1. **Redis服务管理**：确保外部Redis服务的高可用性
2. **网络安全**：配置适当的Redis访问控制和防火墙规则
3. **监控告警**：配置Redis健康检查和性能监控
4. **数据备份**：定期备份Redis数据，配置持久化策略
5. **连接池**：应用程序使用连接池优化Redis连接
6. **数据库分离**：合理分配Redis数据库，避免数据冲突
