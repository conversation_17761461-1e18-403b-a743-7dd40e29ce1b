# API网关技术文档

## 概述

宠物医疗平台API网关是整个微服务架构的统一入口，基于FastAPI构建，提供了完整的请求路由、认证授权、限流控制、日志记录等功能。网关同时支持传统的RESTful API和OpenAI兼容的AI API，为前端应用和第三方集成提供统一的访问接口。

### 核心特性

- **统一入口**：所有外部请求的单一访问点
- **智能路由**：基于路径前缀的动态服务路由
- **多重认证**：支持JWT令牌和API密钥认证
- **精确限流**：基于滑动窗口算法的限流控制
- **全链路追踪**：完整的请求日志和监控
- **OpenAI兼容**：完全兼容OpenAI API规范
- **高可用性**：健康检查和自动降级机制

## 架构设计

### 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   第三方应用    │    │   OpenAI客户端  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   API网关       │
                    │  (Port: 8000)   │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户服务      │    │   宠物服务      │    │   应用服务      │
│  (Port: 8001)   │    │  (Port: 8002)   │    │  (Port: 8003)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 中间件架构

网关采用洋葱模型的中间件架构，请求按以下顺序处理：

```
请求 → 请求日志 → 限流控制 → 认证验证 → 路由转发 → 后端服务
响应 ← 响应日志 ← 响应处理 ← 认证信息 ← 路由响应 ← 后端服务
```

## 核心功能

### 1. 服务路由

网关根据请求路径前缀将请求路由到相应的后端服务：

| 路径前缀 | 目标服务 | 功能描述 |
|---------|---------|---------|
| `/api/v1/auth` | 用户服务 | 认证相关接口 |
| `/api/v1/users` | 用户服务 | 用户管理接口 |
| `/api/v1/permissions` | 用户服务 | 权限管理接口 |
| `/api/v1/api-keys` | 用户服务 | API密钥管理接口 |
| `/api/v1/tenants` | 用户服务 | 租户管理接口 |
| `/api/v1/tenant-management` | 用户服务 | 租户管理扩展接口 |
| `/api/v1/pets` | 宠物服务 | 宠物信息管理接口 |
| `/api/v1/medical-records` | 宠物服务 | 医疗记录接口 |
| `/api/v1/vaccinations` | 宠物服务 | 疫苗记录接口 |
| `/api/v1/breeds` | 宠物服务 | 宠物品种接口 |
| `/api/v1/applications` | 应用服务 | 第三方应用管理接口 |
| `/api/v1/agents` | 应用服务 | AI代理管理接口 |
| `/api/v1/system-agents` | 应用服务 | 系统级智能体专用接口 |
| `/api/v1/ocr` | OCR服务 | OCR识别接口 |
| `/v1/chat/completions` | OpenAI代理 | 聊天完成API |
| `/v1/models` | OpenAI代理 | 模型列表API |
| `/v1/embeddings` | OpenAI代理 | 文本嵌入API |

### 2. 认证机制

#### JWT令牌认证
- 用于用户会话管理
- 支持访问令牌和刷新令牌
- 自动缓存用户信息到Redis

#### API密钥认证
- 用于第三方应用集成
- 支持多种密钥格式
- 灵活的权限控制

#### OpenAI兼容认证
- 支持标准的Bearer认证
- 兼容OpenAI SDK
- 企业自有密钥格式支持

### 3. 限流控制

采用滑动窗口算法实现精确的限流控制：

- **算法优势**：比固定窗口更精确，避免突发流量
- **多维度限流**：支持基于用户ID、API密钥、IP地址的限流
- **分布式支持**：基于Redis实现，支持多实例部署
- **优雅降级**：Redis故障时自动开放策略

## 中间件详细说明

### 1. 认证中间件 (AuthMiddleware)

**功能**：处理所有进入网关的请求认证

**特性**：
- 支持JWT令牌和API密钥双重认证
- 公开端点自动跳过认证
- OpenAI端点特殊处理
- 用户信息缓存优化

**配置**：
```python
# 公开端点（无需认证）
PUBLIC_ENDPOINTS = {
    "/health",
    "/api/v1/auth/login",
    "/api/v1/auth/register",
    "/docs",
    "/redoc"
}

# OpenAI端点（使用自己的认证）
OPENAI_ENDPOINTS = {
    "/v1/chat/completions",
    "/v1/models",
    "/v1/embeddings"
}
```

### 2. 限流中间件 (RateLimitMiddleware)

**功能**：基于滑动窗口算法的精确限流

**算法原理**：
1. 使用Redis有序集合存储请求时间戳
2. 移除窗口外的过期请求
3. 统计当前窗口内的请求数量
4. 判断是否超出限制

**配置参数**：
```python
RATE_LIMIT_PER_MINUTE = 60    # 每分钟请求限制
RATE_LIMIT_BURST = 10         # 突发请求限制
```

### 3. 请求日志中间件 (RequestLogMiddleware)

**功能**：全面的请求日志记录和监控

**记录内容**：
- 请求详细信息（方法、路径、头部、参数）
- 响应信息（状态码、处理时间）
- 用户信息（脱敏处理）
- 链路追踪ID

**存储策略**：
- 应用日志：结构化日志输出
- Redis缓存：实时监控数据
- 计费数据：长期存储分析

### 4. OpenAI计费中间件 (OpenAIBillingMiddleware)

**功能**：OpenAI API的使用统计和计费

**计费策略**：
- 基于用户等级的差异化定价
- Token使用量精确统计
- 月度使用限制检查
- 成本自动计算

**用户等级**：
```python
# 免费用户
"free": {
    "max_requests": 100,      # 每月100次请求
    "max_tokens": 10000,      # 每月10K Token
    "price": 0.0              # 免费
}

# 高级用户
"premium": {
    "max_requests": 1000,     # 每月1000次请求
    "max_tokens": 100000,     # 每月100K Token
    "price": 0.002            # $0.002 per 1K tokens
}

# 企业用户
"enterprise": {
    "max_requests": 10000,    # 每月10000次请求
    "max_tokens": 1000000,    # 每月1M Token
    "price": 0.001            # $0.001 per 1K tokens
}
```

## 路由配置和代理机制

### 1. 通用代理路由 (proxy.py)

**功能**：处理传统RESTful API的代理转发

**特性**：
- 智能请求头处理
- 客户端IP正确传递
- 错误处理和超时管理
- 响应头过滤

**转发流程**：
1. 解析请求路径，匹配目标服务
2. 准备转发请求头（排除逐跳头）
3. 添加转发相关头部信息
4. 发起HTTP请求到后端服务
5. 处理响应并返回客户端

### 2. OpenAI代理路由 (openai_proxy.py)

**功能**：OpenAI兼容API的专用代理

**支持的端点**：
- `/v1/chat/completions` - 聊天完成（支持流式）
- `/v1/models` - 模型列表
- `/v1/embeddings` - 文本嵌入
- `/v1/usage` - 使用统计

**流式响应支持**：
```python
async def stream_generator():
    async with client.stream("POST", url, json=data) as response:
        async for chunk in response.aiter_text():
            if chunk:
                yield chunk
```

## OpenAI兼容API详细说明

### 1. 模型配置

网关支持动态模型配置，通过模型路由管理器统一管理：

```python
# 注册新模型
model_route_manager.register_model(ModelConfig(
    name="ds-vet-answer-32B",
    service_url="http://************:8888",
    description="宠物医疗专用32B参数模型",
    max_tokens=4096,
    owned_by="vet-ai-platform"
))
```

### 2. API密钥管理

支持多种API密钥格式：

```python
# OpenAI标准格式
"sk-proj-1oy7NLULP93kNFR9fEh9nYib1qLtleVYlEiw1BKzg4tIz6Ba"

# 企业自有格式
"y03ZvBwVvHAqAo0nosjdlVGCfCNYGlgm8U"
```

### 3. 使用示例

#### 聊天完成API
```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "ds-vet-answer-32B",
    "messages": [
      {"role": "user", "content": "我的狗狗咳嗽怎么办？"}
    ],
    "stream": false
  }'
```

#### 模型列表API
```bash
curl -X GET http://localhost:8000/v1/models \
  -H "Authorization: Bearer your-api-key"
```

## 配置参数详解

### 环境变量配置

| 变量名 | 默认值 | 说明 |
|-------|--------|------|
| `GATEWAY_PORT` | 8000 | 网关监听端口 |
| `DEBUG` | false | 调试模式开关 |
| `USER_SERVICE_URL` | http://localhost:8001 | 用户服务地址 |
| `PET_SERVICE_URL` | http://localhost:8002 | 宠物服务地址 |
| `APP_SERVICE_URL` | http://localhost:8003 | 应用服务地址 |
| `DATABASE_URL` | postgresql://... | 数据库连接字符串 |
| `REDIS_URL` | redis://localhost:6379/0 | Redis连接字符串 |
| `SECRET_KEY` | your-secret-key | JWT签名密钥 |
| `RATE_LIMIT_PER_MINUTE` | 60 | 每分钟请求限制 |
| `RATE_LIMIT_BURST` | 10 | 突发请求限制 |
| `REQUEST_TIMEOUT` | 30 | 请求超时时间（秒） |
| `LOG_LEVEL` | INFO | 日志级别 |

### 配置文件示例

```python
# gateway/config/settings.py
class Settings(BaseSettings):
    # API基础配置
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Vet Open Platform - API Gateway"
    PROJECT_VERSION: str = "1.0.0"
    
    # 网关服务配置
    GATEWAY_PORT: int = 8000
    
    # 后端服务配置
    USER_SERVICE_URL: str = "http://localhost:8001"
    PET_SERVICE_URL: str = "http://localhost:8002"
    APP_SERVICE_URL: str = "http://localhost:8003"
    
    # 数据存储配置
    DATABASE_URL: str = "postgresql://..."
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # 认证配置
    SECRET_KEY: str = "your-secret-key"
    ALGORITHM: str = "HS256"
    
    # 限流配置
    RATE_LIMIT_PER_MINUTE: int = 60
    RATE_LIMIT_BURST: int = 10
    
    # 超时配置
    REQUEST_TIMEOUT: int = 30
```

## 部署和运维指南

### 1. 本地开发部署

#### 环境准备
```bash
# 1. 克隆项目
git clone <repository-url>
cd vet_open_platform

# 2. 创建虚拟环境
conda create -n vet_open_platform python=3.11
conda activate vet_open_platform

# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置环境变量
cp .env.example .env
# 编辑 .env 文件配置数据库和Redis连接
```

#### 启动服务
```bash
# 方法1：使用启动脚本
python start_gateway.py

# 方法2：直接启动
uvicorn gateway.main:app --host 0.0.0.0 --port 8000 --reload

# 方法3：使用开发脚本（启动所有服务）
python scripts/start_dev.py
```

### 2. Docker容器化部署

#### Dockerfile配置
```dockerfile
FROM python:3.11-slim

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential libpq-dev curl \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制代码
COPY shared/ ./shared/
COPY gateway/ ./gateway/

# 创建非root用户
RUN adduser --disabled-password appuser
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s \
    CMD curl -f http://localhost:8000/health || exit 1

EXPOSE 8000
CMD ["uvicorn", "gateway.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### Docker Compose配置
```yaml
version: '3.8'

services:
  gateway:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=******************************/vet_platform
      - REDIS_URL=redis://redis:6379/0
      - USER_SERVICE_URL=http://user-service:8001
      - PET_SERVICE_URL=http://pet-service:8002
      - APP_SERVICE_URL=http://app-service:8003
    depends_on:
      - redis
      - db
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: vet_platform
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
    ports:
      - "5432:5432"
```

### 3. 生产环境部署

#### 环境配置
```bash
# 生产环境变量
export DEBUG=false
export LOG_LEVEL=WARNING
export RATE_LIMIT_PER_MINUTE=100
export REQUEST_TIMEOUT=60
export SECRET_KEY="your-production-secret-key"
```

#### 性能优化
```python
# 使用Gunicorn部署
gunicorn gateway.main:app \
    --workers 4 \
    --worker-class uvicorn.workers.UvicornWorker \
    --bind 0.0.0.0:8000 \
    --access-logfile - \
    --error-logfile -
```

### 4. 监控和健康检查

#### 健康检查端点

**基础健康检查**：
```bash
curl http://localhost:8000/health
```

响应示例：
```json
{
  "status": "healthy",
  "service": "api_gateway",
  "version": "1.0.0",
  "redis": "connected",
  "backend_services": {
    "user_service": {
      "status": "healthy",
      "response_time": 0.05
    },
    "pet_service": {
      "status": "healthy",
      "response_time": 0.03
    },
    "app_service": {
      "status": "healthy",
      "response_time": 0.04
    }
  }
}
```

**详细状态检查**：
```bash
curl http://localhost:8000/status
```

#### 日志监控

**日志格式**：
```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "level": "INFO",
  "service": "api_gateway",
  "request_id": "uuid-string",
  "message": "API请求: GET /api/v1/pets - 状态码: 200 - 耗时: 0.123秒",
  "user_id": 123,
  "method": "GET",
  "path": "/api/v1/pets",
  "status_code": 200,
  "processing_time": 0.123
}
```

**日志聚合**：
- 使用ELK Stack或类似工具
- 按服务、用户、时间维度分析
- 设置告警规则

#### 性能指标

**关键指标**：
- 请求QPS（每秒查询数）
- 响应时间分布
- 错误率统计
- 限流触发次数
- 后端服务健康状态

**监控工具**：
- Prometheus + Grafana
- 自定义监控面板
- 告警通知机制

### 5. 故障排查

#### 常见问题

**1. 服务连接失败**
```bash
# 检查后端服务状态
curl http://localhost:8001/health  # 用户服务
curl http://localhost:8002/health  # 宠物服务
curl http://localhost:8003/health  # 应用服务

# 检查网络连通性
ping user-service-host
telnet user-service-host 8001
```

**2. Redis连接问题**
```bash
# 检查Redis连接
redis-cli ping

# 检查Redis配置
redis-cli config get "*"
```

**3. 认证失败**
```bash
# 检查JWT配置
echo $SECRET_KEY

# 验证API密钥
curl -H "Authorization: Bearer your-api-key" \
     http://localhost:8000/v1/models
```

**4. 限流问题**
```bash
# 检查限流配置
echo $RATE_LIMIT_PER_MINUTE
echo $RATE_LIMIT_BURST

# 查看Redis中的限流数据
redis-cli keys "rate_limit:*"
```

#### 日志分析

**错误日志查看**：
```bash
# 查看网关日志
tail -f logs/gateway.log

# 过滤错误日志
grep "ERROR" logs/gateway.log

# 按请求ID追踪
grep "request-id-uuid" logs/gateway.log
```

**性能分析**：
```bash
# 慢请求分析
grep "processing_time.*[5-9]\." logs/gateway.log

# 错误率统计
grep "status_code.*[45][0-9][0-9]" logs/gateway.log | wc -l
```

## API使用示例

### 1. 传统RESTful API

#### 用户认证
```bash
# 用户登录
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'

# 响应
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}
```

#### 使用JWT访问API
```bash
# 获取宠物列表
curl -X GET http://localhost:8000/api/v1/pets \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."

# 创建宠物
curl -X POST http://localhost:8000/api/v1/pets \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "name": "小白",
    "breed": "金毛",
    "age": 2,
    "owner_id": 1
  }'
```

### 2. OpenAI兼容API

#### 聊天完成（非流式）
```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer y03ZvBwVvHAqAo0nosjdlVGCfCNYGlgm8U" \
  -d '{
    "model": "ds-vet-answer-32B",
    "messages": [
      {
        "role": "system",
        "content": "你是一个专业的宠物医生助手"
      },
      {
        "role": "user",
        "content": "我的狗狗最近食欲不振，应该怎么办？"
      }
    ],
    "temperature": 0.7,
    "max_tokens": 1000
  }'
```

#### 聊天完成（流式）
```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer y03ZvBwVvHAqAo0nosjdlVGCfCNYGlgm8U" \
  -d '{
    "model": "ds-vet-answer-32B",
    "messages": [
      {"role": "user", "content": "猫咪呕吐的常见原因有哪些？"}
    ],
    "stream": true
  }' \
  --no-buffer
```

#### 获取模型列表
```bash
curl -X GET http://localhost:8000/v1/models \
  -H "Authorization: Bearer y03ZvBwVvHAqAo0nosjdlVGCfCNYGlgm8U"

# 响应
{
  "object": "list",
  "data": [
    {
      "id": "ds-vet-answer-32B",
      "object": "model",
      "created": 1677652288,
      "owned_by": "vet-ai-platform"
    },
    {
      "id": "ds-vet-answer-72B",
      "object": "model",
      "created": 1677652288,
      "owned_by": "vet-ai-platform"
    }
  ]
}
```

#### 文本嵌入
```bash
curl -X POST http://localhost:8000/v1/embeddings \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer y03ZvBwVvHAqAo0nosjdlVGCfCNYGlgm8U" \
  -d '{
    "model": "ds-vet-answer-32B",
    "input": "宠物疫苗接种的重要性"
  }'
```

#### 使用统计
```bash
curl -X GET http://localhost:8000/v1/usage \
  -H "Authorization: Bearer y03ZvBwVvHAqAo0nosjdlVGCfCNYGlgm8U"

# 响应
{
  "object": "usage",
  "user_id": "4",
  "current_month": {
    "requests": 45,
    "tokens": 12500,
    "cost": 0.025
  },
  "limits": {
    "max_requests_per_month": 1000,
    "max_tokens_per_month": 100000
  }
}
```

### 3. Python SDK示例

#### 使用OpenAI SDK
```python
import openai

# 配置客户端
client = openai.OpenAI(
    api_key="y03ZvBwVvHAqAo0nosjdlVGCfCNYGlgm8U",
    base_url="http://localhost:8000/v1"
)

# 聊天完成
response = client.chat.completions.create(
    model="ds-vet-answer-32B",
    messages=[
        {"role": "system", "content": "你是专业的宠物医生"},
        {"role": "user", "content": "狗狗拉肚子怎么办？"}
    ],
    temperature=0.7,
    max_tokens=1000
)

print(response.choices[0].message.content)

# 流式聊天
stream = client.chat.completions.create(
    model="ds-vet-answer-32B",
    messages=[
        {"role": "user", "content": "介绍一下猫咪的常见疾病"}
    ],
    stream=True
)

for chunk in stream:
    if chunk.choices[0].delta.content is not None:
        print(chunk.choices[0].delta.content, end="")
```

#### 使用requests库
```python
import requests

# API配置
BASE_URL = "http://localhost:8000"
API_KEY = "y03ZvBwVvHAqAo0nosjdlVGCfCNYGlgm8U"

headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

# 聊天完成
data = {
    "model": "ds-vet-answer-32B",
    "messages": [
        {"role": "user", "content": "宠物疫苗接种时间表"}
    ]
}

response = requests.post(
    f"{BASE_URL}/v1/chat/completions",
    headers=headers,
    json=data
)

result = response.json()
print(result["choices"][0]["message"]["content"])
```

## 最佳实践

### 1. 安全配置

- **生产环境密钥**：使用强随机密钥，定期轮换
- **HTTPS部署**：生产环境必须使用HTTPS
- **API密钥管理**：实施密钥轮换和权限控制
- **敏感信息脱敏**：日志中不记录完整密钥

### 2. 性能优化

- **连接池配置**：合理设置HTTP连接池大小
- **缓存策略**：充分利用Redis缓存用户信息
- **异步处理**：使用异步I/O提高并发性能
- **资源监控**：监控CPU、内存、网络使用情况

### 3. 可靠性保障

- **健康检查**：实施完善的健康检查机制
- **熔断降级**：后端服务故障时的降级策略
- **重试机制**：网络异常时的智能重试
- **监控告警**：及时发现和处理异常

### 4. 扩展性设计

- **水平扩展**：支持多实例负载均衡部署
- **配置外部化**：通过环境变量或配置中心管理
- **模块化设计**：中间件和路由的模块化架构
- **版本管理**：API版本控制和向后兼容

## 总结

API网关作为宠物医疗平台的统一入口，提供了完整的请求处理、认证授权、限流控制等功能。通过合理的架构设计和配置，能够确保系统的高可用性、安全性和可扩展性。

关键优势：
- **统一管理**：所有外部访问的单一入口点
- **安全可靠**：多重认证和精确限流保护
- **高性能**：异步处理和智能缓存优化
- **易维护**：模块化设计和完善的监控体系
- **标准兼容**：完全兼容OpenAI API规范

通过本文档的指导，开发团队可以快速理解网关的工作原理，正确配置和部署网关服务，并在生产环境中稳定运行。
