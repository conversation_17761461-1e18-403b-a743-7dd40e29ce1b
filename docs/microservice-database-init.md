# 微服务数据库初始化指南

## 📋 概述

本文档介绍了宠物医疗平台微服务架构下的数据库初始化方案。每个微服务都有独立的数据库初始化脚本，确保服务间的独立性和可维护性。

## 🏗️ 架构设计

### 微服务独立性原则

- ✅ **每个服务独立管理自己的数据库表**
- ✅ **避免跨服务的直接模型导入**
- ✅ **通过HTTP API进行服务间通信**
- ✅ **独立的初始化和部署流程**

### 服务列表

| 服务 | 端口 | 数据库表 | 初始化脚本 |
|------|------|----------|------------|
| User Service | 8001 | users, permissions, api_keys, user_sessions | `services/user_service/scripts/init_tables.py` |
| Pet Service | 8002 | pets, medical_records, vaccinations, breeds | `services/pet_service/scripts/init_tables.py` |
| App Service | 8003 | agents, agent_permissions, agent_executions | `services/app_service/scripts/init_tables.py` |
| Gateway | 8000 | api_request_logs | `gateway/scripts/init_tables.py` |

## 🚀 使用方法

### 1. 初始化所有服务（推荐）

```bash
# 初始化所有服务的数据库表和默认数据
python scripts/microservice_init.py

# 删除现有表后重新初始化
python scripts/microservice_init.py --drop --force

# 只创建表，不初始化默认数据
python scripts/microservice_init.py --tables-only
```

### 2. 初始化单个服务

```bash
# 初始化用户服务
python scripts/microservice_init.py --service user_service

# 初始化宠物服务
python scripts/microservice_init.py --service pet_service

# 初始化应用服务
python scripts/microservice_init.py --service app_service

# 初始化网关服务
python scripts/microservice_init.py --service gateway
```

### 3. 直接调用服务初始化脚本

```bash
# 用户服务
python services/user_service/scripts/init_tables.py
python services/user_service/scripts/init_tables.py --drop

# 宠物服务
python services/pet_service/scripts/init_tables.py
python services/pet_service/scripts/init_tables.py --drop

# 应用服务
python services/app_service/scripts/init_tables.py
python services/app_service/scripts/init_tables.py --drop

# 网关服务
python gateway/scripts/init_tables.py
python gateway/scripts/init_tables.py --drop
```

## 📊 初始化内容

### User Service
- **表结构**: users, permissions, api_keys, user_sessions
- **默认权限**: 用户管理、宠物管理、智能体管理等权限
- **默认用户**: admin/admin123 (管理员账号)

### Pet Service
- **表结构**: pets, medical_records, vaccinations, breeds
- **默认数据**: 常见宠物品种数据（犬类、猫类、其他小动物）

### App Service
- **表结构**: agents, agent_permissions, agent_executions
- **系统智能体**: AI问诊、AI视觉识别、报告生成、报告解读

### Gateway
- **表结构**: api_request_logs
- **功能**: API请求日志记录

## 🔧 参数说明

### 通用参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `--drop` | 删除现有表后重新创建 | `--drop` |
| `--tables-only` | 只创建表，不初始化数据 | `--tables-only` |
| `--force` | 强制执行，不询问确认 | `--force` |

### 微服务管理脚本专用参数

| 参数 | 说明 | 可选值 |
|------|------|--------|
| `--service` | 指定要初始化的服务 | `user_service`, `pet_service`, `app_service`, `gateway`, `all` |

## 🔍 故障排除

### 常见问题

1. **模块导入错误**
   ```
   ModuleNotFoundError: No module named 'services.xxx'
   ```
   **解决方案**: 确保在项目根目录执行脚本，并激活正确的conda环境

2. **数据库连接失败**
   ```
   Database connection failed
   ```
   **解决方案**: 检查 `.env` 文件中的数据库配置，确保数据库服务正在运行

3. **权限不足**
   ```
   Permission denied
   ```
   **解决方案**: 确保脚本有执行权限 `chmod +x scripts/*.py`

### 调试模式

```bash
# 查看详细输出
python scripts/microservice_init.py --service user_service -v

# 检查单个服务的初始化脚本
python services/user_service/scripts/init_tables.py --help
```

## 📝 开发指南

### 添加新的微服务

1. **创建服务目录结构**
   ```
   services/new_service/
   ├── scripts/
   │   └── init_tables.py
   ├── models/
   └── ...
   ```

2. **编写初始化脚本**
   - 参考现有服务的 `init_tables.py`
   - 只导入本服务的模型
   - 实现表创建和默认数据初始化

3. **更新管理脚本**
   - 在 `scripts/microservice_init.py` 中添加新服务
   - 更新服务列表和文档

### 最佳实践

1. **独立性**: 每个服务只管理自己的数据库表
2. **幂等性**: 初始化脚本可以重复执行
3. **错误处理**: 提供清晰的错误信息和恢复建议
4. **日志记录**: 记录初始化过程和结果
5. **版本控制**: 使用数据库迁移管理表结构变更

## 🔗 相关文档

- [微服务架构设计](./microservice-architecture.md)
- [数据库设计文档](./database-design.md)
- [API文档](./api-documentation.md)
- [部署指南](./deployment-guide.md)
