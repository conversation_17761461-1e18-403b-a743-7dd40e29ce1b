# 🔍 OCR识别服务API文档

## 📋 概述

OCR识别服务是宠物医疗AI开放平台的核心组件之一，提供强大的图片文字识别功能。基于QwenVL视觉大模型，支持多种图片格式和语言的高精度文字识别。

### 🎯 主要功能

- **📷 图片文字识别**: 支持JPEG、PNG、GIF、BMP、WebP等格式
- **🧠 智能模型**: 集成QwenVL视觉大模型，识别准确率高
- **🔄 批量处理**: 支持批量图片OCR处理和进度跟踪
- **🔌 可插拔架构**: 支持多种OCR模型，可灵活切换
- **📊 任务管理**: 完整的任务状态跟踪和结果存储
- **🌐 多语言支持**: 支持中文、英文、日文、韩文等多种语言

### 🚀 技术特性

- **异步处理**: 支持异步和同步两种处理模式
- **错误重试**: 自动重试机制，提高成功率
- **结果缓存**: 智能缓存机制，提升响应速度
- **监控日志**: 完整的请求日志和性能监控
- **安全认证**: 支持JWT和API Key双重认证

## 🔗 服务端点

### 基础信息
- **服务地址**: `http://localhost:8004`
- **API版本**: `v1`
- **基础路径**: `/api/v1/ocr`

## 📡 API接口

### 1. 单张图片OCR识别（同步）

**端点**: `POST /api/v1/ocr/extract/sync`

**描述**: 同步处理单张图片的OCR识别，直接返回结果。

**请求参数**:
```json
{
  "image_url": "https://example.com/image.jpg",
  "model_type": "qwen_vl",
  "prompt": "请识别图片中的所有文字内容",
  "language": "auto",
  "confidence_threshold": 0.8,
  "task_name": "医疗报告识别"
}
```

**响应示例**:
```json
{
  "success": true,
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "result": {
    "text": "识别出的文字内容",
    "confidence": 0.95,
    "language": "zh",
    "processing_time_ms": 1500,
    "bounding_boxes": [
      {
        "x": 10,
        "y": 20,
        "width": 100,
        "height": 30,
        "text": "文字内容",
        "confidence": 0.98
      }
    ],
    "metadata": {
      "model": "qwen_vl",
      "usage": {
        "total_tokens": 150
      }
    }
  },
  "processing_time_ms": 1500
}
```

### 2. 单张图片OCR识别（异步）

**端点**: `POST /api/v1/ocr/extract`

**描述**: 异步处理单张图片的OCR识别，返回任务ID，需要通过任务状态查询获取结果。

**请求参数**: 同上

**响应示例**:
```json
{
  "success": true,
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "result": null,
  "processing_time_ms": null
}
```

### 3. 批量图片OCR处理

**端点**: `POST /api/v1/ocr/batch`

**描述**: 批量处理多张图片的OCR识别，支持并行处理和进度跟踪。

**请求参数**:
```json
{
  "image_urls": [
    "https://example.com/image1.jpg",
    "https://example.com/image2.jpg"
  ],
  "model_type": "qwen_vl",
  "prompt": "请识别图片中的所有文字内容",
  "language": "auto",
  "confidence_threshold": 0.8,
  "batch_name": "医疗报告批量识别",
  "parallel_count": 3
}
```

### 4. 查询任务状态

**端点**: `GET /api/v1/ocr/task/{task_id}`

**描述**: 根据任务ID查询OCR任务的处理状态和结果。

**响应示例**:
```json
{
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "completed",
  "progress": 100,
  "result": {
    "text": "识别出的文字内容",
    "confidence": 0.95,
    "language": "zh",
    "processing_time_ms": 1500
  },
  "error": null,
  "created_at": "2025-01-14T12:00:00Z",
  "completed_at": "2025-01-14T12:00:02Z"
}
```

### 5. 获取可用模型列表

**端点**: `GET /api/v1/ocr/models`

**描述**: 返回当前支持的所有OCR模型及其状态信息。

**响应示例**:
```json
{
  "models": [
    {
      "id": "qwen_vl",
      "name": "Qwen VL",
      "type": "qwen_vl",
      "available": true,
      "supported_languages": ["auto", "zh", "en", "ja", "ko"],
      "description": "QwenVL视觉大模型"
    }
  ],
  "total": 1
}
```

### 6. 健康检查

**端点**: `GET /api/v1/ocr/health`

**描述**: 检查OCR服务及各个模型的健康状态。

**响应示例**:
```json
{
  "status": "healthy",
  "service": "ocr_service",
  "version": "1.0.0",
  "models": {
    "qwen_vl": true
  },
  "timestamp": "2025-01-14T12:00:00Z"
}
```

## 🔐 认证方式

### JWT令牌认证
```bash
curl -X POST "http://localhost:8004/api/v1/ocr/extract/sync" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "https://example.com/image.jpg",
    "model_type": "qwen_vl"
  }'
```

### API Key认证
```bash
curl -X POST "http://localhost:8004/api/v1/ocr/extract/sync" \
  -H "Authorization: Bearer sk-your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "https://example.com/image.jpg",
    "model_type": "qwen_vl"
  }'
```

## ⚙️ 配置参数

### 环境变量配置

```bash
# QwenVL配置
VL_QWEN_API_KEY=your-qwen-api-key
VL_QWEN_MODEL_NAME=qwen-vl-plus
VL_QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# OCR服务配置
OCR_SERVICE_PORT=8004
OCR_TASK_TIMEOUT=300
OCR_MAX_RETRY_COUNT=3
OCR_BATCH_SIZE=10
```

### 模型参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `model_type` | string | `qwen_vl` | OCR模型类型 |
| `prompt` | string | - | OCR提示词，可提高识别准确率 |
| `language` | string | `auto` | 识别语言，支持auto、zh、en等 |
| `confidence_threshold` | float | `0.8` | 置信度阈值，0.0-1.0 |

## 📊 错误码说明

| 错误码 | 说明 |
|--------|------|
| `OCR_ERROR` | 通用OCR错误 |
| `OCR_TIMEOUT` | OCR处理超时 |
| `OCR_MODEL_NOT_FOUND` | OCR模型未找到 |
| `OCR_INVALID_IMAGE` | 无效的图片格式或数据 |
| `QWEN_VL_NO_API_KEY` | QwenVL API密钥未配置 |
| `QWEN_VL_UNAVAILABLE` | QwenVL服务不可用 |

## 🚀 快速开始

### 1. 启动OCR服务

```bash
# 使用管理脚本启动
python scripts/manage.py service start-single ocr

# 或直接启动
python scripts/start_ocr_service.py
```

### 2. 测试OCR功能

```python
import requests

# 测试图片OCR
response = requests.post(
    "http://localhost:8004/api/v1/ocr/extract/sync",
    headers={"Authorization": "Bearer your-api-key"},
    json={
        "image_url": "https://example.com/test-image.jpg",
        "model_type": "qwen_vl"
    }
)

print(response.json())
```

## 📈 性能优化建议

1. **图片预处理**: 确保图片清晰度和对比度
2. **合理设置超时**: 根据图片复杂度调整超时时间
3. **批量处理**: 对于大量图片，使用批量接口提高效率
4. **缓存策略**: 相同图片会自动缓存结果
5. **并发控制**: 合理设置并行处理数量

---

> 📖 **更多文档**: 查看 [项目主文档](../README.md) 了解完整的平台功能
