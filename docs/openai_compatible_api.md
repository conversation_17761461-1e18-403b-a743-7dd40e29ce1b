# 🤖 OpenAI兼容API接口文档

## 📋 概述

宠物医疗AI开放平台提供完全兼容OpenAI API规范的接口服务，支持标准的OpenAI SDK直接调用。本文档详细说明了所有可用的API端点、请求参数、响应格式和使用示例。

### 🌟 核心特性

- ✅ **完全OpenAI兼容**: 支持标准OpenAI SDK和API格式
- 🚀 **流式响应**: 支持Server-Sent Events (SSE)流式输出
- 🔐 **灵活认证**: 支持多种API密钥格式
- 🎯 **专业模型**: 宠物医疗专用AI模型
- 📊 **使用统计**: 详细的调用统计和监控

## 🔗 基础信息

- **基础URL**: `https://open.haoshouyi.com`
- **API版本**: `v1`
- **协议**: HTTPS
- **认证方式**: Bearer Token (API Key)

## 🔐 认证

所有API请求都需要在请求头中包含有效的API密钥：

```http
Authorization: Bearer sk-proj-your-api-key-here
```

### 支持的API密钥格式

1. **OpenAI标准格式**: `sk-proj-xxxxxxxxxx`
2. **企业自有格式**: 任意长度的字符串

## 📚 API端点

### 1. 聊天

创建聊天完成，支持流式和非流式响应。

#### 接口
```
POST /v1/chat/completions
```

#### 请求参数

| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| `model` | string | 是 | 要使用的模型名称 |
| `messages` | array | 是 | 对话消息列表 |
| `stream` | boolean | 否 | 是否启用流式响应，默认false |
| `temperature` | number | 否 | 控制随机性，0-2之间，默认1 |
| `max_tokens` | integer | 否 | 生成的最大token数 |
| `top_p` | number | 否 | 核采样参数，0-1之间 |
| `frequency_penalty` | number | 否 | 频率惩罚，-2到2之间 |
| `presence_penalty` | number | 否 | 存在惩罚，-2到2之间 |

#### 消息格式

```json
{
  "role": "user|assistant|system",
  "content": "消息内容"
}
```

#### 请求示例

**非流式请求**:
```bash
curl -X POST https://open.haoshouyi.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-proj-your-api-key" \
  -d '{
    "model": "ds-vet-answer-32B",
    "messages": [
      {"role": "system", "content": "你是一个专业的宠物医疗AI助手。"},
      {"role": "user", "content": "狗狗呕吐怎么处理？"}
    ],
    "temperature": 0.7,
    "max_tokens": 1000
  }'
```

**流式请求**:
```bash
curl -N -X POST https://open.haoshouyi.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-proj-your-api-key" \
  -d '{
    "model": "ds-vet-answer-32B",
    "messages": [
      {"role": "user", "content": "介绍猫咪常见疾病"}
    ],
    "stream": true
  }'
```

#### 响应格式

**非流式响应**:
```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": 1677652288,
  "model": "ds-vet-answer-32B",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "狗狗呕吐可能由多种原因引起..."
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 56,
    "completion_tokens": 31,
    "total_tokens": 87
  }
}
```

**流式响应**:
```
data: {"event": "start", "conversation_id": "conv_report_20", "agent_id": 1, "agent_name": "AI问诊智能体", "user_message_id": 154, "model": null}

data: {"event": "model_info", "conversation_id": "conv_report_20", "model": "ds-vet-answer-32B"}

data: {"event": "content", "conversation_id": "conv_report_20", "content": "好的"}

data: {"event": "content", "conversation_id": "conv_report_20", "content": "，"}

data: {"event": "content", "conversation_id": "conv_report_20", "content": "我现在"}
```


## 🎯 支持的模型

| 模型名称 | 描述 | 最大Token | 适用场景 |
|----------|------|-----------|----------|
| `ds-vet-answer-32B` | 宠物医疗专用32B参数模型 | 4096 | 宠物医疗咨询 |

## 💻 SDK使用示例

### Python (OpenAI SDK)

```python
from openai import OpenAI

# 初始化客户端
client = OpenAI(
    api_key="sk-proj-your-api-key",
    base_url="https://open.haoshouyi.com/v1"
)

# # 聊天
# response = client.chat.completions.create(
#     model="ds-vet-answer-32B",
#     messages=[
#         {"role": "system", "content": "你是一个专业的宠物医疗AI助手。"},
#         {"role": "user", "content": "狗狗呕吐怎么处理？"}
#     ]
# )

# print(response.choices[0].message.content)

# 流式聊天
stream = client.chat.completions.create(
    model="ds-vet-answer-32B",
    messages=[{"role": "user", "content": "介绍猫咪常见疾病"}],
    stream=True
)

for chunk in stream:
    if chunk.choices[0].delta.content is not None:
        print(chunk.choices[0].delta.content, end="")

```


## ⚠️ 错误处理

### 常见错误码

| 状态码 | 错误类型 | 描述 |
|--------|----------|------|
| 400 | `invalid_request_error` | 请求参数无效 |
| 401 | `authentication_error` | API密钥无效或缺失 |
| 403 | `permission_error` | 权限不足 |
| 404 | `not_found_error` | 资源不存在 |
| 429 | `rate_limit_exceeded` | 请求频率超限 |
| 500 | `api_error` | 服务器内部错误 |

### 错误响应格式

```json
{
  "error": {
    "message": "不支持的模型 'invalid-model'",
    "type": "invalid_request_error",
    "code": "model_not_found"
  }
}
```


## 🔒 安全最佳实践

1. **保护API密钥**: 不要在客户端代码中暴露API密钥
2. **使用HTTPS**: 始终通过HTTPS发送请求
3. **输入验证**: 验证和清理用户输入
4. **监控使用**: 定期检查API使用情况
5. **轮换密钥**: 定期更换API密钥
