# 宠物医疗 AI 开放平台 - API 接口文档

## 📋 概述

本文档详细描述了宠物医疗 AI 开放平台的所有 API 接口，包括用户服务、宠物服务、应用服务、OCR 服务和 OpenAI 兼容代理服务。

**⚠️ 重要说明**: 本文档已根据实际代码实现进行了全面校准，确保所有参数、字段类型、验证规则和响应格式与代码库完全一致。

### 📝 文档准确性保证

- ✅ **参数完整性**: 所有必需参数和可选参数均已标注
- ✅ **类型准确性**: 字段类型、枚举值、数值范围与代码一致
- ✅ **验证规则**: 字符长度限制、格式要求等验证规则准确
- ✅ **响应格式**: 响应数据结构与实际 API 返回格式匹配
- ✅ **路径正确性**: API 路径与实际路由配置一致

## 🌐 服务架构

### 服务端口配置

- **API 网关**: `http://localhost:8000`
- **用户服务**: `http://localhost:8001`
- **宠物服务**: `http://localhost:8002`
- **应用服务**: `http://localhost:8003`
- **OCR 服务**: `http://localhost:8004`

### API 文档地址

- **API 网关**: `http://localhost:8000/api/v1/docs`
- **用户服务**: `http://localhost:8001/api/v1/docs`
- **宠物服务**: `http://localhost:8002/api/v1/docs`
- **应用服务**: `http://localhost:8003/api/v1/docs`
- **OCR 服务**: `http://localhost:8004/docs`

## 🔐 认证方式

### 1. JWT Token 认证

```bash
Authorization: Bearer <access_token>
```

### 2. API Key 认证

```bash
Authorization: Bearer <api_key>
# 或
X-API-Key: <api_key>
```

## 📚 API 接口详情

## 1. 用户服务 (User Service)

### 1.1 认证接口 (`/api/v1/auth`)

#### 用户登录

- **POST** `/api/v1/auth/login`
- **描述**: 用户登录获取访问令牌
- **请求体**:

```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

- **响应**:

```json
{
  "success": true,
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_in": 3600
  },
  "message": "登录成功"
}
```

#### 用户注册

- **POST** `/api/v1/auth/register`
- **描述**: 新用户注册
- **请求体**:

```json
{
  "email": "<EMAIL>",
  "username": "username",
  "password": "password123",
  "first_name": "张",
  "last_name": "三",
  "phone": "13800138000"
}
```

#### 刷新令牌

- **POST** `/api/v1/auth/refresh`
- **描述**: 使用刷新令牌获取新的访问令牌
- **请求体**:

```json
{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

#### 获取当前用户信息

- **GET** `/api/v1/auth/me`
- **描述**: 获取当前登录用户的详细信息
- **认证**: 需要 JWT Token

#### 用户登出

- **POST** `/api/v1/auth/logout`
- **描述**: 用户登出，使令牌失效
- **认证**: 需要 JWT Token

### 1.2 用户管理接口 (`/api/v1/users`)

#### 获取用户列表

- **GET** `/api/v1/users`
- **描述**: 获取用户列表（需要租户管理员权限）
- **认证**: 需要租户管理员权限
- **查询参数**:
  - `page`: 页码，默认 1，最小 1（可选）
  - `size`: 每页数量，默认 10，范围 1-100（可选）
  - `search`: 搜索关键词，支持用户名、邮箱、姓名搜索（可选）
  - `is_active`: 是否激活过滤，布尔值（可选）

#### 获取用户详情

- **GET** `/api/v1/users/{user_id}`
- **描述**: 获取指定用户的详细信息
- **认证**: 需要用户访问权限

#### 创建用户

- **POST** `/api/v1/users`
- **描述**: 创建新用户（需要租户管理员权限）
- **认证**: 需要租户管理员权限
- **请求体**:

```json
{
  "email": "<EMAIL>",
  "username": "newuser",
  "password": "Password123",
  "first_name": "张",
  "last_name": "三",
  "phone": "13800138000",
  "avatar_url": "https://example.com/avatar.jpg",
  "tenant_id": 1,
  "is_active": true,
  "is_superuser": false,
  "is_verified": false,
  "tenant_role_id": 2
}
```

- **字段说明**:
  - `email`: 邮箱地址（必需）
  - `username`: 用户名，3-100 字符（必需）
  - `password`: 密码，至少 8 字符，必须包含大小写字母和数字（必需）
  - `first_name`: 名字，最大 100 字符（可选）
  - `last_name`: 姓氏，最大 100 字符（可选）
  - `phone`: 电话号码，最大 20 字符（可选）
  - `avatar_url`: 头像 URL，最大 500 字符（可选）
  - `tenant_id`: 所属租户 ID（可选，非全局管理员会被强制设置为当前租户）
  - `is_active`: 是否激活，默认 true（可选）
  - `is_superuser`: 是否超级用户，默认 false（可选）
  - `is_verified`: 是否已验证，默认 false（可选）
  - `tenant_role_id`: 租户角色 ID（可选）

#### 更新用户信息

- **PUT** `/api/v1/users/{user_id}`
- **描述**: 更新用户信息
- **认证**: 需要用户访问权限

#### 删除用户

- **DELETE** `/api/v1/users/{user_id}`
- **描述**: 软删除用户
- **认证**: 需要租户管理员权限

### 1.3 API 密钥管理接口 (`/api/v1/api-keys`)

#### 获取 API 密钥列表

- **GET** `/api/v1/api-keys`
- **描述**: 获取 API 密钥列表
- **认证**: 需要 JWT Token
- **查询参数**:
  - `page`: 页码，默认 1，最小 1（可选）
  - `size`: 每页数量，默认 10，范围 1-100（可选）
  - `is_active`: 是否激活过滤，布尔值（可选）
  - `user_id`: 用户 ID 过滤，仅租户管理员可用（可选）

#### 创建 API 密钥

- **POST** `/api/v1/api-keys`
- **描述**: 创建新的 API 密钥
- **请求体**:

```json
{
  "name": "我的API密钥",
  "description": "用于第三方应用集成",
  "expires_days": 365
}
```

- **字段说明**:
  - `name`: API 密钥名称，最大 100 字符（必需）
  - `description`: API 密钥描述（可选）
  - `expires_days`: 过期天数，1-365 天（可选）

#### 获取 API 密钥详情

- **GET** `/api/v1/api-keys/{key_id}`
- **描述**: 获取指定 API 密钥的详细信息

#### 更新 API 密钥

- **PUT** `/api/v1/api-keys/{key_id}`
- **描述**: 更新 API 密钥信息

#### 删除 API 密钥

- **DELETE** `/api/v1/api-keys/{key_id}`
- **描述**: 删除 API 密钥

#### 验证 API 密钥

- **GET** `/api/v1/api-keys/verify/{api_key}`
- **描述**: 验证 API 密钥并返回用户信息（用于网关认证）

### 1.4 权限管理接口 (`/api/v1/permissions`)

#### 获取权限列表

- **GET** `/api/v1/permissions`
- **描述**: 获取系统所有权限列表

#### 获取用户权限

- **GET** `/api/v1/permissions/user/{user_id}`
- **描述**: 获取指定用户的权限列表

### 1.5 租户管理接口 (`/api/v1/tenants`)

#### 获取租户列表

- **GET** `/api/v1/tenants`
- **描述**: 获取租户列表（全局管理员）

#### 创建租户

- **POST** `/api/v1/tenants`
- **描述**: 创建新租户（全局管理员）

#### 获取租户详情

- **GET** `/api/v1/tenants/{tenant_id}`
- **描述**: 获取租户详细信息

#### 更新租户信息

- **PUT** `/api/v1/tenants/{tenant_id}`
- **描述**: 更新租户信息

### 1.6 租户管理扩展接口 (`/api/v1/tenant-management`)

#### 获取租户统计信息

- **GET** `/api/v1/tenant-management/{tenant_id}/stats`
- **描述**: 获取租户的统计信息

#### 更新租户订阅

- **PUT** `/api/v1/tenant-management/{tenant_id}/subscription`
- **描述**: 更新租户订阅信息

#### 暂停/恢复租户

- **POST** `/api/v1/tenant-management/{tenant_id}/suspend`
- **POST** `/api/v1/tenant-management/{tenant_id}/resume`
- **描述**: 暂停或恢复租户服务

## 2. 宠物服务 (Pet Service)

### 2.1 宠物管理接口 (`/api/v1/pets`)

#### 获取宠物列表

- **GET** `/api/v1/pets`
- **描述**: 获取宠物列表
- **认证**: 需要 JWT Token
- **查询参数**:
  - `page`: 页码，默认 1，最小 1（可选）
  - `size`: 每页数量，默认 10，范围 1-100（可选）
  - `search`: 搜索关键词，支持宠物名称、品种搜索（可选）
  - `species`: 物种过滤（可选）
  - `status`: 状态过滤，枚举值：active/inactive/lost/deceased（可选）
  - `owner_id`: 主人 ID 过滤，仅管理员可用（可选）

#### 获取宠物详情

- **GET** `/api/v1/pets/{pet_id}`
- **描述**: 获取指定宠物的详细信息

#### 创建宠物

- **POST** `/api/v1/pets`
- **描述**: 创建新宠物记录
- **认证**: 需要 JWT Token
- **请求体**:

```json
{
  "name": "小白",
  "species": "犬",
  "breed": "金毛犬",
  "gender": "male",
  "birth_date": "2020-01-01",
  "age_years": 3,
  "age_months": 6,
  "color": "金黄色",
  "weight": 25.5,
  "height": 60.0,
  "microchip_id": "123456789012345",
  "registration_number": "REG001",
  "status": "active",
  "is_neutered": false,
  "is_vaccinated": true,
  "description": "活泼可爱的金毛犬",
  "special_needs": "需要定期运动",
  "avatar_url": "https://example.com/pet-avatar.jpg",
  "owner_id": 1
}
```

- **字段说明**:
  - `name`: 宠物姓名，1-100 字符（必需）
  - `species`: 物种，1-50 字符（必需）
  - `breed`: 品种，最大 100 字符（可选）
  - `gender`: 性别，枚举值：male/female/unknown，默认 unknown（可选）
  - `birth_date`: 出生日期，格式 YYYY-MM-DD（可选）
  - `age_years`: 年龄(年)，0-50（可选）
  - `age_months`: 年龄(月)，0-11（可选）
  - `color`: 毛色，最大 100 字符（可选）
  - `weight`: 体重(kg)，0-200（可选）
  - `height`: 身高(cm)，0-200（可选）
  - `microchip_id`: 芯片号，最大 50 字符（可选）
  - `registration_number`: 注册号，最大 100 字符（可选）
  - `status`: 状态，枚举值：active/inactive/lost/deceased，默认 active（可选）
  - `is_neutered`: 是否绝育，默认 false（可选）
  - `is_vaccinated`: 是否接种疫苗，默认 false（可选）
  - `description`: 描述（可选）
  - `special_needs`: 特殊需求（可选）
  - `avatar_url`: 头像 URL，最大 500 字符（可选）
  - `owner_id`: 主人 ID（必需）

#### 更新宠物信息

- **PUT** `/api/v1/pets/{pet_id}`
- **描述**: 更新宠物信息

#### 删除宠物

- **DELETE** `/api/v1/pets/{pet_id}`
- **描述**: 删除宠物记录

### 2.2 医疗记录接口

#### 获取宠物医疗记录列表

- **GET** `/api/v1/pets/{pet_id}/medical-records`
- **描述**: 获取指定宠物的医疗记录列表
- **认证**: 需要 JWT Token
- **查询参数**:
  - `page`: 页码，默认 1，最小 1（可选）
  - `size`: 每页数量，默认 10，范围 1-100（可选）
- **路径参数**:
  - `pet_id`: 宠物 ID（必需）

#### 创建医疗记录

- **POST** `/api/v1/pets/{pet_id}/medical-records`
- **描述**: 为指定宠物创建新的医疗记录
- **认证**: 需要 JWT Token
- **路径参数**:
  - `pet_id`: 宠物 ID（必需）
- **请求体**:

```json
{
  "pet_id": 1,
  "visit_date": "2024-01-15T10:30:00Z",
  "diagnosis": "轻微感冒",
  "symptoms": "流鼻涕，轻微咳嗽",
  "treatment": "休息，多喝水，服用感冒药",
  "veterinarian_name": "李医生",
  "clinic_name": "宠物医院",
  "cost": 150.0,
  "notes": "建议一周后复查"
}
```

- **字段说明**:
  - `pet_id`: 宠物 ID，必须与路径参数一致（必需）
  - `visit_date`: 就诊日期时间（必需）
  - `diagnosis`: 诊断结果，至少 1 字符（必需）
  - `symptoms`: 症状描述（可选）
  - `treatment`: 治疗方案（可选）
  - `veterinarian_name`: 兽医姓名，最大 100 字符（可选）
  - `clinic_name`: 诊所名称，最大 200 字符（可选）
  - `cost`: 费用，必须 ≥0（可选）
  - `notes`: 备注（可选）

#### 获取医疗记录详情

- **GET** `/api/v1/medical-records/{record_id}`
- **描述**: 获取指定医疗记录的详细信息
- **认证**: 需要 JWT Token
- **路径参数**:
  - `record_id`: 医疗记录 ID（必需）

#### 更新医疗记录

- **PUT** `/api/v1/medical-records/{record_id}`
- **描述**: 更新医疗记录信息
- **认证**: 需要 JWT Token
- **路径参数**:
  - `record_id`: 医疗记录 ID（必需）

### 2.3 疫苗接种接口 (`/api/v1/vaccinations`)

#### 获取疫苗记录列表

- **GET** `/api/v1/vaccinations`
- **描述**: 获取疫苗接种记录列表

#### 创建疫苗记录

- **POST** `/api/v1/vaccinations`
- **描述**: 创建新的疫苗接种记录

#### 获取疫苗记录详情

- **GET** `/api/v1/vaccinations/{vaccination_id}`
- **描述**: 获取指定疫苗记录的详细信息

### 2.4 品种管理接口 (`/api/v1/breeds`)

#### 获取品种列表

- **GET** `/api/v1/breeds`
- **描述**: 获取宠物品种列表

#### 创建品种

- **POST** `/api/v1/breeds`
- **描述**: 创建新的宠物品种

#### 获取品种详情

- **GET** `/api/v1/breeds/{breed_id}`
- **描述**: 获取指定品种的详细信息

### 2.5 内部 API 接口 (`/internal`)

#### 获取宠物信息（内部调用）

- **GET** `/internal/pets/{pet_id}`
- **描述**: 供其他服务内部调用的宠物信息接口
- **认证**: 无需认证（内部调用）

## 3. 应用服务 (App Service)

### 3.1 智能体管理接口 (`/api/v1/agents`)

#### 获取智能体列表

- **GET** `/api/v1/agents`
- **描述**: 获取智能体列表，支持无认证访问公开智能体
- **认证**: 可选（无认证时只能访问公开智能体）
- **查询参数**:
  - `agent_type`: 智能体类型，枚举值：system/custom（可选）
  - `system_category`: 系统智能体分类，枚举值：diagnosis/vision/report_generation/report_interpretation（可选）
  - `status`: 状态过滤，枚举值：active/inactive/draft（可选）
  - `is_public`: 是否公开，布尔值（可选）
  - `search`: 搜索关键词（可选）
  - `page`: 页码，默认 1，最小 1（可选）
  - `page_size`: 每页数量，默认 20，范围 1-100（可选）

#### 获取智能体详情

- **GET** `/api/v1/agents/{agent_id}`
- **描述**: 获取指定智能体的详细信息，支持无认证访问公开智能体

#### 创建智能体

- **POST** `/api/v1/agents`
- **描述**: 创建新的智能体
- **认证**: 需要 JWT Token
- **请求体**:

```json
{
  "name": "我的智能体",
  "description": "智能体描述",
  "agent_type": "custom",
  "prompt_template": "你是一个专业的宠物医疗助手...",
  "is_public": false,
  "config": {
    "model": "gpt-3.5-turbo",
    "temperature": 0.7,
    "max_tokens": 1000
  }
}
```

#### 更新智能体

- **PUT** `/api/v1/agents/{agent_id}`
- **描述**: 更新智能体信息
- **认证**: 需要 JWT Token

#### 删除智能体

- **DELETE** `/api/v1/agents/{agent_id}`
- **描述**: 删除智能体
- **认证**: 需要 JWT Token

#### 执行智能体

- **POST** `/api/v1/agents/{agent_id}/execute`
- **描述**: 执行智能体，支持流式和非流式输出，支持多种输入格式
- **认证**: 需要 JWT Token 或 API Key
- **路径参数**:
  - `agent_id`: 智能体 ID（必需）
- **请求体**（支持多种输入格式）:

**方式 1: OpenAI 风格的 messages 输入**

```json
{
  "messages": [
    {
      "role": "system",
      "content": "你是一个专业的宠物医疗助手"
    },
    {
      "role": "user",
      "content": "我的金毛犬最近食欲不振，该怎么办？"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 1000,
  "stream": false
}
```

**方式 2: 直接文本输入**

```json
{
  "prompt": "我的金毛犬最近食欲不振，精神萎靡，症状持续2天，请给出诊断建议。",
  "temperature": 0.7,
  "max_tokens": 1000,
  "stream": false
}
```

**方式 3: 结构化输入（向后兼容）**

```json
{
  "structured_input": {
    "pet_info": {
      "breed": "金毛犬",
      "age": "3岁",
      "gender": "雄性",
      "weight": "25kg"
    },
    "symptoms": ["食欲不振", "精神萎靡"],
    "additional_info": "症状持续2天"
  },
  "temperature": 0.7,
  "max_tokens": 1000,
  "stream": false
}
```

- **字段说明**:
  - `messages`: OpenAI 风格的对话消息列表（可选，与其他输入方式互斥）
  - `prompt`: 直接提示文本（可选，与其他输入方式互斥）
  - `structured_input`: 结构化输入数据（可选，与其他输入方式互斥）
  - `temperature`: 温度参数，0-2，默认 0.7（可选）
  - `max_tokens`: 最大 token 数，≥1（可选）
  - `stream`: 是否流式输出，默认 false（可选）

#### 获取智能体统计信息

- **GET** `/api/v1/agents/{agent_id}/stats`
- **描述**: 获取智能体的使用统计信息
- **认证**: 需要 JWT Token

### 3.2 系统级智能体接口 (`/api/v1/system-agents`)

#### 获取系统智能体列表

- **GET** `/api/v1/system-agents`
- **描述**: 获取所有系统级智能体列表
- **查询参数**:
  - `category`: 智能体分类 (diagnosis/vision/report_generation/report_interpretation)
  - `is_active`: 是否激活

#### 获取系统智能体详情

- **GET** `/api/v1/system-agents/{agent_id}`
- **描述**: 获取指定系统智能体的详细信息

#### AI 问诊智能体

- **POST** `/api/v1/system-agents/diagnosis/execute`
- **描述**: 执行 AI 问诊智能体
- **请求体**:

```json
{
  "pet_info": {
    "breed": "金毛犬",
    "age": "3岁",
    "gender": "雄性",
    "weight": "25kg"
  },
  "symptoms": ["食欲不振", "精神萎靡", "呕吐"],
  "additional_info": "症状持续2天，体温正常",
  "stream": true
}
```

#### 报告生成智能体

- **POST** `/api/v1/system-agents/report-generation/execute`
- **描述**: 执行报告生成智能体
- **请求体**:

```json
{
  "pet_info": {
    "name": "小白",
    "breed": "金毛犬",
    "age": "3岁"
  },
  "examination_data": {
    "type": "血液检查",
    "results": "血红蛋白: 12.5 g/dL, 白细胞: 8000/μL"
  },
  "stream": false
}
```

#### 报告解读智能体

- **POST** `/api/v1/system-agents/report-interpretation/execute`
- **描述**: 执行报告解读智能体
- **请求体**:

```json
{
  "report_content": "血液检查报告内容...",
  "pet_info": {
    "breed": "金毛犬",
    "age": "3岁"
  },
  "stream": true
}
```

### 3.3 智能体权限管理接口 (`/api/v1/agent-permissions`)

#### 获取智能体权限

- **GET** `/api/v1/agent-permissions/{agent_id}`
- **描述**: 获取指定智能体的权限设置

#### 更新智能体权限

- **PUT** `/api/v1/agent-permissions/{agent_id}`
- **描述**: 更新智能体权限设置
- **请求体**:

```json
{
  "is_public": true,
  "allowed_users": [1, 2, 3],
  "allowed_roles": ["admin", "doctor"]
}
```

### 3.4 会话管理接口 (`/api/v1/conversations`)

#### 获取会话列表

- **GET** `/api/v1/conversations`
- **描述**: 获取用户的会话列表
- **查询参数**:
  - `agent_id`: 智能体 ID 过滤
  - `page`: 页码
  - `page_size`: 每页数量

#### 创建会话

- **POST** `/api/v1/conversations`
- **描述**: 创建新的会话
- **请求体**:

```json
{
  "agent_id": 1,
  "title": "关于小白的健康咨询",
  "context": {
    "pet_id": 123,
    "pet_name": "小白"
  }
}
```

#### 获取会话详情

- **GET** `/api/v1/conversations/{conversation_id}`
- **描述**: 获取指定会话的详细信息

#### 更新会话

- **PUT** `/api/v1/conversations/{conversation_id}`
- **描述**: 更新会话信息

#### 删除会话

- **DELETE** `/api/v1/conversations/{conversation_id}`
- **描述**: 删除会话

### 3.5 消息管理接口 (`/api/v1/messages`)

#### 获取消息列表

- **GET** `/api/v1/messages`
- **描述**: 获取会话中的消息列表
- **查询参数**:
  - `conversation_id`: 会话 ID (必需)
  - `page`: 页码
  - `page_size`: 每页数量

#### 发送消息

- **POST** `/api/v1/messages`
- **描述**: 在会话中发送新消息
- **请求体**:

```json
{
  "conversation_id": 1,
  "content": "我的宠物最近食欲不振，该怎么办？",
  "message_type": "user"
}
```

#### 获取消息详情

- **GET** `/api/v1/messages/{message_id}`
- **描述**: 获取指定消息的详细信息

## 4. OCR 服务 (OCR Service)

### 4.1 OCR 识别接口 (`/api/v1/ocr`)

#### 单张图片 OCR 识别

- **POST** `/api/v1/ocr/extract`
- **描述**: 对单张图片进行 OCR 文字识别
- **认证**: 可选（支持无认证访问）
- **请求体**:

```json
{
  "image_url": "https://example.com/image.jpg",
  "image_base64": "/9j/4AAQSkZJRgABAQAAAQ...",
  "model_type": "qwen_vl",
  "prompt": "请识别这张医疗报告中的所有文字",
  "language": "auto",
  "confidence_threshold": 0.8
}
```

- **字段说明**:

  - `image_url`: 图片 URL（可选，与 image_base64 二选一）
  - `image_base64`: base64 编码的图片数据，不包含 data:image 前缀（可选，与 image_url 二选一）
  - `model_type`: OCR 模型类型，默认"qwen_vl"（可选）
  - `prompt`: OCR 提示词（可选）
  - `language`: 识别语言，默认"auto"（可选）
  - `confidence_threshold`: 置信度阈值，0.0-1.0，默认 0.8（可选）

- **响应**:

```json
{
  "success": true,
  "result": {
    "text": "识别出的文字内容...",
    "confidence": 0.95,
    "language": "zh",
    "processing_time_ms": 2300,
    "bounding_boxes": [
      {
        "x": 10.5,
        "y": 20.3,
        "width": 100.2,
        "height": 15.8,
        "text": "文字内容",
        "confidence": 0.98
      }
    ],
    "metadata": {}
  },
  "processing_time_ms": 2300,
  "error": null
}
```

#### 多图片批量 OCR 识别

- **POST** `/api/v1/ocr/extract/batch`
- **描述**: 批量处理多张图片的 OCR 识别
- **认证**: 需要 JWT Token 或 API Key
- **请求**: 表单数据 (multipart/form-data)
  - `files`: 图片文件列表（必需）
  - `model_type`: OCR 模型类型，默认"qwen_vl"（可选）
  - `prompt`: OCR 提示词（可选）
  - `language`: 识别语言，默认"auto"（可选）
  - `confidence_threshold`: 置信度阈值，默认 0.8（可选）
- **响应**:

```json
{
  "success": true,
  "total_images": 3,
  "successful_count": 3,
  "failed_count": 0,
  "results": [
    {
      "index": 0,
      "filename": "report1.jpg",
      "success": true,
      "result": {
        "text": "识别结果1...",
        "confidence": 0.95,
        "language": "zh",
        "processing_time_ms": 1200,
        "bounding_boxes": [],
        "metadata": {}
      },
      "error": null
    },
    {
      "index": 1,
      "filename": "report2.jpg",
      "success": true,
      "result": {
        "text": "识别结果2...",
        "confidence": 0.88,
        "language": "zh",
        "processing_time_ms": 1500,
        "bounding_boxes": [],
        "metadata": {}
      },
      "error": null
    }
  ],
  "total_processing_time_ms": 3200,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

#### 获取可用模型列表

- **GET** `/api/v1/ocr/models`
- **描述**: 获取 OCR 服务支持的模型列表
- **响应**:

```json
{
  "models": [
    {
      "id": "qwen_vl",
      "name": "Qwen-VL",
      "description": "通义千问视觉语言模型",
      "available": true,
      "supported_languages": ["zh", "en", "auto"]
    }
  ]
}
```

#### OCR 服务健康检查

- **GET** `/api/v1/ocr/health`
- **描述**: 检查 OCR 服务及各模型的健康状态
- **响应**:

```json
{
  "status": "healthy",
  "service": "ocr_service",
  "version": "1.0.0",
  "models": {
    "qwen_vl": true
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 5. OpenAI 兼容代理服务

### 5.1 聊天完成接口 (`/v1/chat/completions`)

#### 聊天完成

- **POST** `/v1/chat/completions`
- **描述**: OpenAI 兼容的聊天完成 API，支持流式和非流式输出
- **认证**: 需要 API Key (sk-前缀)
- **请求体**:

```json
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {
      "role": "system",
      "content": "你是一个专业的宠物医疗助手"
    },
    {
      "role": "user",
      "content": "我的狗狗最近食欲不振，该怎么办？"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 1000,
  "stream": false
}
```

#### 流式聊天完成

- **POST** `/v1/chat/completions`
- **描述**: 流式输出的聊天完成
- **请求体**: 同上，但设置 `"stream": true`
- **响应**: Server-Sent Events (SSE) 流

### 5.2 模型列表接口 (`/v1/models`)

#### 获取模型列表

- **GET** `/v1/models`
- **描述**: 获取可用的 AI 模型列表
- **认证**: 需要 API Key
- **响应**:

```json
{
  "object": "list",
  "data": [
    {
      "id": "gpt-3.5-turbo",
      "object": "model",
      "created": 1677610602,
      "owned_by": "openai"
    },
    {
      "id": "gpt-4",
      "object": "model",
      "created": **********,
      "owned_by": "openai"
    }
  ]
}
```

### 5.3 文本嵌入接口 (`/v1/embeddings`)

#### 创建文本嵌入

- **POST** `/v1/embeddings`
- **描述**: 生成文本的向量嵌入
- **认证**: 需要 API Key
- **请求体**:

```json
{
  "model": "text-embedding-ada-002",
  "input": "宠物医疗相关的文本内容"
}
```

## 6. 健康检查接口

### 6.1 网关健康检查

- **GET** `/health`
- **描述**: API 网关健康检查，包含所有后端服务状态

### 6.2 各服务健康检查

- **GET** `/health` (各服务端口)
- **描述**: 各个微服务的健康检查端点

## 📝 响应格式

### 统一成功响应格式

```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "request_id": "req_123456789",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 分页响应格式

```json
{
  "success": true,
  "data": {
    "items": [],
    "total": 100,
    "page": 1,
    "size": 10,
    "pages": 10
  },
  "message": "获取成功",
  "request_id": "req_123456789"
}
```

### 错误响应格式

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": {
      "field": "email",
      "issue": "邮箱格式不正确"
    }
  },
  "request_id": "req_123456789",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 🔧 错误代码

| 错误代码              | HTTP 状态码 | 描述             |
| --------------------- | ----------- | ---------------- |
| `VALIDATION_ERROR`    | 400         | 请求参数验证失败 |
| `UNAUTHORIZED`        | 401         | 未授权访问       |
| `FORBIDDEN`           | 403         | 权限不足         |
| `NOT_FOUND`           | 404         | 资源不存在       |
| `OPERATION_FAILED`    | 500         | 操作失败         |
| `SERVICE_UNAVAILABLE` | 503         | 服务不可用       |

## 📚 使用示例

### 完整的 API 调用流程示例

#### 1. 用户注册和登录

```bash
# 注册用户
curl -X POST http://localhost:8000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "testuser",
    "password": "password123",
    "first_name": "张",
    "last_name": "三"
  }'

# 用户登录
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

#### 2. 创建宠物记录

```bash
curl -X POST http://localhost:8000/api/v1/pets \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "小白",
    "species": "犬",
    "breed": "金毛犬",
    "gender": "雄性",
    "birth_date": "2020-01-01",
    "weight": 25.5,
    "owner_id": 1
  }'
```

#### 3. 使用 AI 问诊智能体

```bash
curl -X POST http://localhost:8000/api/v1/system-agents/diagnosis/execute \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "pet_info": {
      "breed": "金毛犬",
      "age": "3岁",
      "gender": "雄性",
      "weight": "25kg"
    },
    "symptoms": ["食欲不振", "精神萎靡"],
    "additional_info": "症状持续2天",
    "stream": false
  }'
```

#### 4. OCR 识别医疗报告

```bash
curl -X POST http://localhost:8000/api/v1/ocr/extract \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "https://example.com/medical-report.jpg",
    "model_type": "qwen_vl",
    "prompt": "请识别这张宠物医疗报告中的所有文字信息"
  }'
```

#### 5. 使用 OpenAI 兼容 API

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Authorization: Bearer sk-YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {
        "role": "system",
        "content": "你是一个专业的宠物医疗助手"
      },
      {
        "role": "user",
        "content": "我的猫咪最近不爱吃东西，该怎么办？"
      }
    ],
    "temperature": 0.7,
    "max_tokens": 1000
  }'
```

## 📞 技术支持

如有 API 使用问题，请联系技术支持团队或查看各服务的详细 API 文档：

- 网关文档: `http://localhost:8000/api/v1/docs`
- 用户服务文档: `http://localhost:8001/api/v1/docs`
- 宠物服务文档: `http://localhost:8002/api/v1/docs`
- 应用服务文档: `http://localhost:8003/api/v1/docs`
- OCR 服务文档: `http://localhost:8004/docs`
