# 数据库迁移管理指南

## 📋 概述

本文档介绍宠物医疗平台微服务架构下的数据库迁移管理方案。我们使用 Alembic 作为数据库迁移工具，支持统一管理所有微服务的数据库表结构变更。

## 🏗️ 架构设计

### 统一迁移策略

- ✅ **统一迁移环境**: 所有微服务共享一个迁移环境
- ✅ **模型独立性**: 各微服务保持模型定义的独立性
- ✅ **自动发现**: 自动发现所有微服务的数据库模型
- ✅ **环境适配**: 支持开发、测试、生产环境

### 涵盖的微服务

| 微服务       | 数据库表                                                                                    | 模型文件位置                    |
| ------------ | ------------------------------------------------------------------------------------------- | ------------------------------- |
| User Service | users, tenants, permissions, api_keys, user_sessions, tenant_roles                          | `services/user_service/models/` |
| Pet Service  | pets, breeds, medical_records, vaccinations                                                 | `services/pet_service/models/`  |
| App Service  | agents, agent_permissions, agent_executions, conversations, messages, conversation_contexts | `services/app_service/models/`  |
| Gateway      | api_request_logs                                                                            | `gateway/models/`               |

## 🚀 快速开始

### 1. 环境准备

确保已激活 conda 环境并配置数据库连接：

```bash
# 激活conda环境
conda activate vet_open_platform

# 检查环境变量
echo $DATABASE_URL
```

### 2. 生成初始迁移

```bash
# 生成初始迁移脚本（首次使用）
python scripts/db_migrate.py init
```

### 3. 执行迁移

```bash
# 执行数据库迁移
python scripts/db_migrate.py upgrade
```

### 4. 检查迁移状态

```bash
# 查看迁移状态
python scripts/db_migrate.py status
```

## 📖 详细操作指南

### 模型修改后的迁移流程

当您修改了任何微服务的数据库模型后，按以下步骤操作：

#### 1. 生成迁移脚本

```bash
# 自动检测模型变更并生成迁移脚本
python scripts/db_migrate.py auto "添加用户头像字段"
```

#### 2. 检查迁移脚本

生成的迁移脚本位于 `migrations/versions/` 目录下，文件名格式为：

```
YYYYMMDD_HHMM_revision_description.py
```

**重要**: 请仔细检查生成的迁移脚本，确认：

- 表结构变更正确
- 数据迁移逻辑合理
- 没有意外的删除操作

#### 3. 执行迁移

```bash
# 执行迁移到最新版本
python scripts/db_migrate.py upgrade

# 或执行到指定版本
python scripts/db_migrate.py upgrade <revision_id>
```

#### 4. 验证迁移结果

```bash
# 检查迁移状态
python scripts/db_migrate.py status

# 查看迁移历史
python scripts/db_migrate.py history
```

### 回滚迁移

如果需要回滚迁移：

```bash
# 回滚到上一个版本
python scripts/db_migrate.py downgrade -1

# 回滚到指定版本
python scripts/db_migrate.py downgrade <revision_id>
```

**⚠️ 警告**: 回滚操作可能导致数据丢失，请谨慎操作！

## 🔧 高级操作

### 使用 manage.py 脚本

您也可以通过主管理脚本执行迁移操作：

```bash
# 生成迁移脚本
python scripts/manage.py db migrate revision --autogenerate -m "描述信息"

# 执行迁移
python scripts/manage.py db migrate upgrade

# 查看迁移状态
python scripts/manage.py db migrate check
```

### 直接使用 Alembic 命令

对于高级用户，可以直接使用 Alembic 命令：

```bash
# 查看当前版本
alembic current

# 查看迁移历史
alembic history

# 生成迁移脚本
alembic revision --autogenerate -m "描述信息"

# 执行迁移
alembic upgrade head
```

## 🌍 多环境配置

### 开发环境

使用本地 PostgreSQL 数据库：

```bash
# .env文件配置
DATABASE_URL=postgresql://vet_user:vet_password@localhost:5432/vet_platform
```

### 测试环境

使用测试数据库：

```bash
# .env.test文件配置
DATABASE_URL=***********************************************/vet_platform_test
```

### 生产环境

使用远程 PostgreSQL 数据库：

```bash
# .env.production文件配置
DATABASE_URL=***********************************************/vet_platform
```

## 🐳 Docker 环境中的迁移

### 在 Docker 容器中执行迁移

```bash
# 进入容器
docker exec -it vet_gateway bash

# 在容器内执行迁移
python scripts/db_migrate.py upgrade
```

### 自动化部署中的迁移

在 Docker 启动脚本中添加迁移检查：

```bash
# 检查并执行待执行的迁移
python scripts/db_migrate.py status
python scripts/db_migrate.py upgrade
```

## 📝 最佳实践

### 1. 迁移脚本命名

使用描述性的迁移消息：

```bash
# ✅ 好的命名
python scripts/db_migrate.py auto "添加用户头像字段"
python scripts/db_migrate.py auto "创建宠物疫苗接种表"
python scripts/db_migrate.py auto "修改API密钥表结构"

# ❌ 不好的命名
python scripts/db_migrate.py auto "update"
python scripts/db_migrate.py auto "fix"
```

### 2. 迁移前备份

在生产环境执行迁移前，务必备份数据库：

```bash
# PostgreSQL备份
pg_dump -h hostname -U username -d database_name > backup.sql
```

### 3. 分步迁移

对于大型结构变更，建议分步进行：

```bash
# 第一步：添加新字段（允许NULL）
python scripts/db_migrate.py auto "添加新字段（允许NULL）"

# 第二步：填充数据
python scripts/db_migrate.py auto "填充新字段数据"

# 第三步：设置字段为NOT NULL
python scripts/db_migrate.py auto "设置新字段为NOT NULL"
```

### 4. 测试迁移

在开发环境充分测试迁移脚本：

```bash
# 测试迁移
python scripts/db_migrate.py upgrade

# 测试回滚
python scripts/db_migrate.py downgrade -1

# 重新测试迁移
python scripts/db_migrate.py upgrade
```

## 🚨 故障排除

### 常见问题

#### 1. 模型导入错误

**错误**: `ImportError: No module named 'services.user_service.models'`

**解决**: 确保 PYTHONPATH 正确设置，项目根目录在 Python 路径中。

#### 2. 数据库连接失败

**错误**: `sqlalchemy.exc.OperationalError: could not connect to server`

**解决**: 检查 DATABASE_URL 配置和数据库服务状态。

#### 3. 迁移冲突

**错误**: `alembic.util.exc.CommandError: Multiple head revisions are present`

**解决**: 使用 `alembic merge` 命令合并分支。

### 调试技巧

1. **启用详细日志**:

   ```bash
   # 在alembic.ini中设置
   [logger_alembic]
   level = DEBUG
   ```

2. **检查生成的 SQL**:

   ```bash
   # 查看将要执行的SQL
   alembic upgrade head --sql
   ```

3. **离线模式生成 SQL**:
   ```bash
   # 生成SQL脚本而不执行
   alembic upgrade head --sql > migration.sql
   ```

## 📞 支持

如果遇到问题，请：

1. 查看日志文件：`logs/`目录下的相关日志
2. 检查数据库连接：`python scripts/manage.py db test`
3. 验证环境配置：`python scripts/manage.py db health`

## 🎉 实施完成状态

### ✅ 已完成的功能

1. **迁移环境配置**

   - ✅ 配置了 `alembic.ini` 文件，支持中文注释和时区设置
   - ✅ 创建了 `migrations/env.py` 环境配置文件，支持异步数据库连接
   - ✅ 配置了 `migrations/script.py.mako` 迁移脚本模板

2. **数据库模型集成**

   - ✅ 自动发现所有微服务的数据库模型
   - ✅ 支持 User Service、Pet Service、App Service、Gateway 的所有表
   - ✅ 正确处理枚举类型和 schema 搜索路径

3. **迁移管理工具**

   - ✅ 创建了 `scripts/db_migrate.py` 专用迁移管理脚本
   - ✅ 集成到 `scripts/manage.py` 主管理脚本中
   - ✅ 支持初始化、生成、执行、回滚、状态检查等完整功能

4. **初始迁移**
   - ✅ 生成了初始数据库结构迁移 (53b3f20a1cba)
   - ✅ 成功标记当前数据库状态为已迁移
   - ✅ 测试生成了新的迁移脚本 (70a47cb58bb4)

### 📊 当前迁移状态

```bash
# 当前版本: 53b3f20a1cba (初始数据库结构)
# 最新版本: 70a47cb58bb4 (测试迁移功能)
# 状态: 有待执行的迁移
```

### 🔧 可用命令

```bash
# 基本迁移操作
python scripts/db_migrate.py status          # 查看迁移状态
python scripts/db_migrate.py auto "描述"     # 自动生成迁移
python scripts/db_migrate.py upgrade         # 执行迁移
python scripts/db_migrate.py history         # 查看迁移历史

# 通过主管理脚本
python scripts/manage.py db migrate check    # 检查迁移状态
python scripts/manage.py db migrate upgrade  # 执行迁移
```

---

**注意**: 本指南基于 Alembic 1.13.1 和 SQLAlchemy 2.0.23，请确保版本兼容性。

## 📋 下一步建议

1. **执行待处理的迁移**: `python scripts/db_migrate.py upgrade`
2. **集成到 CI/CD 流程**: 在部署脚本中添加自动迁移检查
3. **团队培训**: 确保团队成员了解迁移操作流程
4. **备份策略**: 建立生产环境迁移前的自动备份机制
