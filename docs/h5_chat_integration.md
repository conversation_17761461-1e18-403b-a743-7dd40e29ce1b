# H5对话界面完整集成方案

## 1. API接口设计

### 1.1 会话管理API

```bash
# 创建新会话
POST /api/v1/conversations/
{
  "title": "AI问诊会话",
  "conversation_type": "diagnosis",
  "config": {"enable_streaming": true}
}

# 获取会话列表
GET /api/v1/conversations/?page=1&size=20

# 获取会话详情
GET /api/v1/conversations/{id}

# 更新会话
PUT /api/v1/conversations/{id}
{"title": "新标题"}

# 删除会话
DELETE /api/v1/conversations/{id}
```

### 1.2 消息管理API

```bash
# 获取消息历史
GET /api/v1/conversations/{id}/messages?page=1&size=20

# 发送普通消息
POST /api/v1/conversations/{id}/messages
{
  "content": "用户消息",
  "message_type": "text"
}

# 发送消息并获取流式回复 ⭐
POST /api/v1/conversations/{id}/messages/stream
{
  "content": "用户消息",
  "message_type": "text"
}
```

## 2. 流式响应事件类型

### 2.1 事件格式
```
data: {"type": "event_type", "data": {...}}
```

### 2.2 事件类型说明

| 事件类型 | 说明 | 数据内容 |
|---------|------|----------|
| `user_message` | 用户消息已保存 | 完整的消息对象 |
| `ai_start` | AI开始处理 | `{"message": "AI正在思考中..."}` |
| `ai_chunk` | AI回复片段 | `{"content": "片段", "full_content": "累积内容"}` |
| `ai_complete` | AI回复完成 | 完整的AI消息对象 |
| `done` | 流式响应结束 | 无 |
| `error` | 发生错误 | `{"message": "错误信息"}` |

## 3. 前后端交互流程

### 3.1 完整对话流程

```
用户发送消息
    ↓
前端调用流式API
    ↓
后端保存用户消息 → 发送user_message事件
    ↓
后端开始AI处理 → 发送ai_start事件
    ↓
AI生成回复片段 → 发送ai_chunk事件 (多次)
    ↓
AI回复完成 → 保存AI消息 → 发送ai_complete事件
    ↓
流式响应结束 → 发送done事件
```

### 3.2 错误处理流程

```
发生错误
    ↓
发送error事件
    ↓
关闭流式连接
    ↓
前端显示错误信息
```

## 4. 前端JavaScript集成示例

### 4.1 基础配置

```javascript
class ChatAPI {
  constructor(baseUrl, token) {
    this.baseUrl = baseUrl;
    this.token = token;
  }

  // 获取认证头
  getHeaders() {
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.token}`
    };
  }
}
```

### 4.2 会话管理

```javascript
// 创建新会话
async createConversation(title, type = 'diagnosis') {
  const response = await fetch(`${this.baseUrl}/conversations/`, {
    method: 'POST',
    headers: this.getHeaders(),
    body: JSON.stringify({
      title: title,
      conversation_type: type,
      config: { enable_streaming: true }
    })
  });
  return await response.json();
}

// 获取会话列表
async getConversations(page = 1, size = 20) {
  const response = await fetch(
    `${this.baseUrl}/conversations/?page=${page}&size=${size}`,
    { headers: this.getHeaders() }
  );
  return await response.json();
}

// 获取消息历史
async getMessages(conversationId, page = 1, size = 20) {
  const response = await fetch(
    `${this.baseUrl}/conversations/${conversationId}/messages?page=${page}&size=${size}`,
    { headers: this.getHeaders() }
  );
  return await response.json();
}
```

### 4.3 流式消息发送

```javascript
// 发送流式消息
sendStreamMessage(conversationId, content, onMessage, onError, onComplete) {
  const url = `${this.baseUrl}/conversations/${conversationId}/messages/stream`;
  
  // 使用fetch + ReadableStream
  fetch(url, {
    method: 'POST',
    headers: this.getHeaders(),
    body: JSON.stringify({
      content: content,
      message_type: 'text'
    })
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    function readStream() {
      return reader.read().then(({ done, value }) => {
        if (done) {
          onComplete && onComplete();
          return;
        }
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              onMessage && onMessage(data);
            } catch (e) {
              console.warn('解析SSE数据失败:', line);
            }
          }
        }
        
        return readStream();
      });
    }
    
    return readStream();
  })
  .catch(error => {
    onError && onError(error);
  });
}
```

### 4.4 UI更新处理

```javascript
class ChatUI {
  constructor(chatAPI) {
    this.api = chatAPI;
    this.currentConversationId = null;
    this.currentAIMessageElement = null;
  }

  // 处理流式消息事件
  handleStreamEvent(event) {
    switch (event.type) {
      case 'user_message':
        this.displayUserMessage(event.data);
        break;
        
      case 'ai_start':
        this.showAIThinking();
        break;
        
      case 'ai_chunk':
        this.updateAIMessage(event.data.full_content);
        break;
        
      case 'ai_complete':
        this.finalizeAIMessage(event.data);
        break;
        
      case 'error':
        this.showError(event.data.message);
        break;
        
      case 'done':
        this.hideTypingIndicator();
        break;
    }
  }

  // 显示用户消息
  displayUserMessage(message) {
    const messageElement = this.createMessageElement('user', message.content);
    this.appendMessage(messageElement);
  }

  // 显示AI思考状态
  showAIThinking() {
    this.currentAIMessageElement = this.createMessageElement('ai', '');
    this.appendMessage(this.currentAIMessageElement);
    this.showTypingIndicator(this.currentAIMessageElement);
  }

  // 更新AI消息内容
  updateAIMessage(content) {
    if (this.currentAIMessageElement) {
      this.hideTypingIndicator();
      this.updateMessageContent(this.currentAIMessageElement, content);
    }
  }

  // 完成AI消息
  finalizeAIMessage(message) {
    if (this.currentAIMessageElement) {
      this.updateMessageContent(this.currentAIMessageElement, message.content);
      this.currentAIMessageElement.dataset.messageId = message.id;
      this.currentAIMessageElement = null;
    }
  }

  // 发送消息
  sendMessage(content) {
    if (!this.currentConversationId) {
      this.showError('请先选择或创建会话');
      return;
    }

    this.api.sendStreamMessage(
      this.currentConversationId,
      content,
      (event) => this.handleStreamEvent(event),
      (error) => this.showError(`发送失败: ${error.message}`),
      () => console.log('消息发送完成')
    );
  }
}
```

## 5. 完整的HTML示例

```html
<!DOCTYPE html>
<html>
<head>
    <title>AI宠物医生对话</title>
    <style>
        .chat-container { max-width: 800px; margin: 0 auto; }
        .message { margin: 10px 0; padding: 10px; border-radius: 8px; }
        .user-message { background: #007bff; color: white; margin-left: 20%; }
        .ai-message { background: #f1f1f1; margin-right: 20%; }
        .typing { opacity: 0.7; }
        .typing::after { content: '...'; animation: dots 1s infinite; }
        @keyframes dots { 0%, 20% { opacity: 0; } 50% { opacity: 1; } }
    </style>
</head>
<body>
    <div class="chat-container">
        <div id="messages"></div>
        <input type="text" id="messageInput" placeholder="输入消息...">
        <button onclick="sendMessage()">发送</button>
    </div>

    <script>
        const chatAPI = new ChatAPI('http://localhost:8000/api/v1', 'your_jwt_token');
        const chatUI = new ChatUI(chatAPI);
        
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const content = input.value.trim();
            if (content) {
                chatUI.sendMessage(content);
                input.value = '';
            }
        }
    </script>
</body>
</html>
```
