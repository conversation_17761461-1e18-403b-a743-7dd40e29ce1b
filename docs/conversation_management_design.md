# 🗣️ 会话管理系统技术设计文档

## 📋 概述

本文档详细描述了宠物医疗AI开放平台会话管理系统的技术设计方案，包括架构分析、功能定位、数据模型设计、API接口设计、缓存策略和集成方案。

## 🏗️ 当前架构分析

### 现有微服务架构

```
┌─────────────────────────────────────────────────────────────┐
│                    API网关层 (8000)                          │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   路由分发       │ │   认证授权       │ │  OpenAI代理     │ │
│  │   限流控制       │ │   API Key验证    │ │  流式响应       │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
┌───────▼──────┐    ┌────────▼──────┐    ┌────────▼──────┐
│ User Service │    │ Pet Service   │    │ App Service   │
│   (8001)     │    │   (8002)      │    │   (8003)      │
│              │    │               │    │               │
│ • 用户管理    │    │ • 宠物档案     │    │ • 智能体管理   │
│ • 认证授权    │    │ • 医疗记录     │    │ • 执行记录     │
│ • 权限控制    │    │ • 疫苗管理     │    │ • 系统智能体   │
│ • API Key    │    │ • 品种信息     │    │ • 自定义智能体 │
│ • 租户管理    │    │               │    │               │
└──────────────┘    └───────────────┘    └───────────────┘
        │                     │                     │
        └─────────────────────┼─────────────────────┘
                              │
        ┌─────────────────────▼─────────────────────┐
        │              数据存储层                    │
        │  ┌─────────────┐    ┌─────────────┐      │
        │  │ PostgreSQL  │    │    Redis    │      │
        │  │  (主数据)    │    │ (缓存&会话) │      │
        │  └─────────────┘    └─────────────┘      │
        └───────────────────────────────────────────┘
```

### 现有功能分析

#### 1. **Gateway服务 (端口8000)**
- **OpenAI兼容API代理**: 完整的OpenAI API规范实现
- **认证中间件**: JWT和API Key双重认证
- **限流中间件**: Redis基础的滑动窗口限流
- **路由分发**: 微服务请求路由和负载均衡
- **流式响应**: SSE流式输出支持

#### 2. **User Service (端口8001)**
- **用户管理**: 用户CRUD、认证、权限控制
- **多租户架构**: 租户级数据隔离和权限管理
- **API Key管理**: 动态密钥验证和缓存
- **会话管理**: UserSession模型（仅用于登录会话）

#### 3. **App Service (端口8003)**
- **智能体管理**: 系统级和自定义智能体
- **执行记录**: AgentExecution模型记录执行历史
- **系统智能体**: AI问诊、视觉识别、报告生成、报告解读

#### 4. **数据存储层**
- **PostgreSQL**: 主要业务数据存储
- **Redis**: 缓存、API Key验证、限流计数器

### 架构缺陷分析

#### 🚨 **缺失的会话管理功能**

1. **对话会话管理**
   - ❌ 缺少对话会话的创建和维护
   - ❌ 无法跟踪多轮对话的上下文
   - ❌ 没有会话状态管理

2. **消息历史记录**
   - ❌ 缺少消息的持久化存储
   - ❌ 无法检索历史对话记录
   - ❌ 没有消息的结构化存储

3. **上下文管理**
   - ❌ 无法保持对话上下文
   - ❌ 缺少智能体间的上下文传递
   - ❌ 没有上下文的生命周期管理

4. **会话与智能体集成**
   - ❌ 智能体执行缺少会话关联
   - ❌ 无法支持连续的多轮对话
   - ❌ 缺少会话级的智能体状态

## 🎯 功能定位建议

### 推荐方案：扩展App Service

基于微服务设计原则和现有架构分析，**建议将会话管理功能集成到App Service中**：

#### ✅ **选择理由**

1. **职责一致性**
   - App Service已负责智能体管理和执行
   - 会话管理与智能体执行密切相关
   - 符合单一职责原则

2. **数据关联性**
   - 会话与智能体执行记录天然关联
   - 可复用现有的AgentExecution模型
   - 减少跨服务数据查询

3. **技术一致性**
   - 已有完整的FastAPI + SQLAlchemy架构
   - 现有的异步处理和流式响应能力
   - 统一的错误处理和日志记录

4. **扩展性考虑**
   - 支持未来新增智能体类型
   - 便于实现智能体间的会话传递
   - 易于添加会话级的业务逻辑

#### 🏗️ **架构调整方案**

```
App Service (8003) - 扩展后
├── api/
│   ├── agents.py              # 现有：智能体管理
│   ├── system_agents.py       # 现有：系统智能体
│   ├── agent_permissions.py   # 现有：权限管理
│   ├── conversations.py       # 新增：会话管理API
│   └── messages.py           # 新增：消息管理API
├── models/
│   ├── agent.py              # 现有：智能体模型
│   ├── conversation.py       # 新增：会话模型
│   └── message.py           # 新增：消息模型
├── services/
│   ├── agent_service.py      # 现有：智能体服务
│   ├── conversation_service.py # 新增：会话服务
│   └── message_service.py    # 新增：消息服务
└── schemas/
    ├── agent.py              # 现有：智能体模式
    ├── conversation.py       # 新增：会话模式
    └── message.py           # 新增：消息模式
```

## 📊 数据模型设计

### 核心数据模型

#### 1. **Conversation (对话会话)**

```python
class ConversationType(str, Enum):
    """会话类型枚举"""
    DIAGNOSIS = "diagnosis"           # AI问诊会话（主要场景）
    GENERAL = "general"              # 通用对话会话

class ConversationStatus(str, Enum):
    """会话状态枚举"""
    ACTIVE = "active"        # 活跃状态
    PAUSED = "paused"        # 暂停状态
    COMPLETED = "completed"  # 已完成
    ARCHIVED = "archived"    # 已归档
    EXPIRED = "expired"      # 已过期

class Conversation(BaseAuditModel):
    """对话会话模型"""
    
    __tablename__ = "conversations"
    
    # 基础信息
    title = Column(String(200), nullable=False, comment="会话标题")
    conversation_type = Column(SQLEnum(ConversationType), 
                             nullable=False, comment="会话类型")
    status = Column(SQLEnum(ConversationStatus), 
                   default=ConversationStatus.ACTIVE, comment="会话状态")
    
    # 关联信息
    user_id = Column(Integer, nullable=False, comment="用户ID")
    tenant_id = Column(Integer, nullable=True, comment="租户ID")
    primary_agent_id = Column(Integer, ForeignKey("agents.id"), 
                            nullable=True, comment="主要智能体ID")
    
    # 会话配置
    config = Column(JSON, nullable=True, comment="会话配置参数")
    context_window_size = Column(Integer, default=10, comment="上下文窗口大小")
    max_messages = Column(Integer, default=100, comment="最大消息数量")
    
    # 状态管理
    last_activity_at = Column(DateTime(timezone=True), 
                            server_default=func.now(), comment="最后活动时间")
    expires_at = Column(DateTime(timezone=True), nullable=True, comment="过期时间")
    
    # 统计信息
    message_count = Column(Integer, default=0, comment="消息数量")
    total_tokens = Column(Integer, default=0, comment="总Token使用量")
    
    # 关系映射
    primary_agent = relationship("Agent")
    messages = relationship("Message", back_populates="conversation",
                          cascade="all, delete-orphan", lazy="dynamic")

#### 2. **Message (消息记录)**

```python
class MessageRole(str, Enum):
    """消息角色枚举"""
    USER = "user"           # 用户消息
    ASSISTANT = "assistant" # 智能体回复
    SYSTEM = "system"       # 系统消息
    FUNCTION = "function"   # 函数调用结果

class MessageType(str, Enum):
    """消息类型枚举"""
    TEXT = "text"           # 文本消息
    IMAGE = "image"         # 图片消息
    FILE = "file"           # 文件消息
    FUNCTION_CALL = "function_call"  # 函数调用
    STRUCTURED = "structured"        # 结构化数据

class MessageStatus(str, Enum):
    """消息状态枚举"""
    PENDING = "pending"     # 等待处理
    PROCESSING = "processing" # 处理中
    COMPLETED = "completed" # 已完成
    FAILED = "failed"       # 处理失败
    CANCELLED = "cancelled" # 已取消

class Message(BaseAuditModel):
    """消息记录模型"""

    __tablename__ = "messages"

    # 关联信息
    conversation_id = Column(Integer, ForeignKey("conversations.id"),
                           nullable=False, comment="会话ID")
    agent_id = Column(Integer, ForeignKey("agents.id"),
                     nullable=True, comment="处理智能体ID")

    # 消息基础信息
    role = Column(SQLEnum(MessageRole), nullable=False, comment="消息角色")
    message_type = Column(SQLEnum(MessageType), default=MessageType.TEXT, comment="消息类型")
    content = Column(Text, nullable=False, comment="消息内容")

    # 消息元数据
    metadata = Column(JSON, nullable=True, comment="消息元数据")
    attachments = Column(JSON, nullable=True, comment="附件信息（图片、文件等）")

    # OCR相关字段
    ocr_result = Column(JSON, nullable=True, comment="OCR识别结果")
    has_ocr_content = Column(Boolean, default=False, comment="是否包含OCR内容")

    # 处理信息
    status = Column(SQLEnum(MessageStatus), default=MessageStatus.COMPLETED, comment="消息状态")
    processing_time_ms = Column(Integer, nullable=True, comment="处理时间(毫秒)")
    token_usage = Column(JSON, nullable=True, comment="Token使用统计")

    # 上下文信息
    sequence_number = Column(Integer, nullable=False, comment="消息序号")
    parent_message_id = Column(Integer, ForeignKey("messages.id"),
                             nullable=True, comment="父消息ID")

    # 错误信息
    error_message = Column(Text, nullable=True, comment="错误信息")
    retry_count = Column(Integer, default=0, comment="重试次数")

    # 关系映射
    conversation = relationship("Conversation", back_populates="messages")
    agent = relationship("Agent")
    parent_message = relationship("Message", remote_side="Message.id")
    child_messages = relationship("Message", remote_side="Message.parent_message_id")

#### 3. **ConversationContext (会话上下文)**

```python
class ContextType(str, Enum):
    """上下文类型枚举"""
    SYSTEM_PROMPT = "system_prompt"     # 系统提示词
    USER_PROFILE = "user_profile"       # 用户画像
    PET_INFO = "pet_info"              # 宠物信息
    MEDICAL_HISTORY = "medical_history" # 医疗历史
    CONVERSATION_SUMMARY = "summary"    # 会话摘要
    AGENT_STATE = "agent_state"        # 智能体状态

class ConversationContext(BaseAuditModel):
    """会话上下文模型"""

    __tablename__ = "conversation_contexts"

    # 关联信息
    conversation_id = Column(Integer, ForeignKey("conversations.id"),
                           nullable=False, comment="会话ID")

    # 上下文信息
    context_type = Column(SQLEnum(ContextType), nullable=False, comment="上下文类型")
    context_key = Column(String(100), nullable=False, comment="上下文键")
    context_value = Column(JSON, nullable=False, comment="上下文值")

    # 生命周期管理
    priority = Column(Integer, default=0, comment="优先级")
    expires_at = Column(DateTime(timezone=True), nullable=True, comment="过期时间")
    is_persistent = Column(Boolean, default=False, comment="是否持久化")

    # 版本控制
    version = Column(Integer, default=1, comment="版本号")

    # 关系映射
    conversation = relationship("Conversation")

    # 数据库约束
    __table_args__ = (
        UniqueConstraint('conversation_id', 'context_type', 'context_key',
                        name='uq_conversation_context'),
        Index('ix_conversation_context_type', 'conversation_id', 'context_type'),
        Index('ix_context_expires', 'expires_at'),
    )



## 🔌 API接口设计

### RESTful API端点

#### 1. **会话管理API**

```python
# 会话CRUD操作
POST   /api/v1/conversations                    # 创建会话
GET    /api/v1/conversations                    # 获取会话列表
GET    /api/v1/conversations/{conversation_id}  # 获取会话详情
PUT    /api/v1/conversations/{conversation_id}  # 更新会话
DELETE /api/v1/conversations/{conversation_id}  # 删除会话

# 会话状态管理
POST   /api/v1/conversations/{conversation_id}/pause    # 暂停会话
POST   /api/v1/conversations/{conversation_id}/resume   # 恢复会话
POST   /api/v1/conversations/{conversation_id}/archive  # 归档会话


```

#### 2. **消息管理API**

```python
# 消息CRUD操作
POST   /api/v1/conversations/{conversation_id}/messages     # 发送消息
GET    /api/v1/conversations/{conversation_id}/messages     # 获取消息列表
GET    /api/v1/conversations/{conversation_id}/messages/{message_id} # 获取消息详情
PUT    /api/v1/conversations/{conversation_id}/messages/{message_id} # 更新消息
DELETE /api/v1/conversations/{conversation_id}/messages/{message_id} # 删除消息

# 流式消息处理
POST   /api/v1/conversations/{conversation_id}/messages/stream # 流式发送消息

# 消息操作
POST   /api/v1/conversations/{conversation_id}/messages/{message_id}/retry   # 重试消息
POST   /api/v1/conversations/{conversation_id}/messages/{message_id}/cancel  # 取消消息
```

#### 3. **上下文管理API**

```python
# 上下文管理
GET    /api/v1/conversations/{conversation_id}/context           # 获取上下文
POST   /api/v1/conversations/{conversation_id}/context           # 设置上下文
PUT    /api/v1/conversations/{conversation_id}/context/{key}     # 更新上下文
DELETE /api/v1/conversations/{conversation_id}/context/{key}     # 删除上下文

# 上下文操作
POST   /api/v1/conversations/{conversation_id}/context/clear     # 清空上下文
POST   /api/v1/conversations/{conversation_id}/context/summary   # 生成摘要
```

### 请求/响应模式

#### 1. **创建会话请求**

```python
class ConversationCreateRequest(BaseModel):
    """创建会话请求"""
    title: str = Field(..., min_length=1, max_length=200, description="会话标题")
    conversation_type: ConversationType = Field(..., description="会话类型")
    primary_agent_id: Optional[int] = Field(None, description="主要智能体ID")
    config: Optional[Dict[str, Any]] = Field(None, description="会话配置")
    context_window_size: int = Field(10, ge=1, le=50, description="上下文窗口大小")
    max_messages: int = Field(100, ge=10, le=1000, description="最大消息数量")
    expires_in_hours: Optional[int] = Field(None, ge=1, le=8760, description="过期时间(小时)")

    # 初始上下文
    initial_context: Optional[Dict[str, Any]] = Field(None, description="初始上下文")

    # 前端配置
    enable_ocr: bool = Field(True, description="启用OCR功能")
    auto_context: bool = Field(True, description="自动管理上下文")

class ConversationResponse(BaseModel):
    """会话响应"""
    id: int
    title: str
    conversation_type: ConversationType
    status: ConversationStatus
    user_id: int
    tenant_id: Optional[int]
    primary_agent_id: Optional[int]
    config: Optional[Dict[str, Any]]
    context_window_size: int
    max_messages: int
    last_activity_at: datetime
    expires_at: Optional[datetime]
    message_count: int
    total_tokens: int
    created_at: datetime
    updated_at: datetime

    # 关联数据
    primary_agent: Optional[Dict[str, Any]] = None
    recent_messages: List[Dict[str, Any]] = []
    context_summary: Optional[str] = None

    class Config:
        from_attributes = True
```

#### 2. **发送消息请求**

```python
class MessageCreateRequest(BaseModel):
    """发送消息请求"""
    content: str = Field(..., min_length=1, description="消息内容")
    message_type: MessageType = Field(MessageType.TEXT, description="消息类型")

    # 附件支持（主要是图片）
    attachments: Optional[List[Dict[str, Any]]] = Field(None, description="附件信息")

    # OCR相关
    ocr_results: Optional[List[Dict[str, Any]]] = Field(None, description="前端传入的OCR识别结果")

    # 处理配置
    stream: bool = Field(True, description="是否流式响应（默认开启）")

    # 上下文控制
    include_context: bool = Field(True, description="是否包含上下文")
    context_window: Optional[int] = Field(None, description="上下文窗口大小")

class MessageResponse(BaseModel):
    """消息响应"""
    id: int
    conversation_id: int
    agent_id: Optional[int]
    role: MessageRole
    message_type: MessageType
    content: str
    metadata: Optional[Dict[str, Any]]
    attachments: Optional[List[Dict[str, Any]]]
    ocr_result: Optional[Dict[str, Any]]
    has_ocr_content: bool
    status: MessageStatus
    processing_time_ms: Optional[int]
    token_usage: Optional[Dict[str, Any]]
    sequence_number: int
    parent_message_id: Optional[int]
    error_message: Optional[str]
    retry_count: int
    created_at: datetime
    updated_at: datetime

    # 关联数据
    agent: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True

## 🚀 Redis缓存策略设计

### 缓存架构

```
Redis缓存层
├── 会话缓存 (conversation:*)
│   ├── 会话基础信息缓存
│   ├── 会话状态缓存
│   └── 活跃会话列表
├── 消息缓存 (message:*)
│   ├── 最近消息缓存
│   ├── 消息内容缓存
│   └── 流式消息缓存
├── 上下文缓存 (context:*)
│   ├── 会话上下文缓存
│   ├── 智能体状态缓存
│   └── 用户画像缓存
└── 性能缓存 (perf:*)
    ├── 热点会话缓存
    ├── 统计数据缓存
    └── 查询结果缓存
```

### 缓存键命名规范

```python
# 会话相关缓存键
CONVERSATION_KEY = "conversation:{conversation_id}"
CONVERSATION_LIST_KEY = "conversation:list:user:{user_id}"
CONVERSATION_ACTIVE_KEY = "conversation:active:user:{user_id}"
CONVERSATION_STATUS_KEY = "conversation:status:{conversation_id}"

# 消息相关缓存键
MESSAGE_KEY = "message:{message_id}"
MESSAGE_LIST_KEY = "message:list:conversation:{conversation_id}"
MESSAGE_RECENT_KEY = "message:recent:conversation:{conversation_id}"
MESSAGE_STREAM_KEY = "message:stream:{conversation_id}:{message_id}"

# 上下文相关缓存键
CONTEXT_KEY = "context:{conversation_id}:{context_type}"
CONTEXT_WINDOW_KEY = "context:window:{conversation_id}"
CONTEXT_SUMMARY_KEY = "context:summary:{conversation_id}"

# 性能相关缓存键
HOT_CONVERSATION_KEY = "hot:conversation:{conversation_id}"
USER_STATS_KEY = "stats:user:{user_id}"
AGENT_STATS_KEY = "stats:agent:{agent_id}"
```

### 缓存策略配置

```python
class CacheConfig:
    """缓存配置"""

    # 会话缓存配置
    CONVERSATION_TTL = 3600 * 24  # 24小时
    CONVERSATION_LIST_TTL = 300   # 5分钟
    CONVERSATION_STATUS_TTL = 60  # 1分钟

    # 消息缓存配置
    MESSAGE_TTL = 3600 * 6        # 6小时
    MESSAGE_LIST_TTL = 300        # 5分钟
    MESSAGE_RECENT_TTL = 60       # 1分钟
    MESSAGE_STREAM_TTL = 30       # 30秒

    # 上下文缓存配置
    CONTEXT_TTL = 3600 * 2        # 2小时
    CONTEXT_WINDOW_TTL = 300      # 5分钟
    CONTEXT_SUMMARY_TTL = 3600    # 1小时

    # 性能缓存配置
    HOT_CONVERSATION_TTL = 3600   # 1小时
    STATS_TTL = 300               # 5分钟

    # 缓存大小限制
    MAX_MESSAGE_CACHE_SIZE = 100  # 每个会话最多缓存100条消息
    MAX_CONTEXT_CACHE_SIZE = 50   # 每个会话最多缓存50个上下文项
```

### 缓存操作实现

```python
class ConversationCacheService:
    """会话缓存服务"""

    def __init__(self):
        self.redis = redis_client
        self.config = CacheConfig()

    async def cache_conversation(self, conversation: Conversation):
        """缓存会话信息"""
        key = f"conversation:{conversation.id}"
        data = {
            "id": conversation.id,
            "title": conversation.title,
            "type": conversation.conversation_type,
            "status": conversation.status,
            "user_id": conversation.user_id,
            "last_activity_at": conversation.last_activity_at.isoformat(),
            "message_count": conversation.message_count
        }
        await self.redis.set(key, data, expire=self.config.CONVERSATION_TTL)

    async def get_conversation_from_cache(self, conversation_id: int) -> Optional[Dict]:
        """从缓存获取会话信息"""
        key = f"conversation:{conversation_id}"
        return await self.redis.get(key)

    async def cache_recent_messages(self, conversation_id: int, messages: List[Message]):
        """缓存最近消息"""
        key = f"message:recent:conversation:{conversation_id}"
        data = [
            {
                "id": msg.id,
                "role": msg.role,
                "content": msg.content,
                "created_at": msg.created_at.isoformat()
            }
            for msg in messages[-self.config.MAX_MESSAGE_CACHE_SIZE:]
        ]
        await self.redis.set(key, data, expire=self.config.MESSAGE_RECENT_TTL)

    async def cache_conversation_context(self, conversation_id: int, context: Dict[str, Any]):
        """缓存会话上下文"""
        key = f"context:window:{conversation_id}"
        await self.redis.set(key, context, expire=self.config.CONTEXT_WINDOW_TTL)

    async def invalidate_conversation_cache(self, conversation_id: int):
        """清除会话相关缓存"""
        patterns = [
            f"conversation:{conversation_id}",
            f"message:*:conversation:{conversation_id}",
            f"context:*:{conversation_id}",
            f"hot:conversation:{conversation_id}"
        ]

        for pattern in patterns:
            keys = await self.redis.keys(pattern)
            if keys:
                await self.redis.delete(*keys)
```

## 🔗 OpenAI兼容接口集成方案

### 集成架构

```
OpenAI兼容API集成架构
┌─────────────────────────────────────────────────────────────┐
│                    客户端请求                                │
│  POST /v1/chat/completions                                  │
│  {                                                          │
│    "model": "ds-vet-answer-32B",                           │
│    "messages": [...],                                       │
│    "conversation_id": "optional",                          │
│    "stream": true                                           │
│  }                                                          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                  Gateway OpenAI Proxy                      │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   请求解析       │ │   会话检测       │ │   上下文注入     │ │
│  │   参数验证       │ │   自动创建       │ │   历史消息       │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   App Service                               │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   会话管理       │ │   消息处理       │ │   智能体执行     │ │
│  │   上下文维护     │ │   流式响应       │ │   结果存储       │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘

### OpenAI API扩展参数

为保持完全兼容性，在现有OpenAI API基础上添加可选的会话管理参数：

```python
class ChatCompletionRequest(BaseModel):
    """扩展的聊天完成请求"""
    # 标准OpenAI参数
    model: str
    messages: List[Dict[str, str]]
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = None
    stream: Optional[bool] = False

    # 会话管理扩展参数（可选）
    conversation_id: Optional[str] = Field(None, description="会话ID")
    conversation_title: Optional[str] = Field(None, description="会话标题")
    auto_create_conversation: bool = Field(True, description="自动创建会话")
    include_conversation_history: bool = Field(True, description="包含会话历史")
    context_window_size: Optional[int] = Field(None, description="上下文窗口大小")

    # 智能体配置扩展
    agent_id: Optional[int] = Field(None, description="指定智能体ID")
    agent_config_override: Optional[Dict[str, Any]] = Field(None, description="智能体配置覆盖")
```

### 向后兼容性保证

1. **完全兼容现有OpenAI API调用**
2. **新增参数均为可选参数**
3. **默认行为保持不变**
4. **渐进式功能启用**

## 🏢 多租户数据隔离设计

### 数据隔离策略

基于现有架构已在API层面实现完善的租户隔离控制，会话管理采用应用层隔离策略：

#### 1. **应用层隔离**

```python
class TenantAwareConversationService(ConversationService):
    """租户感知的会话服务"""

    def __init__(self, db: AsyncSession, tenant_id: Optional[int] = None):
        super().__init__(db)
        self.tenant_id = tenant_id

    def _apply_tenant_filter(self, query):
        """应用租户过滤条件"""
        if self.tenant_id:
            return query.where(Conversation.tenant_id == self.tenant_id)
        else:
            return query.where(Conversation.tenant_id.is_(None))

    async def get_conversations(
        self,
        user_id: int,
        filters: Optional[Dict] = None
    ) -> List[Conversation]:
        """获取用户会话列表（租户隔离）"""
        query = select(Conversation).where(
            Conversation.user_id == user_id,
            Conversation.is_deleted == False
        )

        # 应用租户隔离
        query = self._apply_tenant_filter(query)

        # 应用其他过滤条件
        if filters:
            if filters.get("status"):
                query = query.where(Conversation.status == filters["status"])
            if filters.get("conversation_type"):
                query = query.where(Conversation.conversation_type == filters["conversation_type"])

        result = await self.db.execute(query)
        return result.scalars().all()
```

## 🤖 AI问诊会话管理方案

### 核心业务场景

基于H5前端对话页面的实际需求，会话管理专注于以下核心场景：

1. **用户发起问诊对话**：创建新的问诊会话
2. **图片上传与OCR识别**：前端处理图片上传和OCR调用
3. **问题与OCR结果组合**：前端将用户问题和OCR结果拼接
4. **AI问诊流式回答**：问诊智能体提供流式响应
5. **上下文自动管理**：系统自动维护对话上下文

### 简化的会话服务

```python
class DiagnosisConversationService:
    """AI问诊会话服务"""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.agent_service = AgentService(db)
        self.cache_service = ConversationCacheService()

    async def create_diagnosis_conversation(
        self,
        user_id: int,
        title: Optional[str] = None,
        tenant_id: Optional[int] = None
    ) -> Conversation:
        """创建AI问诊会话"""

        # 获取AI问诊智能体
        diagnosis_agent = await self.agent_service._get_system_agent(
            SystemAgentCategory.DIAGNOSIS
        )

        if not diagnosis_agent:
            raise ValueError("AI问诊智能体未找到")

        # 创建会话
        conversation = Conversation(
            title=title or "AI问诊咨询",
            conversation_type=ConversationType.DIAGNOSIS,
            user_id=user_id,
            tenant_id=tenant_id,
            primary_agent_id=diagnosis_agent.id,
            config={
                "enable_ocr": True,
                "auto_context": True,
                "stream_response": True
            }
        )

        self.db.add(conversation)
        await self.db.commit()
        await self.db.refresh(conversation)

        # 缓存会话信息
        await self.cache_service.cache_conversation(conversation)

        return conversation

    async def send_message_with_ocr(
        self,
        conversation_id: int,
        user_content: str,
        ocr_results: Optional[List[Dict[str, Any]]],
        user_id: int,
        attachments: Optional[List[Dict[str, Any]]] = None
    ) -> AsyncGenerator[str, None]:
        """发送包含OCR结果的消息并获取流式响应"""

        # 1. 获取会话
        conversation = await self.get_conversation(conversation_id, user_id)
        if not conversation:
            raise ValueError("会话不存在或无权限访问")

        # 2. 构建完整的用户消息内容
        full_content = self._build_message_content(user_content, ocr_results)

        # 3. 创建用户消息
        user_message = await self._create_user_message(
            conversation_id, full_content, user_id, attachments, ocr_results
        )

        # 4. 获取会话上下文
        context_messages = await self._get_conversation_context(conversation_id)

        # 5. 构建问诊请求
        diagnosis_input = self._build_diagnosis_input(
            full_content, context_messages, ocr_results
        )

        # 6. 流式执行AI问诊
        assistant_message = await self._create_assistant_message_placeholder(
            conversation_id, conversation.primary_agent_id, user_id
        )

        full_response = ""
        async for chunk in self._stream_diagnosis_response(
            conversation.primary_agent_id, diagnosis_input, user_id
        ):
            full_response += chunk
            yield chunk

        # 7. 更新助手消息
        await self._update_assistant_message(assistant_message.id, full_response)

        # 8. 更新会话统计和缓存
        await self._update_conversation_stats(conversation_id)
        await self._update_conversation_cache(conversation_id)

    def _build_message_content(
        self,
        user_content: str,
        ocr_results: Optional[List[Dict[str, Any]]]
    ) -> str:
        """构建包含OCR结果的完整消息内容"""
        if not ocr_results:
            return user_content

        # 提取OCR识别的文本
        ocr_texts = []
        for ocr_result in ocr_results:
            if ocr_result.get("text"):
                ocr_texts.append(ocr_result["text"])

        if not ocr_texts:
            return user_content

        # 组合用户问题和OCR结果
        combined_content = f"{user_content}\n\n"
        combined_content += "以下是上传图片的识别内容：\n"
        for i, text in enumerate(ocr_texts, 1):
            combined_content += f"图片{i}识别结果：\n{text}\n\n"

        return combined_content.strip()

    async def _stream_diagnosis_response(
        self,
        agent_id: int,
        diagnosis_input: Dict[str, Any],
        user_id: int
    ) -> AsyncGenerator[str, None]:
        """流式执行AI问诊"""

        # 构建执行请求
        execution_request = AgentExecutionRequest(
            input_data=diagnosis_input,
            config_override={"stream": True}
        )

        # 调用智能体服务的流式执行
        async for chunk in self.agent_service.execute_agent_stream(
            agent_id, execution_request, user_id
        ):
            # 解析流式数据
            if chunk.startswith("data: "):
                try:
                    import json
                    data = json.loads(chunk[6:])
                    if data.get("type") == "content":
                        yield data.get("content", "")
                except:
                    continue
```

### 前端集成流程

#### 1. **H5页面对话流程**

```javascript
// 1. 创建会话
const conversation = await createConversation({
    title: "宠物健康咨询"
});

// 2. 用户上传图片
const handleImageUpload = async (imageFile) => {
    // 转换为base64
    const base64Image = await fileToBase64(imageFile);

    // 调用OCR服务
    const ocrResult = await callOCRService({
        image_base64: base64Image
    });

    return ocrResult;
};

// 3. 发送消息（包含OCR结果）
const sendMessage = async (userInput, ocrResults) => {
    const response = await fetch(`/api/v1/conversations/${conversationId}/messages/stream`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
            content: userInput,
            ocr_results: ocrResults,
            stream: true
        })
    });

    // 处理流式响应
    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
            if (line.startsWith('data: ')) {
                const data = JSON.parse(line.slice(6));
                if (data.type === 'content') {
                    // 实时显示AI回复
                    appendToChat(data.content);
                }
            }
        }
    }
};
```

#### 2. **API调用示例**

```python
# 创建问诊会话
POST /api/v1/conversations
{
    "title": "宠物健康咨询",
    "conversation_type": "diagnosis",
    "enable_ocr": true,
    "auto_context": true
}

# 发送包含OCR结果的消息（流式）
POST /api/v1/conversations/{conversation_id}/messages/stream
{
    "content": "我家猫咪最近精神不振，这是它的血检报告，请帮我看看有什么问题",
    "ocr_results": [
        {
            "text": "血常规检查报告\n白细胞: 15.2 x10^9/L\n红细胞: 6.5 x10^12/L\n血红蛋白: 120 g/L",
            "confidence": 0.95,
            "language": "zh"
        }
    ],
    "attachments": [
        {
            "type": "image",
            "filename": "blood_test.jpg",
            "size": 245760
        }
    ],
    "stream": true
}

# 流式响应格式
data: {"type": "start", "message_id": 123}
data: {"type": "content", "content": "根据您提供的"}
data: {"type": "content", "content": "血检报告，我看到"}
data: {"type": "content", "content": "白细胞数值偏高..."}
data: {"type": "end", "message_id": 123, "total_tokens": 150}
```

### Redis缓存隔离

```python
class TenantAwareCache:
    """租户感知的缓存服务"""

    def __init__(self, tenant_id: Optional[int] = None):
        self.tenant_id = tenant_id or 0
        self.redis = redis_client

    def _get_tenant_key(self, key: str) -> str:
        """生成租户隔离的缓存键"""
        return f"tenant:{self.tenant_id}:{key}"

    async def cache_conversation(self, conversation: Conversation):
        """缓存会话信息（租户隔离）"""
        key = self._get_tenant_key(f"conversation:{conversation.id}")
        data = {
            "id": conversation.id,
            "title": conversation.title,
            "tenant_id": conversation.tenant_id,
            "user_id": conversation.user_id,
            # ... 其他字段
        }
        await self.redis.set(key, data, expire=3600)

    async def get_user_conversations(self, user_id: int) -> Optional[List]:
        """获取用户会话列表（租户隔离）"""
        key = self._get_tenant_key(f"conversation:list:user:{user_id}")
        return await self.redis.get(key)
```

## 📈 实施计划

### 阶段一：核心数据模型（3-4天）
- [ ] Conversation、Message、ConversationContext模型实现
- [ ] 数据库表创建和索引优化
- [ ] 基础会话CRUD服务实现
- [ ] 单元测试编写

### 阶段二：AI问诊集成（3-4天）
- [ ] DiagnosisConversationService实现
- [ ] AI问诊智能体会话集成
- [ ] 流式响应支持
- [ ] OCR结果处理和消息组合

### 阶段三：API接口开发（2-3天）
- [ ] 会话管理API接口实现
- [ ] 流式消息API实现
- [ ] 上下文管理API实现
- [ ] API文档和测试

### 阶段四：前端集成和优化（2-3天）
- [ ] H5页面集成测试
- [ ] Redis缓存策略实现
- [ ] 性能优化和监控
- [ ] 生产环境部署

## 🔒 安全考虑

### 数据安全
- 租户级数据隔离
- 用户权限验证
- 敏感信息加密存储
- 审计日志记录

### API安全
- 请求频率限制
- 输入参数验证
- SQL注入防护
- XSS攻击防护

### 缓存安全
- 缓存数据加密
- 访问权限控制
- 缓存键命名空间隔离
- 敏感数据过期策略

## 📊 性能指标

### 目标性能指标
- 会话创建响应时间：< 100ms
- 消息发送响应时间：< 200ms
- 历史消息查询：< 50ms
- 缓存命中率：> 90%
- 并发支持：1000+ 活跃会话

### 监控指标
- API响应时间分布
- 数据库查询性能
- Redis缓存性能
- 内存使用情况
- 错误率统计

## 📝 总结

本次优化后的设计方案专注于实际业务场景，具有以下核心优势：

### 🎯 **设计优化亮点**

1. **场景聚焦**：专注于H5对话页面的核心需求，移除复杂的工作流功能
2. **简化架构**：去除不必要的多用户会话和智能体协作，专注于用户-AI问诊的核心场景
3. **前端友好**：设计完全贴合前端H5页面的使用流程
4. **OCR集成**：前端处理OCR调用，后端专注于会话管理和AI问诊

### 🚀 **核心功能特性**

1. **自动会话管理**：用户无需手动创建会话，系统自动管理
2. **智能上下文保持**：自动维护多轮对话的上下文状态
3. **OCR结果集成**：前端OCR结果与用户问题无缝组合
4. **流式AI回答**：实时流式输出，提升用户体验
5. **高性能缓存**：Redis缓存策略支持快速响应
6. **租户数据隔离**：基于现有API层的完善隔离机制

### 📊 **实施优势**

- **开发周期短**：10-14天完成核心功能，快速上线
- **技术风险低**：基于现有成熟架构，无复杂新技术
- **用户体验佳**：流式响应和自动上下文管理
- **维护成本低**：简化的架构易于维护和扩展

### 🎯 **核心业务流程**

1. **用户上传图片** → 前端调用OCR服务识别
2. **用户输入问题** → 前端组合问题和OCR结果
3. **发送到AI问诊** → 后端自动管理会话和上下文
4. **流式返回答案** → 实时显示AI诊断建议
5. **保持对话连续性** → 支持多轮问答

该方案完美契合H5对话页面的实际需求，为用户提供流畅、智能的AI问诊体验。
