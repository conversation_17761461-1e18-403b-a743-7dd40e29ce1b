# API请求日志系统

## 概述

新的API请求日志系统将原有的Redis临时存储方案升级为PostgreSQL数据库持久化存储，提供了更强大的数据分析、统计查询和长期存储能力。

## 🎯 主要特性

### 1. 数据库持久化存储
- **PostgreSQL存储**: 使用PostgreSQL作为主要存储引擎
- **结构化数据**: 完整的表结构设计，支持复杂查询
- **索引优化**: 针对常见查询场景优化的复合索引
- **数据完整性**: 外键约束和数据验证确保数据质量

### 2. 高性能异步处理
- **批量插入**: 异步批量插入优化性能
- **内存缓冲**: 内存缓冲区减少数据库IO
- **非阻塞**: 日志记录不影响API请求性能
- **错误恢复**: 插入失败时的重试和恢复机制

### 3. 全面的数据收集
- **请求信息**: 方法、路径、参数、头信息
- **用户信息**: 用户ID、邮箱、认证类型
- **性能指标**: 响应时间、状态码、内容大小
- **错误追踪**: 错误类型、错误消息、堆栈信息
- **业务分类**: OpenAI API vs 业务API分类

### 4. 强大的分析功能
- **实时统计**: 请求量、成功率、响应时间统计
- **用户行为**: 用户API使用模式分析
- **性能监控**: 接口性能趋势分析
- **计费支持**: 为计费系统提供准确的使用数据

## 📊 数据库设计

### 表结构: `api_request_logs`

```sql
CREATE TABLE api_request_logs (
    -- 基础字段
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 请求标识
    request_id UUID UNIQUE NOT NULL,
    
    -- 时间信息
    request_start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    request_end_time TIMESTAMP WITH TIME ZONE,
    processing_time_ms FLOAT,
    
    -- 请求基础信息
    method VARCHAR(10) NOT NULL,
    path VARCHAR(500) NOT NULL,
    full_url TEXT,
    query_params JSON,
    
    -- 请求分类
    request_type requesttype NOT NULL,
    
    -- 客户端信息
    client_ip INET,
    user_agent TEXT,
    referer VARCHAR(500),
    
    -- 认证信息
    auth_type authtype,
    user_id INTEGER,
    user_email VARCHAR(255),
    api_key_id INTEGER,
    api_key_name VARCHAR(100),
    
    -- 请求内容
    content_type VARCHAR(100),
    content_length INTEGER,
    
    -- 响应信息
    status_code INTEGER,
    response_content_type VARCHAR(100),
    response_content_length INTEGER,
    
    -- 错误信息
    error_type VARCHAR(100),
    error_message TEXT,
    
    -- 业务字段
    is_successful BOOLEAN,
    is_cached BOOLEAN DEFAULT FALSE,
    
    -- 元数据
    metadata JSON
);
```

### 索引设计

```sql
-- 基础索引
CREATE INDEX idx_request_id ON api_request_logs(request_id);
CREATE INDEX idx_method ON api_request_logs(method);
CREATE INDEX idx_path ON api_request_logs(path);
CREATE INDEX idx_request_type ON api_request_logs(request_type);
CREATE INDEX idx_user_id ON api_request_logs(user_id);
CREATE INDEX idx_status_code ON api_request_logs(status_code);

-- 复合索引（用于常见查询）
CREATE INDEX idx_user_time ON api_request_logs(user_id, request_start_time);
CREATE INDEX idx_type_time ON api_request_logs(request_type, request_start_time);
CREATE INDEX idx_billing_analysis ON api_request_logs(user_id, request_type, is_successful, request_start_time);
```

## 🚀 使用方法

### 1. 数据库初始化

```bash
# 运行数据库迁移
python scripts/init_db.py --drop-tables

# 或者使用管理工具
python scripts/manage.py db init --drop-tables
```

### 2. 服务配置

日志服务会自动启动，默认配置：
- **批量大小**: 100条记录
- **刷新间隔**: 5秒
- **缓冲区**: 内存队列

### 3. API查询

#### 获取请求统计
```bash
curl -H "Authorization: Bearer your-jwt-token" \
     "http://localhost:8000/api/v1/analytics/request-stats?start_time=2025-01-01T00:00:00Z"
```

#### 获取服务状态
```bash
curl -H "Authorization: Bearer your-admin-token" \
     "http://localhost:8000/api/v1/analytics/service-stats"
```

#### 手动刷新缓冲区
```bash
curl -X POST \
     -H "Authorization: Bearer your-admin-token" \
     "http://localhost:8000/api/v1/analytics/flush-logs"
```

## 📈 性能优化

### 1. 批量插入优化
- 使用异步批量插入减少数据库连接开销
- 内存缓冲区避免频繁的小批量写入
- 可配置的批量大小和刷新间隔

### 2. 索引策略
- 针对时间范围查询的复合索引
- 用户维度分析的专用索引
- 错误分析和性能监控的优化索引

### 3. 数据分区（可选）
```sql
-- 按月分区（生产环境推荐）
CREATE TABLE api_request_logs_2025_01 PARTITION OF api_request_logs
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
```

## 🔧 配置选项

### 环境变量
```bash
# 数据库连接
DATABASE_URL=postgresql://user:password@localhost:5432/vet_platform

# 日志服务配置
LOG_BATCH_SIZE=100          # 批量插入大小
LOG_FLUSH_INTERVAL=5        # 刷新间隔（秒）
LOG_RETENTION_DAYS=90       # 日志保留天数
```

### 代码配置
```python
# 自定义日志服务配置
request_log_service = RequestLogService(
    batch_size=200,           # 更大的批量大小
    flush_interval=10         # 更长的刷新间隔
)
```

## 📊 监控和维护

### 1. 健康检查
```bash
# 检查分析服务状态
curl http://localhost:8000/api/v1/analytics/health
```

### 2. 数据清理
```bash
# 清理90天前的日志
curl -X DELETE \
     -H "Authorization: Bearer your-admin-token" \
     "http://localhost:8000/api/v1/analytics/cleanup-logs?days_to_keep=90"
```

### 3. 性能监控
- 监控缓冲区大小
- 跟踪插入成功率
- 观察平均响应时间

## 🧪 测试

### 运行测试脚本
```bash
python test_request_logging.py
```

测试内容包括：
- 基本日志记录功能
- 批量插入性能
- 统计查询功能
- 错误处理机制
- API端点测试

## 📋 迁移指南

### 从旧系统迁移

1. **备份现有数据**（如果需要）
2. **运行数据库迁移**
3. **更新配置文件**
4. **重启服务**
5. **验证功能**

### 兼容性说明
- 保持与现有认证中间件的兼容性
- 不影响现有API的性能
- 向后兼容的配置选项

## 🔮 未来规划

### 短期目标
- [ ] 数据导出功能
- [ ] 更多统计维度
- [ ] 实时告警功能

### 长期目标
- [ ] 数据仓库集成
- [ ] 机器学习分析
- [ ] 自动化运维

## 🆘 故障排除

### 常见问题

1. **缓冲区积压**
   - 检查数据库连接
   - 调整批量大小
   - 增加刷新频率

2. **插入失败**
   - 检查数据库权限
   - 验证表结构
   - 查看错误日志

3. **查询性能慢**
   - 检查索引使用
   - 优化查询条件
   - 考虑数据分区

### 日志位置
- 应用日志: `logs/gateway.log`
- 数据库日志: PostgreSQL日志
- 错误追踪: 应用异常日志
