#!/usr/bin/env python3
"""
使用OpenAI兼容API访问QwenVL视觉模型进行OCR识别测试

直接调用QwenVL的OpenAI兼容接口进行图片文字识别
"""

from openai import AsyncOpenAI
import httpx
from dotenv import load_dotenv
import asyncio
import sys
import os
import base64
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 加载环境变量
load_dotenv(project_root / ".env")


class QwenVLOCRTester:
    """QwenVL OCR测试类"""

    def __init__(self):
        # 从环境变量获取配置
        self.api_key = os.getenv("VL_QWEN_API_KEY", "")
        self.base_url = os.getenv(
            "VL_QWEN_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1")
        self.model_name = os.getenv("VL_QWEN_MODEL_NAME", "qwen-vl-plus")

        # 测试图片URL
        self.test_image_url = "https://bigdata-1312829983.cos.ap-shanghai.myqcloud.com/ai_data/test_image/20250710175634.jpg"

        # 创建OpenAI客户端
        self.client = AsyncOpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )

        print(f"🔧 配置信息:")
        print(
            f"  - API Key: {self.api_key[:10]}..." if self.api_key else "  - API Key: 未配置")
        print(f"  - Base URL: {self.base_url}")
        print(f"  - Model: {self.model_name}")

    async def download_image_as_base64(self, image_url: str) -> str:
        """下载图片并转换为base64格式"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(image_url)
                response.raise_for_status()

                # 转换为base64
                image_base64 = base64.b64encode(
                    response.content).decode('utf-8')

                # 获取内容类型
                content_type = response.headers.get(
                    "content-type", "image/jpeg")

                print(f"📷 图片下载成功:")
                print(f"  - 大小: {len(response.content)} bytes")
                print(f"  - 类型: {content_type}")

                return f"data:{content_type};base64,{image_base64}"

        except Exception as e:
            print(f"❌ 图片下载失败: {e}")
            raise

    async def test_medical_report_ocr(self):
        """测试医学报告专用OCR识别"""
        print("\n🏥 测试医学报告专用OCR识别...")

        try:
            # 下载图片
            image_data_url = await self.download_image_as_base64(self.test_image_url)

            # 医学报告专用提示词
            medical_prompt = """
请仔细识别这份医学检查报告中的所有文字内容以markdown格式输出，要求：

1. 完整识别所有文字，包括：
   - 报告标题和基本信息（患者姓名、性别、年龄、检查日期等）
   - 检查项目名称和分类
   - 检测数值、单位和参考范围
   - 检查结果描述和医生建议
   - 报告日期和医院信息

2. 对于表格内容应使用markdown形式表格语法：
   - 保持行列对应关系
   - 正确识别表头和数据行
   - 保留异常标记（如↑、↓、*、+等）

4. 对于图片最后的备注信息则不用输出，例如下面这些开头的内容
   - 1.强阳性结果
   - 2.阳性结果
   - 3.弱阳性结果
   - 4.临界
   - 5.阴性结果
   - 注：检测结果仅作辅助参考

5. 对于图片中本身的图表内容不用输出，不用输出markdown形式的图片链接

请按照以上要求，完整、准确地识别图片中的所有文字内容。
"""

            print(f"💬 使用医学报告专用提示词")

            start_time = time.time()

            # 调用OpenAI兼容API
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": image_data_url
                                }
                            },
                            {
                                "type": "text",
                                "text": medical_prompt.strip()
                            }
                        ]
                    }
                ],
                max_tokens=3000,
                temperature=0.1
            )

            processing_time = int((time.time() - start_time) * 1000)

            print("✅ 医学报告OCR识别成功")
            print(f"📊 识别结果:")
            print(f"  - 处理时间: {processing_time}ms")
            print(f"  - 模型: {response.model}")

            # 获取识别结果
            if response.choices and len(response.choices) > 0:
                content = response.choices[0].message.content
                print(f"  - 文本长度: {len(content)}")

                print(f"\n📝 医学报告识别结果:")
                print("=" * 80)
                print(content)
                print("=" * 80)

                # 显示使用情况
                if hasattr(response, 'usage') and response.usage:
                    print(f"\n📈 API使用情况:")
                    print(f"  - 输入tokens: {response.usage.prompt_tokens}")
                    print(f"  - 输出tokens: {response.usage.completion_tokens}")
                    print(f"  - 总tokens: {response.usage.total_tokens}")

                return True
            else:
                print("❌ 未获取到识别结果")
                return False

        except Exception as e:
            print(f"❌ 医学报告OCR识别失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始QwenVL OpenAI兼容API OCR识别测试")
        print("=" * 80)

        if not self.api_key:
            print("❌ 未配置VL_QWEN_API_KEY，请检查环境变量")
            return

        print(f"📷 测试图片: {self.test_image_url}")

        try:
            await self.test_medical_report_ocr()

        except Exception as e:
            print(f"❌ 测试过程中出现错误: {e}")

        print("\n" + "=" * 80)
        print("🎉 QwenVL OpenAI兼容API OCR识别测试完成")


async def main():
    """主函数"""
    tester = QwenVLOCRTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
