import requests
import json

# 配置信息
API_KEY = "sk-proj-Nmtw7HENVQiibyAS8VatGESe5LXTLO5P64ufg2IKEjgTyleu"
BASE_URL = "https://open.haoshouyi.com/v1"
CHAT_COMPLETIONS_URL = f"{BASE_URL}/chat/completions"

# 请求头
headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}


def test_non_stream_chat():
    """测试非流式聊天完成"""
    print("=== 非流式聊天测试 ===")

    payload = {
        "model": "ds-vet-answer-32B",
        "messages": [
            {"role": "system", "content": "你是一个专业的宠物医疗AI助手。"},
            {"role": "user", "content": "狗狗呕吐怎么处理？"}
        ],
        "stream": False
    }

    try:
        response = requests.post(CHAT_COMPLETIONS_URL,
                                 headers=headers, json=payload)
        response.raise_for_status()  # 检查HTTP错误

        result = response.json()
        print("响应内容:")
        print(result["choices"][0]["message"]["content"])
        print("\n" + "="*50 + "\n")

    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
    except KeyError as e:
        print(f"响应格式错误: {e}")
        print(f"完整响应: {response.text}")


def test_stream_chat():
    """测试流式聊天完成"""
    print("=== 流式聊天测试 ===")

    payload = {
        "model": "ds-vet-answer-32B",
        "messages": [
            {"role": "user", "content": "介绍猫咪常见疾病"}
        ],
        "stream": True
    }

    try:
        response = requests.post(CHAT_COMPLETIONS_URL,
                                 headers=headers, json=payload, stream=True)
        response.raise_for_status()

        print("流式响应内容:")
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                # 跳过空行和非数据行
                if line.startswith('data: '):
                    data_str = line[6:]  # 移除 'data: ' 前缀

                    # 检查是否是结束标记
                    if data_str.strip() == '[DONE]':
                        break

                    try:
                        data = json.loads(data_str)
                        # 提取内容
                        if 'choices' in data and len(data['choices']) > 0:
                            delta = data['choices'][0].get('delta', {})
                            content = delta.get('content')
                            if content is not None:
                                print(content, end="", flush=True)
                    except json.JSONDecodeError:
                        # 忽略无法解析的行
                        continue

        print("\n" + "="*50 + "\n")

    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
    except Exception as e:
        print(f"处理错误: {e}")


if __name__ == "__main__":
    # 运行测试
    # test_non_stream_chat()
    test_stream_chat()
