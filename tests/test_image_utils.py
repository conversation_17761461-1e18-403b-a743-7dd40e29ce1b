"""
图片工具类测试
"""
from shared.utils.image import ImageUtils
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(
    os.path.join(os.path.dirname(__file__), '..')))


async def test_url_to_base64():
    """测试URL转base64功能"""
    print("=== 测试URL转base64功能 ===")

    # 使用一个公开的测试图片URL
    test_url = "https://bigdata-1312829983.cos.ap-shanghai.myqcloud.com/ai_data/test_image/20250710175634.jpg"

    try:
        # 测试基本转换
        print(f"正在下载图片: {test_url}")
        base64_result = await ImageUtils.url_to_base64(test_url)
        print(base64_result)

    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")


async def main():
    """主测试函数"""
    print("开始测试图片工具类...")

    await test_url_to_base64()

    print("\n测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
