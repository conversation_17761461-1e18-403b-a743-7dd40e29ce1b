from openai import OpenAI

API_KEY = "y03ZvBwVvHAqAo0nosjdlVGCfCNYGlgm8U"
API_BASE = "http://10.201.112.7:8888/v1"
MODEL_NAME = "ds-vet-answer-32B"

client = OpenAI(base_url=API_BASE, api_key=API_KEY)

response = client.chat.completions.create(
    model=MODEL_NAME,
    messages=[
        {
            "role": "system",
            "content": "你是一个专业的宠物医疗AI助手。"
        },
        {
            "role": "user",
            "content": "介绍下你自己"
        }
    ],
    temperature=0.7,
    max_tokens=500
)

# 提取回复内容
reply = response.choices[0].message.content
print("AI回复:", reply)

# 查看使用统计
usage = response.usage
print(f"Tokens 使用: 输入={usage.prompt_tokens}, 输出={usage.completion_tokens}")
