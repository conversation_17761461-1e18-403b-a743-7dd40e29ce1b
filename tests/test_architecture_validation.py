"""
微服务架构验证测试
验证重构后的架构是否符合微服务独立性原则
"""
import ast
import os
import pytest
from pathlib import Path


class ArchitectureValidator:
    """架构验证器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.violations = []
    
    def check_microservice_independence(self):
        """检查微服务独立性"""
        # 检查shared模块是否有违规导入
        self._check_shared_module_imports()
        
        # 检查各微服务是否有跨服务导入
        self._check_cross_service_imports()
        
        return len(self.violations) == 0, self.violations
    
    def _check_shared_module_imports(self):
        """检查shared模块的导入"""
        shared_path = self.project_root / "shared"
        
        for py_file in shared_path.rglob("*.py"):
            if py_file.name == "__init__.py":
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.ImportFrom):
                        if node.module and node.module.startswith("services."):
                            # 检查是否为违规的微服务导入
                            if not self._is_allowed_import(node.module, str(py_file)):
                                self.violations.append(
                                    f"违规导入: {py_file.relative_to(self.project_root)} "
                                    f"导入了 {node.module}"
                                )
                                
            except Exception as e:
                print(f"解析文件失败 {py_file}: {e}")
    
    def _check_cross_service_imports(self):
        """检查跨服务导入"""
        services_path = self.project_root / "services"
        
        for service_dir in services_path.iterdir():
            if not service_dir.is_dir() or service_dir.name.startswith("__"):
                continue
                
            service_name = service_dir.name
            
            for py_file in service_dir.rglob("*.py"):
                if py_file.name == "__init__.py":
                    continue
                    
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    tree = ast.parse(content)
                    
                    for node in ast.walk(tree):
                        if isinstance(node, ast.ImportFrom):
                            # 只检查绝对导入，忽略相对导入
                            if (node.module and 
                                node.module.startswith("services.") and 
                                node.level == 0):  # level == 0 表示绝对导入
                                # 检查是否为跨服务导入（绝对导入）
                                module_parts = node.module.split(".")
                                if len(module_parts) >= 2:
                                    imported_service = module_parts[1]
                                    # 只有当导入的服务与当前服务不同时才算违规
                                    if imported_service != service_name:
                                        self.violations.append(
                                            f"跨服务导入: {py_file.relative_to(self.project_root)} "
                                            f"导入了 {node.module}"
                                        )
                                    
                except Exception as e:
                    print(f"解析文件失败 {py_file}: {e}")
    
    def _is_allowed_import(self, module: str, file_path: str) -> bool:
        """检查是否为允许的导入"""
        # 允许测试文件导入微服务模块
        if "test" in file_path.lower():
            return True
            
        # 允许迁移文件导入微服务模块
        if "migration" in file_path.lower() or "env.py" in file_path:
            return True
            
        return False


def test_microservice_independence():
    """测试微服务独立性"""
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    validator = ArchitectureValidator(project_root)
    
    is_valid, violations = validator.check_microservice_independence()
    
    if not is_valid:
        violation_msg = "\n".join(violations)
        pytest.fail(f"微服务架构违规:\n{violation_msg}")


def test_auth_client_exists():
    """测试认证客户端是否存在"""
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    auth_client_path = Path(project_root) / "shared" / "clients" / "auth_client.py"
    
    assert auth_client_path.exists(), "认证客户端文件不存在"
    
    # 检查AuthClient类是否存在
    with open(auth_client_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    assert "class AuthClient" in content, "AuthClient类不存在"
    assert "verify_jwt_token" in content, "verify_jwt_token方法不存在"
    assert "verify_api_key" in content, "verify_api_key方法不存在"


def test_user_service_internal_api():
    """测试用户服务内部API是否存在"""
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    internal_api_path = Path(project_root) / "services" / "user_service" / "api" / "internal.py"
    
    assert internal_api_path.exists(), "用户服务内部API文件不存在"
    
    # 检查内部API端点是否存在
    with open(internal_api_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    assert "verify_jwt_token_internal" in content, "JWT验证内部API不存在"
    assert "verify_api_key_internal" in content, "API密钥验证内部API不存在"
    assert "check_permission_internal" in content, "权限检查内部API不存在"


def test_deprecated_modules_marked():
    """测试废弃模块是否已标记"""
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    cross_service_path = Path(project_root) / "shared" / "utils" / "cross_service_queries.py"
    
    assert cross_service_path.exists(), "跨服务查询模块不存在"
    
    # 检查是否有废弃警告
    with open(cross_service_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    assert "warnings.warn" in content, "跨服务查询模块未标记为废弃"
    assert "DeprecationWarning" in content, "缺少废弃警告类型"


if __name__ == "__main__":
    # 直接运行验证
    test_microservice_independence()
    test_auth_client_exists()
    test_user_service_internal_api()
    test_deprecated_modules_marked()
    print("✅ 所有架构验证测试通过！")
