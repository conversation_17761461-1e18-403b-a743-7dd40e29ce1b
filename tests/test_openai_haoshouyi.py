from openai import OpenAI

# 初始化客户端
client = OpenAI(
    api_key="sk-proj-Nmtw7HENVQiibyAS8VatGESe5LXTLO5P64ufg2IKEjgTyleu",
    base_url="https://open.haoshouyi.com/v1"
)

# # 聊天完成
# response = client.chat.completions.create(
#     model="ds-vet-answer-32B",
#     messages=[
#         {"role": "system", "content": "你是一个专业的宠物医疗AI助手。"},
#         {"role": "user", "content": "狗狗呕吐怎么处理？"}
#     ]
# )

# print(response.choices[0].message.content)

# 流式聊天
stream = client.chat.completions.create(
    model="ds-vet-answer-32B",
    messages=[{"role": "user", "content": "介绍猫咪常见疾病"}],
    stream=True
)

for chunk in stream:
    if chunk.choices[0].delta.content is not None:
        print(chunk.choices[0].delta.content, end="")
