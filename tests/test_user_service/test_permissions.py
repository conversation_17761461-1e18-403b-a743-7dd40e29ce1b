"""
权限管理API测试
"""
import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from services.user_service.models.user import User
from services.user_service.models.permission import Permission
from .conftest import get_auth_headers


class TestPermissionsAPI:
    """权限管理API测试类"""
    
    @pytest.mark.asyncio
    async def test_get_permissions_success(self, client: AsyncClient, test_permissions: list[Permission], user_token: str):
        """测试获取权限列表成功"""
        headers = get_auth_headers(user_token)
        response = await client.get("/api/v1/permissions/", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert "pages" in data
        
        # 应该包含测试权限
        assert len(data["items"]) >= len(test_permissions)
        assert data["total"] >= len(test_permissions)
    
    @pytest.mark.asyncio
    async def test_get_permissions_unauthorized(self, client: AsyncClient):
        """测试未授权获取权限列表"""
        response = await client.get("/api/v1/permissions/")
        assert response.status_code == 403
    
    @pytest.mark.asyncio
    async def test_get_permissions_with_pagination(self, client: AsyncClient, user_token: str):
        """测试权限列表分页"""
        headers = get_auth_headers(user_token)
        response = await client.get("/api/v1/permissions/?page=1&size=2", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["page"] == 1
        assert data["size"] == 2
        assert len(data["items"]) <= 2
    
    @pytest.mark.asyncio
    async def test_get_permissions_with_category_filter(self, client: AsyncClient, user_token: str):
        """测试权限列表分类过滤"""
        headers = get_auth_headers(user_token)
        response = await client.get("/api/v1/permissions/?category=user", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证所有返回的权限都属于指定分类
        for permission in data["items"]:
            assert permission["category"] == "user"
    
    @pytest.mark.asyncio
    async def test_get_permissions_with_search(self, client: AsyncClient, user_token: str):
        """测试权限列表搜索"""
        headers = get_auth_headers(user_token)
        response = await client.get("/api/v1/permissions/?search=查看", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证搜索结果包含关键词
        for permission in data["items"]:
            assert ("查看" in permission["name"] or 
                   "查看" in permission["display_name"] or 
                   "查看" in permission["description"])
    
    @pytest.mark.asyncio
    async def test_get_permission_by_id_success(self, client: AsyncClient, test_permissions: list[Permission], user_token: str):
        """测试获取权限详情成功"""
        headers = get_auth_headers(user_token)
        permission = test_permissions[0]
        response = await client.get(f"/api/v1/permissions/{permission.id}", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["id"] == permission.id
        assert data["name"] == permission.name
        assert data["display_name"] == permission.display_name
        assert data["description"] == permission.description
        assert data["category"] == permission.category
    
    @pytest.mark.asyncio
    async def test_get_permission_by_id_not_found(self, client: AsyncClient, user_token: str):
        """测试获取不存在的权限"""
        headers = get_auth_headers(user_token)
        response = await client.get("/api/v1/permissions/99999", headers=headers)
        
        assert response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_create_permission_as_admin(self, client: AsyncClient, admin_token: str):
        """测试管理员创建权限"""
        headers = get_auth_headers(admin_token)
        permission_data = {
            "name": "test_permission",
            "display_name": "测试权限",
            "description": "这是一个测试权限",
            "category": "test",
            "is_system": False
        }
        
        response = await client.post("/api/v1/permissions/", json=permission_data, headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["name"] == permission_data["name"]
        assert data["display_name"] == permission_data["display_name"]
        assert data["description"] == permission_data["description"]
        assert data["category"] == permission_data["category"]
        assert data["is_system"] == permission_data["is_system"]
        assert "id" in data
    
    @pytest.mark.asyncio
    async def test_create_permission_as_regular_user(self, client: AsyncClient, user_token: str):
        """测试普通用户创建权限（应该被拒绝）"""
        headers = get_auth_headers(user_token)
        permission_data = {
            "name": "test_permission",
            "display_name": "测试权限",
            "description": "这是一个测试权限",
            "category": "test"
        }
        
        response = await client.post("/api/v1/permissions/", json=permission_data, headers=headers)
        
        assert response.status_code == 403
    
    @pytest.mark.asyncio
    async def test_create_permission_duplicate_name(self, client: AsyncClient, test_permissions: list[Permission], admin_token: str):
        """测试创建重复名称权限"""
        headers = get_auth_headers(admin_token)
        permission_data = {
            "name": test_permissions[0].name,  # 使用已存在的权限名称
            "display_name": "重复权限",
            "description": "这是一个重复的权限",
            "category": "test"
        }
        
        response = await client.post("/api/v1/permissions/", json=permission_data, headers=headers)
        
        assert response.status_code == 400
        assert "权限名称已存在" in response.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_update_permission_as_admin(self, client: AsyncClient, test_permissions: list[Permission], admin_token: str):
        """测试管理员更新权限"""
        headers = get_auth_headers(admin_token)
        
        # 找一个非系统权限来更新
        non_system_permission = None
        for perm in test_permissions:
            if not perm.is_system:
                non_system_permission = perm
                break
        
        if non_system_permission is None:
            # 如果没有非系统权限，先创建一个
            create_data = {
                "name": "updateable_permission",
                "display_name": "可更新权限",
                "description": "这是一个可更新的权限",
                "category": "test",
                "is_system": False
            }
            create_response = await client.post("/api/v1/permissions/", json=create_data, headers=headers)
            assert create_response.status_code == 200
            permission_id = create_response.json()["id"]
        else:
            permission_id = non_system_permission.id
        
        update_data = {
            "display_name": "更新后的权限",
            "description": "这是更新后的描述"
        }
        
        response = await client.put(f"/api/v1/permissions/{permission_id}", json=update_data, headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["display_name"] == update_data["display_name"]
        assert data["description"] == update_data["description"]
    
    @pytest.mark.asyncio
    async def test_update_system_permission_forbidden(self, client: AsyncClient, test_permissions: list[Permission], admin_token: str):
        """测试更新系统权限（应该被拒绝）"""
        headers = get_auth_headers(admin_token)
        
        # 找一个系统权限
        system_permission = None
        for perm in test_permissions:
            if perm.is_system:
                system_permission = perm
                break
        
        assert system_permission is not None, "应该有系统权限用于测试"
        
        update_data = {
            "display_name": "尝试更新系统权限"
        }
        
        response = await client.put(f"/api/v1/permissions/{system_permission.id}", json=update_data, headers=headers)
        
        assert response.status_code == 400
        assert "系统权限不能修改" in response.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_update_permission_as_regular_user(self, client: AsyncClient, test_permissions: list[Permission], user_token: str):
        """测试普通用户更新权限（应该被拒绝）"""
        headers = get_auth_headers(user_token)
        permission = test_permissions[0]
        update_data = {"display_name": "尝试更新"}
        
        response = await client.put(f"/api/v1/permissions/{permission.id}", json=update_data, headers=headers)
        
        assert response.status_code == 403
    
    @pytest.mark.asyncio
    async def test_delete_permission_as_admin(self, client: AsyncClient, admin_token: str, db_session: AsyncSession):
        """测试管理员删除权限"""
        headers = get_auth_headers(admin_token)
        
        # 先创建一个可删除的权限
        create_data = {
            "name": "deletable_permission",
            "display_name": "可删除权限",
            "description": "这是一个可删除的权限",
            "category": "test",
            "is_system": False
        }
        create_response = await client.post("/api/v1/permissions/", json=create_data, headers=headers)
        assert create_response.status_code == 200
        permission_id = create_response.json()["id"]
        
        # 删除权限
        response = await client.delete(f"/api/v1/permissions/{permission_id}", headers=headers)
        
        assert response.status_code == 200
        assert "权限已删除" in response.json()["message"]
    
    @pytest.mark.asyncio
    async def test_delete_system_permission_forbidden(self, client: AsyncClient, test_permissions: list[Permission], admin_token: str):
        """测试删除系统权限（应该被拒绝）"""
        headers = get_auth_headers(admin_token)
        
        # 找一个系统权限
        system_permission = None
        for perm in test_permissions:
            if perm.is_system:
                system_permission = perm
                break
        
        assert system_permission is not None, "应该有系统权限用于测试"
        
        response = await client.delete(f"/api/v1/permissions/{system_permission.id}", headers=headers)
        
        assert response.status_code == 400
        assert "系统权限不能删除" in response.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_get_user_permissions_as_owner(self, client: AsyncClient, test_user: User, user_token: str):
        """测试用户获取自己的权限列表"""
        headers = get_auth_headers(user_token)
        response = await client.get(f"/api/v1/permissions/{test_user.id}/permissions", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert isinstance(data, list)
        # 普通用户可能没有权限，所以列表可能为空
        assert len(data) >= 0
    
    @pytest.mark.asyncio
    async def test_get_user_permissions_as_admin(self, client: AsyncClient, test_user: User, admin_token: str):
        """测试管理员获取用户权限列表"""
        headers = get_auth_headers(admin_token)
        response = await client.get(f"/api/v1/permissions/{test_user.id}/permissions", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert isinstance(data, list)
    
    @pytest.mark.asyncio
    async def test_get_user_permissions_forbidden(self, client: AsyncClient, test_admin_user: User, user_token: str):
        """测试用户获取其他用户权限（应该被拒绝）"""
        headers = get_auth_headers(user_token)
        response = await client.get(f"/api/v1/permissions/{test_admin_user.id}/permissions", headers=headers)
        
        assert response.status_code == 403
    
    @pytest.mark.asyncio
    async def test_assign_user_permissions_as_admin(self, client: AsyncClient, test_user: User, test_permissions: list[Permission], admin_token: str):
        """测试管理员为用户分配权限"""
        headers = get_auth_headers(admin_token)
        
        # 选择一些权限ID
        permission_ids = [perm.id for perm in test_permissions[:2]]
        
        response = await client.post(
            f"/api/v1/permissions/users/{test_user.id}/permissions",
            json={"permission_ids": permission_ids},
            headers=headers
        )
        
        assert response.status_code == 200
        assert "权限分配成功" in response.json()["message"]
        
        # 验证权限已分配
        get_response = await client.get(f"/api/v1/permissions/{test_user.id}/permissions", headers=headers)
        user_permissions = get_response.json()
        user_permission_ids = [perm["id"] for perm in user_permissions]
        
        for perm_id in permission_ids:
            assert perm_id in user_permission_ids
    
    @pytest.mark.asyncio
    async def test_assign_user_permissions_as_regular_user(self, client: AsyncClient, test_admin_user: User, user_token: str):
        """测试普通用户分配权限（应该被拒绝）"""
        headers = get_auth_headers(user_token)
        
        response = await client.post(
            f"/api/v1/permissions/users/{test_admin_user.id}/permissions",
            json={"permission_ids": [1, 2]},
            headers=headers
        )
        
        assert response.status_code == 403
