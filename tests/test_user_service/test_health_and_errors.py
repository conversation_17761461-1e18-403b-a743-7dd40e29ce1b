"""
健康检查和异常处理测试
"""
import pytest
from httpx import AsyncClient
from unittest.mock import patch, AsyncMock

from services.user_service.config import settings
from .conftest import get_auth_headers


class TestHealthCheck:
    """健康检查测试类"""

    @pytest.mark.asyncio
    async def test_health_check_success(self, client: AsyncClient):
        """测试健康检查成功"""
        response = await client.get("/health")

        assert response.status_code == 200
        data = response.json()

        assert data["status"] == "healthy"
        assert data["service"] == "user_service"
        assert data["version"] == settings.PROJECT_VERSION
        assert "redis" in data
        # Redis状态可能是connected或disconnected，取决于测试环境
        assert data["redis"] in ["connected", "disconnected"]

    @pytest.mark.asyncio
    async def test_health_check_redis_failure(self, client: AsyncClient):
        """测试Redis连接失败时的健康检查"""
        # 模拟Redis连接失败
        with patch('shared.redis_simple.redis_client.set', side_effect=Exception("Redis connection failed")):
            response = await client.get("/health")

            assert response.status_code == 503
            data = response.json()

            assert data["status"] == "unhealthy"
            assert data["service"] == "user_service"
            assert data["version"] == settings.PROJECT_VERSION
            assert "error" in data
            assert "Redis connection failed" in data["error"]


class TestGlobalExceptionHandler:
    """全局异常处理测试类"""

    @pytest.mark.asyncio
    async def test_unhandled_exception(self, client: AsyncClient):
        """测试未处理的异常"""
        # 尝试访问一个不存在的端点，这应该触发404而不是全局异常处理器
        response = await client.get("/api/v1/nonexistent-endpoint")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_validation_error(self, client: AsyncClient, user_token: str):
        """测试数据验证错误"""
        headers = get_auth_headers(user_token)

        # 发送无效的JSON数据
        invalid_data = {
            "email": "not-an-email",  # 无效邮箱格式
            "username": "",  # 空用户名
            "password": "123"  # 密码太短
        }

        response = await client.post("/api/v1/auth/register", json=invalid_data, headers=headers)

        assert response.status_code == 422
        data = response.json()

        assert "detail" in data
        assert isinstance(data["detail"], list)
        # 应该包含多个验证错误
        assert len(data["detail"]) > 0


class TestAPIErrorHandling:
    """API错误处理测试类"""

    @pytest.mark.asyncio
    async def test_unauthorized_access(self, client: AsyncClient):
        """测试未授权访问"""
        response = await client.get("/api/v1/users/")

        assert response.status_code == 403
        data = response.json()

        assert "detail" in data

    @pytest.mark.asyncio
    async def test_forbidden_access(self, client: AsyncClient, user_token: str):
        """测试禁止访问"""
        headers = get_auth_headers(user_token)

        # 普通用户尝试访问需要管理员权限的端点
        response = await client.get("/api/v1/users/", headers=headers)

        assert response.status_code == 403
        data = response.json()

        assert "detail" in data

    @pytest.mark.asyncio
    async def test_not_found_error(self, client: AsyncClient, admin_token: str):
        """测试资源不存在错误"""
        headers = get_auth_headers(admin_token)

        # 尝试获取不存在的用户
        response = await client.get("/api/v1/users/99999", headers=headers)

        assert response.status_code == 404
        data = response.json()

        assert "detail" in data

    @pytest.mark.asyncio
    async def test_bad_request_error(self, client: AsyncClient, test_user, admin_token: str):
        """测试错误请求"""
        headers = get_auth_headers(admin_token)

        # 尝试创建重复邮箱的用户
        duplicate_user_data = {
            "email": test_user.email,  # 使用已存在的邮箱
            "username": "newuser",
            "first_name": "New",
            "last_name": "User",
            "password": "NewPassword123"
        }

        response = await client.post("/api/v1/users/", json=duplicate_user_data, headers=headers)

        assert response.status_code == 400
        data = response.json()

        assert "detail" in data
        assert "邮箱已被使用" in data["detail"]

    @pytest.mark.asyncio
    async def test_invalid_token_format(self, client: AsyncClient):
        """测试无效的令牌格式"""
        # 使用无效的Authorization头格式
        headers = {"Authorization": "InvalidFormat token"}

        response = await client.get("/api/v1/auth/me", headers=headers)

        # HTTPBearer在无效格式时返回403
        assert response.status_code == 403

    @pytest.mark.asyncio
    async def test_expired_token(self, client: AsyncClient):
        """测试过期令牌"""
        # 使用明显过期的令牌
        expired_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiZXhwIjoxfQ.invalid"
        headers = get_auth_headers(expired_token)

        response = await client.get("/api/v1/auth/me", headers=headers)

        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_malformed_json(self, client: AsyncClient):
        """测试格式错误的JSON"""
        # 发送格式错误的JSON
        response = await client.post(
            "/api/v1/auth/login",
            content='{"email": "<EMAIL>", "password": }',  # 格式错误的JSON
            headers={"Content-Type": "application/json"}
        )

        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_missing_required_fields(self, client: AsyncClient):
        """测试缺少必填字段"""
        # 发送缺少必填字段的数据
        incomplete_data = {
            "email": "<EMAIL>"
            # 缺少password字段
        }

        response = await client.post("/api/v1/auth/login", json=incomplete_data)

        assert response.status_code == 422
        data = response.json()

        assert "detail" in data
        assert isinstance(data["detail"], list)

        # 检查是否有关于缺少password字段的错误
        password_error_found = False
        for error in data["detail"]:
            if "password" in str(error).lower():
                password_error_found = True
                break

        assert password_error_found

    @pytest.mark.asyncio
    async def test_method_not_allowed(self, client: AsyncClient):
        """测试不允许的HTTP方法"""
        # 对只支持GET的端点使用POST
        response = await client.post("/health")

        assert response.status_code == 405

    @pytest.mark.asyncio
    async def test_content_type_error(self, client: AsyncClient):
        """测试内容类型错误"""
        # 发送非JSON内容到期望JSON的端点
        response = await client.post(
            "/api/v1/auth/login",
            content="email=<EMAIL>&password=test",
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )

        # FastAPI会自动处理这种情况，通常返回422
        assert response.status_code in [422, 400]


class TestDatabaseErrorHandling:
    """数据库错误处理测试类"""

    @pytest.mark.asyncio
    async def test_database_connection_error(self, client: AsyncClient):
        """测试数据库连接错误"""
        # 这个测试比较难模拟，因为我们使用的是内存数据库
        # 在实际环境中，可以通过模拟数据库连接失败来测试
        pass

    @pytest.mark.asyncio
    async def test_database_constraint_violation(self, client: AsyncClient, admin_token: str):
        """测试数据库约束违反"""
        headers = get_auth_headers(admin_token)

        # 尝试创建两个相同邮箱的用户
        user_data = {
            "email": "<EMAIL>",
            "username": "constraintuser1",
            "first_name": "Constraint",
            "last_name": "User",
            "password": "ConstraintPassword123"
        }

        # 第一次创建应该成功
        response1 = await client.post("/api/v1/users/", json=user_data, headers=headers)
        assert response1.status_code == 201

        # 第二次创建相同邮箱应该失败
        user_data["username"] = "constraintuser2"  # 改变用户名但保持邮箱相同
        response2 = await client.post("/api/v1/users/", json=user_data, headers=headers)

        assert response2.status_code == 400
        assert "邮箱已被使用" in response2.json()["detail"]


class TestRateLimitingAndSecurity:
    """速率限制和安全测试类"""

    @pytest.mark.asyncio
    async def test_sql_injection_attempt(self, client: AsyncClient, admin_token: str):
        """测试SQL注入尝试"""
        headers = get_auth_headers(admin_token)

        # 尝试在搜索参数中注入SQL
        malicious_search = "'; DROP TABLE users; --"
        response = await client.get(f"/api/v1/users/?search={malicious_search}", headers=headers)

        # 应该正常处理，不会导致SQL注入
        assert response.status_code == 200
        # 搜索结果应该为空或正常返回
        data = response.json()
        assert "api_keys" in data or "permissions" in data or "users" in data or "items" in data

    @pytest.mark.asyncio
    async def test_xss_attempt(self, client: AsyncClient, admin_token: str):
        """测试XSS尝试"""
        headers = get_auth_headers(admin_token)

        # 尝试在用户数据中注入脚本
        xss_data = {
            "email": "<EMAIL>",
            "username": "<script>alert('xss')</script>",
            "first_name": "<img src=x onerror=alert('xss')>",
            "last_name": "User",
            "password": "password123"
        }

        response = await client.post("/api/v1/users/", json=xss_data, headers=headers)

        # 应该正常创建用户，但脚本内容应该被转义或清理
        if response.status_code == 200:
            data = response.json()
            # 检查返回的数据是否包含原始脚本（不应该包含）
            assert "<script>" not in data["username"]
            assert "onerror=" not in data["first_name"]
