"""
API密钥管理测试
"""
import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from services.user_service.models.user import User, APIKey
from .conftest import get_auth_headers


class TestAPIKeysAPI:
    """API密钥管理API测试类"""

    @pytest.mark.asyncio
    async def test_get_api_keys_success(self, client: AsyncClient, test_user: User, test_api_key: APIKey, user_token: str):
        """测试获取API密钥列表成功"""
        headers = get_auth_headers(user_token)
        response = await client.get("/api/v1/api-keys/", headers=headers)

        assert response.status_code == 200
        data = response.json()

        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert "pages" in data

        # 应该包含测试API密钥
        assert len(data["items"]) >= 1
        assert data["total"] >= 1

        # 验证API密钥信息（不应包含完整密钥）
        api_key_item = data["items"][0]
        assert api_key_item["id"] == test_api_key.id
        assert api_key_item["name"] == test_api_key.name
        assert api_key_item["description"] == test_api_key.description
        assert "key" not in api_key_item  # 不应返回完整密钥

    @pytest.mark.asyncio
    async def test_get_api_keys_unauthorized(self, client: AsyncClient):
        """测试未授权获取API密钥列表"""
        response = await client.get("/api/v1/api-keys/")
        assert response.status_code == 403

    @pytest.mark.asyncio
    async def test_get_api_keys_with_pagination(self, client: AsyncClient, user_token: str):
        """测试API密钥列表分页"""
        headers = get_auth_headers(user_token)
        response = await client.get("/api/v1/api-keys/?page=1&size=5", headers=headers)

        assert response.status_code == 200
        data = response.json()

        assert data["page"] == 1
        assert data["size"] == 5
        assert len(data["items"]) <= 5

    @pytest.mark.asyncio
    async def test_get_api_keys_with_search(self, client: AsyncClient, test_api_key: APIKey, user_token: str):
        """测试API密钥列表搜索"""
        headers = get_auth_headers(user_token)
        response = await client.get(f"/api/v1/api-keys/?search={test_api_key.name}", headers=headers)

        assert response.status_code == 200
        data = response.json()

        # 应该找到匹配的API密钥
        found_key = None
        for key in data["items"]:
            if key["name"] == test_api_key.name:
                found_key = key
                break

        assert found_key is not None

    @pytest.mark.asyncio
    async def test_get_api_keys_admin_can_see_all(self, client: AsyncClient, test_user: User, test_api_key: APIKey, admin_token: str):
        """测试管理员可以查看所有用户的API密钥"""
        headers = get_auth_headers(admin_token)
        response = await client.get("/api/v1/api-keys/", headers=headers)

        assert response.status_code == 200
        data = response.json()

        # 管理员应该能看到所有API密钥
        assert len(data["items"]) >= 1

    @pytest.mark.asyncio
    async def test_create_api_key_success(self, client: AsyncClient, user_token: str):
        """测试创建API密钥成功"""
        headers = get_auth_headers(user_token)
        api_key_data = {
            "name": "新测试API密钥",
            "description": "这是一个新的测试API密钥",
            "expires_days": 30
        }

        response = await client.post("/api/v1/api-keys/", json=api_key_data, headers=headers)

        assert response.status_code == 200
        data = response.json()

        assert "message" in data
        assert "api_key" in data
        assert "api_key_id" in data
        assert "name" in data
        assert "warning" in data

        assert data["name"] == api_key_data["name"]
        assert len(data["api_key"]) > 0  # 应该返回完整的API密钥
        assert "请妥善保存" in data["warning"]

    @pytest.mark.asyncio
    async def test_create_api_key_without_expiry(self, client: AsyncClient, user_token: str):
        """测试创建无过期时间的API密钥"""
        headers = get_auth_headers(user_token)
        api_key_data = {
            "name": "永久API密钥",
            "description": "这是一个永久的API密钥"
        }

        response = await client.post("/api/v1/api-keys/", json=api_key_data, headers=headers)

        assert response.status_code == 200
        data = response.json()

        assert data["name"] == api_key_data["name"]
        assert data["expires_at"] is None

    @pytest.mark.asyncio
    async def test_create_api_key_unauthorized(self, client: AsyncClient):
        """测试未授权创建API密钥"""
        api_key_data = {
            "name": "未授权密钥",
            "description": "这应该失败"
        }

        response = await client.post("/api/v1/api-keys/", json=api_key_data)
        assert response.status_code == 403

    @pytest.mark.asyncio
    async def test_create_api_key_limit_exceeded(self, client: AsyncClient, test_user: User, user_token: str, db_session: AsyncSession):
        """测试API密钥数量限制"""
        headers = get_auth_headers(user_token)

        # 创建多个API密钥直到达到限制
        for i in range(10):  # 假设限制是10个
            api_key_data = {
                "name": f"测试密钥{i}",
                "description": f"第{i}个测试密钥"
            }
            response = await client.post("/api/v1/api-keys/", json=api_key_data, headers=headers)
            if response.status_code != 200:
                break

        # 尝试创建第11个应该失败
        api_key_data = {
            "name": "超限密钥",
            "description": "这应该失败"
        }
        response = await client.post("/api/v1/api-keys/", json=api_key_data, headers=headers)

        assert response.status_code == 400
        assert "API密钥数量已达上限" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_get_api_key_by_id_as_owner(self, client: AsyncClient, test_api_key: APIKey, user_token: str):
        """测试用户获取自己的API密钥详情"""
        headers = get_auth_headers(user_token)
        response = await client.get(f"/api/v1/api-keys/{test_api_key.id}", headers=headers)

        assert response.status_code == 200
        data = response.json()

        assert data["id"] == test_api_key.id
        assert data["name"] == test_api_key.name
        assert data["description"] == test_api_key.description
        assert data["is_active"] == test_api_key.is_active
        assert "key_hash" not in data  # 不应返回密钥哈希

    @pytest.mark.asyncio
    async def test_get_api_key_by_id_as_admin(self, client: AsyncClient, test_api_key: APIKey, admin_token: str):
        """测试管理员获取API密钥详情"""
        headers = get_auth_headers(admin_token)
        response = await client.get(f"/api/v1/api-keys/{test_api_key.id}", headers=headers)

        assert response.status_code == 200
        data = response.json()

        assert data["id"] == test_api_key.id
        assert data["name"] == test_api_key.name

    @pytest.mark.asyncio
    async def test_get_api_key_by_id_forbidden(self, client: AsyncClient, test_admin_user: User, admin_token: str, user_token: str, db_session: AsyncSession):
        """测试用户获取其他用户的API密钥（应该被拒绝）"""
        # 先用管理员创建一个API密钥
        admin_headers = get_auth_headers(admin_token)
        api_key_data = {
            "name": "管理员密钥",
            "description": "管理员的API密钥"
        }
        create_response = await client.post("/api/v1/api-keys/", json=api_key_data, headers=admin_headers)
        assert create_response.status_code == 200
        admin_api_key_id = create_response.json()["api_key_id"]

        # 普通用户尝试获取管理员的API密钥
        user_headers = get_auth_headers(user_token)
        response = await client.get(f"/api/v1/api-keys/{admin_api_key_id}", headers=user_headers)

        assert response.status_code == 404  # 应该返回404而不是403，因为查询时已经过滤了用户

    @pytest.mark.asyncio
    async def test_get_api_key_by_id_not_found(self, client: AsyncClient, user_token: str):
        """测试获取不存在的API密钥"""
        headers = get_auth_headers(user_token)
        response = await client.get("/api/v1/api-keys/99999", headers=headers)

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_api_key_success(self, client: AsyncClient, test_api_key: APIKey, user_token: str):
        """测试更新API密钥成功"""
        headers = get_auth_headers(user_token)
        update_data = {
            "name": "更新后的API密钥",
            "description": "更新后的描述"
        }

        response = await client.put(f"/api/v1/api-keys/{test_api_key.id}", json=update_data, headers=headers)

        assert response.status_code == 200
        data = response.json()

        assert data["name"] == update_data["name"]
        assert data["description"] == update_data["description"]

    @pytest.mark.asyncio
    async def test_update_api_key_forbidden(self, client: AsyncClient, test_admin_user: User, admin_token: str, user_token: str):
        """测试用户更新其他用户的API密钥（应该被拒绝）"""
        # 先用管理员创建一个API密钥
        admin_headers = get_auth_headers(admin_token)
        api_key_data = {
            "name": "管理员密钥",
            "description": "管理员的API密钥"
        }
        create_response = await client.post("/api/v1/api-keys/", json=api_key_data, headers=admin_headers)
        assert create_response.status_code == 200
        admin_api_key_id = create_response.json()["api_key_id"]

        # 普通用户尝试更新管理员的API密钥
        user_headers = get_auth_headers(user_token)
        update_data = {"name": "尝试更新"}
        response = await client.put(f"/api/v1/api-keys/{admin_api_key_id}", json=update_data, headers=user_headers)

        assert response.status_code == 404  # 应该返回404而不是403

    @pytest.mark.asyncio
    async def test_update_api_key_not_found(self, client: AsyncClient, user_token: str):
        """测试更新不存在的API密钥"""
        headers = get_auth_headers(user_token)
        update_data = {"name": "不存在的密钥"}

        response = await client.put("/api/v1/api-keys/99999", json=update_data, headers=headers)

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_api_key_success(self, client: AsyncClient, user_token: str, db_session: AsyncSession):
        """测试删除API密钥成功"""
        # 先创建一个API密钥
        headers = get_auth_headers(user_token)
        api_key_data = {
            "name": "待删除密钥",
            "description": "这个密钥将被删除"
        }
        create_response = await client.post("/api/v1/api-keys/", json=api_key_data, headers=headers)
        assert create_response.status_code == 200
        api_key_id = create_response.json()["api_key_id"]

        # 删除API密钥
        response = await client.delete(f"/api/v1/api-keys/{api_key_id}", headers=headers)

        assert response.status_code == 200
        assert "API密钥已删除" in response.json()["message"]

        # 验证API密钥已被软删除
        from sqlalchemy import select
        result = await db_session.execute(select(APIKey).where(APIKey.id == api_key_id))
        api_key = result.scalar_one_or_none()
        assert api_key is not None
        assert api_key.is_active is False
        assert api_key.deleted_at is not None

    @pytest.mark.asyncio
    async def test_delete_api_key_forbidden(self, client: AsyncClient, test_admin_user: User, admin_token: str, user_token: str):
        """测试用户删除其他用户的API密钥（应该被拒绝）"""
        # 先用管理员创建一个API密钥
        admin_headers = get_auth_headers(admin_token)
        api_key_data = {
            "name": "管理员密钥",
            "description": "管理员的API密钥"
        }
        create_response = await client.post("/api/v1/api-keys/", json=api_key_data, headers=admin_headers)
        assert create_response.status_code == 200
        admin_api_key_id = create_response.json()["api_key_id"]

        # 普通用户尝试删除管理员的API密钥
        user_headers = get_auth_headers(user_token)
        response = await client.delete(f"/api/v1/api-keys/{admin_api_key_id}", headers=user_headers)

        assert response.status_code == 404  # 应该返回404而不是403

    @pytest.mark.asyncio
    async def test_delete_api_key_not_found(self, client: AsyncClient, user_token: str):
        """测试删除不存在的API密钥"""
        headers = get_auth_headers(user_token)
        response = await client.delete("/api/v1/api-keys/99999", headers=headers)

        assert response.status_code == 404
