"""
用户服务测试配置和fixtures
"""
# 修复asyncio兼容性问题 - 必须在最开始
from shared.utils.security import get_password_hash, create_access_token
from services.user_service.models.permission import Permission
from services.user_service.models.user import User, APIKey, UserSession
from services.user_service.main import app
from shared.redis_simple import redis_client
from shared.database import Base, get_async_db
import asyncio
import builtins
if not hasattr(asyncio, 'TimeoutError'):
    asyncio.TimeoutError = builtins.TimeoutError

import os
import sys
import pytest
import pytest_asyncio
from typing import AsyncGenerator, Generator
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import StaticPool

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))


# 测试数据库配置
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"

# 创建测试数据库引擎
test_engine = create_async_engine(
    TEST_DATABASE_URL,
    poolclass=StaticPool,
    connect_args={"check_same_thread": False},
    echo=False
)

TestSessionLocal = async_sessionmaker(
    test_engine,
    class_=AsyncSession,
    expire_on_commit=False
)


@pytest_asyncio.fixture(scope="function")
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """创建测试数据库会话"""
    # 创建所有表
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    # 创建会话
    async with TestSessionLocal() as session:
        yield session

    # 清理数据库
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest_asyncio.fixture(scope="function")
async def client(db_session: AsyncSession) -> AsyncGenerator[AsyncClient, None]:
    """创建测试客户端"""

    # 覆盖数据库依赖
    async def override_get_db():
        yield db_session

    app.dependency_overrides[get_async_db] = override_get_db

    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac

    # 清理依赖覆盖
    app.dependency_overrides.clear()


@pytest_asyncio.fixture
async def test_permissions(db_session: AsyncSession) -> list[Permission]:
    """创建测试权限"""
    permissions_data = [
        {"name": "view_users", "display_name": "查看用户",
            "description": "查看用户信息", "category": "user", "is_system": True},
        {"name": "edit_users", "display_name": "编辑用户",
            "description": "编辑用户信息", "category": "user", "is_system": True},
        {"name": "delete_users", "display_name": "删除用户",
            "description": "删除用户", "category": "user", "is_system": True},
        {"name": "view_permissions", "display_name": "查看权限",
            "description": "查看权限信息", "category": "permission", "is_system": True},
        {"name": "manage_api_keys", "display_name": "管理API密钥",
            "description": "管理API密钥", "category": "api", "is_system": True},
        {"name": "system_admin", "display_name": "系统管理",
            "description": "系统管理权限", "category": "system", "is_system": True},
    ]

    permissions = []
    for perm_data in permissions_data:
        permission = Permission(**perm_data)
        db_session.add(permission)
        permissions.append(permission)

    await db_session.commit()

    # 刷新对象以获取ID
    for permission in permissions:
        await db_session.refresh(permission)

    return permissions


@pytest_asyncio.fixture
async def test_user(db_session: AsyncSession, test_permissions: list[Permission]) -> User:
    """创建测试用户"""
    user = User(
        email="<EMAIL>",
        username="testuser",
        first_name="Test",
        last_name="User",
        hashed_password=get_password_hash("TestPassword123"),
        is_active=True,
        is_superuser=False
    )

    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)

    return user


@pytest_asyncio.fixture
async def test_admin_user(db_session: AsyncSession, test_permissions: list[Permission]) -> User:
    """创建测试管理员用户"""
    admin_user = User(
        email="<EMAIL>",
        username="admin",
        first_name="Admin",
        last_name="User",
        hashed_password=get_password_hash("AdminPassword123"),
        is_active=True,
        is_superuser=True
    )

    # 为管理员分配所有权限
    admin_user.permissions = test_permissions

    db_session.add(admin_user)
    await db_session.commit()
    await db_session.refresh(admin_user)

    return admin_user


@pytest_asyncio.fixture
async def test_inactive_user(db_session: AsyncSession) -> User:
    """创建测试非活跃用户"""
    user = User(
        email="<EMAIL>",
        username="inactiveuser",
        first_name="Inactive",
        last_name="User",
        hashed_password=get_password_hash("InactivePassword123"),
        is_active=False,
        is_superuser=False
    )

    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)

    return user


@pytest_asyncio.fixture
async def user_token(test_user: User) -> str:
    """创建用户访问令牌"""
    return create_access_token(data={"sub": str(test_user.id)})


@pytest_asyncio.fixture
async def admin_token(test_admin_user: User) -> str:
    """创建管理员访问令牌"""
    return create_access_token(data={"sub": str(test_admin_user.id)})


@pytest_asyncio.fixture
async def test_api_key(db_session: AsyncSession, test_user: User) -> APIKey:
    """创建测试API密钥"""
    api_key = APIKey(
        name="Test API Key",
        key="test_api_key_value",
        user_id=test_user.id,
        description="测试用API密钥",
        is_active=True
    )

    db_session.add(api_key)
    await db_session.commit()
    await db_session.refresh(api_key)

    return api_key


def get_auth_headers(token: str) -> dict:
    """获取认证头"""
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


# 测试数据
TEST_USER_DATA = {
    "email": "<EMAIL>",
    "username": "newuser",
    "first_name": "New",
    "last_name": "User",
    "password": "NewUserPassword123"
}

TEST_LOGIN_DATA = {
    "email": "<EMAIL>",
    "password": "TestPassword123"
}

TEST_ADMIN_LOGIN_DATA = {
    "email": "<EMAIL>",
    "password": "AdminPassword123"
}

TEST_REGISTER_DATA = {
    "email": "<EMAIL>",
    "username": "registeruser",
    "first_name": "Register",
    "last_name": "User",
    "password": "RegisterPassword123"
}
