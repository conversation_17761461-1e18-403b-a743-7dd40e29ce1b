"""
认证API测试
"""
import pytest
from httpx import AsyncC<PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from services.user_service.models.user import User, UserSession
from .conftest import (
    TEST_LOGIN_DATA,
    TEST_ADMIN_LOGIN_DATA,
    TEST_REGISTER_DATA,
    get_auth_headers
)


class TestAuthAPI:
    """认证API测试类"""

    @pytest.mark.asyncio
    async def test_login_success(self, client: AsyncClient, test_user: User):
        """测试成功登录"""
        response = await client.post("/api/v1/auth/login", json=TEST_LOGIN_DATA)

        assert response.status_code == 200
        data = response.json()

        assert "access_token" in data
        assert "refresh_token" in data
        assert "expires_in" in data
        assert data["expires_in"] > 0

    @pytest.mark.asyncio
    async def test_login_invalid_email(self, client: AsyncClient):
        """测试无效邮箱登录"""
        login_data = {"email": "<EMAIL>",
                      "password": "password"}
        response = await client.post("/api/v1/auth/login", json=login_data)

        assert response.status_code == 401
        assert "Incorrect email or password" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_login_invalid_password(self, client: AsyncClient, test_user: User):
        """测试无效密码登录"""
        login_data = {"email": test_user.email, "password": "wrongpassword"}
        response = await client.post("/api/v1/auth/login", json=login_data)

        assert response.status_code == 401
        assert "Incorrect email or password" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_login_inactive_user(self, client: AsyncClient, test_inactive_user: User):
        """测试非活跃用户登录"""
        login_data = {"email": test_inactive_user.email,
                      "password": "InactivePassword123"}
        response = await client.post("/api/v1/auth/login", json=login_data)

        assert response.status_code == 400
        assert "Inactive user" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_register_success(self, client: AsyncClient):
        """测试成功注册"""
        response = await client.post("/api/v1/auth/register", json=TEST_REGISTER_DATA)

        assert response.status_code == 201
        data = response.json()

        assert data["email"] == TEST_REGISTER_DATA["email"]
        assert data["username"] == TEST_REGISTER_DATA["username"]
        assert data["first_name"] == TEST_REGISTER_DATA["first_name"]
        assert data["last_name"] == TEST_REGISTER_DATA["last_name"]
        assert "id" in data
        assert "password" not in data

    @pytest.mark.asyncio
    async def test_register_duplicate_email(self, client: AsyncClient, test_user: User):
        """测试重复邮箱注册"""
        register_data = TEST_REGISTER_DATA.copy()
        register_data["email"] = test_user.email

        response = await client.post("/api/v1/auth/register", json=register_data)

        assert response.status_code == 400
        assert "Email already registered" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_register_duplicate_username(self, client: AsyncClient, test_user: User):
        """测试重复用户名注册"""
        register_data = TEST_REGISTER_DATA.copy()
        register_data["username"] = test_user.username

        response = await client.post("/api/v1/auth/register", json=register_data)

        assert response.status_code == 400
        assert "Username already taken" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_register_invalid_data(self, client: AsyncClient):
        """测试无效数据注册"""
        # 测试缺少必填字段
        invalid_data = {"email": "<EMAIL>"}
        response = await client.post("/api/v1/auth/register", json=invalid_data)
        assert response.status_code == 422

        # 测试无效邮箱格式
        invalid_data = TEST_REGISTER_DATA.copy()
        invalid_data["email"] = "invalid-email"
        response = await client.post("/api/v1/auth/register", json=invalid_data)
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_profile_success(self, client: AsyncClient, test_user: User, user_token: str):
        """测试获取用户资料成功"""
        headers = get_auth_headers(user_token)
        response = await client.get("/api/v1/auth/me", headers=headers)

        assert response.status_code == 200
        data = response.json()

        assert data["id"] == test_user.id
        assert data["email"] == test_user.email
        assert data["username"] == test_user.username
        assert data["first_name"] == test_user.first_name
        assert data["last_name"] == test_user.last_name
        assert data["is_active"] == test_user.is_active
        assert data["is_superuser"] == test_user.is_superuser

    @pytest.mark.asyncio
    async def test_get_profile_unauthorized(self, client: AsyncClient):
        """测试未授权获取用户资料"""
        response = await client.get("/api/v1/auth/me")
        assert response.status_code == 403

    @pytest.mark.asyncio
    async def test_get_profile_invalid_token(self, client: AsyncClient):
        """测试无效令牌获取用户资料"""
        headers = get_auth_headers("invalid_token")
        response = await client.get("/api/v1/auth/me", headers=headers)
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_refresh_token_success(self, client: AsyncClient, test_user: User, db_session: AsyncSession):
        """测试刷新令牌成功"""
        # 先登录获取refresh token
        login_response = await client.post("/api/v1/auth/login", json=TEST_LOGIN_DATA)
        login_data = login_response.json()
        refresh_token = login_data["refresh_token"]

        # 使用refresh token获取新的access token
        refresh_data = {"refresh_token": refresh_token}
        response = await client.post("/api/v1/auth/refresh", json=refresh_data)

        assert response.status_code == 200
        data = response.json()

        assert "access_token" in data
        assert "expires_in" in data
        assert data["expires_in"] > 0

    @pytest.mark.asyncio
    async def test_refresh_token_invalid(self, client: AsyncClient):
        """测试无效刷新令牌"""
        refresh_data = {"refresh_token": "invalid_refresh_token"}
        response = await client.post("/api/v1/auth/refresh", json=refresh_data)

        assert response.status_code == 401
        assert "Invalid refresh token" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_logout_success(self, client: AsyncClient, test_user: User, db_session: AsyncSession):
        """测试注销成功"""
        # 先登录
        login_response = await client.post("/api/v1/auth/login", json=TEST_LOGIN_DATA)
        login_data = login_response.json()
        access_token = login_data["access_token"]
        refresh_token = login_data["refresh_token"]

        # 注销
        headers = get_auth_headers(access_token)
        logout_data = {"refresh_token": refresh_token}
        response = await client.post("/api/v1/auth/logout", json=logout_data, headers=headers)

        assert response.status_code == 200
        assert "Logged out successfully" in response.json()["message"]

        # 验证session已被标记为非活跃
        from sqlalchemy import select
        result = await db_session.execute(
            select(UserSession).where(
                UserSession.refresh_token == refresh_token)
        )
        session = result.scalar_one_or_none()
        assert session is not None
        assert session.is_active is False

    @pytest.mark.asyncio
    async def test_logout_unauthorized(self, client: AsyncClient):
        """测试未授权注销"""
        logout_data = {"refresh_token": "some_token"}
        response = await client.post("/api/v1/auth/logout", json=logout_data)
        assert response.status_code == 403

    @pytest.mark.asyncio
    async def test_logout_invalid_refresh_token(self, client: AsyncClient, user_token: str):
        """测试无效刷新令牌注销"""
        headers = get_auth_headers(user_token)
        logout_data = {"refresh_token": "invalid_refresh_token"}
        response = await client.post("/api/v1/auth/logout", json=logout_data, headers=headers)

        # logout即使refresh_token无效也会返回成功，这是设计决策
        assert response.status_code == 200
        assert response.json()["message"] == "Logged out successfully"
