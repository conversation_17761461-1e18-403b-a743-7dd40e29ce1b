"""
用户管理API测试
"""
import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from services.user_service.models.user import User
from .conftest import TEST_USER_DATA, get_auth_headers


class TestUsersAPI:
    """用户管理API测试类"""

    @pytest.mark.asyncio
    async def test_get_users_as_admin(self, client: AsyncClient, test_admin_user: User, test_user: User, admin_token: str):
        """测试管理员获取用户列表"""
        headers = get_auth_headers(admin_token)
        response = await client.get("/api/v1/users/", headers=headers)

        assert response.status_code == 200
        data = response.json()

        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert "pages" in data

        # 应该包含至少两个用户（admin和test用户）
        assert len(data["items"]) >= 2
        assert data["total"] >= 2

    @pytest.mark.asyncio
    async def test_get_users_as_regular_user(self, client: AsyncClient, test_user: User, user_token: str):
        """测试普通用户获取用户列表（应该被拒绝）"""
        headers = get_auth_headers(user_token)
        response = await client.get("/api/v1/users/", headers=headers)

        assert response.status_code == 403

    @pytest.mark.asyncio
    async def test_get_users_unauthorized(self, client: AsyncClient):
        """测试未授权获取用户列表"""
        response = await client.get("/api/v1/users/")
        assert response.status_code == 403

    @pytest.mark.asyncio
    async def test_get_users_with_pagination(self, client: AsyncClient, admin_token: str):
        """测试用户列表分页"""
        headers = get_auth_headers(admin_token)
        response = await client.get("/api/v1/users/?page=1&size=1", headers=headers)

        assert response.status_code == 200
        data = response.json()

        assert data["page"] == 1
        assert data["size"] == 1
        assert len(data["items"]) <= 1

    @pytest.mark.asyncio
    async def test_get_users_with_search(self, client: AsyncClient, test_user: User, admin_token: str):
        """测试用户列表搜索"""
        headers = get_auth_headers(admin_token)
        response = await client.get(f"/api/v1/users/?search={test_user.username}", headers=headers)

        assert response.status_code == 200
        data = response.json()

        # 应该找到匹配的用户
        found_user = None
        for user in data["items"]:
            if user["username"] == test_user.username:
                found_user = user
                break

        assert found_user is not None
        assert found_user["email"] == test_user.email

    @pytest.mark.asyncio
    async def test_get_users_with_active_filter(self, client: AsyncClient, test_inactive_user: User, admin_token: str):
        """测试用户列表活跃状态过滤"""
        headers = get_auth_headers(admin_token)

        # 获取活跃用户
        response = await client.get("/api/v1/users/?is_active=true", headers=headers)
        assert response.status_code == 200
        active_users = response.json()["items"]

        # 获取非活跃用户
        response = await client.get("/api/v1/users/?is_active=false", headers=headers)
        assert response.status_code == 200
        inactive_users = response.json()["items"]

        # 验证过滤结果
        for user in active_users:
            assert user["is_active"] is True

        for user in inactive_users:
            assert user["is_active"] is False

    @pytest.mark.asyncio
    async def test_get_user_by_id_as_owner(self, client: AsyncClient, test_user: User, user_token: str):
        """测试用户获取自己的详情"""
        headers = get_auth_headers(user_token)
        response = await client.get(f"/api/v1/users/{test_user.id}", headers=headers)

        assert response.status_code == 200
        data = response.json()

        assert data["id"] == test_user.id
        assert data["email"] == test_user.email
        assert data["username"] == test_user.username
        assert data["first_name"] == test_user.first_name
        assert data["last_name"] == test_user.last_name

    @pytest.mark.asyncio
    async def test_get_user_by_id_as_admin(self, client: AsyncClient, test_user: User, admin_token: str):
        """测试管理员获取用户详情"""
        headers = get_auth_headers(admin_token)
        response = await client.get(f"/api/v1/users/{test_user.id}", headers=headers)

        assert response.status_code == 200
        data = response.json()

        assert data["id"] == test_user.id
        assert data["email"] == test_user.email

    @pytest.mark.asyncio
    async def test_get_user_by_id_forbidden(self, client: AsyncClient, test_user: User, test_admin_user: User, user_token: str):
        """测试用户获取其他用户详情（应该被拒绝）"""
        headers = get_auth_headers(user_token)
        response = await client.get(f"/api/v1/users/{test_admin_user.id}", headers=headers)

        assert response.status_code == 403

    @pytest.mark.asyncio
    async def test_get_user_by_id_not_found(self, client: AsyncClient, admin_token: str):
        """测试获取不存在的用户"""
        headers = get_auth_headers(admin_token)
        response = await client.get("/api/v1/users/99999", headers=headers)

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_create_user_as_admin(self, client: AsyncClient, admin_token: str):
        """测试管理员创建用户"""
        headers = get_auth_headers(admin_token)
        response = await client.post("/api/v1/users/", json=TEST_USER_DATA, headers=headers)

        assert response.status_code == 201
        data = response.json()

        assert data["email"] == TEST_USER_DATA["email"]
        assert data["username"] == TEST_USER_DATA["username"]
        assert data["first_name"] == TEST_USER_DATA["first_name"]
        assert data["last_name"] == TEST_USER_DATA["last_name"]
        assert "id" in data
        assert "password" not in data

    @pytest.mark.asyncio
    async def test_create_user_as_regular_user(self, client: AsyncClient, user_token: str):
        """测试普通用户创建用户（应该被拒绝）"""
        headers = get_auth_headers(user_token)
        response = await client.post("/api/v1/users/", json=TEST_USER_DATA, headers=headers)

        assert response.status_code == 403

    @pytest.mark.asyncio
    async def test_create_user_duplicate_email(self, client: AsyncClient, test_user: User, admin_token: str):
        """测试创建重复邮箱用户"""
        headers = get_auth_headers(admin_token)
        user_data = TEST_USER_DATA.copy()
        user_data["email"] = test_user.email

        response = await client.post("/api/v1/users/", json=user_data, headers=headers)

        assert response.status_code == 400
        assert "邮箱已被使用" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_create_user_duplicate_username(self, client: AsyncClient, test_user: User, admin_token: str):
        """测试创建重复用户名用户"""
        headers = get_auth_headers(admin_token)
        user_data = TEST_USER_DATA.copy()
        user_data["username"] = test_user.username

        response = await client.post("/api/v1/users/", json=user_data, headers=headers)

        assert response.status_code == 400
        assert "用户名已被使用" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_update_user_as_owner(self, client: AsyncClient, test_user: User, user_token: str):
        """测试用户更新自己的信息"""
        headers = get_auth_headers(user_token)
        update_data = {
            "first_name": "Updated",
            "last_name": "Name"
        }

        response = await client.put(f"/api/v1/users/{test_user.id}", json=update_data, headers=headers)

        assert response.status_code == 200
        data = response.json()

        assert data["first_name"] == "Updated"
        assert data["last_name"] == "Name"
        assert data["email"] == test_user.email  # 其他字段不变

    @pytest.mark.asyncio
    async def test_update_user_as_admin(self, client: AsyncClient, test_user: User, admin_token: str):
        """测试管理员更新用户信息"""
        headers = get_auth_headers(admin_token)
        update_data = {
            "first_name": "Admin Updated",
            "is_active": False
        }

        response = await client.put(f"/api/v1/users/{test_user.id}", json=update_data, headers=headers)

        assert response.status_code == 200
        data = response.json()

        assert data["first_name"] == "Admin Updated"
        assert data["is_active"] is False

    @pytest.mark.asyncio
    async def test_update_user_forbidden(self, client: AsyncClient, test_admin_user: User, user_token: str):
        """测试用户更新其他用户信息（应该被拒绝）"""
        headers = get_auth_headers(user_token)
        update_data = {"first_name": "Hacker"}

        response = await client.put(f"/api/v1/users/{test_admin_user.id}", json=update_data, headers=headers)

        assert response.status_code == 403

    @pytest.mark.asyncio
    async def test_update_user_not_found(self, client: AsyncClient, admin_token: str):
        """测试更新不存在的用户"""
        headers = get_auth_headers(admin_token)
        update_data = {"first_name": "Not Found"}

        response = await client.put("/api/v1/users/99999", json=update_data, headers=headers)

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_user_as_admin(self, client: AsyncClient, test_user: User, admin_token: str, db_session: AsyncSession):
        """测试管理员删除用户"""
        headers = get_auth_headers(admin_token)
        response = await client.delete(f"/api/v1/users/{test_user.id}", headers=headers)

        assert response.status_code == 200
        assert "用户已删除" in response.json()["message"]

        # 验证用户已被软删除
        await db_session.refresh(test_user)
        assert test_user.is_active is False
        assert test_user.deleted_at is not None

    @pytest.mark.asyncio
    async def test_delete_user_as_regular_user(self, client: AsyncClient, test_admin_user: User, user_token: str):
        """测试普通用户删除用户（应该被拒绝）"""
        headers = get_auth_headers(user_token)
        response = await client.delete(f"/api/v1/users/{test_admin_user.id}", headers=headers)

        assert response.status_code == 403

    @pytest.mark.asyncio
    async def test_delete_user_not_found(self, client: AsyncClient, admin_token: str):
        """测试删除不存在的用户"""
        headers = get_auth_headers(admin_token)
        response = await client.delete("/api/v1/users/99999", headers=headers)

        assert response.status_code == 404
