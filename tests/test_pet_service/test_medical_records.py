"""
医疗记录API测试
"""
import pytest
from datetime import datetime
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from services.user_service.models.user import User
from services.pet_service.models.pet import Pet, MedicalRecord


class TestMedicalRecordsAPI:
    """医疗记录API测试类"""
    
    @pytest.mark.asyncio
    async def test_create_medical_record(
        self,
        client: AsyncClient,
        db_session: AsyncSession,
        test_pet: Pet,
        auth_headers: dict
    ):
        """测试创建医疗记录"""
        record_data = {
            "pet_id": test_pet.id,
            "visit_date": "2024-01-15T10:00:00",
            "diagnosis": "Regular checkup",
            "symptoms": "None",
            "treatment": "Vaccination",
            "veterinarian_name": "Dr. <PERSON>",
            "clinic_name": "Pet Care Clinic",
            "cost": 150.0,
            "notes": "Healthy pet"
        }
        
        response = await client.post(
            f"/api/v1/pets/{test_pet.id}/medical-records",
            json=record_data,
            headers=auth_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["diagnosis"] == "Regular checkup"
        assert data["pet_id"] == test_pet.id
        assert data["cost"] == 150.0
    
    @pytest.mark.asyncio
    async def test_create_medical_record_pet_id_mismatch(
        self,
        client: AsyncClient,
        test_pet: Pet,
        auth_headers: dict
    ):
        """测试创建医疗记录时宠物ID不匹配"""
        record_data = {
            "pet_id": 999,  # 不匹配的ID
            "visit_date": "2024-01-15T10:00:00",
            "diagnosis": "Regular checkup"
        }
        
        response = await client.post(
            f"/api/v1/pets/{test_pet.id}/medical-records",
            json=record_data,
            headers=auth_headers
        )
        
        assert response.status_code == 400
    
    @pytest.mark.asyncio
    async def test_get_medical_records(
        self,
        client: AsyncClient,
        db_session: AsyncSession,
        test_pet: Pet,
        auth_headers: dict
    ):
        """测试获取医疗记录列表"""
        # 创建测试医疗记录
        record = MedicalRecord(
            pet_id=test_pet.id,
            visit_date=datetime.now(),
            diagnosis="Test diagnosis",
            treatment="Test treatment"
        )
        db_session.add(record)
        await db_session.commit()
        
        response = await client.get(
            f"/api/v1/pets/{test_pet.id}/medical-records",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["total"] >= 1
        assert len(data["items"]) >= 1
        assert data["items"][0]["diagnosis"] == "Test diagnosis"
    
    @pytest.mark.asyncio
    async def test_get_medical_record_by_id(
        self,
        client: AsyncClient,
        db_session: AsyncSession,
        test_pet: Pet,
        auth_headers: dict
    ):
        """测试根据ID获取医疗记录"""
        # 创建测试医疗记录
        record = MedicalRecord(
            pet_id=test_pet.id,
            visit_date=datetime.now(),
            diagnosis="Test diagnosis",
            treatment="Test treatment"
        )
        db_session.add(record)
        await db_session.commit()
        await db_session.refresh(record)
        
        response = await client.get(
            f"/api/v1/medical-records/{record.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == record.id
        assert data["diagnosis"] == "Test diagnosis"
    
    @pytest.mark.asyncio
    async def test_update_medical_record(
        self,
        client: AsyncClient,
        db_session: AsyncSession,
        test_pet: Pet,
        auth_headers: dict
    ):
        """测试更新医疗记录"""
        # 创建测试医疗记录
        record = MedicalRecord(
            pet_id=test_pet.id,
            visit_date=datetime.now(),
            diagnosis="Original diagnosis",
            treatment="Original treatment"
        )
        db_session.add(record)
        await db_session.commit()
        await db_session.refresh(record)
        
        update_data = {
            "diagnosis": "Updated diagnosis",
            "cost": 200.0
        }
        
        response = await client.put(
            f"/api/v1/medical-records/{record.id}",
            json=update_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["diagnosis"] == "Updated diagnosis"
        assert data["cost"] == 200.0
    
    @pytest.mark.asyncio
    async def test_delete_medical_record(
        self,
        client: AsyncClient,
        db_session: AsyncSession,
        test_pet: Pet,
        auth_headers: dict
    ):
        """测试删除医疗记录"""
        # 创建测试医疗记录
        record = MedicalRecord(
            pet_id=test_pet.id,
            visit_date=datetime.now(),
            diagnosis="Test diagnosis"
        )
        db_session.add(record)
        await db_session.commit()
        await db_session.refresh(record)
        
        response = await client.delete(
            f"/api/v1/medical-records/{record.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 204
        
        # 验证记录已被删除
        response = await client.get(
            f"/api/v1/medical-records/{record.id}",
            headers=auth_headers
        )
        assert response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_access_other_user_medical_record_forbidden(
        self,
        client: AsyncClient,
        db_session: AsyncSession,
        test_admin: User,
        auth_headers: dict
    ):
        """测试访问其他用户的医疗记录被禁止"""
        # 创建属于管理员的宠物和医疗记录
        admin_pet = Pet(
            name="Admin Pet",
            species="Cat",
            owner_id=test_admin.id
        )
        db_session.add(admin_pet)
        await db_session.commit()
        await db_session.refresh(admin_pet)
        
        record = MedicalRecord(
            pet_id=admin_pet.id,
            visit_date=datetime.now(),
            diagnosis="Admin pet diagnosis"
        )
        db_session.add(record)
        await db_session.commit()
        await db_session.refresh(record)
        
        # 普通用户尝试访问管理员的医疗记录
        response = await client.get(
            f"/api/v1/medical-records/{record.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 403
    
    @pytest.mark.asyncio
    async def test_pagination_medical_records(
        self,
        client: AsyncClient,
        test_pet: Pet,
        auth_headers: dict
    ):
        """测试医疗记录分页"""
        response = await client.get(
            f"/api/v1/pets/{test_pet.id}/medical-records?page=1&size=5",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["page"] == 1
        assert data["size"] == 5
        assert "pages" in data
        assert "total" in data
