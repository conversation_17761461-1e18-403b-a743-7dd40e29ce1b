"""
品种管理API测试
"""
import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from services.user_service.models.user import User
from services.pet_service.models.pet import Breed


class TestBreedsAPI:
    """品种API测试类"""
    
    @pytest.mark.asyncio
    async def test_get_breeds_public(
        self,
        client: AsyncClient,
        test_breed: Breed
    ):
        """测试获取品种列表（公开接口）"""
        response = await client.get("/api/v1/breeds/")
        
        assert response.status_code == 200
        data = response.json()
        assert data["total"] >= 1
        assert len(data["items"]) >= 1
        assert data["items"][0]["name"] == test_breed.name
    
    @pytest.mark.asyncio
    async def test_get_breed_by_id_public(
        self,
        client: AsyncClient,
        test_breed: Breed
    ):
        """测试根据ID获取品种（公开接口）"""
        response = await client.get(f"/api/v1/breeds/{test_breed.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_breed.id
        assert data["name"] == test_breed.name
        assert data["species"] == test_breed.species
    
    @pytest.mark.asyncio
    async def test_create_breed_admin_required(
        self,
        client: AsyncClient,
        admin_auth_headers: dict
    ):
        """测试创建品种（需要管理员权限）"""
        breed_data = {
            "name": "Labrador Retriever",
            "species": "Dog",
            "description": "A friendly and outgoing dog breed",
            "origin_country": "Canada",
            "average_weight_min": 25.0,
            "average_weight_max": 36.0,
            "life_expectancy": 12,
            "temperament": "Friendly, Outgoing, Active"
        }
        
        response = await client.post(
            "/api/v1/breeds/",
            json=breed_data,
            headers=admin_auth_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "Labrador Retriever"
        assert data["species"] == "Dog"
        assert data["origin_country"] == "Canada"
    
    @pytest.mark.asyncio
    async def test_create_breed_forbidden_for_regular_user(
        self,
        client: AsyncClient,
        auth_headers: dict
    ):
        """测试普通用户不能创建品种"""
        breed_data = {
            "name": "Test Breed",
            "species": "Dog"
        }
        
        response = await client.post(
            "/api/v1/breeds/",
            json=breed_data,
            headers=auth_headers
        )
        
        assert response.status_code == 403
    
    @pytest.mark.asyncio
    async def test_create_breed_duplicate_name(
        self,
        client: AsyncClient,
        test_breed: Breed,
        admin_auth_headers: dict
    ):
        """测试创建重复品种名称"""
        breed_data = {
            "name": test_breed.name,  # 重复的名称
            "species": test_breed.species
        }
        
        response = await client.post(
            "/api/v1/breeds/",
            json=breed_data,
            headers=admin_auth_headers
        )
        
        assert response.status_code == 400
        assert "已存在" in response.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_update_breed(
        self,
        client: AsyncClient,
        test_breed: Breed,
        admin_auth_headers: dict
    ):
        """测试更新品种信息"""
        update_data = {
            "description": "Updated description",
            "life_expectancy": 15
        }
        
        response = await client.put(
            f"/api/v1/breeds/{test_breed.id}",
            json=update_data,
            headers=admin_auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["description"] == "Updated description"
        assert data["life_expectancy"] == 15
    
    @pytest.mark.asyncio
    async def test_update_breed_forbidden_for_regular_user(
        self,
        client: AsyncClient,
        test_breed: Breed,
        auth_headers: dict
    ):
        """测试普通用户不能更新品种"""
        update_data = {"description": "Updated description"}
        
        response = await client.put(
            f"/api/v1/breeds/{test_breed.id}",
            json=update_data,
            headers=auth_headers
        )
        
        assert response.status_code == 403
    
    @pytest.mark.asyncio
    async def test_delete_breed(
        self,
        client: AsyncClient,
        db_session: AsyncSession,
        admin_auth_headers: dict
    ):
        """测试删除品种"""
        # 创建一个新品种用于删除测试
        breed = Breed(
            name="Test Delete Breed",
            species="Cat",
            description="For deletion test"
        )
        db_session.add(breed)
        await db_session.commit()
        await db_session.refresh(breed)
        
        response = await client.delete(
            f"/api/v1/breeds/{breed.id}",
            headers=admin_auth_headers
        )
        
        assert response.status_code == 204
        
        # 验证品种已被删除
        response = await client.get(f"/api/v1/breeds/{breed.id}")
        assert response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_get_breeds_by_species(
        self,
        client: AsyncClient,
        test_breed: Breed
    ):
        """测试根据物种获取品种列表"""
        response = await client.get(f"/api/v1/breeds/species/{test_breed.species}/breeds")
        
        assert response.status_code == 200
        data = response.json()
        assert all(item["species"] == test_breed.species for item in data["items"])
    
    @pytest.mark.asyncio
    async def test_search_breeds(
        self,
        client: AsyncClient,
        test_breed: Breed
    ):
        """测试搜索品种"""
        response = await client.get(f"/api/v1/breeds/?search={test_breed.name[:5]}")
        
        assert response.status_code == 200
        data = response.json()
        assert any(test_breed.name in item["name"] for item in data["items"])
    
    @pytest.mark.asyncio
    async def test_filter_breeds_by_species(
        self,
        client: AsyncClient,
        test_breed: Breed
    ):
        """测试按物种过滤品种"""
        response = await client.get(f"/api/v1/breeds/?species={test_breed.species}")
        
        assert response.status_code == 200
        data = response.json()
        assert all(item["species"] == test_breed.species for item in data["items"])
    
    @pytest.mark.asyncio
    async def test_pagination_breeds(
        self,
        client: AsyncClient
    ):
        """测试品种分页"""
        response = await client.get("/api/v1/breeds/?page=1&size=5")
        
        assert response.status_code == 200
        data = response.json()
        assert data["page"] == 1
        assert data["size"] == 5
        assert "pages" in data
        assert "total" in data
