"""
宠物服务测试配置
"""
import asyncio
import pytest
import pytest_asyncio
from typing import AsyncGenerator
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import StaticPool

from shared.database import Base, get_async_db
from shared.utils.security import get_password_hash
from services.pet_service.main import app
from services.user_service.models.user import User
from services.pet_service.models.pet import Pet, Breed, PetGender, PetStatus


# 测试数据库URL
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"

# 创建测试数据库引擎
test_engine = create_async_engine(
    TEST_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
    echo=False
)

TestSessionLocal = async_sessionmaker(
    test_engine, class_=AsyncSession, expire_on_commit=False
)


@pytest_asyncio.fixture(scope="function")
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """创建测试数据库会话"""
    # 确保所有模型都被导入（注意导入顺序）
    from services.user_service.models.permission import Permission
    from services.user_service.models.user import User, APIKey, UserSession
    from services.pet_service.models.pet import Pet, MedicalRecord, Vaccination, Breed

    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    async with TestSessionLocal() as session:
        yield session

    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest_asyncio.fixture(scope="function")
async def client(db_session: AsyncSession) -> AsyncGenerator[AsyncClient, None]:
    """创建测试客户端"""

    def override_get_async_db():
        return db_session

    app.dependency_overrides[get_async_db] = override_get_async_db

    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac

    app.dependency_overrides.clear()


@pytest_asyncio.fixture
async def test_user(db_session: AsyncSession) -> User:
    """创建测试用户"""
    user = User(
        email="<EMAIL>",
        username="testuser",
        hashed_password=get_password_hash("testpassword"),
        first_name="Test",
        last_name="User",
        is_active=True,
        is_superuser=False,
        is_verified=True
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    return user


@pytest_asyncio.fixture
async def test_admin(db_session: AsyncSession) -> User:
    """创建测试管理员"""
    admin = User(
        email="<EMAIL>",
        username="admin",
        hashed_password=get_password_hash("adminpassword"),
        first_name="Admin",
        last_name="User",
        is_active=True,
        is_superuser=True,
        is_verified=True
    )
    db_session.add(admin)
    await db_session.commit()
    await db_session.refresh(admin)
    return admin


@pytest_asyncio.fixture
async def test_pet(db_session: AsyncSession, test_user: User) -> Pet:
    """创建测试宠物"""
    pet = Pet(
        name="Buddy",
        species="Dog",
        breed="Golden Retriever",
        gender=PetGender.MALE,
        age_years=3,
        color="Golden",
        weight=30.5,
        status=PetStatus.ACTIVE,
        is_neutered=True,
        is_vaccinated=True,
        owner_id=test_user.id
    )
    db_session.add(pet)
    await db_session.commit()
    await db_session.refresh(pet)
    return pet


@pytest_asyncio.fixture
async def test_breed(db_session: AsyncSession) -> Breed:
    """创建测试品种"""
    breed = Breed(
        name="Golden Retriever",
        species="Dog",
        description="A friendly and intelligent dog breed",
        origin_country="Scotland",
        average_weight_min=25.0,
        average_weight_max=35.0,
        life_expectancy=12,
        temperament="Friendly, Intelligent, Devoted"
    )
    db_session.add(breed)
    await db_session.commit()
    await db_session.refresh(breed)
    return breed


@pytest.fixture
def auth_headers(test_user: User) -> dict:
    """创建认证头"""
    from shared.utils.security import create_access_token
    token = create_access_token(data={"sub": str(test_user.id)})
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def admin_auth_headers(test_admin: User) -> dict:
    """创建管理员认证头"""
    from shared.utils.security import create_access_token
    token = create_access_token(data={"sub": str(test_admin.id)})
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()
