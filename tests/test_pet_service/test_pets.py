"""
宠物管理API测试
"""
import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from services.user_service.models.user import User
from services.pet_service.models.pet import Pet


class TestPetsAPI:
    """宠物API测试类"""

    @pytest.mark.asyncio
    async def test_create_pet(
        self,
        client: AsyncClient,
        db_session: AsyncSession,
        test_user: User,
        auth_headers: dict
    ):
        """测试创建宠物"""
        pet_data = {
            "name": "Max",
            "species": "Dog",
            "breed": "Labrador",
            "gender": "male",
            "age_years": 2,
            "color": "Black",
            "weight": 25.5,
            "owner_id": test_user.id
        }

        response = await client.post(
            "/api/v1/pets/",
            json=pet_data,
            headers=auth_headers
        )

        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "Max"
        assert data["species"] == "Dog"
        assert data["owner_id"] == test_user.id

    @pytest.mark.asyncio
    async def test_create_pet_unauthorized(
        self,
        client: AsyncClient,
        test_user: User
    ):
        """测试未授权创建宠物"""
        pet_data = {
            "name": "Max",
            "species": "Dog",
            "owner_id": test_user.id
        }

        response = await client.post("/api/v1/pets/", json=pet_data)
        # 在我们的实现中，未授权请求返回403而不是401
        assert response.status_code == 403

    @pytest.mark.asyncio
    async def test_create_pet_for_other_user_forbidden(
        self,
        client: AsyncClient,
        test_user: User,
        test_admin: User,
        auth_headers: dict
    ):
        """测试普通用户不能为其他用户创建宠物"""
        pet_data = {
            "name": "Max",
            "species": "Dog",
            "owner_id": test_admin.id  # 尝试为管理员创建宠物
        }

        response = await client.post(
            "/api/v1/pets/",
            json=pet_data,
            headers=auth_headers
        )

        assert response.status_code == 403

    @pytest.mark.asyncio
    async def test_get_pets(
        self,
        client: AsyncClient,
        test_pet: Pet,
        auth_headers: dict
    ):
        """测试获取宠物列表"""
        response = await client.get("/api/v1/pets/", headers=auth_headers)

        assert response.status_code == 200
        data = response.json()
        assert data["total"] >= 1
        assert len(data["items"]) >= 1
        assert data["items"][0]["name"] == test_pet.name

    @pytest.mark.asyncio
    async def test_get_pet_by_id(
        self,
        client: AsyncClient,
        test_pet: Pet,
        auth_headers: dict
    ):
        """测试根据ID获取宠物"""
        response = await client.get(f"/api/v1/pets/{test_pet.id}", headers=auth_headers)

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_pet.id
        assert data["name"] == test_pet.name

    @pytest.mark.asyncio
    async def test_get_pet_not_found(
        self,
        client: AsyncClient,
        auth_headers: dict
    ):
        """测试获取不存在的宠物"""
        response = await client.get("/api/v1/pets/999", headers=auth_headers)
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_pet(
        self,
        client: AsyncClient,
        test_pet: Pet,
        auth_headers: dict
    ):
        """测试更新宠物信息"""
        update_data = {
            "name": "Updated Buddy",
            "weight": 32.0
        }

        response = await client.put(
            f"/api/v1/pets/{test_pet.id}",
            json=update_data,
            headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Updated Buddy"
        assert data["weight"] == 32.0

    @pytest.mark.asyncio
    async def test_delete_pet(
        self,
        client: AsyncClient,
        test_pet: Pet,
        auth_headers: dict
    ):
        """测试删除宠物"""
        response = await client.delete(f"/api/v1/pets/{test_pet.id}", headers=auth_headers)
        assert response.status_code == 204

        # 验证宠物已被删除
        response = await client.get(f"/api/v1/pets/{test_pet.id}", headers=auth_headers)
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_get_pet_stats(
        self,
        client: AsyncClient,
        test_pet: Pet,
        auth_headers: dict
    ):
        """测试获取宠物统计信息"""
        response = await client.get(f"/api/v1/pets/{test_pet.id}/stats", headers=auth_headers)

        assert response.status_code == 200
        data = response.json()
        assert data["pet_id"] == test_pet.id
        assert "medical_records_count" in data
        assert "vaccinations_count" in data
        assert "age_display" in data

    @pytest.mark.asyncio
    async def test_search_pets(
        self,
        client: AsyncClient,
        test_pet: Pet,
        auth_headers: dict
    ):
        """测试搜索宠物"""
        response = await client.get(
            "/api/v1/pets/?search=Buddy",
            headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        assert data["total"] >= 1
        assert any(item["name"] == "Buddy" for item in data["items"])

    @pytest.mark.asyncio
    async def test_filter_pets_by_species(
        self,
        client: AsyncClient,
        test_pet: Pet,
        auth_headers: dict
    ):
        """测试按物种过滤宠物"""
        response = await client.get(
            "/api/v1/pets/?species=Dog",
            headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        assert all(item["species"] == "Dog" for item in data["items"])

    @pytest.mark.asyncio
    async def test_pagination(
        self,
        client: AsyncClient,
        auth_headers: dict
    ):
        """测试分页功能"""
        response = await client.get(
            "/api/v1/pets/?page=1&size=5",
            headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        assert data["page"] == 1
        assert data["size"] == 5
        assert "pages" in data
        assert "total" in data
