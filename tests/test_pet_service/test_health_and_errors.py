"""
宠物服务健康检查和错误处理测试
"""
import pytest
from httpx import AsyncClient


class TestHealthAndErrors:
    """健康检查和错误处理测试类"""

    @pytest.mark.asyncio
    async def test_health_check(self, client: AsyncClient):
        """测试健康检查端点"""
        response = await client.get("/health")

        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "pet_service"
        assert "version" in data

    @pytest.mark.asyncio
    async def test_openapi_docs(self, client: AsyncClient):
        """测试OpenAPI文档端点"""
        response = await client.get("/api/v1/openapi.json")
        assert response.status_code == 200

        data = response.json()
        assert "openapi" in data
        assert "info" in data
        assert "paths" in data

    @pytest.mark.asyncio
    async def test_docs_ui(self, client: AsyncClient):
        """测试文档UI端点"""
        response = await client.get("/api/v1/docs")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]

    @pytest.mark.asyncio
    async def test_not_found_endpoint(self, client: AsyncClient):
        """测试不存在的端点"""
        response = await client.get("/api/v1/nonexistent")
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_method_not_allowed(self, client: AsyncClient):
        """测试不允许的HTTP方法"""
        response = await client.patch("/api/v1/pets/")
        assert response.status_code == 405

    @pytest.mark.asyncio
    async def test_invalid_json(self, client: AsyncClient, auth_headers: dict):
        """测试无效的JSON数据"""
        response = await client.post(
            "/api/v1/pets/",
            content="invalid json",
            headers={**auth_headers, "Content-Type": "application/json"}
        )
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_missing_required_fields(self, client: AsyncClient, auth_headers: dict):
        """测试缺少必需字段"""
        pet_data = {
            # 缺少必需的name和species字段
            "owner_id": 1
        }

        response = await client.post(
            "/api/v1/pets/",
            json=pet_data,
            headers=auth_headers
        )

        assert response.status_code == 422
        data = response.json()
        assert "detail" in data

    @pytest.mark.asyncio
    async def test_invalid_field_types(self, client: AsyncClient, auth_headers: dict):
        """测试无效的字段类型"""
        pet_data = {
            "name": "Test Pet",
            "species": "Dog",
            "owner_id": "invalid_id",  # 应该是整数
            "weight": "invalid_weight"  # 应该是浮点数
        }

        response = await client.post(
            "/api/v1/pets/",
            json=pet_data,
            headers=auth_headers
        )

        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_field_validation_errors(self, client: AsyncClient, auth_headers: dict):
        """测试字段验证错误"""
        pet_data = {
            "name": "",  # 空字符串，应该至少1个字符
            "species": "Dog",
            "owner_id": 1,
            "age_years": -1,  # 负数，应该>=0
            "weight": 300  # 超出合理范围
        }

        response = await client.post(
            "/api/v1/pets/",
            json=pet_data,
            headers=auth_headers
        )

        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_cors_headers(self, client: AsyncClient):
        """测试CORS头"""
        # 使用GET请求来测试CORS头，因为OPTIONS可能不被支持
        response = await client.get("/api/v1/pets/")

        # 检查CORS相关头是否存在（可能在响应中）
        # 注意：在测试环境中，CORS头可能不会完全显示
        # 我们主要测试服务能正常响应
        assert response.status_code in [200, 401, 403]  # 任何有效的HTTP状态码

    @pytest.mark.asyncio
    async def test_large_request_body(self, client: AsyncClient, auth_headers: dict):
        """测试大请求体"""
        pet_data = {
            "name": "Test Pet",
            "species": "Dog",
            "owner_id": 1,
            "description": "x" * 10000  # 非常长的描述
        }

        response = await client.post(
            "/api/v1/pets/",
            json=pet_data,
            headers=auth_headers
        )

        # 应该能处理合理大小的请求
        assert response.status_code in [201, 422]  # 创建成功或验证失败

    @pytest.mark.asyncio
    async def test_concurrent_requests(self, client: AsyncClient, auth_headers: dict):
        """测试并发请求"""
        import asyncio

        async def make_request():
            return await client.get("/api/v1/pets/", headers=auth_headers)

        # 发送多个并发请求
        tasks = [make_request() for _ in range(5)]
        responses = await asyncio.gather(*tasks)

        # 所有请求都应该成功
        for response in responses:
            assert response.status_code == 200
