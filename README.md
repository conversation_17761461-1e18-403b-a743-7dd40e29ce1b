# 宠物医疗AI开放平台

一个基于FastAPI的微服务架构宠物医疗AI开放平台，提供完整的宠物管理、用户权限控制和OpenAI兼容API网关功能。

## 🏗️ 项目架构

### 核心技术栈
- **后端框架**: FastAPI + SQLAlchemy + Pydantic
- **数据库**: PostgreSQL + Redis
- **API网关**: 自建FastAPI网关（支持OpenAI兼容API）
- **容器化**: Docker + Docker Compose
- **异步任务**: Celery + Redis（可选）
- **日志系统**: Loguru + 结构化日志
- **认证授权**: JWT + API Key双重认证

### 微服务架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用       │    │   第三方客户端   │    │   OpenAI SDK    │
│  (Web/Mobile)   │    │  (API调用)      │    │   兼容客户端     │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      API网关 (8000)       │
                    │  ┌─────────────────────┐  │
                    │  │  OpenAI兼容API      │  │
                    │  │  /v1/chat/...      │  │
                    │  └─────────────────────┘  │
                    │  ┌─────────────────────┐  │
                    │  │  业务API路由        │  │
                    │  │  /api/v1/...       │  │
                    │  └─────────────────────┘  │
                    │  ┌─────────────────────┐  │
                    │  │  认证 & 限流 & 日志  │  │
                    │  └─────────────────────┘  │
                    └─────────┬───┬─────────────┘
                              │   │
                    ┌─────────▼───▼─────────────┐
                    │      业务服务层           │
                    └─────────┬───┬─────────────┘
                              │   │
              ┌───────────────▼───▼───────────────┐
              │           数据存储层               │
              │  ┌─────────────┐ ┌─────────────┐  │
              │  │ PostgreSQL  │ │    Redis    │  │
              │  │   (主数据)   │ │ (缓存&会话) │  │
              │  └─────────────┘ └─────────────┘  │
              └───────────────────────────────────┘
```

### 服务组件
- **API网关层** (端口 8000)
  - 统一入口和路由分发
  - JWT认证和API Key验证
  - 请求限流和日志记录
  - OpenAI兼容API代理
  - 健康检查和服务监控

- **业务服务层**
  - **用户权限服务** (端口 8001): 用户管理、认证、权限控制、API Key管理
  - **宠物管理服务** (端口 8002): 宠物档案、医疗记录、疫苗管理、品种信息
  - **应用服务** (端口 8003): 智能体管理、AI服务集成、业务逻辑处理

- **数据存储层**
  - **PostgreSQL**: 主要业务数据存储
  - **Redis**: 缓存、会话管理、速率限制计数器

- **AI服务层**
  - 远程LLM服务集成
  - 支持企业自有模型 (ds-vet-answer-32B等)
  - 模型路由和负载均衡

## 🚀 快速开始

### 环境要求
- **Python**: 3.9+ (推荐 3.11)
- **数据库**: PostgreSQL 13+
- **缓存**: Redis 6+
- **容器化**: Docker + Docker Compose (可选)
- **操作系统**: Linux/macOS/Windows

### 安装和运行

1. 克隆项目
```bash
<NAME_EMAIL>:bigdata/vet_open_platform.git
cd vet_open_platform
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和Redis连接
```

4. 初始化开发环境（推荐）
```bash
# 一键设置完整开发环境（包括数据库初始化和默认用户创建）
python scripts/manage.py dev setup
```

5. 启动服务
```bash
# 启动所有服务（推荐）
python scripts/manage.py service start

# 或者使用开发脚本
python scripts/start_dev.py

# 或者分别启动单个服务
python scripts/manage.py service start-single gateway  # API网关
python scripts/manage.py service start-single user     # 用户服务
python scripts/manage.py service start-single pet      # 宠物服务
```

### 默认账户
初始化后会自动创建以下账户：
- **管理员账户**: `<EMAIL>` / `VetAdmin123!`
- **测试账户**: `<EMAIL>` / `TestUser123!`

### 手动管理（可选）
```bash
# 仅初始化数据库
python scripts/init_db.py

# 重新创建所有表
python scripts/init_db.py --drop-tables

# 测试数据库连接
python scripts/manage.py db test
```

## ✨ 核心功能

### 🤖 OpenAI兼容API网关
- **🔌 完全兼容OpenAI API**: 支持 `/v1/chat/completions`, `/v1/models` 等标准端点
- **🏢 企业模型支持**: 直接使用企业模型名称 (`ds-vet-answer-32B` 等)
- **🌊 流式响应**: 支持Server-Sent Events (SSE) 实时流式输出
- **🔐 双重认证**: 支持OpenAI标准API Key + JWT认证
- **📊 智能限流**: 基于用户和API Key的多维度速率限制
- **📈 使用统计**: 详细的调用量统计和成本分析

> 📖 详细文档: [OpenAI兼容API网关文档](docs/openai_api_gateway.md)

### 🤖 智能体管理系统
- **🧠 系统级智能体**: 四个预定义专业AI助手
  - **AI问诊智能体**: 分析宠物症状，提供诊断建议
  - **AI视觉识别智能体**: 图像分析，健康状况评估
  - **AI报告生成智能体**: 生成专业健康管理报告
  - **AI报告解读智能体**: 解读医疗报告，提供通俗解释
- **🎨 用户自定义智能体**: 创建个性化AI助手
  - **自定义提示词**: 灵活配置智能体行为
  - **版本管理**: 支持版本控制和回滚
  - **权限控制**: 细粒度的访问权限管理
  - **公开分享**: 支持智能体公开和私有模式

### 🔍 OCR识别服务
- **📷 图片识别**: 支持多种格式图片的文字识别
- **🧠 智能模型**: 集成QwenVL视觉大模型，识别准确率高
- **🔄 批量处理**: 支持批量图片OCR处理和进度跟踪
- **🔌 可插拔架构**: 支持多种OCR模型，可灵活切换
- **📊 任务管理**: 完整的任务状态跟踪和结果存储
- **🌐 多语言支持**: 支持中文、英文等多种语言识别

### 🏥 宠物医疗管理
- **📋 宠物档案**: 完整的宠物信息、品种数据库、成长记录
- **💉 疫苗管理**: 疫苗计划、接种记录、到期提醒
- **🩺 医疗记录**: 诊断记录、处方管理、治疗历史追踪
- **👥 多用户支持**: 宠物主人、兽医、诊所的权限分级管理

### 🔧 企业级特性
- **🏗️ 微服务架构**: 模块化设计，服务独立部署和扩展
- **🛡️ 安全认证**: JWT + API Key双重认证，细粒度权限控制
- **⚡ 高性能**: Redis缓存，异步处理，连接池优化
- **📝 完整日志**: 结构化日志，请求追踪，性能监控
- **🐳 容器化**: Docker支持，一键部署，环境隔离
- **🔄 健康检查**: 自动服务发现，故障自愈，优雅降级

## 🌐 服务端口

| 服务 | 端口 | 描述 | 状态 |
|------|------|------|------|
| **API网关** | [8000](http://localhost:8000) | 统一入口 + OpenAI兼容API | ✅ 运行中 |
| **用户服务** | [8001](http://localhost:8001) | 用户管理、认证、权限 | ✅ 运行中 |
| **宠物服务** | [8002](http://localhost:8002) | 宠物档案、医疗记录 | ✅ 运行中 |
| **应用服务** | [8003](http://localhost:8003) | 智能体管理、AI服务 | ✅ 运行中 |
| **OCR服务** | [8004](http://localhost:8004) | 图片OCR识别、文本提取 | ✅ 运行中 |

## 📚 API文档

### 🔗 在线文档
启动服务后，可以访问以下地址查看交互式API文档：

| 服务 | Swagger UI | ReDoc | 健康检查 |
|------|------------|-------|----------|
| **API网关** | [/api/v1/docs](http://localhost:8000/api/v1/docs) | [/api/v1/redoc](http://localhost:8000/api/v1/redoc) | [/health](http://localhost:8000/health) |
| **用户服务** | [/api/v1/docs](http://localhost:8001/api/v1/docs) | [/api/v1/redoc](http://localhost:8001/api/v1/redoc) | [/health](http://localhost:8001/health) |
| **宠物服务** | [/api/v1/docs](http://localhost:8002/api/v1/docs) | [/api/v1/redoc](http://localhost:8002/api/v1/redoc) | [/health](http://localhost:8002/health) |
| **应用服务** | [/api/v1/docs](http://localhost:8003/api/v1/docs) | [/api/v1/redoc](http://localhost:8003/api/v1/redoc) | [/health](http://localhost:8003/health) |

### 🤖 OpenAI兼容API
- **基础URL**: `http://localhost:8000/v1/`
- **兼容性**: 完全兼容OpenAI Python SDK
- **支持端点**: `/chat/completions`, `/models`
- **详细文档**: [OpenAI兼容API网关文档](docs/openai_api_gateway.md)

### 🧪 快速测试

#### 使用 cURL 测试
```bash
# 测试聊天完成API
curl -X POST "http://localhost:8000/v1/chat/completions" \
     -H "Authorization: Bearer sk-proj-your-api-key-here" \
     -H "Content-Type: application/json" \
     -d '{
       "model": "ds-vet-answer-32B",
       "messages": [
         {"role": "system", "content": "你是一个专业的宠物医疗助手"},
         {"role": "user", "content": "我的狗狗最近食欲不振，可能是什么原因？"}
       ],
       "stream": false
     }'

# 获取可用模型列表
curl -H "Authorization: Bearer sk-proj-your-api-key-here" \
     "http://localhost:8000/v1/models"
```

#### 使用 Python OpenAI SDK
```python
from openai import OpenAI

# 配置客户端
client = OpenAI(
    api_key="sk-proj-your-api-key-here",
    base_url="http://localhost:8000/v1"
)

# 发送聊天请求
response = client.chat.completions.create(
    model="ds-vet-answer-32B",
    messages=[
        {"role": "system", "content": "你是一个专业的宠物医疗助手"},
        {"role": "user", "content": "我的狗狗最近食欲不振，可能是什么原因？"}
    ],
    stream=False
)

print(response.choices[0].message.content)
```

### 🔍 OCR识别API使用示例

```python
import requests

# 单张图片OCR识别（同步）
response = requests.post(
    "http://localhost:8000/api/v1/ocr/extract/sync",
    headers={"Authorization": "Bearer your-api-key"},
    json={
        "image_url": "https://example.com/image.jpg",
        "model_type": "qwen_vl",
        "prompt": "请识别图片中的所有文字内容",
        "language": "auto",
        "confidence_threshold": 0.8
    }
)

result = response.json()
print(f"识别文本: {result['result']['text']}")
print(f"置信度: {result['result']['confidence']}")

# 查询任务状态
task_response = requests.get(
    f"http://localhost:8000/api/v1/ocr/task/{result['task_id']}",
    headers={"Authorization": "Bearer your-api-key"}
)

print(f"任务状态: {task_response.json()['status']}")
```

> 💡 **提示**: 使用初始化时创建的管理员账户登录后，可以在用户服务中创建API密钥

## 📖 文档和指南

### 📋 项目文档
- **[API网关文档](docs/api_gateway.md)** - 网关架构和配置说明
- **[OpenAI兼容API文档](docs/openai_api_gateway.md)** - OpenAI API使用指南
- **[OCR识别服务文档](docs/ocr_service_api.md)** - OCR API使用指南和配置说明
- **[脚本使用说明](scripts/README.md)** - 管理脚本详细说明

### 🛠️ 开发指南
- **环境设置**: 使用 `python scripts/manage.py dev setup` 一键配置
- **代码规范**: 遵循PEP 8，使用Black格式化，Flake8检查
- **测试**: 使用pytest进行单元测试和集成测试
- **日志**: 使用Loguru结构化日志，支持JSON格式输出

### 🚀 部署指南
- **开发环境**: 直接运行Python脚本
- **生产环境**: 使用Docker Compose或Kubernetes
- **监控**: 内置健康检查端点，支持Prometheus指标导出
- **扩展**: 支持水平扩展，无状态服务设计

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🆘 支持和反馈

- **问题报告**: [GitHub Issues](https://github.com/your-repo/issues)
- **功能请求**: [GitHub Discussions](https://github.com/your-repo/discussions)
- **文档问题**: 欢迎提交PR改进文档

---

<div align="center">

**🐾 让AI技术更好地服务宠物健康 🐾**

Made with ❤️ by Vet AI Platform Team

</div>